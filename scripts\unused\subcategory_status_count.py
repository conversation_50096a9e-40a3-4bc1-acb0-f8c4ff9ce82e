import requests
from collections import Counter
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
import sys

#
# Read the recruit_userfield10 to get the reason why a candidate wasn't processed.
# This will continue to exist even after the move to database.
# Run this after every subcategory_pool.py and generate_subcat_candidate_files.
# Still need to work on reducing the bad pdf/doc/docx
#

# Initialize data storage
all_statuses = []

# Function to fetch data with pagination
def fetch_data():
    enum_env = Environment.PROD  # Prod
    token = get_token_for_env(enum_env)
    credentials = get_dataverse_credentials_for_env(enum_env)
    url = credentials["RESOURCE_URL"]
    # Append the entity and query parameters to the URL
    url += "/api/data/v9.0/contacts?$select=recruit_userfield10"  # Replace YourEntity with the actual table name

    # Headers for API request
    headers = {
        "Authorization": f"Bearer {token.get_token()}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }


    while url:
        response = requests.get(url, headers=headers)

        """ 

        # Print the raw response content for debugging
        print(f"Response Status: {response.status_code}")
        print(f"Response Content: {response.text[:500]}")  # Print first 500 characters to inspect
        """ 
        if response.status_code == 200:
            try:
                data = response.json()  # Try parsing JSON
                
                # Ensure 'value' key exists in response
                if "value" not in data:
                    print("Warning: 'value' key missing in response.")
                    print("Full Response:", response.text)
                    break
                
                # Extract 'recruit_userfield10' values, ignoring None values
                all_statuses.extend(
                    record.get("recruit_userfield10") for record in data["value"]
                    if record.get("recruit_userfield10") is not None
                )
                
                # Get next page URL for pagination
                url = data.get("@odata.nextLink")
            except requests.exceptions.JSONDecodeError:
                print("Error: Response is not valid JSON. Full Response:")
                print(response.text)
                break
        else:
            print(f"HTTP Error: {response.status_code}, {response.text}")
            break

def get_contacts_by_status(status_to_match):
    """
    Get contact records based on recruit_userfield10 matching the specified status.
    Prints the records in CSV format.
    
    Args:
        status_to_match (str): The status to match in recruit_userfield10 (e.g., "no resume")
    """
    enum_env = Environment.PROD
    token = get_token_for_env(enum_env)
    credentials = get_dataverse_credentials_for_env(enum_env)
    url = credentials["RESOURCE_URL"]
    
    # Append the entity and query parameters to the URL
    # Filter for contacts where recruit_userfield10 contains the status_to_match
    # Using emailaddress instead of emailaddress2 as that's likely the correct field name
    url += f"/api/data/v9.0/contacts?$select=contactid,emailaddress2,recruit_cvurl,recruit_userfield10&$filter=contains(recruit_userfield10,'{status_to_match}')"
    
    # Headers for API request
    headers = {
        "Authorization": f"Bearer {token.get_token()}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    record_count = 0
    
    # Print CSV header
    print("contactid,email_address,recruit_cvurl")
    
    while url:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            try:
                data = response.json()
                
                if "value" not in data:
                    print("Warning: 'value' key missing in response.")
                    print("Full Response:", response.text)
                    break
                
                # Process each record and print in CSV format
                for record in data["value"]:
                    contactid = record.get("contactid", "")
                    email = record.get("emailaddress2", "")
                    cvurl = record.get("recruit_cvurl", "")
                    
                    # Replace empty values with "null"
                    if not email or email.strip() == "":
                        email = "null"
                    else:
                        # Escape commas and quotes in fields
                        email = email.replace('"', '""')
                        # Wrap fields in quotes if they contain commas
                        if ',' in email:
                            email = f'"{email}"'
                    
                    if not cvurl or cvurl.strip() == "":
                        cvurl = "null"
                    else:
                        # Escape commas and quotes in fields
                        cvurl = cvurl.replace('"', '""')
                        # Wrap fields in quotes if they contain commas
                        if ',' in cvurl:
                            cvurl = f'"{cvurl}"'
                    
                    # Print CSV row
                    print(f"{contactid},{email},{cvurl}")
                    record_count += 1
                
                # Get next page URL for pagination
                url = data.get("@odata.nextLink")
            except requests.exceptions.JSONDecodeError:
                print("Error: Response is not valid JSON. Full Response:")
                print(response.text)
                break
        else:
            print(f"HTTP Error: {response.status_code}, {response.text}")
            break
    
    print(f"Total records found: {record_count}", file=sys.stderr)

def fetch_and_print():
    # Start fetching
    fetch_data()

    # Define failure categories
    failure_categories = {
        "no resume": "no resume",
        "wrong resume url": "wrong resume url",
        "bad resume": "bad resume",
        "nosubcat": "nosubcat",
        "failedpdf": "failedpdf",
        "faileddocx": "faileddocx",
        "faileddoc": "faileddoc",
        "failedtxt": "failedtxt"
    }

    # Count occurrences of each failure category
    status_counts = Counter()

    for status in all_statuses:
        for key, phrase in failure_categories.items():
            if phrase in status.lower():
                status_counts[key] += 1

    # Display results
    for status, count in status_counts.items():
        print(f"{status}: {count}")

if __name__ == "__main__":
    # Example usage of the new function
    # contacts = get_contacts_by_status("no resume")
    # print(f"Found {len(contacts)} contacts with 'no resume' status")
    # for contact in contacts[:5]:  # Print first 5 contacts as example
    #     print(contact)
    
    #fetch_and_print()
    get_contacts_by_status("failedpdf")