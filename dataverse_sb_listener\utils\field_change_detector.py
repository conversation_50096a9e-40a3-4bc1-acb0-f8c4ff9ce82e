#!/usr/bin/env python3
"""
Field Change Detector for Dataverse Service Bus Messages

This module provides generic functionality to detect and log which fields have changed
in Dataverse entity update messages. It can work with any entity type and provides
both logging and filtering capabilities.
"""

import sys
import os
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger

class ChangeType(Enum):
    """Types of field changes that can occur."""
    ADDED = "added"
    MODIFIED = "modified"
    REMOVED = "removed"

@dataclass
class FieldChange:
    """Represents a single field change."""
    field_name: str
    old_value: Any
    new_value: Any
    change_type: ChangeType
    field_type: str = "unknown"

class FieldChangeDetector:
    """
    Generic field change detector for Dataverse entity updates.
    
    This class can detect which fields have changed in service bus messages
    and provide detailed logging of those changes. It works with any entity type.
    """
    
    def __init__(self, logger: Optional[AppLogger] = None):
        """
        Initialize the field change detector.
        
        Args:
            logger: Logger instance for outputting change information
        """
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        
        # Configuration for field filtering
        self.fields_of_interest: Set[str] = set()
        self.ignore_fields: Set[str] = {
            'modifiedon', 'modifiedby', 'modifiedonbehalfby',  # Standard audit fields
            'timezoneruleversionnumber', 'utcconversiontimezonecode',  # Timezone fields
            'rowversion'  # Row version field
        }
        
    def set_fields_of_interest(self, fields: List[str]) -> None:
        """
        Set fields that are of interest for change detection.
        If empty, all fields will be considered.
        
        Args:
            fields: List of field names to monitor
        """
        self.fields_of_interest = set(fields)
        self.logger.info(f"Set fields of interest: {fields}")
        
    def add_ignore_fields(self, fields: List[str]) -> None:
        """
        Add fields to ignore during change detection.
        
        Args:
            fields: List of field names to ignore
        """
        self.ignore_fields.update(fields)
        self.logger.info(f"Added ignore fields: {fields}")
        
    def detect_changes(self, entity_info: Dict[str, Any]) -> List[FieldChange]:
        """
        Detect field changes from entity information.
        
        Args:
            entity_info: Parsed entity information from service bus message
            
        Returns:
            List of field changes detected
        """
        changes = []
        
        # Extract attributes from the message
        attributes = entity_info.get('attributes', {})
        
        # Process each attribute as a potential change
        for field_name, field_info in attributes.items():
            # Skip ignored fields
            if field_name in self.ignore_fields:
                continue
                
            # Skip if we have fields of interest and this field isn't one of them
            if self.fields_of_interest and field_name not in self.fields_of_interest:
                continue
            
            # Extract the value
            if isinstance(field_info, dict):
                raw_value = field_info.get('raw_value')
                field_type = field_info.get('type', 'unknown')
            else:
                raw_value = field_info
                field_type = 'unknown'
            
            # Create change record
            change = FieldChange(
                field_name=field_name,
                old_value=None,  # We don't have old values in current message format
                new_value=raw_value,
                change_type=ChangeType.MODIFIED,  # Assume modified since it's in the update
                field_type=field_type
            )
            
            changes.append(change)
        
        return changes
    
    def log_changes(self, entity_info: Dict[str, Any], changes: List[FieldChange]) -> None:
        """
        Log detected field changes in a structured format.
        
        Args:
            entity_info: Entity information
            changes: List of detected field changes
        """
        entity_name = entity_info.get('entity_name', 'unknown')
        entity_id = entity_info.get('entity_id', 'unknown')
        message_type = entity_info.get('message_type', 'unknown')
        
        self.logger.info("=" * 60)
        self.logger.info(f"FIELD CHANGES DETECTED")
        self.logger.info(f"Entity: {entity_name}")
        self.logger.info(f"Entity ID: {entity_id}")
        self.logger.info(f"Message Type: {message_type}")
        self.logger.info(f"Total Changes: {len(changes)}")
        self.logger.info("=" * 60)
        
        if not changes:
            self.logger.info("No field changes detected")
            return
        
        # Group changes by type
        for change in changes:
            self._log_single_change(change)
        
        self.logger.info("=" * 60)
    
    def _log_single_change(self, change: FieldChange) -> None:
        """
        Log a single field change.
        
        Args:
            change: Field change to log
        """
        change_symbol = {
            ChangeType.ADDED: "+",
            ChangeType.MODIFIED: "~",
            ChangeType.REMOVED: "-"
        }.get(change.change_type, "?")
        
        # Format the value for display
        if change.field_type == 'date' and hasattr(change.new_value, 'parsed_date'):
            display_value = f"{change.new_value['raw_value']} -> {change.new_value['parsed_date']}"
        else:
            display_value = str(change.new_value)
        
        self.logger.info(f"{change_symbol} {change.field_name} ({change.field_type}): {display_value}")
    
    def should_process_message(self, entity_info: Dict[str, Any]) -> bool:
        """
        Determine if a message should be processed based on field changes.
        
        Args:
            entity_info: Entity information
            
        Returns:
            True if message should be processed, False otherwise
        """
        # If no fields of interest are set, process all messages
        if not self.fields_of_interest:
            return True
        
        # Check if any changed fields are in our fields of interest
        attributes = entity_info.get('attributes', {})
        changed_fields = set(attributes.keys())
        
        # Remove ignored fields
        changed_fields -= self.ignore_fields
        
        # Check for intersection with fields of interest
        relevant_changes = changed_fields & self.fields_of_interest
        
        if relevant_changes:
            self.logger.info(f"Processing message - relevant fields changed: {list(relevant_changes)}")
            return True
        else:
            self.logger.info(f"Skipping message - no relevant fields changed. Changed fields: {list(changed_fields)}")
            return False
    
    def get_change_summary(self, changes: List[FieldChange]) -> Dict[str, Any]:
        """
        Get a summary of field changes.
        
        Args:
            changes: List of field changes
            
        Returns:
            Summary dictionary
        """
        summary = {
            'total_changes': len(changes),
            'changes_by_type': {},
            'changes_by_field_type': {},
            'changed_fields': []
        }
        
        for change in changes:
            # Count by change type
            change_type = change.change_type.value
            summary['changes_by_type'][change_type] = summary['changes_by_type'].get(change_type, 0) + 1
            
            # Count by field type
            field_type = change.field_type
            summary['changes_by_field_type'][field_type] = summary['changes_by_field_type'].get(field_type, 0) + 1
            
            # List changed fields
            summary['changed_fields'].append(change.field_name)
        
        return summary

# Predefined field sets for common entity types
CONTACT_FIELDS_OF_INTEREST = {
    'fullname', 'firstname', 'lastname', 'emailaddress1'
    'recruit_mobilehome', 'address2_city', 'recruit_address2_state',
    'recruit_candidatecontactstatus', 'recruit_availability',
}

VACANCY_FIELDS_OF_INTEREST = {
    'crimson_jobtitle', 'crimson_location', 'statecode', 'statuscode',
    'recruit_mandatorytagcontrol0', 'recruit_mandatorytagcontrol2', 
    'recruit_adverttext2', 'recruit_booleanuserfield3',
    'crimson_deliveryownerid', 'ownerid'
}

def create_detector_for_entity(entity_name: str, logger: Optional[AppLogger] = None) -> FieldChangeDetector:
    """
    Create a field change detector configured for a specific entity type.
    
    Args:
        entity_name: Name of the entity (e.g., 'contact', 'crimson_vacancy')
        logger: Logger instance
        
    Returns:
        Configured field change detector
    """
    detector = FieldChangeDetector(logger)
    
    # Configure based on entity type
    if entity_name.lower() == 'contact':
        detector.set_fields_of_interest(list(CONTACT_FIELDS_OF_INTEREST))
        detector.logger.info(f"Created detector for contact entity with {len(CONTACT_FIELDS_OF_INTEREST)} fields of interest")
    elif entity_name.lower() == 'crimson_vacancy':
        detector.set_fields_of_interest(list(VACANCY_FIELDS_OF_INTEREST))
        detector.logger.info(f"Created detector for crimson_vacancy entity with {len(VACANCY_FIELDS_OF_INTEREST)} fields of interest")
    else:
        # For unknown entities, don't set fields of interest (process all fields)
        detector.logger.info(f"Created detector for unknown entity '{entity_name}' - will process all fields")
    
    return detector 