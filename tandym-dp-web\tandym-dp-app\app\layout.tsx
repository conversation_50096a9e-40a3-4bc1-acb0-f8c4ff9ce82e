"use client";
import "./globals.css";
import { NotificationProvider } from "@/hooks/useNotification";
import { SkillsProvider } from "@/context/SkillsContext";
import { Providers } from "@/components/providers";
import { EntitlementProvider } from "@/context/EntitlementContext";
import dynamic from "next/dynamic";
import React, { useEffect } from "react";
import { usePathname } from "next/navigation";
import { TAB_ROUTE_MAP } from "@/utils/tabRoutes";
import AppInsightsClient from "@/components/AppInsights";
import ClientLayout from "@/components/ClientLayout";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  useEffect(() => {
    if (typeof window !== "undefined") {
      if (!localStorage.getItem("filtersInitialized")) {
        if (
          localStorage.getItem("recruiter-filter-store") ||
          localStorage.getItem("mercury-filter-store")
        ) {
          localStorage.removeItem("mercury-filter-store");
          localStorage.removeItem("recruiter-filter-store");
        }
        localStorage.setItem("filtersInitialized", "true");
      }
    }
  }, []);
  return (
    <html lang="en">
      <body suppressHydrationWarning={true}>
        <Providers>
          <AppInsightsClient />
          <EntitlementProvider>
            <SkillsProvider>
              <NotificationProvider>
                <ClientLayout>{children}</ClientLayout>
              </NotificationProvider>
            </SkillsProvider>
          </EntitlementProvider>
        </Providers>
      </body>
    </html>
  );
}
