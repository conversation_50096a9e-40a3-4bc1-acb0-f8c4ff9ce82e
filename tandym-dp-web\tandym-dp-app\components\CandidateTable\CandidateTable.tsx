import {
  <PERSON>H<PERSON>er,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { ArrowUpDown, FileText, SquareArrowOutUpRight } from "lucide-react";
import Loading from "@/components/Loading";
import WhyFitAction from "../candidates/WhyFitAction";
import AppToolTip from "../AppToolTip";
import ThumbAction from "../candidates/ThumbAction";
import { Candidate, Vacancy } from "@/app/candidates/helper";
import {
  unFormattedDateWithBrowserTimezoneInDDMMYY,
  VACANCY_FILTER_URL_REGEX,
} from "@/utils/utils";
import { useState, useEffect } from "react";
import Modal from "../Modal";
import { useNotification } from "@/hooks/useNotification";
import { useSession } from "next-auth/react";
import { trackedFetch } from "@/library/trackApi";
import { getAppInsights } from "@/library/appInsights";
import { But<PERSON> } from "../ui/button";
import DiscardModal from "../candidates/DiscardModal";
import { useEntitlement } from "@/context/EntitlementContext";
import { initAppInsights } from "@/library/appInsights";
import { NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL } from "@/api/config";
import { updateLocalStoredVacancyTimeStamp } from "@/utils/updatelocalStoredVacancyTimeStamp";
import { UPDATE_TIMESTAMPS_STATUS } from "@/types/vacancy_status_api";
import { Input } from "../ui/input";
import { filterSlider, type iFilterSlider } from "../candidates/filterMenu";
import { VACANCY_FILTER_OTHER_LABELS } from "@/library/utils";

interface TableProps {
  handleCandidateSort: (name: string) => void;
  isResumeModalOpen: boolean;
  loading?: boolean;
  paginatedCandidates: Candidate[];
  selectedVacancy: Vacancy;
  candidates: Candidate[];
  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;
  vacancyCandidates: {
    [key: string]: Candidate[];
  } | null;
  setVacancyCandidates: React.Dispatch<
    React.SetStateAction<{
      [key: string]: Candidate[];
    } | null>
  >;
  fetchResume: (name: Candidate) => void;
  showSelectBox?: boolean;
  handleCheckboxClick?: (id: number) => void;
  selectedRows?: number[];
  mercuryPortal: boolean;
  fetchCandidatesById: (vacamcyId: string) => void;
  emailId: string;
  isTableReadOnly?: boolean; // New prop to control table read-only state
  vacancyId: string | undefined;
  catalystRegenerationData: any;
  filterProps: iFilterSlider;
  tableLoading: boolean;
}

const CandidateTable = ({
  handleCandidateSort,
  isResumeModalOpen,
  loading,
  paginatedCandidates,
  selectedVacancy,
  candidates,
  setCandidates,
  vacancyCandidates,
  setVacancyCandidates,
  fetchResume,
  showSelectBox,
  handleCheckboxClick,
  selectedRows,
  mercuryPortal,
  fetchCandidatesById,
  emailId,
  isTableReadOnly = false, // New prop to control table read-only state
  vacancyId,
  catalystRegenerationData,
  filterProps,
  tableLoading,
}: TableProps) => {
  const { entitlements } = useEntitlement();
  const { data: session } = useSession();
  const [existingEdit, setExistingEdit] = useState(false);
  const [candidateEditId, setCandidateEditId] = useState<string>("");
  const [candidateEditId2, setCandidateEditId2] = useState<string>("");
  const [expandPopupOpen, setExpandPopupOpen] = useState(false);
  const [hideDislikedCandidates, setHideDislikedCandidates] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [mercuryURL, setMercuryURL] = useState<string>("");
  const [currentActionableCandidateId, setCurrentActionableCandidateId] =
    useState<null | number>(null);
  const handleHideDislikedCandidates = () => {
    setHideDislikedCandidates(!hideDislikedCandidates);
  };
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotification();
  // Add at the top inside CandidateTable component
  const [confirmShortlist, setConfirmShortlist] = useState<{
    open: boolean;
    contactId: string | null;
  }>({ open: false, contactId: null });

  const showShortlist = entitlements?.Shorting_Listing ?? false;

  // Filter candidates based on hide disliked state
  const filteredCandidates = Array.isArray(paginatedCandidates)
    ? hideDislikedCandidates
      ? paginatedCandidates.filter(
          (c) =>
            c?.candidate_data?.recruiter_review_decision?.vote !== "dislike" ||
            (currentActionableCandidateId === c.id &&
              c?.candidate_data?.recruiter_review_decision?.vote === "dislike")
        )
      : paginatedCandidates
    : [];

  useEffect(() => {
    initAppInsights();
  }, []);

  let isShortlistDisabled =
    NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL === "true";

  if (isTableReadOnly) {
    isShortlistDisabled = true;
  }

  if (mercuryPortal) {
    isShortlistDisabled = isTableReadOnly ? true : !mercuryPortal;
  }

  function getEnvType(envCode: string): "sandbox" | "prod" | "uat" {
    const code = envCode.toLowerCase();
    if (["qa", "dv", "sb"].includes(code)) {
      return "sandbox";
    }
    if (code === "ua") {
      return "uat";
    }
    if (code === "prod") {
      return "prod";
    }
    return "sandbox";
  }

  const getDomainClient = () => {
    const url = window.location.href;
    const match = url.match(VACANCY_FILTER_URL_REGEX);
    const result = match && match[1] ? match[1] : "prod";

    const envType = getEnvType(result);
    const crmDomain =
      envType === "prod"
        ? "tandymgroup.crm.dynamics.com"
        : `tandymgroup-${envType}.crm.dynamics.com`;

    return crmDomain;
  };

  const handleOpenPopup = async (contactId: string, mercuryPortal: boolean) => {
    const crmDomain = getDomainClient();
    let tandymURL = "";
    try {
      tandymURL = window.top?.location?.href || "";
    } catch (err) {
      console.warn(
        "Unable to access top window location due to cross-origin:",
        err
      );
      tandymURL = window.location.href;
    }
    const url = `https://${crmDomain}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&forceUCI=1&pagetype=entityrecord&etn=contact&id=${contactId}`;
    url && window.open(url, "_blank");
  };
  const refreshVacancy_id = selectedVacancy?.vacancy_id || (vacancyId ?? "");
  const handleShortlist = async (contactId: string) => {
    if (!contactId || !selectedVacancy?.vacancy_id) {
      showNotification("Candidate Contact ID is missing", "error");
      return;
    }

    const payload = {
      vacancy_id: selectedVacancy?.vacancy_id,
      candidate_id: contactId,
      reviewer_email: emailId || session?.user?.email,
      portal_name: mercuryPortal ? "Mercury" : "Recruiter",
    };
    setIsLoading(true);

    try {
      const response = await trackedFetch(
        `/api/vacancies/shortlisted`,
        {
          method: "POST",
          body: JSON.stringify(payload),
          headers: {
            "Content-Type": "application/json",
          },
        },
        { context: "Post Shortlisted Vacancies" }
      );
      const data = await response.json();

      if (data?.data?.status_code === 200) {
        // Success
        showNotification(
          data?.data?.message
            ? `${data?.data?.message} (Code : 200)`
            : data?.message
            ? `${data?.message} (Code : 200)`
            : "Shortlisted the candidate successfully for this vacancy:(Code : 200)",
          "success"
        );
        getAppInsights()?.trackEvent({
          name: mercuryPortal
            ? "FE_MercuryPostVacanciesShortlisted"
            : "FE_PostVacanciesShortlisted",
          properties: {
            reviewerEmail: emailId || session?.user?.email,
            candidateId: contactId,
            embeddedFrom: document.referrer,
            context: mercuryPortal
              ? "MercuryPostVacanciesShortlisted"
              : "PostVacanciesShortlisted",
          },
        });
        // Remove the shortlisted candidate from the table
        setCandidates((prev) =>
          prev.filter(
            (candidate) => candidate?.candidate_data?.contactid !== contactId
          )
        );
        updateLocalStoredVacancyTimeStamp(
          refreshVacancy_id,
          mercuryPortal ?? undefined
        );
        fetchCandidatesById(selectedVacancy?.vacancy_id);
      } else if (
        data?.status_code === 409 ||
        (typeof data?.error === "string" &&
          data.error.toLowerCase().includes("already shortlisted"))
      ) {
        // Already shortlisted
        showNotification(
          `${data.error} (Code : 409)` ||
            `${data.detail?.message} (Code : 409)` ||
            "Failed to shortlist the candidate for this vacancy- (Code : 409)",
          "warning"
        );
      } else {
        showNotification(
          `Failed to shortlist the candidate for this vacancy - (Code : ${data?.status_code})`,
          "error"
        );
        // All other errors
        throw new Error(
          data?.error ||
            data?.detail?.message ||
            "Failed to shortlist candidate"
        );
      }
    } catch (error) {
      console.error("Error while shortlisting candidate:", error);
      getAppInsights()?.trackException({
        error: new Error(
          mercuryPortal
            ? "MercuryPost Vacancies Shortlisted api with error is " + error
            : "Post Vacancies Shortlisted api with error is " + error
        ),
        severityLevel: 3,
      });
    } finally {
      setIsLoading(false);
    }
  };
  const StatusCompletion = catalystRegenerationData?.status
    ?.toLowerCase()
    ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED);
  const StatusQueued = catalystRegenerationData?.status
    ?.toLowerCase()
    ?.includes(UPDATE_TIMESTAMPS_STATUS.QUEUED);
  const StatusError = catalystRegenerationData?.status
    ?.toLowerCase()
    ?.includes(UPDATE_TIMESTAMPS_STATUS.ERROR);
  const StatusInProcess = catalystRegenerationData?.status
    ?.toLowerCase()
    ?.includes(UPDATE_TIMESTAMPS_STATUS.IN_PROCESS);

  return (
    <div className="grid grid-cols-4 gap-1 w-full max-h-[80vh]">
      {!filterProps?.showFilter && (
        <div className="w-full">
          {filterSlider({
            ...filterProps,
          })}
        </div>
      )}
      <div
        className={
          filterProps?.showFilter
            ? `grid col-span-4 w-full max-h-[80vh] rounded-lg ${
                expandPopupOpen ? "overflow-hidden" : "overflow-auto"
              } justify-items-start`
            : `grid col-span-3 max-h-[80vh] rounded-lg ${
                expandPopupOpen ? "overflow-hidden" : "overflow-auto"
              }`
        }
        style={{ alignItems: "flex-start" }}
      >
        <table className="w-full caption-bottom text-sm border border-gray-800 rounded-lg">
          <TableHeader className="rounded-lg sticky top-0 z-[10]">
            <TableRow className="bg-gray-900 rounded-lg hover:bg-gray-900 text-white text-[12px]">
              {showSelectBox && (
                <TableHead className="p-2 text-white w-[100px] rounded-tl-lg">
                  Select
                </TableHead>
              )}
              <TableHead
                className={`p-2 text-white w-[150px] cursor-pointer ${
                  !showSelectBox ? "rounded-tl-lg" : ""
                }`}
              >
                Name
              </TableHead>
              <TableHead
                onClick={() => handleCandidateSort("city")}
                className="p-2 text-white w-[90px] cursor-pointer"
              >
                Location
                <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
              </TableHead>
              <TableHead
                onClick={() => handleCandidateSort("distance_from_work_site")}
                className="p-2 text-white w-[90px] cursor-pointer"
              >
                Miles from Worksite
                <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
              </TableHead>
              <TableHead
                onClick={() => handleCandidateSort("availability")}
                className="p-2 text-white w-[90px] cursor-pointer"
              >
                Availability Date
                <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
              </TableHead>
              <TableHead className="p-2 text-white w-[140px] cursor-pointer">
                AI Agent Status
              </TableHead>
              <TableHead
                onClick={() => handleCandidateSort("freshness_index")}
                className="p-2 text-white w-[90px] cursor-pointer"
              >
                Freshness Index
                <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
              </TableHead>
              <TableHead
                className="p-2 text-white w-[70px] cursor-pointer"
                onClick={() => handleCandidateSort("overallscore")}
              >
                Total Score
                <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
              </TableHead>
              <TableHead className="p-2 text-white w-[80px]">
                Parsed Resume
              </TableHead>
              <TableHead className="p-2 text-white w-[85px]">
                <div className="flex flex-col items-center">
                  <span className="text-xs mb-1">Rating</span>
                  <button
                    onClick={handleHideDislikedCandidates}
                    className={`flex items-center gap-1 px-2 py-1 text-xs border border-white rounded transition-colors ${
                      hideDislikedCandidates
                        ? "bg-white text-gray-900"
                        : "bg-transparent hover:bg-white hover:text-gray-900"
                    }`}
                  >
                    <span>{hideDislikedCandidates ? "View" : "Hide"}</span>
                    <svg className="w-3 h-3 fill-red-500" viewBox="0 0 24 24">
                      <path d="M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z" />
                    </svg>
                  </button>
                </div>
              </TableHead>

              <TableHead className="p-2 text-white w-[180px] 2xl:w-[300px] text-left">
                Why Fit
              </TableHead>
              {(showShortlist || mercuryPortal) && (
                <TableHead className="p-2 text-white w-[100px] text-left">
                  Shortlist
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {(tableLoading || loading) && !isResumeModalOpen ? (
              <TableRow>
                <TableCell colSpan={12} className="p-4 text-center">
                  <Loading height="h-[50vh]" />
                </TableCell>
              </TableRow>
            ) : candidates.length === 0 ? (
              !tableLoading &&
              !loading &&
              (StatusCompletion ||
                StatusQueued ||
                StatusError ||
                StatusInProcess) ? (
                <TableRow>
                  <TableCell
                    colSpan={12}
                    className="p-4 text-center text-gray-700"
                  >
                    {StatusQueued
                      ? "Matching has been queued. Please wait for some time and check again."
                      : StatusInProcess
                      ? "Matching is in progress. Please wait for some time and check again."
                      : "No candidates found."}
                  </TableCell>
                </TableRow>
              ) : (
                !tableLoading &&
                !loading && (
                  <TableRow>
                    <TableCell
                      colSpan={12}
                      className="p-4 text-center text-gray-700"
                    >
                      Please fix the vacancy template to initiate the Catalyst Match generation.
                    </TableCell>
                  </TableRow>
                )
              )
            ) : (
              filteredCandidates?.length > 0 &&
              filteredCandidates?.map((candidate, ind) => {
                return (
                  <TableRow
                    key={candidate?.candidate_data?.contactid + ind}
                    className={ind % 2 === 1 ? "bg-gray-100" : "bg-white"}
                  >
                    {showSelectBox && selectedRows && handleCheckboxClick && (
                      <TableCell className="p-2 text-[14px] w-[100px]">
                        <Input
                          type="checkbox"
                          checked={selectedRows.includes(candidate?.id)}
                          onChange={() => handleCheckboxClick(candidate?.id)}
                          className="accent-gray-800 cursor-pointer"
                        />
                      </TableCell>
                    )}
                    <TableCell
                      className="p-2 text-[14px] w-[250px]"
                      onClick={() => {
                        handleOpenPopup(
                          candidate?.candidate_data?.contactid,
                          mercuryPortal
                        );
                      }}
                    >
                      <AppToolTip
                        text={
                          <span className="flex items-center">
                            Open `{candidate?.candidate_data?.name}` in new tab{" "}
                            <SquareArrowOutUpRight size={14} className="ml-1" />
                          </span>
                        }
                        header={
                          <p className="truncate md:max-w-[250px] text-blue-500 hover:underline">
                            {candidate?.candidate_data?.name}
                          </p>
                        }
                        direction="top"
                      />
                    </TableCell>
                    <TableCell className="p-2 text-[14px] w-[90px]">
                      {(() => {
                        const { city, state } = candidate?.candidate_data;
                        const isMissing = (val: string | null | undefined) =>
                          !val || val === VACANCY_FILTER_OTHER_LABELS.MISSING;

                        if (isMissing(city) && isMissing(state)) return "";
                        if (!isMissing(city) && !isMissing(state))
                          return `${city}, ${state}`;
                        return !isMissing(city) ? city : state;
                      })()}
                    </TableCell>
                    <TableCell className="p-2 text-[13px] w-[70px]">
                      {candidate?.candidate_data?.["distance_from_work_site"]
                        ? candidate?.candidate_data?.["distance_from_work_site"]
                        : ""}
                    </TableCell>
                    <TableCell className="p-2 text-[14px] w-[90px]">
                      {(() => {
                        const availability =
                          candidate?.candidate_data?.availability;
                        if (
                          !availability ||
                          availability === VACANCY_FILTER_OTHER_LABELS.MISSING
                        )
                          return "";
                        try {
                          return unFormattedDateWithBrowserTimezoneInDDMMYY(
                            availability
                          );
                        } catch {
                          return "";
                        }
                      })()}
                    </TableCell>
                    <TableCell className="p-2 text-[14px] w-[140px]">
                      {candidate?.candidate_data?.["info_bot_response"] ?? ""}
                      <br />
                      {candidate?.candidate_data?.["info_bot_response_date"] !=
                      null
                        ? unFormattedDateWithBrowserTimezoneInDDMMYY(
                            candidate?.candidate_data?.[
                              "info_bot_response_date"
                            ]
                          )
                        : null}
                    </TableCell>
                    <TableCell className="p-2 text-[13px] w-[70px]">
                      {candidate?.candidate_data?.["freshness_index"]}
                    </TableCell>
                    <TableCell className="p-2 text-[14px] w-[70px]">
                      {typeof candidate?.candidate_data?.[
                        "classification score"
                      ]?.overallscore === "number"
                        ? candidate?.candidate_data?.[
                            "classification score"
                          ].overallscore.toFixed(2)
                        : "N/A"}
                    </TableCell>
                    <TableCell className="p-2 space-x-2 h-14 w-[50px] sticky right-[0px]">
                      <AppToolTip
                        header={
                          <FileText
                            size={18}
                            className="text-gray-700"
                            onClick={() => fetchResume(candidate)}
                            // aria-disabled={!candidate.candidate_contactid}
                          />
                        }
                        text="View Resume"
                      />
                    </TableCell>
                    <TableCell className="p-2 space-x-2 h-14 w-[85px]">
                      <ThumbAction
                        candidate={candidate}
                        setCandidates={setCandidates}
                        candidates={candidates}
                        vacancyId={selectedVacancy?.vacancy_id}
                        vacancyRefNo={selectedVacancy?.refno}
                        vacancyCandidates={vacancyCandidates || {}}
                        setVacancyCandidates={setVacancyCandidates}
                        selectedVacancy={selectedVacancy}
                        mercuryPortal={mercuryPortal}
                        session={session}
                        emailId={emailId}
                        isTableReadOnly={isTableReadOnly}
                        setCurrentActionableCandidateId={
                          setCurrentActionableCandidateId
                        }
                      />
                    </TableCell>
                    <TableCell className="p-2 pl-0 space-x-2 h-14 w-[180px] 2xl:w-[300px]">
                      <div className={`flex gap-[5px]`}>
                        <WhyFitAction
                          candidate={candidate}
                          setCandidates={setCandidates}
                          selectedVacancy={selectedVacancy}
                          existingEdit={existingEdit}
                          setExistingEdit={setExistingEdit}
                          candidateEditId={candidateEditId}
                          candidateEditId2={candidateEditId2}
                          setCandidateEditId={setCandidateEditId}
                          setCandidateEditId2={setCandidateEditId2}
                          setExpandPopupOpen={setExpandPopupOpen}
                          isTableReadOnly={isTableReadOnly}
                        />
                      </div>
                    </TableCell>
                    {(showShortlist || mercuryPortal) && (
                      <TableCell className="p-2 space-x-2 h-14 w-[80px]">
                        <Button
                          className="rounded w-24"
                          onClick={() =>
                            setConfirmShortlist({
                              open: true,
                              contactId: candidate?.candidate_data?.contactid,
                            })
                          }
                          disabled={
                            isShortlistDisabled || isTableReadOnly
                              ? true
                              : candidate?.candidate_data?.shortlisted_details
                                  ?.status ===
                                VACANCY_FILTER_OTHER_LABELS.SUCCESS
                          }
                        >
                          {candidate?.candidate_data?.shortlisted_details
                            ?.status !== VACANCY_FILTER_OTHER_LABELS.SUCCESS
                            ? "Shortlist"
                            : "Shortlisted"}
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </table>
        {isModalOpen && mercuryPortal && mercuryURL && (
          <Modal
            isOpen={isModalOpen}
            width="max-w-6xl"
            height="h-[90vh]"
            children={
              <div className="relative w-full h-[80vh]">
                <iframe
                  src={mercuryURL}
                  width="100%"
                  height="100%"
                  allowFullScreen
                />
              </div>
            }
            onClose={() => setIsModalOpen(false)}
          />
        )}
        {isLoading && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
            <Loading height="h-[50px]" />
          </div>
        )}
        {confirmShortlist.open && (
          <DiscardModal
            isOpenDiscardModal={confirmShortlist?.open}
            title="Confirm Shortlist"
            cancelTabChange={() =>
              setConfirmShortlist({ open: false, contactId: null })
            }
            message="This action will shortlist the candidate for the selected vacancy. Do you want to continue?"
            confirmButtonText="Confirm"
            confirmTabChange={async () => {
              setConfirmShortlist({ open: false, contactId: null });
              if (confirmShortlist?.contactId) {
                await handleShortlist(confirmShortlist?.contactId);
              }
            }}
          />
        )}
      </div>
    </div>
  );
};

export default CandidateTable;
