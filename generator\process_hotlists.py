#!/usr/bin/env python3
"""
Contractor Candidate Table Analyzer

This script reads the entire mercury_contactorcandidate table from Dataverse and analyzes the data by:
1. Grouping records by _createdby_value
2. Listing all candidate IDs for each _createdby_value
3. Identifying records where _createdby_value and _owninguser_value are different
4. Searching for contractor candidates created by a specific user

Usage:
    python process_hotlists.py [--env ENVIRONMENT] [--output-file OUTPUT_FILE] [--creator CREATOR] [--candidates CANDIDATES] [--check-contact HOTLIST_NAME|OWNER_EMAIL|CONTACT_ID] [--add-contact HOTLIST_NAME|OWNER_EMAIL|CONTACT_ID] [--create-hotlist HOTLIST_NAME|OWNER_EMAIL]

Environment options: SANDBOX, UAT, PROD
Creator: Email address to get all hotlists owned by that user
Candidates: Format: delivery_owner_email|hotlist_name to get all candidates in that hotlist
Check-contact: Format: hotlist_name|owner_email|contact_id to check if contact exists in hotlist
Add-contact: Format: hotlist_name|owner_email|contact_id to add contact to hotlist
Create-hotlist: Format: hotlist_name|owner_email to create a new hotlist owned by the specified user
"""

import sys
import os
import json
import argparse
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from common.appLogger import AppLogger
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_common import read_fields_from_dataverse
from common.secrets_env import load_secrets_env_variables

def create_logger(log_file: str = None) -> AppLogger:
    """
    Create and configure a logger for the hotlist analyzer.
    
    Args:
        log_file: Optional log file path. If None, logs to stdout only.
        
    Returns:
        AppLogger: Configured logger instance
    """
    logger_config = {
        "name": "hotlist_analyzer",
        "log_level": "INFO",
        "log_to_stdout": False,
        "use_json": False,
        "log_file": "/mnt/incoming/logs/hotlist_analyzer.log"
    }

    
    return AppLogger(logger_config)


def fetch_all_hotlist_records(token, dataverse_url: str, logger: AppLogger) -> List[Dict[str, Any]]:
    """
    Fetch all records from the hotlist table using pagination.
    
    Args:
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of all hotlist records
    """
    logger.info("Starting to fetch all hotlist records...")
    
    # Fields to retrieve from the hotlist table
    fields = [
        'mercury_name',
        '_createdby_value',
        'mercury_hotlistid',
        '_owninguser_value',
        'statecode',
        'mercury_contactorcandidate'
    ]
    
    all_records = []
    page_count = 0
    total_records = 0
    
    # Use the read_fields_from_dataverse function with pagination
    # Filter for active records and non-contractor candidates
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='mercury_hotlist',
        fields=fields,
        whereClause="statecode eq 0 and mercury_contactorcandidate eq false",  # Only active, non-contractor candidate hotlists
        logger=logger,
        count=True  # Get total count
    )
    
    if not response:
        logger.error("Failed to retrieve data from hotlist table")
        return []
    
    # Get the first page of results
    if 'value' in response:
        all_records.extend(response['value'])
        total_records = len(response['value'])
        page_count = 1
        logger.info(f"Retrieved page {page_count}: {len(response['value'])} records")
    
    # Handle pagination if there are more pages
    while '@odata.nextLink' in response:
        page_count += 1
        logger.info(f"Fetching page {page_count}...")
        
        # Make request to the next page URL
        headers = {
            'Authorization': f'Bearer {token.get_token()}',
            'Accept': 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
        }
        
        import requests
        next_response = requests.get(response['@odata.nextLink'], headers=headers)
        
        if next_response.status_code == 200:
            response = next_response.json()
            if 'value' in response:
                all_records.extend(response['value'])
                total_records += len(response['value'])
                logger.info(f"Retrieved page {page_count}: {len(response['value'])} records (Total: {total_records})")
        else:
            logger.error(f"Failed to retrieve page {page_count}: {next_response.status_code}")
            break
    
    logger.info(f"Completed fetching hotlist records. Total records: {len(all_records)}")
    return all_records





def fetch_user_emails(token, dataverse_url: str, user_guids: set, logger: AppLogger) -> Dict[str, str]:
    """
    Fetch email addresses for user GUIDs from the systemuser table.
    
    Args:
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        user_guids: Set of user GUIDs to fetch emails for
        logger: Logger instance
        
    Returns:
        Dictionary mapping user GUIDs to email addresses
    """
    if not user_guids:
        return {}
    
    logger.info(f"Fetching email addresses for {len(user_guids)} users...")
    
    # Fields to retrieve from systemuser table
    fields = ['systemuserid', 'internalemailaddress']
    
    # Build filter for all user GUIDs
    guid_filters = [f"systemuserid eq '{guid}'" for guid in user_guids]
    where_clause = " or ".join(guid_filters)
    
    user_emails = {}
    
    # Fetch user data
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='systemuser',
        fields=fields,
        whereClause=where_clause,
        logger=logger
    )
    
    if response and 'value' in response:
        for user in response['value']:
            user_id = user.get('systemuserid')
            email = user.get('internalemailaddress', 'No Email')
            if user_id:
                user_emails[user_id] = email
        
        logger.info(f"Successfully fetched emails for {len(user_emails)} users")
    else:
        logger.warning("Failed to fetch user emails from systemuser table")
    
    return user_emails


def get_hotlists_by_owner(owner_id: str, token, dataverse_url: str, logger: AppLogger) -> List[str]:
    """
    Get all hotlist names owned by a specific user.
    
    Args:
        owner_id: The systemuserid of the owner
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of hotlist names owned by the user
    """
    logger.info(f"Fetching hotlists owned by user ID: {owner_id}")
    
    # Fields to retrieve from the hotlist table
    fields = ['mercury_name', 'mercury_hotlistid', '_owninguser_value', 'statecode', 'mercury_contactorcandidate']
    
    # Build filter for owner, active records, and non-contractor candidates
    where_clause = f"_owninguser_value eq '{owner_id}' and statecode eq 0 and mercury_contactorcandidate eq false"
    
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='mercury_hotlist',
        fields=fields,
        whereClause=where_clause,
        logger=logger
    )
    
    hotlist_names = []
    if response and 'value' in response:
        for record in response['value']:
            hotlist_name = record.get('mercury_name')
            if hotlist_name:
                hotlist_names.append(hotlist_name)
        
        logger.info(f"Found {len(hotlist_names)} hotlists owned by user {owner_id}")
    else:
        logger.info(f"No hotlists found for user {owner_id}")
    
    return hotlist_names


def print_hotlists_by_owner(owner_email: str, hotlist_names: List[str], logger: AppLogger):
    """
    Print the hotlists owned by a specific user in a formatted way.
    
    Args:
        owner_email: Email address of the owner
        hotlist_names: List of hotlist names
        logger: Logger instance
    """
    logger.info("=" * 80)
    logger.info("HOTLISTS BY OWNER")
    logger.info("=" * 80)
    
    logger.info(f"Owner: {owner_email}")
    logger.info(f"Total Hotlists: {len(hotlist_names)}")
    logger.info("")
    
    if hotlist_names:
        logger.info("HOTLIST NAMES:")
        logger.info("-" * 40)
        
        for i, hotlist_name in enumerate(hotlist_names, 1):
            logger.info(f"{i}. {hotlist_name}")
        
        logger.info("")
        logger.info("JSON LIST:")
        logger.info("-" * 40)
        logger.info(json.dumps(hotlist_names, indent=2))
    else:
        logger.info("No hotlists found for this owner.")
    
    logger.info("=" * 80)


def get_candidates_by_hotlist(delivery_owner_email: str, hotlist_name: str, token, dataverse_url: str, logger: AppLogger) -> List[str]:
    """
    Get all candidate email addresses in a specific hotlist.
    
    Args:
        delivery_owner_email: Email address of the delivery owner
        hotlist_name: Name of the hotlist
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of candidate email addresses in the hotlist
    """
    logger.info(f"Getting candidates in hotlist '{hotlist_name}' owned by {delivery_owner_email}")
    
    try:
        # Step 1: Get delivery owner ID from email
        owner_id = get_user_id_by_email(delivery_owner_email, token, dataverse_url, logger)
        if not owner_id:
            logger.warning(f"User with email {delivery_owner_email} not found")
            return []
        
        logger.info(f"Found owner ID: {owner_id} for email {delivery_owner_email}")
        
        # Step 2: Get hotlist ID from name and owner
        hotlist_id = get_hotlist_id_by_name_and_owner(hotlist_name, owner_id, token, dataverse_url, logger)
        if not hotlist_id:
            logger.warning(f"Hotlist '{hotlist_name}' with owner {delivery_owner_email} not found")
            return []
        
        logger.info(f"Found hotlist ID: {hotlist_id}")
        
        # Step 3: Get contacts associated with this hotlist
        contacts = get_contractor_candidates_by_hotlist(hotlist_id, token, dataverse_url, logger)
        if not contacts:
            logger.info(f"No contacts found in hotlist {hotlist_id}")
            return []
        
        # Extract email addresses for the --candidates functionality
        candidate_emails = [contact['email'] for contact in contacts]
        
        logger.info(f"Found {len(candidate_emails)} candidate emails in hotlist '{hotlist_name}'")
        return candidate_emails
        
    except Exception as e:
        logger.error(f"Error getting candidates by hotlist: {e}")
        return []


def get_contacts_by_hotlist(delivery_owner_email: str, hotlist_name: str, token, dataverse_url: str, logger: AppLogger) -> List[Dict[str, str]]:
    """
    Get all contacts with full information in a specific hotlist.
    
    Args:
        delivery_owner_email: Email address of the delivery owner
        hotlist_name: Name of the hotlist
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of contact dictionaries with email and contactid
    """
    logger.info(f"Getting contacts in hotlist '{hotlist_name}' owned by {delivery_owner_email}")
    
    try:
        # Step 1: Get delivery owner ID from email
        owner_id = get_user_id_by_email(delivery_owner_email, token, dataverse_url, logger)
        if not owner_id:
            logger.warning(f"User with email {delivery_owner_email} not found")
            return []
        
        logger.info(f"Found owner ID: {owner_id} for email {delivery_owner_email}")
        
        # Step 2: Get hotlist ID from name and owner
        hotlist_id = get_hotlist_id_by_name_and_owner(hotlist_name, owner_id, token, dataverse_url, logger)
        if not hotlist_id:
            logger.warning(f"Hotlist '{hotlist_name}' with owner {delivery_owner_email} not found")
            return []
        
        logger.info(f"Found hotlist ID: {hotlist_id}")
        
        # Step 3: Get contacts associated with this hotlist
        contacts = get_contractor_candidates_by_hotlist(hotlist_id, token, dataverse_url, logger)
        
        logger.info(f"Found {len(contacts)} contacts in hotlist '{hotlist_name}'")
        return contacts
        
    except Exception as e:
        logger.error(f"Error getting contacts by hotlist: {e}")
        return []


def get_contractor_candidates_by_hotlist(hotlist_id: str, token, dataverse_url: str, logger: AppLogger) -> List[Dict[str, str]]:
    """
    Get all contacts associated with a specific hotlist.
    
    Args:
        hotlist_id: mercury_hotlistid
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of contact dictionaries with email and contactid
    """
    logger.info(f"Fetching contacts for hotlist {hotlist_id}")
    
    # Build the API URL for the contacts query
    fields_query = "fullname,emailaddress2,contactid"
    filter_query = f"mercury_mercury_hotlist_contact/any(h:h/mercury_hotlistid eq {hotlist_id})"
    
    url = f"{dataverse_url}/api/data/v9.0/contacts?$select={fields_query}&$filter={filter_query}"
    
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Accept': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }
    
    try:
        import requests
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            contacts = data.get('value', [])
            
            # Extract contact information
            contact_list = []
            for contact in contacts:
                email = contact.get('emailaddress2')
                contact_id = contact.get('contactid')
                if email and contact_id:
                    contact_list.append({
                        'email': email,
                        'contactid': contact_id
                    })
            
            logger.info(f"Found {len(contact_list)} contacts in hotlist {hotlist_id}")
            return contact_list
        else:
            logger.error(f"Failed to fetch contacts: {response.status_code}, {response.text}")
            return []
            
    except Exception as e:
        logger.error(f"Error fetching contacts: {e}")
        return []





def print_candidates_by_hotlist(delivery_owner_email: str, hotlist_name: str, candidate_emails: List[str], logger: AppLogger):
    """
    Print the candidates in a specific hotlist in a formatted way.
    
    Args:
        delivery_owner_email: Email address of the delivery owner
        hotlist_name: Name of the hotlist
        candidate_emails: List of candidate email addresses
        logger: Logger instance
    """
    logger.info("=" * 80)
    logger.info("CANDIDATES IN HOTLIST")
    logger.info("=" * 80)
    
    logger.info(f"Delivery Owner: {delivery_owner_email}")
    logger.info(f"Hotlist: {hotlist_name}")
    logger.info(f"Total Candidates: {len(candidate_emails)}")
    logger.info("")
    
    if candidate_emails:
        logger.info("CANDIDATE EMAIL ADDRESSES:")
        logger.info("-" * 40)
        
        for i, email in enumerate(candidate_emails, 1):
            logger.info(f"{i}. {email}")
        
        logger.info("")
        logger.info("JSON LIST:")
        logger.info("-" * 40)
        logger.info(json.dumps(candidate_emails, indent=2))
    else:
        logger.info("No candidates found in this hotlist.")
    
    logger.info("=" * 80)





def check_contact_in_hotlist(hotlist_name: str, owner_email: str, contact_id: str, token, dataverse_url: str, logger: AppLogger) -> bool:
    """
    Check if a contact exists in a specific hotlist.
    
    Args:
        hotlist_name: Name of the hotlist
        owner_email: Email address of the hotlist owner
        contact_id: Contact ID to check
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        bool: True if contact exists in the hotlist, False otherwise
    """
    logger.info(f"Checking if contact {contact_id} exists in hotlist '{hotlist_name}' owned by {owner_email}")
    
    try:
        # Step 1: Get all contacts in the hotlist using get_contacts_by_hotlist
        contacts = get_contacts_by_hotlist(owner_email, hotlist_name, token, dataverse_url, logger)
        
        if not contacts:
            logger.info(f"No contacts found in hotlist '{hotlist_name}'")
            return False
        
        # Step 2: Check if the contact_id exists in the results
        contact_exists = any(contact.get('contactid') == contact_id for contact in contacts)
        
        if contact_exists:
            logger.info(f"Contact {contact_id} found in hotlist '{hotlist_name}'")
        else:
            logger.info(f"Contact {contact_id} not found in hotlist '{hotlist_name}'")
        
        return contact_exists
        
    except Exception as e:
        logger.error(f"Error checking contact in hotlist: {e}")
        return False


def add_contact_to_hotlist(hotlist_name: str, owner_email: str, contact_id: str, token, dataverse_url: str, logger: AppLogger) -> bool:
    """
    Add a contact to a specific hotlist.
    
    Args:
        hotlist_name: Name of the hotlist
        owner_email: Email address of the hotlist owner
        contact_id: Contact ID to add to the hotlist
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        bool: True if contact was successfully added, False otherwise
    """
    logger.info(f"Adding contact {contact_id} to hotlist '{hotlist_name}' owned by {owner_email}")
    
    try:
        # Step 1: Get owner ID from email address
        owner_id = get_user_id_by_email(owner_email, token, dataverse_url, logger)
        if not owner_id:
            logger.warning(f"User with email {owner_email} not found")
            return False
        
        logger.info(f"Found owner ID: {owner_id} for email {owner_email}")
        
        # Step 2: Get hotlist ID from name and owner
        hotlist_id = get_hotlist_id_by_name_and_owner(hotlist_name, owner_id, token, dataverse_url, logger)
        if not hotlist_id:
            logger.warning(f"Hotlist '{hotlist_name}' with owner {owner_email} not found")
            return False
        
        logger.info(f"Found hotlist ID: {hotlist_id}")
        
        # Step 3: Check if contact already exists in hotlist
        contact_exists = check_contact_in_hotlist(hotlist_name, owner_email, contact_id, token, dataverse_url, logger)
        if contact_exists:
            logger.warning(f"Contact {contact_id} already exists in hotlist '{hotlist_name}'")
            return True  # Consider this a success since the goal is achieved
        
        # Step 4: Add contact to hotlist using Dataverse association
        nav_property = "mercury_mercury_hotlist_contact"
        url = f"{dataverse_url}/api/data/v9.0/contacts({contact_id})/{nav_property}/$ref"
        
        payload = {
            "@odata.id": f"{dataverse_url}/api/data/v9.0/mercury_hotlists({hotlist_id})"
        }
        
        headers = {
            "Authorization": f"Bearer {token.get_token()}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        import requests
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code in (204, 1223):  # 204 = success for association
            logger.info(f"Contact {contact_id} successfully added to hotlist '{hotlist_name}'")
            return True
        else:
            logger.error(f"Failed to add contact to hotlist: {response.status_code} {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error adding contact to hotlist: {e}")
        return False


def get_user_id_by_email(email: str, token, dataverse_url: str, logger: AppLogger) -> str:
    """
    Get the systemuserid for a user by email address.
    
    Args:
        email: Email address of the user
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        str: systemuserid if found, None otherwise
    """
    logger.info(f"Looking up user ID for email: {email}")
    
    # Fields to retrieve from the systemuser table
    fields = ['systemuserid', 'internalemailaddress']
    
    # Build filter for email
    where_clause = f"internalemailaddress eq '{email}'"
    
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='systemuser',
        fields=fields,
        whereClause=where_clause,
        logger=logger
    )
    
    if response and 'value' in response and len(response['value']) > 0:
        user_id = response['value'][0].get('systemuserid')
        logger.info(f"Found user ID: {user_id} for email {email}")
        return user_id
    else:
        logger.warning(f"No user found with email '{email}'")
        return None


def get_azure_object_id_by_email(email: str, token, dataverse_url: str, logger: AppLogger) -> str:
    """
    Get the Azure Active Directory Object ID for a user by email address.
    
    Args:
        email: Email address of the user
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        str: Azure Object ID if found, None otherwise
    """
    logger.info(f"Looking up Azure Object ID for email: {email}")
    
    # Fields to retrieve from the systemuser table
    fields = ['systemuserid', 'internalemailaddress', 'azureactivedirectoryobjectid']
    
    # Build filter for email
    where_clause = f"internalemailaddress eq '{email}'"
    
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='systemuser',
        fields=fields,
        whereClause=where_clause,
        logger=logger
    )
    
    if response and 'value' in response and len(response['value']) > 0:
        azure_object_id = response['value'][0].get('azureactivedirectoryobjectid')
        if azure_object_id:
            logger.info(f"Found Azure Object ID: {azure_object_id} for email {email}")
            return azure_object_id
        else:
            logger.warning(f"No Azure Object ID found for email '{email}'")
            return None
    else:
        logger.warning(f"No user found with email '{email}'")
        return None


def get_hotlist_id_by_name_and_owner(hotlist_name: str, owner_id: str, token, dataverse_url: str, logger: AppLogger) -> str:
    """
    Get the mercury_hotlistid for a hotlist by name and owner.
    
    Args:
        hotlist_name: Name of the hotlist
        owner_id: Owner ID of the hotlist
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        str: mercury_hotlistid if found, None otherwise
    """
    logger.info(f"Looking up hotlist ID for '{hotlist_name}' owned by {owner_id}")
    
    # Fields to retrieve from the hotlist table
    fields = ['mercury_hotlistid', 'mercury_name', '_owninguser_value']
    
    # Build filter for hotlist name and owner
    where_clause = f"mercury_name eq '{hotlist_name}' and _owninguser_value eq '{owner_id}'"
    
    response = read_fields_from_dataverse(
        token=token,
        dataverse_url=dataverse_url,
        table_name='mercury_hotlist',
        fields=fields,
        whereClause=where_clause,
        logger=logger
    )
    
    if response and 'value' in response and len(response['value']) > 0:
        hotlist_id = response['value'][0].get('mercury_hotlistid')
        logger.info(f"Found hotlist ID: {hotlist_id}")
        return hotlist_id
    else:
        logger.warning(f"No hotlist found with name '{hotlist_name}' and owner {owner_id}")
        return None


def get_contacts_in_hotlist(hotlist_id: str, token, dataverse_url: str, logger: AppLogger) -> List[Dict[str, Any]]:
    """
    Get all contacts in a specific hotlist.
    
    Args:
        hotlist_id: mercury_hotlistid
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        List of contact records
    """
    logger.info(f"Fetching contacts in hotlist {hotlist_id}")
    
    # Build the API URL for the contacts query
    fields_query = "fullname,emailaddress1,contactid"
    filter_query = f"mercury_mercury_hotlist_contact/any(h:h/mercury_hotlistid eq {hotlist_id})"
    
    url = f"{dataverse_url}/api/data/v9.0/contacts?$select={fields_query}&$filter={filter_query}"
    
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Accept': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }
    
    try:
        import requests
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            contacts = data.get('value', [])
            logger.info(f"Found {len(contacts)} contacts in hotlist {hotlist_id}")
            return contacts
        else:
            logger.error(f"Failed to fetch contacts: {response.status_code}, {response.text}")
            return []
            
    except Exception as e:
        logger.error(f"Error fetching contacts: {e}")
        return []


def create_hotlist(hotlist_name: str, owner_email: str, token, dataverse_url: str, logger: AppLogger) -> Dict[str, Any]:
    """
    Create a new hotlist owned by a specific user.
    
    Args:
        hotlist_name: Name of the hotlist to create
        owner_email: Email address of the hotlist owner
        token: Dataverse access token
        dataverse_url: Base URL of the Dataverse environment
        logger: Logger instance
        
    Returns:
        Dictionary containing creation result with hotlist_id if successful
    """
    logger.info(f"Creating hotlist '{hotlist_name}' owned by {owner_email}")
    
    try:
        # Step 1: Get owner ID and Azure Object ID from email address
        owner_id = get_user_id_by_email(owner_email, token, dataverse_url, logger)
        if not owner_id:
            logger.warning(f"User with email {owner_email} not found")
            return {
                'success': False,
                'error': f"User with email {owner_email} not found",
                'hotlist_id': None
            }
        
        logger.info(f"Found owner ID: {owner_id} for email {owner_email}")
        
        # Step 1.5: Get Azure Active Directory Object ID for impersonation
        azure_object_id = get_azure_object_id_by_email(owner_email, token, dataverse_url, logger)
        if not azure_object_id:
            logger.warning(f"Azure Object ID not found for email {owner_email}, will create without impersonation")
            azure_object_id = None
        else:
            logger.info(f"Found Azure Object ID: {azure_object_id} for email {owner_email}")
        
        # Step 2: Check if hotlist with same name already exists for this owner
        existing_hotlist_id = get_hotlist_id_by_name_and_owner(hotlist_name, owner_id, token, dataverse_url, logger)
        if existing_hotlist_id:
            logger.warning(f"Hotlist '{hotlist_name}' already exists for owner {owner_email}")
            return {
                'success': False,
                'error': f"Hotlist '{hotlist_name}' already exists for owner {owner_email}",
                'hotlist_id': existing_hotlist_id
            }
        
        # Step 3: Create the hotlist using Dataverse API
        url = f"{dataverse_url}/api/data/v9.0/mercury_hotlists"
        
        payload = {
            "mercury_name": hotlist_name,
            "statecode": 0,  # Active state
            "mercury_contactorcandidate": False  # Not a contractor candidate hotlist
        }
        
        headers = {
            "Authorization": f"Bearer {token.get_token()}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0"
        }
        
        # Add CallerObjectId header only if we have the Azure Object ID
        if azure_object_id:
            headers["CallerObjectId"] = azure_object_id  # Impersonate the owner using Azure Object ID
            logger.info(f"Using impersonation with Azure Object ID: {azure_object_id}")
        else:
            logger.info("Creating hotlist without impersonation (will be owned by the service account)")
        
        import requests
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code in (201, 204):  # 201 = Created, 204 = No Content
            # Extract the created hotlist ID from the response
            hotlist_id = None
            
            # Try to get the ID from the response headers or body
            if 'OData-EntityId' in response.headers:
                # Extract ID from header like: "OData-EntityId": "https://.../mercury_hotlists(********-1234-1234-1234-************)"
                entity_id = response.headers['OData-EntityId']
                import re
                match = re.search(r'mercury_hotlists\(([^)]+)\)', entity_id)
                if match:
                    hotlist_id = match.group(1)
            
            # If not found in headers, try to get from response body
            if not hotlist_id and response.text:
                try:
                    response_data = response.json()
                    hotlist_id = response_data.get('mercury_hotlistid')
                except:
                    pass
            
            logger.info(f"Hotlist '{hotlist_name}' successfully created with ID: {hotlist_id}")
            return {
                'success': True,
                'hotlist_id': hotlist_id,
                'hotlist_name': hotlist_name,
                'owner_email': owner_email,
                'owner_id': owner_id
            }
        else:
            logger.error(f"Failed to create hotlist: {response.status_code} {response.text}")
            return {
                'success': False,
                'error': f"Failed to create hotlist: {response.status_code} {response.text}",
                'hotlist_id': None
            }
            
    except Exception as e:
        logger.error(f"Error creating hotlist: {e}")
        return {
            'success': False,
            'error': f"Error creating hotlist: {e}",
            'hotlist_id': None
        }


def print_create_hotlist_result(result: Dict[str, Any], logger: AppLogger):
    """
    Print the hotlist creation result in a formatted way.
    
    Args:
        result: Creation result dictionary
        logger: Logger instance
    """
    logger.info("=" * 80)
    logger.info("CREATE HOTLIST RESULT")
    logger.info("=" * 80)
    
    if result['success']:
        logger.info("✅ HOTLIST CREATED SUCCESSFULLY")
        logger.info(f"Hotlist Name: {result['hotlist_name']}")
        logger.info(f"Owner Email: {result['owner_email']}")
        logger.info(f"Owner ID: {result['owner_id']}")
        logger.info(f"Hotlist ID: {result['hotlist_id']}")
    else:
        logger.info("❌ HOTLIST CREATION FAILED")
        logger.info(f"Error: {result['error']}")
        if result['hotlist_id']:
            logger.info(f"Note: Hotlist with this name already exists (ID: {result['hotlist_id']})")
    
    logger.info("=" * 80)


def analyze_hotlist_data(records: List[Dict[str, Any]], user_emails: Dict[str, str], logger: AppLogger) -> Dict[str, Any]:
    """
    Analyze the hotlist data by grouping by _createdby_value and checking for mismatches.
    
    Args:
        records: List of hotlist records
        user_emails: Dictionary mapping user GUIDs to email addresses
        logger: Logger instance
        
    Returns:
        Dictionary containing analysis results
    """
    logger.info("Starting analysis of hotlist data...")
    
    # Group records by _createdby_value
    created_by_groups = defaultdict(list)
    mismatch_records = []
    
    for record in records:
        created_by = record.get('_createdby_value', 'Unknown')
        owning_user = record.get('_owninguser_value', 'Unknown')
        
        # Add to the group
        created_by_groups[created_by].append(record)
        
        # Check for mismatch between created_by and owning_user
        if created_by != owning_user:
            mismatch_records.append({
                'mercury_hotlistid': record.get('mercury_hotlistid', 'Unknown'),
                'mercury_name': record.get('mercury_name', 'Unknown'),
                '_createdby_value': created_by,
                '_owninguser_value': owning_user
            })
    
    # Prepare analysis results
    analysis_results = {
        'total_records': len(records),
        'unique_created_by_values': len(created_by_groups),
        'mismatch_count': len(mismatch_records),
        'created_by_groups': {},
        'mismatch_records': mismatch_records
    }
    
    # Convert defaultdict to regular dict and format the groups
    for created_by, group_records in created_by_groups.items():
        analysis_results['created_by_groups'][created_by] = {
            'count': len(group_records),
            'mercury_names': [record.get('mercury_name', 'Unknown') for record in group_records]
        }
    
    logger.info(f"Analysis completed:")
    logger.info(f"  - Total records: {analysis_results['total_records']}")
    logger.info(f"  - Unique created_by values: {analysis_results['unique_created_by_values']}")
    logger.info(f"  - Records with mismatched created_by/owning_user: {analysis_results['mismatch_count']}")
    
    return analysis_results


def print_hotlist_analysis_results(analysis_results: Dict[str, Any], user_emails: Dict[str, str], logger: AppLogger):
    """
    Print the hotlist analysis results in a formatted way.
    
    Args:
        analysis_results: Analysis results dictionary
        user_emails: Dictionary mapping user GUIDs to email addresses
        logger: Logger instance
    """
    logger.info("=" * 80)
    logger.info("HOTLIST ANALYSIS RESULTS")
    logger.info("=" * 80)
    
    # Summary
    logger.info(f"Total Records: {analysis_results['total_records']}")
    logger.info(f"Unique Created By Values: {analysis_results['unique_created_by_values']}")
    logger.info(f"Records with Mismatched Created By/Owning User: {analysis_results['mismatch_count']}")
    logger.info("")
    
    # Groups by created_by_value
    logger.info("GROUPS BY CREATED BY VALUE:")
    logger.info("-" * 40)
    
    for created_by, group_data in sorted(analysis_results['created_by_groups'].items()):
        # Get email for created_by user
        created_by_email = user_emails.get(created_by, f"{created_by} (No Email Found)")
        logger.info(f"Created By: {created_by_email}")
        logger.info(f"  Count: {group_data['count']}")
        logger.info(f"  Mercury Names:")
        for name in group_data['mercury_names']:
            logger.info(f"    - {name}")
        logger.info("")
    
    # Mismatch records
    if analysis_results['mismatch_records']:
        logger.info("RECORDS WITH MISMATCHED CREATED BY / OWNING USER:")
        logger.info("-" * 50)
        
        for record in analysis_results['mismatch_records']:
            created_by_email = user_emails.get(record['_createdby_value'], f"{record['_createdby_value']} (No Email Found)")
            owning_user_email = user_emails.get(record['_owninguser_value'], f"{record['_owninguser_value']} (No Email Found)")
            
            logger.info(f"Hotlist ID: {record['mercury_hotlistid']}")
            logger.info(f"  Mercury Name: {record['mercury_name']}")
            logger.info(f"  Created By: {created_by_email}")
            logger.info(f"  Owning User: {owning_user_email}")
            logger.info("")
    else:
        logger.info("No records found with mismatched created_by and owning_user values.")
    
    logger.info("=" * 80)




def main():
    """Main function to run the hotlist analyzer."""
    parser = argparse.ArgumentParser(description='Analyze hotlist table from Dataverse')
    parser.add_argument('--env', 
                       choices=['SANDBOX', 'UAT', 'PROD'],
                       default='PROD',
                       help='Dataverse environment (default: PROD)')

    parser.add_argument('--creator',
                       help='Search for hotlists created by specific user (GUID or email)')
    parser.add_argument('--candidates',
                       help='Get candidates in hotlist (format: delivery_owner_email|hotlist_name)')
    parser.add_argument('--check-contact',
                       help='Check if contact exists in hotlist (format: hotlist_name|owner_email|contact_id)')
    parser.add_argument('--add-contact',
                       help='Add contact to hotlist (format: hotlist_name|owner_email|contact_id)')
    parser.add_argument('--create-hotlist',
                       help='Create a new hotlist (format: hotlist_name|owner_email)')
    
    args = parser.parse_args()
    load_secrets_env_variables()
    # Create logger
    logger = create_logger()
    logger.info("Starting Hotlist Analyzer")
    logger.info(f"Environment: {args.env}")
    
    try:
        # Map environment string to enum
        env_map = {
            'SANDBOX': Environment.SANDBOX,
            'UAT': Environment.UAT,
            'PROD': Environment.PROD
        }
        environment = env_map[args.env]
        
        # Get Dataverse credentials and token
        logger.info("Getting Dataverse credentials and token...")
        credentials = get_dataverse_credentials_for_env(environment, logger)
        token = get_token_for_env(environment, logger)
        dataverse_url = credentials["RESOURCE_URL"]
        
        logger.info(f"Connected to Dataverse: {dataverse_url}")
        
        # Execute specific logic based on arguments
        if args.create_hotlist:
            # Create hotlist logic
            logger.info(f"Creating hotlist: {args.create_hotlist}")
            
            # Parse the create-hotlist argument (format: hotlist_name|owner_email)
            try:
                parts = args.create_hotlist.split('|')
                if len(parts) != 2:
                    logger.error("Invalid format for --create-hotlist. Use: hotlist_name|owner_email")
                    return 1
                
                hotlist_name = parts[0].strip()
                owner_email = parts[1].strip()
                
                # Create hotlist
                result = create_hotlist(hotlist_name, owner_email, token, dataverse_url, logger)
                
                # Print result
                print_create_hotlist_result(result, logger)
                

                
            except Exception as e:
                logger.error(f"Error parsing create-hotlist argument: {e}")
                return 1
                
        elif args.add_contact:
            # Add contact to hotlist logic
            logger.info(f"Adding contact to hotlist: {args.add_contact}")
            
            # Parse the add-contact argument (format: hotlist_name|owner_email|contact_id)
            try:
                parts = args.add_contact.split('|')
                if len(parts) != 3:
                    logger.error("Invalid format for --add-contact. Use: hotlist_name|owner_email|contact_id")
                    return 1
                
                hotlist_name = parts[0].strip()
                owner_email = parts[1].strip()
                contact_id = parts[2].strip()
                
                # Add contact to hotlist
                contact_added = add_contact_to_hotlist(hotlist_name, owner_email, contact_id, token, dataverse_url, logger)
                
                # Print result
                logger.info("=" * 80)
                logger.info("ADD CONTACT RESULT")
                logger.info("=" * 80)
                logger.info(f"Hotlist: {hotlist_name}")
                logger.info(f"Owner Email: {owner_email}")
                logger.info(f"Contact ID: {contact_id}")
                logger.info(f"Contact Added: {'YES' if contact_added else 'NO'}")
                logger.info("=" * 80)
                

                
            except Exception as e:
                logger.error(f"Error parsing add-contact argument: {e}")
                return 1
                
        elif args.check_contact:
            # Contact check logic
            logger.info(f"Checking contact in hotlist: {args.check_contact}")
            
            # Parse the check-contact argument (format: hotlist_name|owner_email|contact_id)
            try:
                parts = args.check_contact.split('|')
                if len(parts) != 3:
                    logger.error("Invalid format for --check-contact. Use: hotlist_name|owner_email|contact_id")
                    return 1
                
                hotlist_name = parts[0].strip()
                owner_email = parts[1].strip()
                contact_id = parts[2].strip()
                
                # Check if contact exists in hotlist
                contact_exists = check_contact_in_hotlist(hotlist_name, owner_email, contact_id, token, dataverse_url, logger)
                
                # Print result
                logger.info("=" * 80)
                logger.info("CONTACT CHECK RESULT")
                logger.info("=" * 80)
                logger.info(f"Hotlist: {hotlist_name}")
                logger.info(f"Owner Email: {owner_email}")
                logger.info(f"Contact ID: {contact_id}")
                logger.info(f"Contact Exists: {'YES' if contact_exists else 'NO'}")
                logger.info("=" * 80)
                

                
            except Exception as e:
                logger.error(f"Error parsing check-contact argument: {e}")
                return 1
                
        elif args.creator:
            # Creator search logic - get hotlists owned by user
            logger.info(f"Searching for hotlists owned by: {args.creator}")
            
            # Get user ID from email
            user_id = get_user_id_by_email(args.creator, token, dataverse_url, logger)
            if not user_id:
                logger.error(f"User with email {args.creator} not found")
                return 1
            
            logger.info(f"Found user ID: {user_id} for email {args.creator}")
            
            # Get hotlists owned by this user
            hotlists = get_hotlists_by_owner(user_id, token, dataverse_url, logger)
            
            # Print results
            print_hotlists_by_owner(args.creator, hotlists, logger)
            

                
        elif args.candidates:
            # Candidates search logic - get candidates in hotlist
            logger.info(f"Getting candidates in hotlist: {args.candidates}")
            
            # Parse the candidates argument (format: delivery_owner_email|hotlist_name)
            try:
                parts = args.candidates.split('|')
                if len(parts) != 2:
                    logger.error("Invalid format for --candidates. Use: delivery_owner_email|hotlist_name")
                    return 1
                
                delivery_owner_email = parts[0].strip()
                hotlist_name = parts[1].strip()
                
                # Get candidates in the hotlist
                candidate_emails = get_candidates_by_hotlist(delivery_owner_email, hotlist_name, token, dataverse_url, logger)
                
                # Print results
                print_candidates_by_hotlist(delivery_owner_email, hotlist_name, candidate_emails, logger)
                

                
            except Exception as e:
                logger.error(f"Error parsing candidates argument: {e}")
                return 1
                
        else:
            # Default: Get entire mercury_hotlist organized
            logger.info("Fetching and organizing entire mercury_hotlist")
            
            # Fetch hotlist records (not contractor candidates)
            records = fetch_all_hotlist_records(token, dataverse_url, logger)
            if records is None:
                logger.error("Failed to fetch data from hotlist table")
                return 1
            
            if len(records) == 0:
                logger.info("No hotlist records found in SANDBOX environment.")
                logger.info("This is normal if no hotlists have been created yet.")
                logger.info("You can create a hotlist using the --create-hotlist option.")
                return 0
            
            # Get user emails for display
            user_guids = set()
            for record in records:
                created_by = record.get('_createdby_value')
                owning_user = record.get('_owninguser_value')
                if created_by:
                    user_guids.add(created_by)
                if owning_user:
                    user_guids.add(owning_user)
            
            user_emails = fetch_user_emails(token, dataverse_url, user_guids, logger)
            
            # Analyze hotlist data
            analysis_results = analyze_hotlist_data(records, user_emails, logger)
            
            # Print results
            print_hotlist_analysis_results(analysis_results, user_emails, logger)
            

        
        logger.info("Hotlist analysis completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Error during hotlist analysis: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 