// /pages/auth-complete.tsx
"use client";
import { useEffect } from "react";

export default function AuthComplete() {
  useEffect(() => {
    if (window.opener) {
      // Notify the main window
      window.opener.postMessage("authcomplete", window.location.origin);
      window.parent.postMessage({ status: "authenticated" },  "*");
       window.parent.frames.postMessage({ status: "authenticated" },  "*");

      // Close the popup
      window.close();
    }
  }, []);

  return <p>Authentication complete. You can close this window.</p>;
}
