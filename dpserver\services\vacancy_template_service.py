from common.db.postgres_connector import PostgresConnector
from common.appLogger import AppLogger
from dataverse_helper.token_manager import Environment
import psycopg2
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
import json
import uuid

class VacancyTemplateService:
    """Service for handling Vacancy Template operations."""
    
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector, env: Environment = Environment.PROD):
        """Initialize with logger and database environment."""
        self.logger = logger
        self.db = db_connector
        self.schema = self.db.schema
        self.env = env

    def _get_active_db_connection(self):
        """
        Ensures an active DB connection is available, attempting to (re)connect if necessary.
        """
        conn = self.db.connection
        if not conn or conn.closed:
            self.logger.info(f"DB connection for {self.db.config.get('dbname', 'N/A')} is None or closed. Attempting to connect.")
            conn = self.db.connect()
            if not conn:
                self.logger.error(f"Failed to establish initial DB connection to {self.db.config.get('dbname', 'N/A')}.")
                raise Exception("Database service unavailable at the moment.")
        else:
            try:
                with conn.cursor() as cur_ping:
                    cur_ping.execute("SELECT 1")
                self.logger.debug(f"Existing DB connection to {self.db.config.get('dbname', 'N/A')} is active.")
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as ping_error:
                self.logger.warning(f"DB connection test failed for {self.db.config.get('dbname', 'N/A')} ('{ping_error}'). Attempting to reconnect.")
                try:
                    self.db.close()
                except Exception as close_e:
                    self.logger.warning(f"Error closing stale DB connection: {close_e}")
                conn = self.db.connect()
                if not conn:
                    self.logger.error(f"Failed to re-establish DB connection to {self.db.config.get('dbname', 'N/A')}.")
                    raise Exception("Database service unavailable after reconnect attempt.")
        
        if not conn or conn.closed:
            self.logger.error(f"DB connection to {self.db.config.get('dbname', 'N/A')} is still unavailable.")
            raise Exception("Database connection unavailable.")
        
        return conn

    def _transform_template_for_ui(self, job_template: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform the job template data into a format that the UI expects.
        
        Args:
            job_template: Raw job template data from database
            
        Returns:
            Transformed data in UI-friendly format
        """
        try:
            # Initialize transformed data with defaults
            transformed = {
                "requiredSkills": [],
                "preferredSkills": [],
                "recencyMustHaveSkills": "Current +1",
                "additionalJobTitles": [],
                "city": "",
                "state": "",
                "miles": "50",
                "yearsExperience": "",
                "degrees": [],
                "certificationsLicenses": [],
                "industry": "No",
                "confidential": "No",
                "enableCatalystMatch": False
            }
            
            # Process technical skills based on weight
            if isinstance(job_template.get('technical_skills'), list):
                for skill in job_template['technical_skills']:
                    if isinstance(skill, dict) and skill.get('name'):
                        if skill.get('weight') == 'high':
                            transformed['requiredSkills'].append(skill['name'])
                        else:
                            transformed['preferredSkills'].append(skill['name'])
            
            # Process degrees based on weight (only high weight degrees)
            if isinstance(job_template.get('degrees'), list):
                for degree in job_template['degrees']:
                    if isinstance(degree, dict) and degree.get('name') and degree.get('weight') == 'high':
                        transformed['degrees'].append(degree['name'])
            
            # Process job location
            if isinstance(job_template.get('job_location'), list) and len(job_template['job_location']) > 0:
                location = job_template['job_location'][0]
                if isinstance(location, dict):
                    transformed['city'] = location.get('city', '')
                    transformed['state'] = location.get('state', '')
                    transformed['miles'] = str(location.get('radius', '50'))
            
            # Process other fields
            if job_template.get('recency_of_must_have_skills'):
                transformed['recencyMustHaveSkills'] = job_template['recency_of_must_have_skills']
            
            if isinstance(job_template.get('addition_job_titles'), list):
                transformed['additionalJobTitles'] = [title for title in job_template['addition_job_titles'] if title]
            
            if job_template.get('years_of_experience'):
                transformed['yearsExperience'] = str(job_template['years_of_experience'])
            
            # Process certifications based on weight (only high weight certifications)
            if isinstance(job_template.get('certifications'), list):
                for cert in job_template['certifications']:
                    if isinstance(cert, dict) and cert.get('name') and cert.get('weight') == 'high':
                        transformed['certificationsLicenses'].append(cert['name'])
            
            if job_template.get('industry'):
                transformed['industry'] = job_template['industry']
            
            if job_template.get('confidential'):
                transformed['confidential'] = job_template['confidential']
            
            self.logger.info(f"Transformed template data: {transformed}")
            return transformed
            
        except Exception as e:
            self.logger.error(f"Error transforming template data: {str(e)}")
            # Return default structure if transformation fails
            return {
                "requiredSkills": [],
                "preferredSkills": [],
                "recencyMustHaveSkills": "Current +1",
                "additionalJobTitles": [],
                "city": "",
                "state": "",
                "miles": "50",
                "yearsExperience": "",
                "degrees": [],
                "certificationsLicenses": [],
                "industry": "No",
                "confidential": "No",
                "enableCatalystMatch": False
            }

    def _transform_ui_to_job_template(self, ui_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform UI data back into the job template format for database storage.
        
        Args:
            ui_data: UI-friendly template data
            
        Returns:
            Job template data in database format
        """
        try:
            job_template = {}
            
            # Transform technical skills (required and preferred)
            technical_skills = []
            
            # Add required skills with high weight
            for skill in ui_data.get('required_skills', []):
                if skill.strip():
                    technical_skills.append({
                        "name": skill.strip(),
                        "weight": "high"
                    })
            
            # Add preferred skills with medium weight
            for skill in ui_data.get('preferred_skills', []):
                if skill.strip():
                    technical_skills.append({
                        "name": skill.strip(),
                        "weight": "medium"
                    })
            
            if technical_skills:
                job_template['technical_skills'] = technical_skills
            
            # Transform degrees (all as high weight)
            degrees = []
            for degree in ui_data.get('degrees', []):
                if degree.strip():
                    degrees.append({
                        "name": degree.strip(),
                        "weight": "high"
                    })
            
            if degrees:
                job_template['degrees'] = degrees
            
            # Transform job location
            city = ui_data.get('city', '').strip()
            state = ui_data.get('state', '').strip()
            miles = ui_data.get('miles', '50').strip()
            
            if city and state:
                job_template['job_location'] = [{
                    "city": city,
                    "state": state,
                    "radius": int(miles) if miles.isdigit() else 50,
                    "lat": None,  # Will be updated by the function you provide
                    "long": None   # Will be updated by the function you provide
                }]
            
            # Transform other fields
            if ui_data.get('recency_must_have_skills'):
                job_template['recency_of_must_have_skills'] = ui_data['recency_must_have_skills']
            
            if ui_data.get('additional_job_titles'):
                job_template['addition_job_titles'] = [title.strip() for title in ui_data['additional_job_titles'] if title.strip()]
            
            if ui_data.get('years_experience'):
                job_template['years_of_experience'] = ui_data['years_experience']
            
            # Transform certifications (all as high weight)
            certifications = []
            for cert in ui_data.get('certifications_licenses', []):
                if cert.strip():
                    certifications.append({
                        "name": cert.strip(),
                        "weight": "high"
                    })
            
            if certifications:
                job_template['certifications'] = certifications
            
            if ui_data.get('industry'):
                job_template['industry'] = ui_data['industry']
            
            if ui_data.get('confidential'):
                job_template['confidential'] = ui_data['confidential']
            
            self.logger.info(f"Transformed UI data to job template: {job_template}")
            return job_template
            
        except Exception as e:
            self.logger.error(f"Error transforming UI data to job template: {str(e)}")
            return {}

    def get_template(self, vacancy_id: str) -> Dict[str, Any]:
        """
        Get the vacancy template data from the vacancies table by vacancy_id.
        
        Args:
            vacancy_id: The vacancy ID to retrieve template data for
            
        Returns:
            Dictionary with template data from database
        """
        try:
            conn = self._get_active_db_connection()
            
            if not vacancy_id:
                return {
                    "success": False,
                    "error": "vacancy_id is required",
                    "message": "Vacancy ID is required to get template data"
                }
            
            self.logger.info(f"Getting vacancy template data for vacancy_id: {vacancy_id}")
            
            # Query to get job_template data from vacancies table by vacancy_id
            query = """
                SELECT vacancy_data->>'job_template' as job_template
                FROM vacancies 
                WHERE vacancy_id = %s
                AND vacancy_data->>'job_template' IS NOT NULL 
                AND vacancy_data->>'job_template' != 'null'
            """
            
            with conn.cursor() as cursor:
                cursor.execute(query, (vacancy_id,))
                result = cursor.fetchone()
                
                if result and result[0]:
                    try:
                        # Parse the JSON job_template data
                        job_template = json.loads(result[0])
                        self.logger.info(f"Retrieved job template: {job_template}")
                        
                        # Transform the data into UI-friendly format
                        transformed_data = self._transform_template_for_ui(job_template)
                        
                        return {
                            "success": True,
                            "message": "Template retrieved successfully",
                            "data": transformed_data
                        }
                    except json.JSONDecodeError as json_error:
                        self.logger.error(f"Error parsing job_template JSON: {json_error}")
                        return {
                            "success": False,
                            "error": "Invalid JSON in job_template",
                            "message": "Failed to parse template data"
                        }
                else:
                    self.logger.info("No job template found in vacancies table")
                    return {
                        "success": True,
                        "message": "No template found",
                        "data": {}
                    }
            
        except Exception as e:
            self.logger.error(f"Error getting vacancy template: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to get vacancy template"
            }

    def validate_template(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate the vacancy template and return any errors.
        
        Args:
            template_data: Dictionary containing template data to validate
            
        Returns:
            Dictionary with validation results and errors
        """
        try:
            self.logger.info("Validating vacancy template")
            
            errors = []
            
            # TODO: Add your validation logic here
            # This is where you'll implement the template validation logic
            
            # Example validation (replace with your actual logic):
            if not template_data.get('city'):
                errors.append("City is required")
            
            if not template_data.get('state'):
                errors.append("State is required")
            
            if not template_data.get('yearsExperience'):
                errors.append("Years of Experience is required")
            
            # Check if any required skills are provided for specific categories
            required_skills = template_data.get('required_skills', [])
            if not required_skills and template_data.get('category') not in ['APP', 'Nursing', 'Therapy', 'Clinical', 'Corporate Finance', 'Corporate Services']:
                errors.append("Required skills are needed for this category")
            
            return {
                "success": len(errors) == 0,
                "errors": errors,
                "message": "Template validation completed"
            }
            
        except Exception as e:
            self.logger.error(f"Error validating vacancy template: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to validate vacancy template"
            }

    def save_template(self, vacancy_id: str, template_data: Dict[str, Any], regenerate: bool = False) -> Dict[str, Any]:
        """
        Save the vacancy template and optionally regenerate matches.
        Validation is performed during the save operation.
        
        Args:
            vacancy_id: The vacancy ID to save the template for
            template_data: Dictionary containing template data to save
            regenerate: Boolean flag to indicate if regeneration is needed
            
        Returns:
            Dictionary with save result
        """
        try:
            # Print the data received from the UI
            print("=" * 80)
            print("SAVE TEMPLATE - DATA RECEIVED FROM UI")
            print("=" * 80)
            print(f"Vacancy ID: {vacancy_id}")
            print(f"Regenerate flag: {regenerate}")
            print(f"Template data type: {type(template_data)}")
            print("Template data:")
            print(json.dumps(template_data, indent=2, default=str))
            print("=" * 80)
            
            # Also log to the logger
            self.logger.info(f"SAVE TEMPLATE - Vacancy ID: {vacancy_id}")
            self.logger.info(f"SAVE TEMPLATE - Regenerate: {regenerate}")
            self.logger.info(f"SAVE TEMPLATE - Data received: {json.dumps(template_data, indent=2, default=str)}")
            
            conn = self._get_active_db_connection()
            
            # First validate the template
            validation_result = self.validate_template(template_data)
            if not validation_result["success"]:
                return {
                    "success": False,
                    "errors": validation_result["errors"],
                    "message": "Template validation failed"
                }
            
            # Generate a unique template ID
            template_id = str(uuid.uuid4())
            submission_time = datetime.now(timezone.utc)
            
            self.logger.info(f"Saving vacancy template: {template_id}, regenerate: {regenerate}")
            
            # Transform UI data to job template format
            ui_job_template = self._transform_ui_to_job_template(template_data)
            
            if not ui_job_template:
                return {
                    "success": False,
                    "error": "Failed to transform template data",
                    "message": "Could not create job template from provided data"
                }
            
            # Get the existing job_template from vacancies table
            existing_job_template = None
            get_query = """
                SELECT vacancy_data->>'job_template' as job_template
                FROM vacancies 
                WHERE vacancy_id = %s
            """
            
            with conn.cursor() as cursor:
                cursor.execute(get_query, (vacancy_id,))
                result = cursor.fetchone()
                
                if result and result[0]:
                    try:
                        existing_job_template = json.loads(result[0])
                        self.logger.info(f"Retrieved existing job template: {existing_job_template}")
                    except json.JSONDecodeError as json_error:
                        self.logger.error(f"Error parsing existing job_template JSON: {json_error}")
                        existing_job_template = None
                else:
                    self.logger.info("No existing job template found")
            
            # Print both templates for comparison
            print("=" * 80)
            print("JOB TEMPLATE COMPARISON")
            print("=" * 80)
            print(f"Vacancy ID: {vacancy_id}")
            print(f"Regenerate flag: {regenerate}")
            print("\nUI Job Template (created from form data):")
            print(json.dumps(ui_job_template, indent=2, default=str))
            print("\nExisting Job Template (from database):")
            print(json.dumps(existing_job_template, indent=2, default=str) if existing_job_template else "None")
            print("=" * 80)
            
            # Log both templates
            self.logger.info(f"UI Job Template: {json.dumps(ui_job_template, indent=2, default=str)}")
            self.logger.info(f"Existing Job Template: {json.dumps(existing_job_template, indent=2, default=str) if existing_job_template else 'None'}")
            
            # Extract client_naics_code from existing template and add to ui_job_template
            if existing_job_template and 'client_naics_code' in existing_job_template:
                ui_job_template['client_naics_code'] = existing_job_template['client_naics_code']
                self.logger.info(f"Added client_naics_code from existing template: {existing_job_template['client_naics_code']}")
                print(f"Added client_naics_code: {existing_job_template['client_naics_code']}")
            else:
                self.logger.info("No client_naics_code found in existing template")
                print("No client_naics_code found in existing template")
            
            return {
                "success": True,
                "template_id": template_id,
                "message": "Vacancy template saved successfully",
                "regenerate": regenerate,
                "timestamp": submission_time.isoformat(),
                "data": template_data
            }
            
        except Exception as e:
            self.logger.error(f"Error saving vacancy template: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to save vacancy template"
            } 