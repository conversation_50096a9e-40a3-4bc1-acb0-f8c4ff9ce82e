{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_bbcfe3._.js", "server/edge/chunks/[root of the server]__c55477._.js", "server/edge/chunks/edge-wrapper_aaa207.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/candidates{(\\\\.json)}?", "originalSource": "/candidates"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/jobs{(\\\\.json)}?", "originalSource": "/jobs"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/skills-editor{(\\\\.json)}?", "originalSource": "/skills-editor"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/workforce-index{(\\\\.json)}?", "originalSource": "/workforce-index"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/vacancy{(\\\\.json)}?", "originalSource": "/vacancy"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/vacancy/:path*{(\\\\.json)}?", "originalSource": "/vacancy/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/experiments{(\\\\.json)}?", "originalSource": "/experiments"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/stats{(\\\\.json)}?", "originalSource": "/stats"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/CandidateTuning/For_Mercury_Portal{(\\\\.json)}?", "originalSource": "/CandidateTuning/For_Mercury_Portal"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "asmo4IhPGQk+LiRdCjsGpWeD0Wx0k6noY8L51VfY+cI=", "__NEXT_PREVIEW_MODE_ID": "11ab8fb73e31c6c7dd9f95f9f1f0b122", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "66dd63f6bd46d8c8d6b945b15d21f3c3098b0d7274cf4d096af66c9515908775", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dffa6a40701d3f2ed48fe9d9586d58a4fdc177be95e91a58138a1b23def4a04c"}}}, "instrumentation": null, "functions": {}}