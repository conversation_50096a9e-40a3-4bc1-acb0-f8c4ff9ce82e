import requests
import json
import os

from data_helper.category_subcat_cache_db import CategoryCache
from dataverse_helper.dv_common import read_fields_from_dataverse, update_vacancy_row_in_dataverse
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from common.db.config_postgres import PostgresEnvironment
from common.db.global_dbconnector import GlobalDBConnector
from data_helper.vacancy_db import VacancyDB
from generator.jobtemplate_utils import populate_job_template, extract_validate_job_template, extract_between_markers
from utils.email.sendgrid_helper import SendGridEmailHelper
from common.utils.geo_helper import get_coordinates
from generator.vancacy_openai_utils import get_lat_long_openai
from common.appLogger import getGlobalAppLogger
from dataverse_helper.dv_conn import getSandBoxDataverseCredentials, getUATDataverseCredentials, getProdDataverseCredentials
from common.secrets_env import load_secrets_env_variables
from catalyst_match.config_helper import get_support_team_emails, get_fallback_email, get_support_email

# Email configuration constants
DEFAULT_CRM_BASE_URL = os.getenv("EMAIL_NOTIFICATION_CRM_BASE_URL", "https://tandymgroup.crm.dynamics.com/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id=")

# Dataverse columns to fetch
DV_COLUMNS_TO_FETCH = [
    'crimson_vacancyid',               # Vacancy ID
    'crimson_jobtitle',
    'crimson_addresscity',
    'crimson_vacancyrefno',           # Ref No: PR/503125, CR/503125 etc
    'crimson_jobsummaryemail',
    'mercury_emaildescription',
    'crimson_jobsummary',
    'recruit_adverttext2',
    'recruit_booleanuserfield3',       # Catalyst Match
    'recruit_adverttext3',             # Advertized Job Description - Broadbean, Shazamme
    'recruit_dateuserfield1',          # Start Date set by recruiter
    'recruit_mandatorytagcontrol0',    # Job Category
    'recruit_mandatorytagcontrol2',    # Job Subcategory
    'recruit_numericuserfield5',       # Processed flag
    'mercury_visibleonportal',         # Visible on portal
    'recruit_publishedpostingurl',     # job URL posted on https://careers.tandymgroup.com
    'createdon'                        # Created on
]



def send_template_error_notification(email_helper, dataverse_url, owninguser, deliveryowner, vacancy_ref, vacancy_title, vacancy_id, error_text, logger=None):
    """
    Send email notification to the owning user about template errors.
    
    This function notifies the vacancy owner (or delivery owner as fallback) when there are
    issues with the job template that prevent Catalyst Match from being enabled. The email
    includes details about the specific error and provides a link to the vacancy in Mercury
    so the user can fix the template and resubmit for search match.
    
    Args:
        email_helper: SendGridEmailHelper instance
        dataverse_url: Base URL for the Dataverse environment
        owninguser: Email of the owning user
        deliveryowner: Email of the delivery owner (fallback)
        vacancy_ref: Vacancy reference number
        vacancy_title: Job title
        vacancy_id: Vacancy ID
        error_text: Description of the template error
        logger: Logger instance
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if logger is None:
        logger = getGlobalAppLogger()
    
    # Determine recipient email with fallback logic
    recipient_email = None
    if deliveryowner and '@' in deliveryowner:
        recipient_email = deliveryowner
        logger.info(f"Using delivery owner email for template error notification: {recipient_email}")
    elif owninguser and '@' in owninguser:
        recipient_email = owninguser
        logger.info(f"Using owning user email for template error notification: {recipient_email}")
    else:
        recipient_email = get_fallback_email()
        logger.info(f"Using fallback email for template error notification: {recipient_email}")
    
    # Prepare email context
    email_context = {
        "recruiter_name": recipient_email.split('@')[0].replace('.', ' ').title(),  # Convert email to name
        "vacancy_title": vacancy_title or "Unknown Position",
        "vacancy_ref": vacancy_ref,
        "vacancy_url": f"{dataverse_url}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id={vacancy_id}",
        "error_description": error_text,
        "support_email": get_support_email()
    }
    
    try:
        response = email_helper.send_templated_email(
            template_base="template_error",
            context=email_context,
            from_email=os.getenv("SENDGRID_DEFAULT_SENDER", "<EMAIL>"),
            to_emails=recipient_email,
            cc_emails=get_support_team_emails(),
            subject_template="{{ vacancy_ref }}: {{ vacancy_title }} - Job Template Error - Action Required"
        )
        
        if 'error' in response:
            raise Exception(response['error'])
        
        logger.info(f"Template error notification sent successfully to {recipient_email} for vacancy {vacancy_ref}")
        return True
        
    except Exception as email_error:
        logger.error(f"Failed to send template error notification for vacancy {vacancy_ref}: {email_error}")
        return False

def process_job_template_vacancy(
    vacancy_id=None, rerun=False, logger=None, env=None
):
    """
    Process a vacancy for catalyst match including job template validation and database insertion.
    
    Args:
        vacancy_id: Vacancy ID to process
        rerun: Whether to reprocess existing job templates
        logger: Logger instance
        env: Environment enum (e.g., Environment.PROD, Environment.UAT)
    
    Returns:
        bool: True if processing completed successfully, False if there was an error or should skip
    """
    # Import here to avoid circular imports
    from generator.populate_vacancy_skills import process_vacancy_old
    
    if vacancy_id is None:
        return False
    
    # Get token and dataverse_url from environment
    token = get_token_for_env(env, logger=logger)
    credentials = get_dataverse_credentials_for_env(env, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]
    
    # Get database connector from environment
    if env == Environment.SANDBOX:
        postgres_env = PostgresEnvironment.DEV
    elif env == Environment.UAT:
        postgres_env = PostgresEnvironment.UAT
    elif env == Environment.AZ_APP_VAULT:
        postgres_env = PostgresEnvironment.AZ_APP_VAULT
    else:
        postgres_env = PostgresEnvironment.PROD
    
    db_connector = GlobalDBConnector.get_connector(postgres_env, logger=logger)
    db_connector.connect()
    if db_connector is None:
        logger.error("Failed to connect to postgres")
        return False
        
    # Query Dataverse to get the vacancy row
    fields = DV_COLUMNS_TO_FETCH
    whereClause = f"crimson_vacancyid eq '{vacancy_id}'"
    expand = "owninguser($select=domainname),crimson_deliveryownerid($select=domainname),crimson_clientid($select=name)"
    
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=expand, additional_headers=None)
    
    if not rep['value']:
        logger.error(f"Vacancy with ID {vacancy_id} not found in Dataverse")
        return False
    
    row = rep['value'][0]
    logger.debug(f"Retrieved vacancy data from Dataverse for vacancy_id: {vacancy_id}")
    
    ref_no = row['crimson_vacancyrefno']
    vacancy_id = row['crimson_vacancyid']
    advert_text2 = row['recruit_adverttext2']
    processed = row['recruit_numericuserfield5']
    if processed is None:
        processed = 0
    owninguser = row['owninguser']['domainname'] if row['owninguser'] is not None else ""
    deliveryowner = row['crimson_deliveryownerid']['domainname'] if row['crimson_deliveryownerid'] is not None else ""
    
    # Initialize variables that were previously passed as parameters
    error_code = 0
    new_job_data = {}
    
    # Initialize SendGrid helper for email notifications
    try:
        email_helper = SendGridEmailHelper(logger=logger)
        logger.debug("SendGrid Email Helper initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize SendGrid Email Helper: {e}")
        email_helper = None
    
    #Check if we generated job template json
  
    category_cache = CategoryCache(logger, db_connector)
    category_id = category_cache.get_category_id(row['recruit_mandatorytagcontrol0'])
    subcategory_id = category_cache.get_subcategory_id(row['recruit_mandatorytagcontrol2'])
    vacancy_db = VacancyDB(logger, db_connector)
    new_vacancy_db_data = vacancy_db.retrieve_vacancy_from_db(vacancy_id)
    if new_vacancy_db_data is None:
        logger.info(f"vacancy {ref_no} not found in the database let us reprocess it again")
        success, advert_text_minus_template = extract_between_markers(advert_text2, False, logger)
        if success and len(advert_text_minus_template) > 10:
            ret = process_vacancy_old(token, dataverse_url, row, advert_text_minus_template, db_connector, logger)
            if ret:
                #get new data from postgres
                new_vacancy_db_data = vacancy_db.retrieve_vacancy_from_db(vacancy_id)
            else:
                return False
        else:
            logger.error(f"job template is not valid for {ref_no}")
            error_text = "Missing Starting marker --------------------- or please add text above job template"
            new_row_data = {'recruit_booleanuserfield3': False}
            response = update_vacancy_row_in_dataverse(token, dataverse_url, vacancy_id, new_row_data)
            if response.status_code != 204:
                logger.error(f"Failed to turn off catalyst match: {response.status_code}, {response.text}")
            logger.info(f"Turned off catalyst match for {ref_no} - error text is -- {error_text}")
            #Send email to the user with the error text.
            if email_helper is not None:
                try:
                    # Send template error notification
                    send_template_error_notification(
                        email_helper=email_helper,
                        dataverse_url=dataverse_url,
                        owninguser=owninguser,
                        deliveryowner=deliveryowner,
                        vacancy_ref=ref_no,
                        vacancy_title=row['crimson_jobtitle'],
                        vacancy_id=vacancy_id,
                        error_text=error_text,
                        logger=logger
                    )
                except Exception as email_error:
                    logger.error(f"Failed to send template error notification for {ref_no}: {email_error}")
            else:
                logger.warning(f"Email helper not available - skipping template error notification for {ref_no}")
            return False
    if 'job_template' in new_vacancy_db_data and not rerun: #Only run when rerun is true
        logger.debug(f"job template already exists for {ref_no}")
    else:
        logger.info(f"catalyst match is enabled for {ref_no}")

        ret_value, ret_text = populate_job_template(token, dataverse_url, advert_text2, vacancy_id, logger)
        if ret_value == "job template exists":
            #Pass the client to add to the template before sending to chatgpt
            client_name = row['crimson_clientid']['name']
            error_code, json_text = extract_validate_job_template(advert_text2, client_name, row['recruit_mandatorytagcontrol0'], logger)
            if error_code == 0:
                logger.info(f"json text is {json_text}")
        else:
            logger.error(f"job template should not be created here for {ref_no}")
        if ret_value != "job template exists" or error_code > 0:
            error_text = "Missing items in the job template"
            if error_code == 1:
                error_text = "Missing location "
            if error_code == 2:
                error_text = "Missing Required skills"
            if error_code == 3:
                error_text = "Missing certifications and degrees"
            if error_code == 4:
                error_text = "missing years of experience"
            if error_code == 5:
                error_text = "State is unknown"
            if error_code == 6:
                error_text = "Please fill in the job template before enabling catalyst match"
            #turn off catalyst match
            new_row_data = {'recruit_booleanuserfield3': False}
            response = update_vacancy_row_in_dataverse(token, dataverse_url, vacancy_id, new_row_data)
            if response.status_code != 204:
                logger.error(f"Failed to turn off catalyst match: {response.status_code}, {response.text}")
            logger.info(f"Turned off catalyst match for {ref_no} - error text is -- {error_text}")
            #Send email to the user with the error text.
            if email_helper is not None:
                try:
                    # Send template error notification
                    send_template_error_notification(
                        email_helper=email_helper,
                        dataverse_url=dataverse_url,
                        owninguser=owninguser,
                        deliveryowner=deliveryowner,
                        vacancy_ref=ref_no,
                        vacancy_title=row['crimson_jobtitle'],
                        vacancy_id=vacancy_id,
                        error_text=error_text,
                        logger=logger
                    )
                except Exception as email_error:
                    logger.error(f"Failed to send template error notification for {ref_no}: {email_error}")
            else:
                logger.warning(f"Email helper not available - skipping template error notification for {ref_no}")

            return False
        
        
        #Add this meta data to the database.
        
        new_job_data['job_template'] = {}
        new_job_data['job_template']['job_location'] = json_text['job location']
        new_job_data['job_template']['industry'] = json_text['industry']
        new_job_data['job_template']['technical_skills'] = json_text['technical skills']
        if 'tools & platforms' in json_text:
            new_job_data['job_template']['tools_and_platforms'] = json_text['tools & platforms']
        else:
            new_job_data['job_template']['tools_and_platforms'] = []
        if 'degrees and certifications' in json_text:
            new_job_data['job_template']['degrees'] = json_text['degrees and certifications']
        elif 'degrees' in json_text:
            new_job_data['job_template']['degrees'] = json_text['degrees']
        if 'certifications' in json_text:
            new_job_data['job_template']['certifications'] = json_text['certifications']
        if "addition job titles" in json_text:
            new_job_data['job_template']['addition_job_titles'] = json_text['addition job titles']
        new_job_data['job_template']['years_of_experience'] = json_text['years of experience']
        new_job_data['job_template']['confidential'] = json_text['confidential']
        new_job_data['job_template']['recency_of_must_have_skills'] = json_text['recency of must have skills']
        new_job_data['job_template']['client_name_industry'] = json_text['client name industry']['name']
        new_job_data['job_template']['client_naics_code'] = json_text['client name industry']['naics_code']
        
        
        # Add coordinates to job_template.job_location if it exists
        
        logger.info(f"job location is {json_text['job location'][0]}")
        lat, long = get_coordinates(json_text['job location'][0]['city'], json_text['job location'][0]['state'], db_connector, logger=logger)
        if lat is  None or long is None:
            lat, long = get_lat_long_openai(json_text['job location'][0]['city'], json_text['job location'][0]['state'], logger=logger)
        new_job_data['job_template']['job_location'][0]['lat'] = lat
        new_job_data['job_template']['job_location'][0]['long'] = long
        logger.debug(f"lat and long are {lat} and {long} for {new_job_data['job_template']['job_location'][0]['city']}, {new_job_data['job_template']['job_location'][0]['state']}")
        

        new_vacancy_db_data['job_template'] = new_job_data['job_template']
        new_vacancy_db_data['job_url'] = row['recruit_publishedpostingurl']
        new_vacancy_db_data['job title'] = row['crimson_jobtitle']
        new_vacancy_db_data['category_id'] = category_id
        new_vacancy_db_data['subcategory_id'] = subcategory_id
        new_vacancy_db_data['deliveryowner'] = deliveryowner
        new_vacancy_db_data['owninguser'] = owninguser
       

        logger.debug(f"vacancy id is {vacancy_id}, {category_id}, {subcategory_id}, owninguser is {owninguser}, deliveryowner is {deliveryowner}")

        ret_vac = vacancy_db.insert_vacancy(new_vacancy_db_data, category_id=category_id, subcategory_id=subcategory_id)
        if ret_vac is not None:
            logger.info(f"job template is inserted in the database for {ref_no}")
        else:
            logger.error(f"job template is not inserted in the database for {ref_no}")

    return True

def test_template_error_notification(env, logger=None):
    """Test the template error notification functionality."""
    
    if logger is None:
        logger = getGlobalAppLogger()
    
    # Load environment variables
    load_secrets_env_variables()
    
    # Get environment-specific credentials
    if env == 0:
        credentials = getSandBoxDataverseCredentials()
    elif env == 1:
        credentials = getUATDataverseCredentials()
    else:
        credentials = getProdDataverseCredentials()
    
    dataverse_url = credentials["RESOURCE_URL"]
    
    logger.info("Starting template error notification test")
    
    # Initialize SendGrid helper
    try:
        email_helper = SendGridEmailHelper(logger=logger)
        logger.debug("SendGrid Email Helper initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize SendGrid Email Helper: {e}")
        return False
    
    # Test data
    test_data = {
        "owninguser": "<EMAIL>",
        "deliveryowner": "<EMAIL>",
        "vacancy_ref": "CR/503090",
        "vacancy_title": "Senior Software Engineer",
        "vacancy_id": "366ce5b2-48b6-ef11-b8e8-7c1e52462d0b",
        "error_text": "Missing technical skills in job template",
        "dataverse_url": "https://tandymgroup.crm.dynamics.com/"  # Test Dataverse URL
    }
    
    # Test with owning user email
    logger.info("Testing with owning user email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser=test_data["owninguser"],
        deliveryowner=test_data["deliveryowner"],
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text=test_data["error_text"],
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with owning user email")
    else:
        logger.error("❌ Template error notification test failed with owning user email")
    
    # Test with fallback to delivery owner email
    logger.info("Testing with fallback to delivery owner email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser="",  # Empty owning user to test fallback
        deliveryowner=test_data["deliveryowner"],
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text="Missing location and years of experience",
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with delivery owner fallback")
    else:
        logger.error("❌ Template error notification test failed with delivery owner fallback")
    
    # Test with fallback to default email
    logger.info("Testing with fallback to default email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser="",  # Empty owning user
        deliveryowner="",  # Empty delivery owner to test default fallback
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text="Missing certifications and degrees",
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with default email fallback")
    else:
        logger.error("❌ Template error notification test failed with default email fallback")
    
    logger.info("Template error notification test completed")
    return True 