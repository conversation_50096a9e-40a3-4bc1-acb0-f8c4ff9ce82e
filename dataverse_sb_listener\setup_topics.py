#!/usr/bin/env python3
"""
Setup script for Service Bus topics and subscriptions.

This script helps create the necessary topics and subscriptions for development.
Run this script before starting the listener with topic configurations.
"""

import sys
import os
import argparse
from pathlib import Path

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from azure.servicebus.management import ServiceBusAdministrationClient
from azure.core.exceptions import ResourceNotFoundError, ServiceRequestError, ClientAuthenticationError
from dotenv import load_dotenv

def load_environment(env_file=None):
    """Load environment variables from .env file."""
    if env_file and Path(env_file).exists():
        load_dotenv(env_file)
        print(f"Loaded environment from {env_file}")
    else:
        # Try to load from common .env files
        env_files = [".env.development", ".env", ".env.local"]
        for env_file in env_files:
            if Path(env_file).exists():
                load_dotenv(env_file)
                print(f"Loaded environment from {env_file}")
                break

def check_service_bus_connection(connection_string):
    """Test if the service bus connection is valid."""
    try:
        client = ServiceBusAdministrationClient.from_connection_string(connection_string)
        # Try to list topics to test the connection
        list(client.list_topics())
        return client
    except (ServiceRequestError, ClientAuthenticationError) as e:
        print(f"❌ Failed to connect to Service Bus: {e}")
        return None

def topic_exists(client, topic_name):
    """Check if a topic exists."""
    try:
        client.get_topic(topic_name=topic_name)
        return True
    except ResourceNotFoundError:
        return False
    except Exception as e:
        print(f"❌ Error checking if topic '{topic_name}' exists: {e}")
        return False

def subscription_exists(client, topic_name, subscription_name):
    """Check if a subscription exists."""
    try:
        client.get_subscription(topic_name=topic_name, subscription_name=subscription_name)
        return True
    except ResourceNotFoundError:
        return False
    except Exception as e:
        print(f"❌ Error checking if subscription '{subscription_name}' exists on topic '{topic_name}': {e}")
        return False

def create_topic_and_subscription(client, topic_name, subscription_name):
    """Create topic and subscription if they don't exist."""
    # Check if topic exists, create if it doesn't
    if not topic_exists(client, topic_name):
        try:
            client.create_topic(topic_name=topic_name)
            print(f"✅ Created topic '{topic_name}'")
        except Exception as e:
            print(f"❌ Failed to create topic '{topic_name}': {e}")
            return False
    else:
        print(f"ℹ️  Topic '{topic_name}' already exists")
    
    # Check if subscription exists, create if it doesn't
    if not subscription_exists(client, topic_name, subscription_name):
        try:
            client.create_subscription(topic_name=topic_name, subscription_name=subscription_name)
            print(f"✅ Created subscription '{subscription_name}' on topic '{topic_name}'")
        except Exception as e:
            print(f"❌ Failed to create subscription '{subscription_name}' on topic '{topic_name}': {e}")
            return False
    else:
        print(f"ℹ️  Subscription '{subscription_name}' on topic '{topic_name}' already exists")
    
    return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Setup Service Bus topics and subscriptions")
    parser.add_argument("--env-file", help="Path to .env file (e.g., .env.development)")
    parser.add_argument("--topics", nargs="+", default=["contacts", "vacancies"], 
                       help="List of topics to create")
    parser.add_argument("--subscription", default="sandbox-test",
                       help="Subscription name to create on each topic")
    parser.add_argument("--connection-string", 
                       help="Service Bus connection string (overrides env var)")
    
    args = parser.parse_args()
    
    # Load environment
    load_environment(args.env_file)
    
    # Get connection string
    connection_string = args.connection_string or os.getenv('SERVICE_BUS_CONNECTION_STRING')
    if not connection_string:
        print("❌ SERVICE_BUS_CONNECTION_STRING not found in environment or provided as argument")
        print("Please set it in your .env file or provide it with --connection-string")
        return 1
    
    print("🔧 Setting up Service Bus topics and subscriptions...")
    print(f"Topics: {args.topics}")
    print(f"Subscription: {args.subscription}")
    print()
    
    # Test connection and get client
    admin_client = check_service_bus_connection(connection_string)
    if not admin_client:
        print("❌ Failed to establish connection to Service Bus. Please check your connection string.")
        return 1
    
    print("✅ Service Bus connection successful")
    print()
    
    # Create topics and subscriptions
    success_count = 0
    for topic in args.topics:
        print(f"Processing topic: {topic}")
        if create_topic_and_subscription(admin_client, topic, args.subscription):
            success_count += 1
        print()
    
    print(f"🎉 Setup complete! {success_count}/{len(args.topics)} topics configured successfully.")
    
    if success_count == len(args.topics):
        print("\nYou can now run the listener with topic configuration:")
        print("python main.py --config configs/mixed_config.json")
        return 0
    else:
        print("\n⚠️  Some topics failed to configure. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit(main()) 