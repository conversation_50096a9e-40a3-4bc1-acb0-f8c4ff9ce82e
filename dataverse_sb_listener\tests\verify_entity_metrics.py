#!/usr/bin/env python3
"""
Verification script for entity-specific metrics.

This script quickly tests that the entity-specific metrics are working correctly.
"""

import sys
import os
import time
import requests

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from dataverse_sb_listener.health import start_health_server, set_ready, set_healthy, update_message_processed, update_message_failed

def main():
    """Test entity-specific metrics."""
    print("🔍 Testing Entity-Specific Metrics")
    print("=" * 50)
    
    # Start the health server
    print("Starting health server...")
    health_thread = start_health_server(port=8080)
    time.sleep(2)  # Wait for server to start
    
    # Set up health status
    set_ready(True)
    set_healthy(True)
    
    # Simulate entity-specific processing
    print("\n📊 Simulating entity processing...")
    
    # Contact entity
    print("  - Processing contact messages...")
    update_message_processed("contact")
    update_message_processed("contact")
    update_message_processed("contact")
    update_message_failed("contact")
    
    # Vacancy entity
    print("  - Processing vacancy messages...")
    update_message_processed("crimson_vacancy")
    update_message_processed("crimson_vacancy")
    update_message_failed("crimson_vacancy")
    
    # Generic processing (no entity specified)
    print("  - Processing generic messages...")
    update_message_processed()
    update_message_processed()
    
    time.sleep(1)  # Wait for metrics to update
    
    # Test the metrics endpoint
    print("\n🔍 Checking metrics endpoint...")
    try:
        response = requests.get("http://localhost:8080/metrics", timeout=5)
        if response.status_code == 200:
            metrics_text = response.text
            print("✅ Metrics endpoint is working")
            
            # Check for entity-specific metrics
            if "listener_entity_messages_processed_total{entity=\"contact\"}" in metrics_text:
                print("✅ Contact entity metrics found")
            else:
                print("❌ Contact entity metrics NOT found")
                
            if "listener_entity_messages_processed_total{entity=\"crimson_vacancy\"}" in metrics_text:
                print("✅ Vacancy entity metrics found")
            else:
                print("❌ Vacancy entity metrics NOT found")
                
            # Display the metrics
            print("\n📈 Current Metrics:")
            lines = metrics_text.split('\n')
            for line in lines:
                if line.startswith('listener_entity_messages_processed_total{entity='):
                    print(f"  {line}")
                elif line.startswith('listener_entity_messages_failed_total{entity='):
                    print(f"  {line}")
                elif line.startswith('listener_entity_success_rate_percent{entity='):
                    print(f"  {line}")
                    
        else:
            print(f"❌ Metrics endpoint returned status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing metrics: {e}")
    
    # Test the health endpoint
    print("\n🔍 Checking health endpoint...")
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint is working")
            
            entity_metrics = data.get('entity_metrics', {})
            if entity_metrics:
                print("✅ Entity metrics found in health response:")
                for entity, metrics in entity_metrics.items():
                    print(f"  {entity}: {metrics['processed']} processed, {metrics['failed']} failed")
            else:
                print("❌ No entity metrics found in health response")
                
        else:
            print(f"❌ Health endpoint returned status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing health endpoint: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Entity metrics verification complete!")
    print("You can also visit http://localhost:8080/docs for Swagger documentation")
    print("Press Ctrl+C to stop the server")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")

if __name__ == "__main__":
    main() 