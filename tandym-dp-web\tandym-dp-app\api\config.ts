const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://0.0.0.0:8005";
const PORTAL_SERVICE_BASE_URL =
  process.env.DP_PORTAL_SERVICE || "http://0.0.0.0:8006";
// Ensure this is the correct base URL for your experiment APIs,
// the example uses localhost:8006, so adjust if necessary.
// For this example, I will use the user-provided localhost:8006
export const EXPERIMENT_BASE_URL =
  process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:8005";

export const isADLogin = (): boolean =>
  process.env.NEXT_PUBLIC_AD_LOGIN === "true";

export const isParsedResume =
  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === "true";

export const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;

export const NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL =
  process.env.NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL;

export const API_ENDPOINTS = {
  subCategories: `${BASE_URL}/subcategories`,
  subCategoriesPools: `${BASE_URL}/subcategory/pools`,
  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,
  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,
  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,
  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,
  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,
  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,
  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,
  getVacancies: `${BASE_URL}/vacancies`,
  getVacancyByVacancyId: `${BASE_URL}/vacancy/:vacancy_id`,
  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,
  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,
  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,
  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,
  getCandidateStats: `${BASE_URL}/api/candidate-stats`,
  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,
  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,
  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,
  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,
  getCatalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,
  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,

  // New Experiment Endpoints
  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,
  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,
  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,
  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,
  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,
  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint
  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results

  // New Stats Endpoints
  getCatalystMatchStats: `${BASE_URL}/catalystmatch/stats`,
  getCatalystMatchStatsByCategory: `${BASE_URL}/catalystmatch/stats?aggregate_by_category=true`,

  // Applicant Stats Endpoints
  getApplicantStatsSummary: `${BASE_URL}/applicants/stats/summary`,
  getApplicantStatsByVacancy: `${BASE_URL}/applicants/stats/by-vacancy`,
  getApplicantStatsByCategory: `${BASE_URL}/applicants/stats/by-category`,
  getApplicantStatsTrends: `${BASE_URL}/applicants/stats/trends`,
  
  // CatalystMatch Endpoints
  submitCatalystMatch: `${BASE_URL}/catalyst-match/submit`,
  
  // Vacancy Template Endpoints
  getVacancyTemplate: `${BASE_URL}/vacancy-template/get`,
  saveVacancyTemplate: `${BASE_URL}/vacancy-template/save`,
};
