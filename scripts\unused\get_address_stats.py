import os
import json
from collections import defaultdict

# Get stats from candidate json file on how many candidates have city, state, country informatiom
def analyze_json_files(directory):
    no_city_count = 0
    city_no_state_count = 0
    state_no_city_count = 0
    us_count = 0
    empty_country_count = 0
    country_count = defaultdict(int)

    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            file_path = os.path.join(directory, filename)
            with open(file_path, 'r') as file:
                try:
                    data = json.load(file)
                    city = data.get("city")
                    state = data.get("state")
                    country = data.get("country")

                    # Count cases based on city and state presence
                    if not city:
                        no_city_count += 1
                    if city and not state:
                        city_no_state_count += 1
                    if state and not city:
                        state_no_city_count += 1

                    # Country analysis
                    if country == "United States":
                        us_count += 1
                    elif not country:
                        empty_country_count += 1
                    else:
                        country_count[country] += 1

                except json.JSONDecodeError:
                    print(f"Error decoding JSON in file: {filename}")

    # Display results
    print("Analysis Results:")
    print(f"Number of files without a city: {no_city_count}")
    print(f"Number of files with a city but no state: {city_no_state_count}")
    print(f"Number of files with a state but no city: {state_no_city_count}")
    print(f"Number of files with country as United States: {us_count}")
    print(f"Number of files with an empty country: {empty_country_count}")
    print("Country-wise distribution:")
    for country, count in country_count.items():
        print(f"{country}: {count}")





if __name__ == "__main__":
    directory = "/mnt/incoming/classify-prod"
    analyze_json_files(directory)
