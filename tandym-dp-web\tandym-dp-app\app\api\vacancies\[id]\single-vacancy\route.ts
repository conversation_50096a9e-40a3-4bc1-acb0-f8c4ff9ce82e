import { fetchVacanciesByVacancyId } from "@/api/serverActions";
import { NextRequest } from "next/server";
import { AuthError<PERSON><PERSON><PERSON>, CheckAuth } from "@/utils/auth-utils";
import { APPLICATION_NAVIGATION_ROUTES } from "@/library/utils";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const response = await CheckAuth(APPLICATION_NAVIGATION_ROUTES.VACANCY);
      if (response) {
  try {
    const { id } = await params;
    const vacancyDetails = await fetchVacanciesByVacancyId(id);
    return Response.json(vacancyDetails);
  } catch (error) {
    let status = 500;
    let errorResponse = {
      error: true,
      message: "Internal server error",
    };

    if (error instanceof Error) {
      try {
        const parsed = JSON.parse(error.message);
        errorResponse = parsed;
        status = parsed.status ?? 500;
      } catch {
        errorResponse.message = error.message;
      }
    }

    return Response.json(errorResponse, { status });
  }
}
else
{
   return AuthErrorHandler();
}

}
