#!/usr/bin/env python3
"""
Email Processing Helpers

Modular helper functions for email processing operations.
Reduces code duplication and improves maintainability.
"""

from typing import List, Dict, Optional
from datetime import datetime


class EmailProcessingStats:
    """Class to track email processing statistics across batches"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all statistics to zero"""
        self.total_processed = 0
        self.with_attachments = 0
        self.without_attachments = 0
        self.moved_to_invalid = 0
        self.forwarded_to_mercury = 0
        self.moved_to_queue = 0
        self.failed_processing = 0
        self.remaining_in_inbox = 0
        self.total_batches = 0
        self.total_emails_found = 0
    
    def add_email(self, has_attachments: bool):
        """Add an email to the processing count"""
        self.total_processed += 1
        if has_attachments:
            self.with_attachments += 1
        else:
            self.without_attachments += 1
    
    def add_result(self, final_status: str, failed_step: Optional[str] = None):
        """Add a processing result to statistics"""
        if failed_step:
            self.failed_processing += 1
            self.remaining_in_inbox += 1
        elif final_status == 'inbox':
            self.remaining_in_inbox += 1
        elif final_status == 'invalid_attachment':
            self.moved_to_invalid += 1
        elif final_status == 'forwarded':
            self.forwarded_to_mercury += 1
            self.remaining_in_inbox += 1
        elif final_status == 'queue':
            self.forwarded_to_mercury += 1
            self.moved_to_queue += 1
    
    def increment_batch(self, emails_found_in_batch: int):
        """Increment batch counter and add emails found"""
        self.total_batches += 1
        self.total_emails_found += emails_found_in_batch
    
    def display_statistics(self, move_no_attachments: bool, fwd_mercurycv_mode: Optional[str], is_batch_mode: bool = False, logger=None):
        """Display processing statistics in a formatted way"""
        if logger is None:
            return  # Cannot display without logger
            
        if is_batch_mode:
            stats_header = f"\n📊 FINAL BATCH PROCESSING STATISTICS:"
            batch_info = f"  🔄 Total batches processed: {self.total_batches}"
            emails_info = f"  📧 Total emails found: {self.total_emails_found}"
            logger.info(stats_header)
            logger.info(batch_info)
            logger.info(emails_info)
            logger.debug(f"Final batch statistics: {self.total_batches} batches, {self.total_emails_found} emails found")
        else:
            stats_header = f"\n📊 PROCESSING STATISTICS:"
            emails_info = f"  📧 Total emails processed: {self.total_processed}"
            logger.info(stats_header)
            logger.info(emails_info)
            logger.debug(f"Processing statistics: {self.total_processed} emails processed")
        
        attachments_info = f"  📎 With attachments: {self.with_attachments}"
        no_attachments_info = f"  📄 Without attachments: {self.without_attachments}"
        logger.info(attachments_info)
        logger.info(no_attachments_info)
        logger.debug(f"Attachments breakdown: {self.with_attachments} with, {self.without_attachments} without")
        
        if move_no_attachments:
            invalid_info = f"  📁 Moved to Invalid Attachment folder: {self.moved_to_invalid}"
            logger.info(invalid_info)
            logger.debug(f"Moved to Invalid Attachment folder: {self.moved_to_invalid}")
        
        if fwd_mercurycv_mode:
            forwarded_info = f"  📤 Forwarded to Mercury CV: {self.forwarded_to_mercury}"
            logger.info(forwarded_info)
            logger.debug(f"Forwarded to Mercury CV: {self.forwarded_to_mercury}")
            if fwd_mercurycv_mode == "move_to_queue":
                queue_info = f"  📁 Moved to ContactId_Queue: {self.moved_to_queue}"
                logger.info(queue_info)
                logger.debug(f"Moved to ContactId_Queue: {self.moved_to_queue}")
        
        if self.failed_processing > 0:
            failed_info = f"  ❌ Failed processing: {self.failed_processing}"
            logger.info(failed_info)
            logger.warning(f"Failed processing: {self.failed_processing}")
        
        remaining_text = "Final emails remaining in inbox" if is_batch_mode else "Remaining in inbox for display"
        remaining_info = f"  📋 {remaining_text}: {self.remaining_in_inbox}"
        logger.info(remaining_info)
        logger.debug(f"{remaining_text}: {self.remaining_in_inbox}")


def fetch_emails_with_mode(email_fetcher, live_mode: bool, count: int) -> List[Dict]:
    """
    Fetch emails based on mode (live or test) - centralized logic
    
    Args:
        email_fetcher: AzureEmailFetcher instance
        live_mode: If True, fetch all emails; if False, use test filter
        count: Number of emails to fetch
        
    Returns:
        List of email dictionaries
    """
    if live_mode:
        return email_fetcher.fetch_emails(
            mailbox="<EMAIL>",
            count=count
        )
    else:
        # Test mode: fetch with sender filtering
        return email_fetcher.fetch_emails(
            mailbox="<EMAIL>", 
            count=count,
            sender_filter=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        )


def process_email_batch(email_fetcher, emails: List[Dict], move_no_attachments: bool, 
                       fwd_mercurycv_mode: Optional[str], process_single_email_func, 
                       logger, batch_number: Optional[int] = None) -> Dict:
    """
    Process a batch of emails and return results
    
    Args:
        email_fetcher: AzureEmailFetcher instance
        emails: List of emails to process
        move_no_attachments: Whether to move emails without attachments
        fwd_mercurycv_mode: Mercury CV forwarding mode
        process_single_email_func: Function to process single email
        logger: Logger instance
        batch_number: Batch number for logging (optional)
        
    Returns:
        Dictionary with processing results
    """
    batch_results = {
        'processed_emails': [],
        'failed_emails': [],
        'remaining_emails': [],
        'stats': EmailProcessingStats()
    }
    
    batch_text = f"batch {batch_number}" if batch_number else "emails"
    
    for email in emails:
        email_id = email.get('id')
        subject = email.get('subject', '(No Subject)')
        has_attachments = email.get('hasAttachments', False)
        
        # Track email in statistics
        batch_results['stats'].add_email(has_attachments)
        
        try:
            result = process_single_email_func(email_fetcher, email, move_no_attachments, fwd_mercurycv_mode, logger)
            batch_results['processed_emails'].append(result)
            
            # Track result in statistics
            batch_results['stats'].add_result(result['final_status'], result['failed_step'])
            
            # Add to appropriate result lists
            if result['failed_step']:
                batch_results['failed_emails'].append({
                    'email': email,
                    'failed_step': result['failed_step'],
                    'error_message': result['error_message'],
                    'batch_number': batch_number
                })
                batch_results['remaining_emails'].append(email)
                
            elif result['final_status'] in ['inbox', 'forwarded']:
                batch_results['remaining_emails'].append(email)
                
        except Exception as e:
            logger.error(f"❌ Unexpected error processing Email ID: {email_id}, Subject: '{subject[:50]}...', Error: {e}")
            batch_results['failed_emails'].append({
                'email': email,
                'failed_step': 'unexpected_error',
                'error_message': str(e),
                'batch_number': batch_number
            })
            batch_results['remaining_emails'].append(email)
            batch_results['stats'].failed_processing += 1
    
    return batch_results


def display_failed_processing_details(failed_processing: List[Dict], max_display: int = 10, logger=None):
    """
    Display details of failed email processing
    
    Args:
        failed_processing: List of failed processing dictionaries
        max_display: Maximum number of failures to display
        logger: Logger instance for logging
    """
    if not failed_processing or logger is None:
        return
        
    header = f"❌ FAILED PROCESSING DETAILS ({len(failed_processing)} emails):"
    logger.warning(f"\n{header}")
    logger.warning(f"Failed processing details: {len(failed_processing)} emails failed")
    
    for i, failed in enumerate(failed_processing[:max_display], 1):
        email = failed['email']
        email_id = email.get('id', 'Unknown ID')
        subject = email.get('subject', '(No Subject)')
        batch_num = failed.get('batch_number')
        
        if batch_num:
            email_line = f"  {i}. 📧 Batch {batch_num} - {subject[:40]}..."
            logger.warning(email_line)
        else:
            email_line = f"  {i}. 📧 {subject[:40]}..."
            logger.warning(email_line)
        
        failed_step_line = f"     ❌ Failed Step: {failed['failed_step']}"
        error_line = f"     🔍 Error: {failed['error_message']}"
        logger.warning(failed_step_line)
        logger.warning(error_line)
        
        logger.error(f"Failed email {i}: {subject[:40]} - Step: {failed['failed_step']}, Error: {failed['error_message']}")
    
    if len(failed_processing) > max_display:
        remaining_msg = f"     ... and {len(failed_processing) - max_display} more failed emails"
        logger.warning(remaining_msg)
        logger.warning(f"Displayed {max_display} failed emails, {len(failed_processing) - max_display} more failed.")


def get_folder_display_name(live_mode: bool, move_no_attachments: bool, 
                           fwd_mercurycv_mode: Optional[str], is_batch_mode: bool = False) -> str:
    """
    Generate appropriate folder display name based on processing modes
    
    Args:
        live_mode: Whether in live mode
        move_no_attachments: Whether attachment processing is enabled
        fwd_mercurycv_mode: Mercury CV forwarding mode
        is_batch_mode: Whether in batch processing mode
        
    Returns:
        Formatted folder display name
    """
    base_name = "Inbox"
    modifiers = []
    
    if not live_mode:
        modifiers.append("Test")
    
    if is_batch_mode:
        modifiers.append("Batch Processed")
    elif move_no_attachments and fwd_mercurycv_mode:
        modifiers.append("Processed")
    elif move_no_attachments:
        modifiers.append("Attachment Filtered")
    elif fwd_mercurycv_mode:
        modifiers.append("Mercury CV Processed")
    
    if is_batch_mode:
        modifiers.append("Final Remaining")
    else:
        modifiers.append("Remaining")
    
    if modifiers:
        return f"{base_name} ({' + '.join(modifiers)})"
    else:
        return base_name


def print_batch_summary(batch_number: int, current_batch: List[Dict], 
                       batch_results: Optional[Dict], mode_text: str, logger=None):
    """
    Print summary for a single batch processing
    
    Args:
        batch_number: Current batch number
        current_batch: List of emails in current batch
        batch_results: Results from processing the batch
        mode_text: Mode description text
        logger: Logger instance for logging
    """
    if logger is None:
        return  # Cannot display without logger
        
    found_msg = f"📥 Found {len(current_batch)} emails in batch {batch_number}"
    logger.info(found_msg)
    
    if batch_results:
        processed_count = len(current_batch) - len(batch_results.get('remaining_emails', []))
        remaining_count = len(batch_results.get('remaining_emails', []))
        failed_count = len(batch_results.get('failed_emails', []))
        
        summary_msg = f"✅ Batch {batch_number} complete: {processed_count} processed, {remaining_count} remaining, {failed_count} failed"
        logger.info(summary_msg)
    else:
        no_processing_msg = f"ℹ️  Batch {batch_number} complete: {len(current_batch)} emails (no processing enabled)"
        logger.info(no_processing_msg) 