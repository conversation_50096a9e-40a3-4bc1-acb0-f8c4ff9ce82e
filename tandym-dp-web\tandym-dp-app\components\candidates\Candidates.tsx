"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  FilterIcon,
  LockKeyhole,
  RefreshCcw,
} from "lucide-react";
import { Candidate, ResumeData, Vacancy } from "../../app/candidates/helper";
import Loading from "@/components/Loading";
import { Input } from "@/components/ui/input";
import VacancyItem from "@/components/candidates/VacancyItem";
import CandidateResume from "@/components/candidates/CandidateResume";
import CandidateTable from "../CandidateTable/CandidateTable";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { Button } from "../ui/button";
import RegenerateButton from "./RegenerateButton";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { trackedFetch } from "@/library/trackApi";
import { getAppInsights } from "@/library/appInsights";
import VacancyDetails from "./VacancyDetails";
import { Badge } from "../ui/badge";
import { fetchEntitlements } from "@/api/serverActions";
import { NotificationProvider } from "@/hooks/useNotification";
import NotificationList from "../notifications/NotificationList";
import { initAppInsights } from "@/library/appInsights";
import { useEntitlement } from "@/context/EntitlementContext";
import { useVacancyStatus } from "@/hooks/useVacancyStatus";
import { CatalystMatchStatus } from "@/types/vacancy_status_api";
import { updateLocalStoredVacancyTimeStamp } from "@/utils/updatelocalStoredVacancyTimeStamp";
import { UPDATE_TIMESTAMPS_STATUS } from "@/types/vacancy_status_api";
import {
  AI_AGENTS_RESPONSE_STATUS,
  VACANCY_FILTER_LABELS,
  VACANCY_FILTER_OTHER_LABELS,
} from "@/library/utils";
import { useDebounce } from "@/hooks/useDebounce";
import type { iFilterSlider } from "./filterMenu";
import {
  defaultFilter,
  useDefaultFilterStore,
  useMercuryFilterStore,
  type ArrayFilterKeys,
  type VacancyFilter,
} from "@/store/vacancyFilterStore";
import { capitalizeEachWord } from "@/utils/utils";
import { extractLocations } from "./helper";
import CatalystMatchForm from "../CatalystMatchForm";

const DEFAULT_PER_PAGE = 150;
const SHOW_SEARCH_BY_EMAIL = false;

const Candidates = ({
  vacancyid,
  mercuryPortal,
  emailId,
  entitlementData,
}: {
  vacancyid?: string;
  mercuryPortal?: boolean;
  emailId?: string;
  entitlementData?: { [key: string]: boolean };
}) => {
  const isFetchCandidatesRunningRef = useRef(false);
  const prevVacancyRef = useRef<string | null>(null);

  const storeRef = useMemo(
    () => (mercuryPortal ? useMercuryFilterStore : useDefaultFilterStore),
    [mercuryPortal]
  );

  const useFilterStore = mercuryPortal
    ? useMercuryFilterStore
    : useDefaultFilterStore;

  const pathname = usePathname();
  const pageNotFound =
    vacancyid === undefined &&
    !pathname.includes(`/CandidateTuning/For_Mercury_Portal?vacancyid=`);
  const session = useSession();
  const name = session?.data?.user?.name;
  // Entitlement hook
  const { entitlements } = useEntitlement();

  // vacancy table filter entitlement
  const filterEntitlement =
    (entitlements?.Filter_Feature || entitlementData?.Filter_Feature) ?? false;
  // mercury disable
  const isMercuryReadOnly = false;

  const uniqueShortList = ["Yes", "No"];
  const uniqueReviewValues = ["ThumbsUp", "ThumbsDown", "NoReview"];
  const uniqueAiAgentStatus = [
    "Responded",
    "Contacted",
    "Not Contacted (Blank)",
    "Not interested",
  ]?.sort((a, b) => a.localeCompare(b));
  const responded = "responded";
  const contacted = "contacted";
  const notInterested = "not interested";

  const [vacancies, setVacancies] = useState<Vacancy[]>([]);
  const [vacancyCandidates, setVacancyCandidates] = useState<Record<
    string,
    Candidate[]
  > | null>({});
  const [selectedResume, setSelectedResume] = useState<ResumeData | null>(null);
  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);
  const [selectedVacancy, setSelectedVacancy] = useState<Vacancy | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [filteredCandidatesList, setFilteredCandidatesList] = useState<
    Candidate[]
  >([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableLoader, setTableLoader] = useState<boolean>(
    mercuryPortal ?? false
  );
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE);
  const [search, setSearch] = useState<string>("");
  const [vacancySearch, setVacancySearch] = useState<string>("");
  const [activeVacancy, setActiveVacancy] = useState<Vacancy | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [preventInitiallApiCall, setPreventInitiallApiCall] =
    useState<boolean>(false);
  const [isParsedVacancyPopupOpen, setIsParsedVacancyPopupOpen] =
    useState<boolean>(false);
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    direction: "asc" | "desc" | null;
  }>({
    key: null,
    direction: null,
  });
  const [statusVacancyId, setStatusVacancyId] = useState<string>("");
  const [updatedStatusCompleted, setUpdatedStatusCompleted] =
    useState<boolean>(false);
  const [canRegenerate, setCanRegenerate] = useState(true);
  // set data from catalyst match status api
  const [catalystRegenerationData, setCatalystRegenerationData] = useState<
    CatalystMatchStatus | undefined
  >();
  const [showUpdate, setShowUpdate] = useState(false);
  const [isRefreshButtonClick, setIsRefreshButtonClick] = useState(false);
  const [sortConfigCan, setSortConfigCan] = useState<{
    key: string | null;
    direction: "asc" | "desc" | null;
  }>({
    key: null,
    direction: null,
  });
  const [showVacancyList, setShowVacancyList] = useState<boolean>(true);
  const [searchText, setSearchText] = useState<string>("");
  const [showFilter, setShowFilter] = useState(true);
  const [activeTab, setActiveTab] = useState<string>("vacancy-template");
  // Distance filter state is required for filtering candidates by distance Range bar default values
  const [distance, setDistance] = useState<[number, number]>([0, 0]);
  // Availability date range state for calendar years dropdown
  const [maxMinAvailabilityDateRange, setMaxMinAvailabilityDateRange] =
    useState<{
      from: string;
      to: string;
    }>({ from: "", to: "" });

  // zustand store values
  const filters = useFilterStore((state) => state?.filters);
  const setFilterField = useFilterStore((state) => state?.setFilterField);
  const toggleSearchField = useFilterStore((state) => state?.toggleSearchField);
  const handleCheckBoxSelection = useFilterStore(
    (state) => state?.handleCheckBoxSelection
  );
  const resetFilter = useFilterStore((state) => state?.resetFilter);
  const clearFiltersForVacancyList = useFilterStore(
    (state) => state?.clearFiltersForVacancy
  );
  const distanceRange = useFilterStore(
    (state) => state.filters[VACANCY_FILTER_LABELS.DISTANCE_RANGE]
  );
  const setDistanceRange = useFilterStore((state) => state.setDistanceRange);

  // Debounce for filter input search filed
  const debouncedValue = useDebounce(searchText, 300);

  const showMercuryFilter =
    filterEntitlement && showFilter && candidates?.length > 0;

  const {
    [VACANCY_FILTER_LABELS.STATE]: state,
    [VACANCY_FILTER_LABELS.CITY]: city,
    [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: freshnessIndex,
    [VACANCY_FILTER_LABELS.SHORT_LISTED]: shortListed,
    [VACANCY_FILTER_LABELS.REVIEW_DECISION]: reviewStatus,
    [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: aiAgentStatus,
    [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: searchByName,
    [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: totalScoreRange,
    [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: availabilityDateRange,
    [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: searchFields,
  } = filters;

  // Set initial distance and availability year ranges
  useEffect(() => {
    const initialDistances =
      candidates
        ?.map((c) => c?.candidate_data?.distance_from_work_site)
        .filter((d): d is number => d !== null && d !== undefined) || [];

    const minMaxYears = candidates?.map((c) => {
      const year = new Date(c?.candidate_data?.availability).getFullYear();
      return year >= 2020 && year <= 2030 ? year : null;
    });

    const minYear = Math.min(
      ...(minMaxYears.filter((y): y is number => y !== null) || [])
    );
    const maxYear = Math.max(
      ...(minMaxYears.filter((y): y is number => y !== null) || [])
    );

    if (minYear && maxYear) {
      setMaxMinAvailabilityDateRange({
        from: String(minYear),
        to: String(maxYear),
      });
    }

    const minDistance =
      initialDistances?.length > 0 ? Math.min(...initialDistances) : 0;
    const maxDistance =
      initialDistances?.length > 0 ? Math.max(...initialDistances) : 0;

    if (initialDistances?.length > 0) {
      setDistance([minDistance, maxDistance]);
      setDistanceRange([minDistance, maxDistance]);
    }
  }, [candidates, setDistanceRange]);

  const isFunnelFill =
    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.STATE]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.CITY]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.length > 0 ||
    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[0] > 0 ||
    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[1] < 1 ||
    (filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length > 0 &&
      filters?.[VACANCY_FILTER_LABELS.SEARCH_BY_NAME]?.includes(
        searchText.trim()
      )) || // adapt if searchText replaced
    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from.trim() !==
      "" ||
    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to.trim() !==
      "" ||
    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[0] > distance?.[0] ||
    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[1] < distance?.[1] ||
    false;

  const filterIcon = () => (
    <FilterIcon
      fill={isFunnelFill ? "#2a70ea" : "white"}
      color={isFunnelFill ? "#2a70ea" : "black"}
      className="hover:cursor-pointer"
      onClick={handleFilterIcon}
    />
  );

  let showSearchFieldError =
    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length === 0;

  useEffect(() => {
    initAppInsights();
  }, []);

  const setRegenerationReadOnly = (value: boolean) => {
    setCanRegenerate(!value);
  };

  const handleReloadTable = () => {
    const vacancy_id = vacancyid ?? selectedVacancy?.vacancy_id;
    if (vacancy_id) {
      fetchCandidatesById(vacancy_id);
    }
  };

  useEffect(() => {
    if (emailId) {
      try {
        const setEntitlement = async () => {
          await fetchEntitlements(emailId);
        };
        setEntitlement();
      } catch (error) {
        console.error("Error fetching entitlements::", error);
      }
      localStorage.setItem("emailId", emailId);
    }
  }, [emailId]);

  const openParsedVacancyPopup = () => {
    setActiveVacancy(selectedVacancy);
    setIsParsedVacancyPopupOpen(true);
  };

  const fetchVacancies = async () => {
    try {
      setLoading(true);
      const response = await trackedFetch(
        "/api/vacancies",
        {},
        { context: "GetVacancies" }
      );
      if (response.ok) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: { vacancies: Vacancy[] } = await response.json();
        if (data?.vacancies) {
          setVacancies(Array.isArray(data?.vacancies) ? data.vacancies : []);
        }
        getAppInsights()?.trackEvent({
          name: mercuryPortal
            ? "FE_MercuryVacanciesFetched"
            : "FE_VacanciesFetched",
          properties: {
            count: data?.vacancies?.length ?? 0,
            context: mercuryPortal ? "MercuryGetVacancies" : "GetVacancies",
          },
        });
        return data?.vacancies;
      } else {
        console.error("Failed to fetch attributes:", response.statusText);
      }
    } catch (error) {
      getAppInsights()?.trackException({
        error: new Error(
          mercuryPortal
            ? "Mercury vacancies api with error is " + error
            : "vacancies api with error is " + error
        ),
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCandidatesById = async (vacancyid: string) => {
    if (isFetchCandidatesRunningRef.current) {
      // Skip if a fetch call is already running
      return;
    }
    isFetchCandidatesRunningRef.current = true;
    setTableLoader(true);
    try {
      const response = await trackedFetch(
        `/api/vacancies/${vacancyid?.split("/").join("-")}`,
        {},
        {
          context: "GetCandidatesById",
        }
      );
      if (response.ok) {
        const data: { candidates: Candidate[] } = await response.json();
        setCandidates(Array.isArray(data?.candidates) ? data?.candidates : []);
        getAppInsights()?.trackEvent({
          name: "FE_CandidatesFetchedById",
          properties: {
            vacancyid,
            context: mercuryPortal
              ? "MercuryGetCandidatesByVacancyId"
              : "GetCandidatesByVacancyId",
          },
        });
        if (isRefreshButtonClick) {
          updateLocalStoredVacancyTimeStamp(
            (vacancyid ?? "") || (selectedVacancy?.vacancy_id ?? ""),
            mercuryPortal ?? false
          );
        }
      } else {
        console.error("Failed to fetch attributes:", response?.statusText);
      }
    } catch (error: any) {
      console.error("Error fetching skills:", error);
      getAppInsights()?.trackException({
        error: new Error(
          mercuryPortal
            ? "Mercury CandidatesById api with error is " + error
            : "CandidatesById api with error is " + error
        ),
        severityLevel: 3,
      });
    } finally {
      setTableLoader(false);
      setLoading(false);
      setUpdatedStatusCompleted(false);
      isFetchCandidatesRunningRef.current = false; // reset flag on finish
    }
  };

  const isEmptyObject = (
    obj: Record<string, unknown> | null | undefined
  ): boolean =>
    !!obj && Object.keys(obj)?.length === 0 && obj?.constructor === Object;

  const showEntitlementCatalystMatchStatus =
    !entitlements || isEmptyObject(entitlements)
      ? entitlementData
      : entitlements;

  const statusApiCall =
    showEntitlementCatalystMatchStatus?.Update_Availability ?? false;
  const dependencyValue = mercuryPortal
    ? vacancyid ?? ""
    : selectedVacancy?.vacancy_id;

  const {
    isVacancyApiCalled,
    catalyst_match_status,
    update_timestamps,
    isStatusInProcess,
    statusCode,
    statusError,
    setStatusData,
    setIsStatusInProcess,
    setIsVacancyApiCalled,
  } = useVacancyStatus(
    statusApiCall ?? false,
    statusVacancyId,
    new Date().toISOString(),
    [dependencyValue]
  );

  let isReadOnly = isVacancyApiCalled && isStatusInProcess;

  const fetchSingleVacancyByVacancyId = async (vacancyid: string) => {
    try {
      const response = await trackedFetch(
        `/api/vacancies/${vacancyid}/single-vacancy`,
        {},
        { context: "GetSingleVacancyById" }
      );
      if (response.ok) {
        const data: { vacancy: Vacancy | null } = await response.json();
        setSelectedVacancy(data?.vacancy || null);
        getAppInsights()?.trackEvent({
          name: "FE_SingleVacancyFetched",
          properties: {
            vacancyid,
            context: mercuryPortal
              ? "MercuryGetSingleVacancyById"
              : "GetSingleVacancyById",
          },
        });
        if (data?.vacancy === null) {
          setTableLoader(false);
        }
      } else {
        console.error("Failed to fetch vacancy:", response.statusText);
      }
    } catch (error) {
      console.error("Error fetching single vacancy:", error);
      getAppInsights()?.trackException({
        error: new Error(
          mercuryPortal
            ? "Mercury SingleVacancy api with error is " + error
            : "SingleVacancy api with error is " + error
        ),
        severityLevel: 3,
      });
    }
  };

  useEffect(() => {
    setCatalystRegenerationData(catalyst_match_status);
  }, [catalyst_match_status]);

  useEffect(() => {
    if (mercuryPortal && vacancyid && selectedVacancy === null) {
      // Call fetch single Vacany only if selectedVacancy is  null
      fetchSingleVacancyByVacancyId(vacancyid);
    } else if (!mercuryPortal) {
      vacancies?.length === 0 &&
        fetchVacancies().then((vacancies) => {
          if (vacancies && vacancies.length > 0) {
            vacancies?.map((vacancy: any) => {
              if (
                vacancy?.vacancy_id?.toLowerCase() === vacancyid?.toLowerCase()
              ) {
                setSelectedVacancy(vacancy);
              }
            });
          }
        });
    }
    if (
      (isVacancyApiCalled || !statusApiCall) &&
      selectedVacancy !== null &&
      isStatusInProcess !== null &&
      statusApiCall
    ) {
      fetchCandidatesById(selectedVacancy.vacancy_id);
      if (
        !isStatusInProcess &&
        isStatusInProcess !== null &&
        preventInitiallApiCall
      ) {
        fetchSingleVacancyByVacancyId(selectedVacancy.vacancy_id);
        setPreventInitiallApiCall(true); // By making as true next time onwards it will call the API
      }
    } else if (selectedVacancy?.vacancy_id?.length && !mercuryPortal) {
      setStatusVacancyId(selectedVacancy.vacancy_id);
    } else {
      setStatusVacancyId(vacancyid ?? "");
    }
  }, [
    vacancyid,
    isVacancyApiCalled,
    selectedVacancy?.vacancy_id,
    statusApiCall,
    mercuryPortal,
  ]);
  // Call fetchCandidatesById when there's a statusError or unsuccessful status code (not 200)
  useEffect(() => {
    if (
      ((statusError && statusError.length > 0) ||
        (statusCode !== 200 && statusCode !== null)) &&
      selectedVacancy !== null
    ) {
      fetchCandidatesById(selectedVacancy.vacancy_id);
      if (mercuryPortal && vacancyid && selectedVacancy === null) {
        fetchSingleVacancyByVacancyId(vacancyid);
      }
    }
  }, [statusError, statusCode, selectedVacancy]);

  useEffect(() => {
    // Note: This will run only if entitlement is disabled or not available
    if (!statusApiCall && selectedVacancy && !isVacancyApiCalled) {
      fetchCandidatesById(selectedVacancy?.vacancy_id);
    }
  }, [selectedVacancy]);

  useEffect(() => {
    const handleUnload = () => {
      const currentVacancyId =
        selectedVacancy?.vacancy_id?.toLowerCase() ?? vacancyid?.toLowerCase();
      if (currentVacancyId) {
        storeRef.getState().saveCurrentFiltersForVacancy(currentVacancyId);
      }
    };

    window.addEventListener("beforeunload", handleUnload);
    return () => {
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [selectedVacancy?.vacancy_id, vacancyid, storeRef]);

  useEffect(() => {
    const currentVacancyId =
      selectedVacancy?.vacancy_id?.toLowerCase() ?? vacancyid?.toLowerCase();

    if (!currentVacancyId) return;

    const {
      saveCurrentFiltersForVacancy,
      loadFiltersForVacancy,
      setFilterField,
      resetFilter,
    } = storeRef.getState(); // Save existing filters before changing to the new one

    if (
      prevVacancyRef?.current &&
      prevVacancyRef?.current !== currentVacancyId
    ) {
      saveCurrentFiltersForVacancy(prevVacancyRef?.current);
    } // Load filters for the new vacancy if available

    const loaded = loadFiltersForVacancy(currentVacancyId);
    if (loaded) {
      const keys = Object.keys(defaultFilter) as (keyof VacancyFilter)[];
      keys.length !== 0 &&
        keys?.forEach((key) => {
          if (loaded[key] !== undefined) {
            setFilterField(key, loaded[key]);
          }
        });
    } else {
      resetFilter(distanceRange?.[1], 1); // Start fresh
    }

    prevVacancyRef.current = currentVacancyId;
  }, [selectedVacancy?.vacancy_id, vacancyid, storeRef]);

  const handleSort = (key: string = "refno") => {
    let direction: "asc" | "desc" | null = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    const sortedData = [...vacancies]?.sort((a, b) => {
      if (a?.refno < b?.refno) return direction === "asc" ? -1 : 1;
      if (a?.refno > b?.refno) return direction === "asc" ? 1 : -1;
      return 0;
    });

    setSortConfig({ key, direction });
    setVacancies(sortedData);
  };
  const toNumber = (value: unknown, fallback = null): number | null => {
    if (
      value === null ||
      value === undefined ||
      value === VACANCY_FILTER_OTHER_LABELS.MISSING ||
      value === ""
    )
      return null;

    const parsed = parseFloat(value as string);
    return isNaN(parsed) ? fallback : parsed;
  };

  const handleCandidateSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfigCan?.key === key && sortConfigCan?.direction === "asc") {
      direction = "desc";
    }

    const getValue = (candidate: Candidate): string | number | Date | null => {
      const data = candidate?.candidate_data;

      const classificationScore = data["classification score"] ?? {};
      switch (key) {
        case VACANCY_FILTER_OTHER_LABELS.NAME:
          return data.name?.toLowerCase() || "";
        case "overallscore":
          return classificationScore?.overallscore ?? 0;
        case "jobtitlescore":
          return classificationScore?.jobtitlescore ?? 0;
        case "softskillsscore":
          return classificationScore?.softskillsscore ?? 0;
        case "technicalskills":
          return classificationScore?.["technical skills"] ?? 0;
        case "toolsplatformsscore":
          return classificationScore?.toolsplatformsscore ?? 0;
        case "degreesandcertifications":
          return classificationScore?.["degrees and certifications"] ?? 0;
        case "industryexperiencescore":
          return classificationScore?.industryexperiencescore ?? 0;
        case "relevantexperiencescore":
          return classificationScore?.relevantexperiencescore ?? 0;
        case "jobtitle_recency_score":
          return classificationScore?.jobtitle_recency_score ?? 0;
        case "availability": {
          const availability = data?.availability;
          // Treat null, empty, or "Missing" as missing value
          if (
            !availability ||
            availability === VACANCY_FILTER_OTHER_LABELS.MISSING
          )
            return null;
          const date = new Date(availability);
          // Check for invalid date
          if (isNaN(date.getTime())) return null;

          return data?.availability;
        }
        case VACANCY_FILTER_OTHER_LABELS.CITY:
          const { city, state } = data;
          const isValid = (v: string | null | undefined) =>
            v && v !== VACANCY_FILTER_OTHER_LABELS.MISSING;
          if (isValid(city) && isValid(state))
            return `${city}, ${state}`.toLowerCase();
          if (isValid(city)) return city.toLowerCase();
          if (isValid(state)) return state.toLowerCase();
          return null;
        case "freshness":
          return classificationScore?.jobtitle_recency_score ?? 0;
        case "freshness_index":
          return data["freshness_index"];
        case "distance_from_work_site":
          return toNumber(data["distance_from_work_site"], null);
        default:
          return "";
      }
    };

    const sortedCandidates = [...filteredCandidatesList]?.sort((a, b) => {
      const aValue = getValue(a);
      const bValue = getValue(b);

      const isANull = aValue === null || aValue === undefined;
      const isBNull = bValue === null || bValue === undefined;

      if (isANull && isBNull) return 0;
      if (isANull) return 1; // nulls always last
      if (isBNull) return -1;

      // Strings
      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Dates
      if (aValue instanceof Date && bValue instanceof Date) {
        return direction === "asc"
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      // Numbers (including parsed distance)
      if (typeof aValue === "number" && typeof bValue === "number") {
        return direction === "asc" ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    setSortConfigCan({ key, direction });
    setFilteredCandidatesList(sortedCandidates);
  };

  const handleVacancyClick = (vacancy: Vacancy) => {
    setPreventInitiallApiCall(false);
    setStatusData && setStatusData(null);
    setIsVacancyApiCalled && setIsVacancyApiCalled(false);
    setSelectedVacancy(vacancy ?? null);
    setPage(1);
    setSortConfigCan({
      key: null,
      direction: null,
    });
  };

  const filteredCandidates = useMemo(() => {
    return Array.isArray(filteredCandidatesList)
      ? filteredCandidatesList.filter((c) =>
          c.candidate_data.email?.toLowerCase().includes(search.toLowerCase())
        )
      : [];
  }, [filteredCandidatesList, search]);

  // Paginate candidates
  const paginatedCandidates = useMemo(() => {
    const safeLimit = limit > 0 ? limit : 10;
    const start = (page - 1) * safeLimit;
    return filteredCandidates.slice(start, start + safeLimit);
  }, [filteredCandidates, page, limit]);

  const filteredVacancy = useMemo(() => {
    if (!vacancySearch) return vacancies;
    return vacancies.filter((vacancy) =>
      (vacancy.refno ?? "").toLowerCase().includes(vacancySearch.toLowerCase())
    );
  }, [vacancies, vacancySearch]);

  const fetchResume = async (candidate: Candidate) => {
    if (!candidate?.candidate_contactid) {
      console.error("Candidate contact ID is missing");
      return;
    }

    try {
      setLoading(true);
      setIsResumeModalOpen(true);
      const response = await trackedFetch(
        `/api/vacancies/resume/${candidate.candidate_contactid}`,
        {},
        { context: "GetResume" }
      );
      if (response.ok) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: any = await response.json();
        setSelectedResume(data);
        getAppInsights()?.trackEvent({
          name: "FE_ResumeFetched",
          properties: {
            candidateId: candidate?.candidate_contactid,
            context: mercuryPortal ? "MercuryGetResume" : "GetResume",
          },
        });
      } else {
        console.error("Failed to fetch resume:", response?.statusText);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("Error fetching resume:", error);
      getAppInsights()?.trackException({
        error: new Error(
          mercuryPortal
            ? "MercuryGetResume api with error is " + error
            : "GetResume api with error is " + error
        ),
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshVacancies = () => {
    const refreshVacancy_id = selectedVacancy?.vacancy_id || (vacancyid ?? "");
    if (statusApiCall) {
      if (selectedVacancy) {
        setIsVacancyApiCalled?.(false);
        setStatusVacancyId(selectedVacancy?.vacancy_id);
        setStatusData?.(null);
        setIsStatusInProcess?.(false);
        setIsRefreshButtonClick(true);
        setShowUpdate(false);
      } else if (vacancyid?.length && mercuryPortal) {
        setIsVacancyApiCalled?.(false);
        setStatusVacancyId(vacancyid);
        setStatusData?.(null);
        setIsStatusInProcess?.(false);
        setIsRefreshButtonClick(true);
        setShowUpdate(false);
      }
    }
    fetchCandidatesById(refreshVacancy_id);
    fetchSingleVacancyByVacancyId(refreshVacancy_id);
  };

  const isTableReadOnly = useMemo(() => {
    return isStatusInProcess || tableLoader;
  }, [isStatusInProcess, tableLoader]);

  const setIsStatusInProcessByClickOfRegenerateButton = () => {
    setIsStatusInProcess?.(true);
  };

  useEffect(() => {
    const utcTimestamp = new Date().toISOString();

    if (mercuryPortal && vacancyid) {
      const mercuryKey = "mercuryPortalVacancies";
      try {
        const existing = localStorage.getItem(mercuryKey);
        const data: Record<string, string> = existing
          ? JSON.parse(existing)
          : {};
        const normalizedVacancyId = vacancyid.toLowerCase(); // ✅ normalize
        data[normalizedVacancyId] = utcTimestamp;
        localStorage.setItem(mercuryKey, JSON.stringify(data));
      } catch (err) {
        console.error("Error updating mercuryPortalVacancies:", err);
      }
    }

    if (selectedVacancy?.vacancy_id) {
      const generalKey = "selectedVacancyTimestamps";
      try {
        const existing = localStorage?.getItem(generalKey);
        const data: Record<string, string> = existing
          ? JSON.parse(existing)
          : {};

        const normalizedSelectedId = selectedVacancy?.vacancy_id?.toLowerCase(); // ✅ normalize
        data[normalizedSelectedId] = utcTimestamp;

        localStorage?.setItem(generalKey, JSON.stringify(data));
      } catch (err) {
        console.error("Error updating selectedVacancyTimestamps:", err);
      }
    }
  }, [
    mercuryPortal,
    vacancyid,
    selectedVacancy?.vacancy_id,
    isRefreshButtonClick,
  ]);

  useEffect(() => {
    if (!selectedVacancy?.vacancy_id || !update_timestamps) return;

    const key = mercuryPortal
      ? "mercuryPortalVacancies"
      : "selectedVacancyTimestamps";

    try {
      const stored = localStorage?.getItem(key);
      if (!stored) return;

      const parsed = JSON.parse(stored) as Record<string, string>;

      const lookupKey = (
        selectedVacancy?.vacancy_id ||
        vacancyid ||
        ""
      ).toLowerCase();
      const localTimestamp = parsed[lookupKey];

      if (!localTimestamp) return;

      const backendTimestamp = update_timestamps?.data_last_updated_at ?? null;
      const completedStatus =
        catalyst_match_status?.completed_at ??
        catalyst_match_status?.initiated_at ??
        null;

      let isStatusCompleted =
        catalyst_match_status?.status === UPDATE_TIMESTAMPS_STATUS.COMPLETED;

      const shouldShowUpdate =
        new Date(localTimestamp).getTime() <=
        new Date(backendTimestamp).getTime();

      if (
        new Date(localTimestamp).getTime() <
          new Date(completedStatus).getTime() &&
        isStatusCompleted
      ) {
        const refreshVacancy_id =
          selectedVacancy?.vacancy_id || (vacancyid ?? "");
        isStatusCompleted = false;

        updateLocalStoredVacancyTimeStamp(
          lookupKey, // use normalized key
          mercuryPortal ?? false,
          completedStatus
        );

        fetchCandidatesById(refreshVacancy_id);
        fetchSingleVacancyByVacancyId(refreshVacancy_id);
      }

      setShowUpdate(shouldShowUpdate);
    } catch (error) {
      console.error("Error comparing timestamps for showUpdate:", error);
    }
  }, [
    selectedVacancy?.vacancy_id,
    update_timestamps,
    mercuryPortal,
    updatedStatusCompleted,
    catalyst_match_status,
  ]);

  const handleFilterIcon = () => {
    setShowFilter(!showFilter);
    setShowVacancyList(!showVacancyList);
  };

  const handleResetFilter = () => {
    resetFilter(distanceRange?.[1], 1);
    resetFilter(distance?.[1], 1);
    setSearchText("");
  };

  const uniqueLocation = useMemo(
    () => extractLocations(candidates),
    [candidates]
  );

  const uniqueFreshnessIndex = useMemo(
    () => [
      ...new Set(
        candidates
          ?.map((c) => c?.candidate_data?.freshness_index)
          ?.filter(
            (freshness_index) =>
              freshness_index !== null &&
              freshness_index !== undefined &&
              freshness_index !== ""
          )
      ),
    ],
    [candidates]
  );

  const toggleSearchFieldLocal = (field: string) => {
    toggleSearchField(field);
  };

  const handleCheckBoxSelectionLocal = (
    label: string,
    selectedValue: string
  ) => {
    handleCheckBoxSelection(label as ArrayFilterKeys, selectedValue);
  };

  useEffect(() => {
    if (filterEntitlement && candidates?.length > 0) {
      const filtered =
        candidates &&
        candidates?.filter((candidate) => {
          const candidateShortListed =
            typeof candidate?.candidate_data?.shortlisted_details?.status ===
              "string" &&
            (candidate?.candidate_data?.shortlisted_details?.status.toLowerCase() ===
              VACANCY_FILTER_OTHER_LABELS.SUCCESS ||
              candidate?.candidate_data?.shortlisted_details?.status.toLowerCase() !==
                "failed")
              ? "Yes"
              : "No";
          const candidateState = capitalizeEachWord(
            candidate?.candidate_data?.state?.toLowerCase() ?? "".trim()
          );
          const candidateCity = capitalizeEachWord(
            candidate?.candidate_data?.city?.toLowerCase() ?? ""
          );
          const candidateFreshnessIndex =
            candidate?.candidate_data?.freshness_index ?? "";
          const candidateInfoBotResponse =
            candidate?.candidate_data?.info_bot_response ?? "";
          const candidateReview =
            candidate?.candidate_data?.recruiter_review_decision?.vote;

          // Map candidate review vote to filter values
          let reviewDecision: string;
          if (candidateReview === null || candidateReview === undefined) {
            reviewDecision = VACANCY_FILTER_OTHER_LABELS.NOREVIEW;
          } else if (candidateReview === VACANCY_FILTER_OTHER_LABELS.LIKE) {
            reviewDecision = VACANCY_FILTER_OTHER_LABELS.THUMBSUP;
          } else if (candidateReview === VACANCY_FILTER_OTHER_LABELS.DISLIKE) {
            reviewDecision = VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN;
          } else {
            reviewDecision = VACANCY_FILTER_OTHER_LABELS.NOREVIEW;
          }

          // Map info_bot_response to AI Agent Status:
          let aiAgentStatusValue = "";
          if (!candidateInfoBotResponse || candidateInfoBotResponse === "") {
            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.NOT_CONTACTED_BLANK;
          } else if (
            candidateInfoBotResponse.toLowerCase()?.includes(responded)
          ) {
            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.RESPONDED;
          } else if (
            candidateInfoBotResponse.toLowerCase()?.includes(contacted)
          ) {
            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.CONTACTED;
          } else if (
            candidateInfoBotResponse.toLowerCase()?.includes(notInterested)
          ) {
            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.NOT_INTERESTED;
          } else {
            aiAgentStatusValue = candidateInfoBotResponse;
          }

          const candidateName =
            candidate?.candidate_data?.name?.toLowerCase() ?? "";
          const candidateWhyFit =
            candidate?.candidate_data?.current_fitness_reason?.reason?.toLowerCase() ??
            "";
          const candidateTotalScore =
            candidate?.candidate_data?.[
              VACANCY_FILTER_OTHER_LABELS.CLASSIFICATIONSCORE
            ]?.overallscore?.toFixed(2) ?? 0;

          const candidateAvailability =
            candidate?.candidate_data?.availability ?? "";

          // Match filters using store values
          const matchesReviewStatus =
            filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.includes(
              reviewDecision
            );

          const matchesState =
            filters?.[VACANCY_FILTER_LABELS.STATE]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.STATE]?.includes(candidateState);

          const matchesCity =
            filters?.[VACANCY_FILTER_LABELS.CITY]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.CITY]?.includes(candidateCity);

          const matchesFreshnessIndex =
            filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.includes(
              candidateFreshnessIndex
            );

          const matchesShortList =
            filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.includes(
              candidateShortListed
            );

          const matchesAiAgentStatus =
            filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.length === 0 ||
            filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.includes(
              aiAgentStatusValue
            );

          const matchesTotalScore =
            candidateTotalScore <=
              filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[1]?.toFixed(
                2
              ) &&
            candidateTotalScore >=
              filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[0]?.toFixed(
                2
              );

          const candidateDistance =
            candidate?.candidate_data?.distance_from_work_site;
          const minDistance =
            filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[0] ?? 0;
          const maxDistance =
            filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[1] ??
            distanceRange?.[1];

          const matchesDistance =
            candidateDistance === null || candidateDistance === undefined
              ? minDistance === 0 && maxDistance === distanceRange?.[1]
              : candidateDistance >= distanceRange?.[0] &&
                candidateDistance <= distanceRange?.[1];

          const matchesSearch = (() => {
            const searchText = debouncedValue.trim();
            const searchFields =
              filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS] ?? [];

            if (searchText === "") {
              // No search text entered
              if (searchFields?.length === 0) {
                showSearchFieldError = true; // show warning for missing field selection
              }
              // Don't filter in this case, so return true to include all candidates
              return true;
            } else {
              // Search text is entered
              if (searchFields?.length === 0) {
                showSearchFieldError = true; // show warning for missing field selection
                return true;
              }
              // Normal filtering when searchFields selected with non-empty search text
              const searchLower = searchText.toLowerCase();
              const byName =
                searchFields?.includes(VACANCY_FILTER_OTHER_LABELS.NAME) &&
                candidateName?.includes(searchLower);
              const byWhyFit =
                searchFields?.includes(VACANCY_FILTER_OTHER_LABELS.WHYFIT) &&
                candidateWhyFit?.includes(searchLower);

              return byName || byWhyFit;
            }
          })();

          const matchesAvailabilityDate = (() => {
            const from =
              filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from;
            const to =
              filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to;

            if (!from && !to) return true;
            if (!candidateAvailability) return false;

            function normalize(date: Date) {
              return new Date(
                date.getFullYear(),
                date.getMonth(),
                date.getDate()
              );
            }

            const candidateDate = normalize(new Date(candidateAvailability));
            const fromDate = from ? normalize(new Date(from)) : null;
            const toDate = to ? normalize(new Date(to)) : null;

            if (fromDate && toDate && fromDate > toDate) {
              return true;
            }

            // Inclusive range: candidateDate >= fromDate && candidateDate <= toDate
            if (fromDate && candidateDate < fromDate) return false;
            if (toDate && candidateDate > toDate) return false;

            return true;
          })();

          return (
            matchesState &&
            matchesCity &&
            matchesFreshnessIndex &&
            matchesShortList &&
            matchesReviewStatus &&
            matchesAiAgentStatus &&
            matchesTotalScore &&
            matchesSearch &&
            matchesAvailabilityDate &&
            matchesDistance
          );
        });
      setFilteredCandidatesList(filtered);
    } else {
      if (!filterEntitlement && selectedVacancy?.vacancy_id) {
        clearFiltersForVacancyList(selectedVacancy?.vacancy_id);
      }
      setFilteredCandidatesList(candidates);
    }
  }, [
    filterEntitlement,
    candidates,
    filters?.[VACANCY_FILTER_LABELS.STATE],
    filters?.[VACANCY_FILTER_LABELS.CITY],
    filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX],
    filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED],
    filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION],
    filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS],
    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE],
    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from,
    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to,
    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS],
    filters?.[VACANCY_FILTER_LABELS.SEARCH_BY_NAME],
    debouncedValue,
    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE],
    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE],
    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE],
  ]);

  const filterProps: iFilterSlider = {
    filterEntitlement,
    showFilter,
    isFunnelFill,
    showSearchFieldError,
    searchFields,
    searchText,
    uniqueReviewValues,
    reviewStatus,
    uniqueShortList,
    shortListed,
    uniqueLocation,
    state,
    city,
    uniqueFreshnessIndex,
    freshnessIndex,
    availabilityDateRange,
    uniqueAiAgentStatus,
    aiAgentStatus,
    totalScoreRange,
    handleResetFilter,
    handleFilterIcon,
    toggleSearchField: toggleSearchFieldLocal,
    setSearchText,
    handleCheckBoxSelection: handleCheckBoxSelectionLocal,
    setTotalScoreRange: (val) =>
      setFilterField(VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE, val),
    setAvailabilityDateRange: (
      value: React.SetStateAction<{ from: string; to: string }>
    ): void => {
      const resolvedValue =
        typeof value === "function" ? value(availabilityDateRange) : value;
      setFilterField(
        VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE,
        resolvedValue
      );
    },
    distanceRange,
    setDistanceRange: (val) =>
      setFilterField(VACANCY_FILTER_LABELS.DISTANCE_RANGE, val),
    distance,
    maxMinAvailabilityDateRange,
  };

  return (
    <NotificationProvider>
      <NotificationList />
      <div className="p-4">
        {isParsedVacancyPopupOpen && (
          <VacancyDetails
            vacancy={selectedVacancy}
            setActiveVacancy={() => {
              setActiveVacancy(null);
              setIsParsedVacancyPopupOpen(false);
            }}
            mercuryPortal={mercuryPortal ?? false}
          />
        )}
        {!vacancyid && !mercuryPortal ? (
          <h2 className="text-2xl font-bold text-center py-2">
            Catalyst Match
          </h2>
        ) : (
          selectedVacancy && (
            <>
              <div className="flex justify-center items-baseline">
                <h2 className="text-2xl font-bold text-center">
                  Catalyst Match for the Vacancy
                </h2>
                <span className="ml-2">
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      openParsedVacancyPopup();
                    }}
                    className="text-blue-600 underline hover:text-blue-800 cursor-pointer"
                  >
                    [Parsed Vacancy]
                  </a>
                </span>
              </div>
            </>
          )
        )}

        <div className="flex flex-col lg:flex-row gap-4 justify-center">
          {/* Vacancy Panel */}
          {showVacancyList && !vacancyid && !mercuryPortal && (
            <div className="w-full lg:w-[350px] p-2 lg:border-r border-b lg:border-b-0 z-30 lg:order-2 order-2">
              <h3
                className="text-lg font-semibold mb-2 cursor-pointer"
                onClick={() => handleSort()}
              >
                Vacancy List{" "}
                <ArrowUpDown className="text-black inline ml-1 h-4 w-4" />
              </h3>
              <p>
                <Input
                  type="search"
                  placeholder="Search by vacancy code"
                  value={vacancySearch}
                  onChange={(e) => setVacancySearch(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 my-2"
                />
              </p>
              {loading && !filteredVacancy.length ? (
                <Loading height="h-[50vh]" />
              ) : (
                <ul className="max-h-[66vh] overflow-y-auto">
                  {filteredVacancy?.map((vacancy) => (
                    <VacancyItem
                      vacancy={vacancy}
                      handleVacancyClick={handleVacancyClick}
                      selectedVacancy={selectedVacancy}
                      setSearch={setSearch}
                      key={vacancy.refno}
                      activeVacancy={activeVacancy}
                      setActiveVacancy={setActiveVacancy}
                    />
                  ))}
                </ul>
              )}
            </div>
          )}
          {/* Candidates Table */}
          <div className="flex-1 lg:order-3">
            {loading && !selectedVacancy ? (
              <div className="flex items-center justify-center h-[50vh]">
                <Loading height="h-[50vh]" />
              </div>
            ) : selectedVacancy && !vacancyid && filteredCandidatesList ? (
              <>
                <div className="flex justify-end mb-2 mr-1">
                  <Button
                    className="rounded"
                    onClick={handleRefreshVacancies}
                    disabled={isTableReadOnly!}
                  >
                    {showUpdate && <p>Updates Available</p>}
                    <RefreshCcw className="w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform" />
                  </Button>
                </div>
                <div
                  className={`flex items-center w-[100%] ${
                    showFilter ? "justify-between" : ""
                  }`}
                >
                  {selectedVacancy && (
                    <h3 className="text-lg font-semibold mb-2">
                      &quot;Candidates for {selectedVacancy?.refno}&quot;
                    </h3>
                  )}
                </div>
                <div className={`flex items-center w-[100%] justify-between`}>
                  <div className="flex items-center">
                    <Tabs
                      value={activeTab}
                      onValueChange={setActiveTab}
                      className="w-full"
                    >
                      <TabsList className="grid w-fit grid-cols-2">
                        <TabsTrigger value="vacancy-template">
                          Vacancy Template
                        </TabsTrigger>
                        <TabsTrigger value="match-results">
                          Match Results
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="flex justify-end"
                      id="regenerate-button-container-1"
                    >
                      {/* regenerate Recruitment*/}
                      {entitlements?.Regenerate &&
                        !loading &&
                        selectedVacancy &&
                        selectedVacancy?.vacancy_id && (
                          <RegenerateButton
                            vacancyId={selectedVacancy.vacancy_id}
                            emailId={
                              emailId ?? session?.data?.user?.email ?? ""
                            }
                            canRegenerate={canRegenerate}
                            onReloadTable={handleReloadTable}
                            setRegenerationReadOnly={setRegenerationReadOnly}
                            setIsStatusInProcessByClickOfRegenerateButton={
                              setIsStatusInProcessByClickOfRegenerateButton
                            }
                            catalystRegenerationData={catalystRegenerationData}
                            setIsLoading={setIsLoading}
                            isTableReadOnly={isTableReadOnly!}
                            mercuryPortal={mercuryPortal ?? false}
                            isClosed={selectedVacancy.closed}
                          />
                        )}
                    </div>
                  </div>
                </div>

                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="w-full"
                >
                  <TabsContent value="match-results">
                    
                  </TabsContent>
                  <TabsContent value="vacancy-template" className="mt-4">
                    <CatalystMatchForm />
                  </TabsContent>

                  <TabsContent value="match-results" className="mt-4">
                    <span className="flex justify-end">
                      {isReadOnly && (
                      <span className="bg-blue-200 px-2 py-1 mb-1 rounded-md flex items-center gap-2 mr-2 justify-end w-[220px]">
                        <LockKeyhole size={15} />
                        <h6 className="text-sm">
                          The below table is read-only
                        </h6>
                      </span>
                    )}
                    </span>
                    {filterEntitlement &&
                      showFilter &&
                      candidates?.length > 0 &&
                      filterIcon()}
                    {selectedVacancy && (
                      <CandidateTable
                        handleCandidateSort={handleCandidateSort}
                        isResumeModalOpen={isResumeModalOpen}
                        loading={loading}
                        tableLoading={tableLoader}
                        paginatedCandidates={paginatedCandidates}
                        selectedVacancy={selectedVacancy}
                        candidates={filteredCandidatesList}
                        setCandidates={setFilteredCandidatesList}
                        vacancyCandidates={vacancyCandidates}
                        setVacancyCandidates={setVacancyCandidates}
                        fetchResume={fetchResume}
                        mercuryPortal={mercuryPortal ?? false}
                        fetchCandidatesById={fetchCandidatesById}
                        emailId={emailId ?? ""}
                        isTableReadOnly={isTableReadOnly!}
                        vacancyId={vacancyid}
                        catalystRegenerationData={catalystRegenerationData}
                        filterProps={filterProps}
                      />
                    )}
                    {/* Pagination */}
                    <div className="flex justify-between items-center mt-4">
                      <Select
                        value={limit as unknown as string}
                        onValueChange={(val: string) => {
                          setLimit(Number(val));
                          setPage(1);
                        }}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Select per page-" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {[50, 100, 150, 200, 500].map((size) => (
                              <SelectItem
                                key={size}
                                value={size as unknown as string}
                              >
                                {size} per page
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <div>
                        <Button
                          variant={"outline"}
                          onClick={() => setPage((p) => Math.max(p - 1, 1))}
                          disabled={page === 1}
                          className="px-2 py-0.5 mr-1"
                        >
                          <ChevronLeft />
                        </Button>
                        <Button
                          variant={"outline"}
                          onClick={() =>
                            setPage((p) =>
                              p * limit < filteredCandidates?.length ? p + 1 : p
                            )
                          }
                          className="px-2 py-0.5"
                          disabled={
                            Math.ceil(filteredCandidates?.length / limit) ===
                            page
                          }
                        >
                          <ChevronRight />
                        </Button>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </>
            ) : selectedVacancy && vacancyid ? (
              <div className="">
                {SHOW_SEARCH_BY_EMAIL && (
                  <Input
                    type="search"
                    placeholder="Search by email"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="w-1/3 border border-gray-300 rounded-lg p-2 my-2"
                  />
                )}
                <div className="flex justify-end mb-2 mr-1">
                  <Button
                    className="rounded"
                    onClick={handleRefreshVacancies}
                    disabled={isTableReadOnly!}
                  >
                    {showUpdate && <p>Updates Available</p>}
                    <RefreshCcw className="w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform" />
                  </Button>
                </div>
                {/* regenerate mercury */}
                <div
                  className={`${"justify-end"}`}
                  id="regenerate-button-container-2"
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <Tabs
                        value={activeTab}
                        onValueChange={setActiveTab}
                        className="w-full"
                      >
                        <TabsList className="grid w-fit grid-cols-2">
                          <TabsTrigger value="vacancy-template">
                            Vacancy Template
                          </TabsTrigger>
                          <TabsTrigger value="match-results">
                            Match Results
                          </TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* regenerate */}
                      {(entitlementData?.Regenerate ||
                        entitlements?.Regenerate) &&
                        !loading &&
                        selectedVacancy &&
                        selectedVacancy.vacancy_id && (
                          <RegenerateButton
                            vacancyId={selectedVacancy?.vacancy_id}
                            emailId={
                              emailId ?? session?.data?.user?.email ?? ""
                            }
                            canRegenerate={canRegenerate}
                            onReloadTable={handleReloadTable}
                            setRegenerationReadOnly={setRegenerationReadOnly}
                            setIsStatusInProcessByClickOfRegenerateButton={
                              setIsStatusInProcessByClickOfRegenerateButton
                            }
                            catalystRegenerationData={catalystRegenerationData}
                            setIsLoading={setIsLoading}
                            isTableReadOnly={isTableReadOnly!}
                            mercuryPortal={mercuryPortal ?? false}
                            isClosed={selectedVacancy.closed}
                          />
                        )}
                    </div>
                  </div>
                  <Tabs
                    value={activeTab}
                    onValueChange={setActiveTab}
                    className="w-full"
                  >
                    <TabsContent value="match-results">
                      {canRegenerate && isMercuryReadOnly && (
                        <div className="bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <LockKeyhole size={15} />
                            <h6 className="text-sm">
                              The below table is read-only
                            </h6>
                          </div>
                          <Badge variant="outline" className="bg-blue-200">
                            Read only
                          </Badge>
                        </div>
                      )}
                    </TabsContent>
                    <TabsContent value="vacancy-template" className="mt-4">
                      <CatalystMatchForm />
                    </TabsContent>

                    <TabsContent value="match-results" className="mt-4">
                      {showMercuryFilter && (
                        <div className="mb-2">{filterIcon()}</div>
                      )}
                      {selectedVacancy && (
                        <CandidateTable
                          handleCandidateSort={handleCandidateSort}
                          isResumeModalOpen={isResumeModalOpen}
                          loading={loading}
                          tableLoading={tableLoader}
                          paginatedCandidates={paginatedCandidates}
                          selectedVacancy={selectedVacancy}
                          candidates={candidates}
                          setCandidates={setCandidates}
                          vacancyCandidates={vacancyCandidates}
                          setVacancyCandidates={setVacancyCandidates}
                          fetchResume={fetchResume}
                          mercuryPortal={mercuryPortal ?? false}
                          fetchCandidatesById={fetchCandidatesById}
                          emailId={emailId ?? ""}
                          isTableReadOnly={isTableReadOnly!}
                          vacancyId={vacancyid}
                          catalystRegenerationData={catalystRegenerationData}
                          filterProps={filterProps}
                        />
                      )}
                      {/* Pagination */}
                      <div className="flex justify-between items-center mt-4">
                        <Select
                          value={limit as unknown as string}
                          onValueChange={(val: string) => {
                            setLimit(Number(val));
                            setPage(1);
                          }}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Select per page-" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {[50, 100, 150, 200, 500].map((size) => (
                                <SelectItem
                                  key={size}
                                  value={size as unknown as string}
                                >
                                  {size} per page
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <div>
                          <Button
                            variant={"outline"}
                            onClick={() => setPage((p) => Math.max(p - 1, 1))}
                            disabled={page === 1}
                            className="px-2 py-0.5 mr-1"
                          >
                            <ChevronLeft />
                          </Button>
                          <Button
                            variant={"outline"}
                            onClick={() =>
                              setPage((p) =>
                                p * limit < filteredCandidates.length
                                  ? p + 1
                                  : p
                              )
                            }
                            className="px-2 py-0.5"
                            disabled={
                              Math.ceil(filteredCandidates.length / limit) ===
                              page
                            }
                          >
                            <ChevronRight />
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            ) : (
              (filteredCandidatesList?.length === 0 || statusCode !== 200) && (
                <div className="">
                  {SHOW_SEARCH_BY_EMAIL && (
                    <Input
                      type="search"
                      placeholder="Search by email"
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      className="w-1/3 border border-gray-300 rounded-lg p-2 my-2"
                    />
                  )}
                  <div className="flex justify-end mb-2 mr-1">
                    <Button
                      className="rounded"
                      onClick={handleRefreshVacancies}
                      disabled={isTableReadOnly!}
                    >
                      {showUpdate && <p>Updates Available</p>}
                      <RefreshCcw className="w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform" />
                    </Button>
                  </div>
                  {/* regenerate and tabs */}
                  <div className="flex items-center justify-between w-full mb-4">
                    <div className="flex items-center">
                      <Tabs
                        value={activeTab}
                        onValueChange={setActiveTab}
                        className="w-full"
                      >
                        <TabsList className="grid w-fit grid-cols-2">
                          <TabsTrigger value="vacancy-template">
                            Vacancy Template
                          </TabsTrigger>
                          <TabsTrigger value="match-results">
                            Match Results
                          </TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>
                    <div
                      className="flex justify-end"
                      id="regenerate-button-container-3"
                    >
                      {(entitlementData?.Regenerate ||
                        entitlements?.Regenerate) &&
                        !loading &&
                        vacancyid && (
                          <RegenerateButton
                            vacancyId={selectedVacancy?.vacancy_id ?? vacancyid}
                            emailId={
                              emailId ?? session?.data?.user?.email ?? ""
                            }
                            canRegenerate={canRegenerate}
                            onReloadTable={handleReloadTable}
                            setRegenerationReadOnly={setRegenerationReadOnly}
                            setIsStatusInProcessByClickOfRegenerateButton={
                              setIsStatusInProcessByClickOfRegenerateButton
                            }
                            catalystRegenerationData={catalystRegenerationData}
                            setIsLoading={setIsLoading}
                            isTableReadOnly={isTableReadOnly!}
                            mercuryPortal={mercuryPortal ?? false}
                            isClosed={selectedVacancy?.closed!}
                          />
                        )}
                    </div>
                  </div>
                  <Tabs
                    value={activeTab}
                    onValueChange={setActiveTab}
                    className="w-full"
                  >
                    <TabsContent value="match-results">
                      {canRegenerate && isMercuryReadOnly && (
                        <div className="bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <LockKeyhole size={15} />
                            <h6 className="text-sm">
                              The below table is read-only
                            </h6>
                          </div>
                          <Badge variant="outline" className="bg-blue-200">
                            Read only
                          </Badge>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="vacancy-template" className="mt-4">
                      <CatalystMatchForm />
                    </TabsContent>

                    <TabsContent value="match-results" className="mt-4">
                      <CandidateTable
                        handleCandidateSort={handleCandidateSort}
                        isResumeModalOpen={isResumeModalOpen}
                        loading={loading}
                        paginatedCandidates={paginatedCandidates}
                        selectedVacancy={selectedVacancy!}
                        candidates={filteredCandidatesList}
                        setCandidates={setFilteredCandidatesList}
                        vacancyCandidates={vacancyCandidates}
                        setVacancyCandidates={setVacancyCandidates}
                        fetchResume={fetchResume}
                        mercuryPortal={mercuryPortal ?? false}
                        fetchCandidatesById={fetchCandidatesById}
                        emailId={emailId ?? ""}
                        isTableReadOnly={isTableReadOnly!}
                        vacancyId={vacancyid}
                        catalystRegenerationData={catalystRegenerationData}
                        filterProps={filterProps}
                        tableLoading={tableLoader}
                      />
                      {/* Pagination */}
                      <div className="flex justify-between items-center mt-4">
                        <Select
                          value={limit as unknown as string}
                          onValueChange={(val: string) => {
                            setLimit(Number(val));
                            setPage(1);
                          }}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Select per page-" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {[50, 100, 150, 200, 500]?.map((size) => (
                                <SelectItem
                                  key={size}
                                  value={size as unknown as string}
                                >
                                  {size} per page
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <div>
                          <Button
                            variant={"outline"}
                            onClick={() => setPage((p) => Math.max(p - 1, 1))}
                            disabled={page === 1}
                            className="px-2 py-0.5 mr-1"
                          >
                            <ChevronLeft />
                          </Button>
                          <Button
                            variant={"outline"}
                            onClick={() =>
                              setPage((p) =>
                                p * limit < filteredCandidates?.length
                                  ? p + 1
                                  : p
                              )
                            }
                            className="px-2 py-0.5"
                            disabled={
                              Math.ceil(filteredCandidates?.length / limit) ===
                              page
                            }
                          >
                            <ChevronRight />
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              )
            )}
          </div>
        </div>
        <CandidateResume
          vacancy={selectedVacancy}
          selectedResume={selectedResume}
          setSelectedResume={setSelectedResume}
          isResumeModalOpen={isResumeModalOpen}
          setIsResumeModalOpen={setIsResumeModalOpen}
          isLoading={loading}
        />
        {vacancyid === undefined &&
          !loading &&
          pageNotFound &&
          mercuryPortal && (
            <div className="bg-white border-t p-4 h-screen flex items-center justify-center">
              <p>
                <span className="text-lg mr-1">404</span>Page not found
              </p>
            </div>
          )}
        {isLoading && (
          <div className="fixed inset-0 z-[2000] flex items-center justify-center bg-black bg-opacity-30">
            <div className="bg-transparent rounded flex items-center justify-center">
              <Loading />
            </div>
          </div>
        )}
      </div>
    </NotificationProvider>
  );
};

export default Candidates;
