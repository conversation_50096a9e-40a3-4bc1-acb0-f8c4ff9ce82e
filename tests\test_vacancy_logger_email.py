#!/usr/bin/env python3
"""
Test script to verify that the vacancy logger sends emails when errors occur.
This test uses the email_on_error configuration from the catalyst_match_config.json file.
"""

import os
import sys
from common.appLogger import getGlobalAppLogger_vacancy, AppLogger
from common.secrets_env import load_secrets_env_variables

def test_email_config_reading():
    """Test that email configuration can be read from the config file."""
    print("Testing Email Configuration Reading")
    print("=" * 50)
    
    # Test reading email config
    email_config = AppLogger.get_email_on_error_config()
    
    if email_config:
        print("✓ Email configuration found:")
        print(f"  - Enabled: {email_config.get('enabled', False)}")
        print(f"  - To emails: {email_config.get('to_emails', [])}")
        print(f"  - Subject prefix: {email_config.get('subject_prefix', 'N/A')}")
        return True
    else:
        print("⚠ No email configuration found or email notifications disabled")
        print("  Check that 'email_on_error.enabled' is set to true in config file")
        return False

def test_vacancy_logger_email():
    """Test that the vacancy logger sends emails when errors occur."""
    print("\nTesting Vacancy Logger Email Notifications")
    print("=" * 50)
    
    # Load secrets and environment variables
    try:
        load_secrets_env_variables()
        print("✓ Secrets loaded successfully")
    except Exception as e:
        print(f"⚠ Warning: Could not load secrets: {e}")
        print("Continuing with existing environment variables...")
    
    # Check prerequisites
    if not os.getenv("SENDGRID_API_KEY"):
        print("❌ SENDGRID_API_KEY environment variable not set!")
        print("Please set it: export SENDGRID_API_KEY='your_api_key'")
        return False
    
    # Get the vacancy logger
    logger = getGlobalAppLogger_vacancy()
    
    # Check if email notifications are configured
    if hasattr(logger, '_email_config') and logger._email_config.get('enabled', False):
        print("✓ Vacancy logger configured with email notifications")
        print(f"✓ Target emails: {logger._email_config.get('to_emails', [])}")
        print(f"✓ Subject prefix: {logger._email_config.get('subject_prefix', 'N/A')}")
    else:
        print("⚠ Vacancy logger not configured with email notifications")
        print("  Check the 'email_on_error' section in catalyst_match_config.json")
        return False
    
    # Test simple error logging
    print("\nCalling logger.error() - this should send an email...")
    logger.error("This is a test error message from vacancy logger")
    
    # Test error with exception
    print("\nCalling logger.error() with exception - this should send an email with traceback...")
    try:
        # Intentionally cause an exception
        result = 1 / 0
    except Exception as e:
        logger.error("Division by zero error occurred during vacancy processing", exc_info=True)
    
    print("\n✓ Errors logged. Check your email inbox!")
    print("You should receive emails with subject: 'VACANCY ERROR - vacancy'")
    
    return True

def main():
    """Main test function."""
    print("Vacancy Logger Email Notification Test")
    print("=" * 60)
    print("This test will send emails using the vacancy logger configuration")
    print("=" * 60)
    
    # Check if user wants to proceed
    response = input("\nDo you want to proceed with sending real emails? (yes/no): ").lower().strip()
    if response not in ['yes', 'y', '']:  # Empty string (just Enter) is treated as yes
        print("Test cancelled.")
        return
    
    print("\nStarting tests...")
    
    # Test email config reading
    config_success = test_email_config_reading()
    
    # Test vacancy logger email functionality
    logger_success = test_vacancy_logger_email()
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Email Config Reading: {'✓ PASSED' if config_success else '❌ FAILED'}")
    print(f"Vacancy Logger Email: {'✓ PASSED' if logger_success else '❌ FAILED'}")
    
    if config_success and logger_success:
        print("\n🎉 All tests completed! Check your email inbox.")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
    
    print("\nNote: Emails were sent using the vacancy logger configuration")
    print("If you don't receive emails, check:")
    print("1. SendGrid API key is valid")
    print("2. Email configuration in catalyst_match_config.json")
    print("3. Check spam/junk folder")
    print("4. Check SendGrid dashboard for delivery status")

if __name__ == "__main__":
    main()
