trigger:
  branches:
    include:
      - main
      - dev

parameters:
  - name: environment
    displayName: Environment
    type: string
    default: dv
    values:
      - dv

  - name: testSuite
    displayName: Test Suite
    type: string
    default: tandym_dataprocessing
    values:
      - tandym_dataprocessing

  - name: captureScreenshots
    displayName: Capture all screenshots
    type: boolean
    default: true

extends:
  template: e2e-test.yml
  parameters:
    environment: ${{ parameters.environment }}
    testSuite: ${{ parameters.testSuite }}
    captureScreenshots: ${{ parameters.captureScreenshots }}
