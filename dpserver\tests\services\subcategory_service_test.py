import pytest
from unittest.mock import Mock, MagicMock,patch
from dpserver.services.subcategory_service import SubcategoryService
from unittest.mock import MagicMock

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    mock_db = Mock()
    mock_db.schema = "public"
    mock_db.connection = Mock()
    return mock_db

@pytest.fixture
def service(mock_logger, mock_db_connector):
    return SubcategoryService(mock_logger, mock_db_connector)
    
def test_get_subcategories_success(service, mock_db_connector, mock_logger):
    # Arrange
    mock_cursor = MagicMock()
    mock_cursor.fetchall.return_value = [
        (1, "Subcat1", 10, "Cat1", 5),
        (2, "Subcat2", 20, "Cat2", 7)
    ]
    mock_conn = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

    service._get_active_db_connection = MagicMock(return_value=mock_conn)

    # Act
    result = service.get_subcategories()

    # Assert
    assert "subcategories" in result
    assert len(result["subcategories"]) == 2
    
    
def test_get_subcategories_empty(service, mock_db_connector):
    # Arrange
    mock_cursor = MagicMock()
    mock_cursor.fetchall.return_value = []
    mock_cursor.cursor.return_value.__enter__.return_value = mock_cursor    

    service._get_active_db_connection = MagicMock(return_value=mock_cursor)

    # Act
    result = service.get_subcategories()

    # Assert
    assert result == {"subcategories": []}    
    
    
def test_get_subcategories_exception(service, mock_db_connector, mock_logger):
    # Arrange
    mock_cursor = MagicMock()
    mock_cursor.execute.side_effect = Exception("DB error")
    mock_cursor.cursor.return_value.__enter__.return_value = mock_cursor    

    service._get_active_db_connection = MagicMock(return_value=mock_cursor)

    # Act
    result = service.get_subcategories()

    # Assert
    assert result == {"error": "Error fetching subcategories"}
    mock_logger.error.assert_called()
    
    
def test_get_pool_data_success(service):
    # Arrange
    pool_data = [{"id": 1, "count": 5}]
    with patch("dpserver.services.subcategory_service.SubcategoryPoolService") as MockPoolService:
        instance = MockPoolService.return_value
        instance.get_new_pool_data.return_value = pool_data

        # Act
        result = service.get_pool_data()

        # Assert
        assert result == pool_data
        instance.get_new_pool_data.assert_called_once()

def test_get_pool_data_service_error(service):
    # Arrange
    error_result = {"error": "Some error"}
    with patch("dpserver.services.subcategory_service.SubcategoryPoolService") as MockPoolService:
        instance = MockPoolService.return_value
        instance.get_new_pool_data.return_value = error_result

        # Act
        result = service.get_pool_data()

        # Assert
        assert result == error_result
        instance.get_new_pool_data.assert_called_once()

def test_get_pool_data_exception(service, mock_logger):
    # Arrange
    with patch("dpserver.services.subcategory_service.SubcategoryPoolService") as MockPoolService:
        instance = MockPoolService.return_value
        instance.get_new_pool_data.side_effect = Exception("DB error")

        # Act
        result = service.get_pool_data()

        # Assert
        assert result == []
        mock_logger.error.assert_called()

def test_get_all_subcategory_weight_configs_success(service, mock_db_connector, mock_logger):
    # Arrange
    mock_cursor = MagicMock()
    mock_conn = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

    # Set return values
    mock_cursor.fetchall.return_value = [
        (
            1, 10, "SubcatA", 100, 10, 20, 30, 40, 50, 60, 70, 80, 90, "{}", "2024-01-01", "2024-01-02", "user1", False
        ),
        (
            2, 20, "SubcatB", 200, 20, 30, 40, 50, 60, 70, 80, 90, 100, "{}", "2024-02-01", "2024-02-02", "user2", False
        )
    ]

    # Patch the active connection
    service._get_active_db_connection = MagicMock(return_value=mock_conn)

    # Act
    result = service.get_all_subcategory_weight_configs()

    # Assert
    assert "subcategory_weight_configs" in result
    assert len(result["subcategory_weight_configs"]) == 2
    mock_logger.info.assert_called_once_with("Fetching all subcategory weight configurations.")
    mock_cursor.execute.assert_called_once()
    mock_conn.close.assert_called_once()


def test_get_all_subcategory_weight_configs_empty(service, mock_db_connector,mock_logger):
    # Arrange
    service.logger = mock_logger
    mock_cursor = Mock()
    mock_conn = MagicMock()
    mock_db_connector.connection.cursor.return_value = mock_cursor
    mock_cursor.fetchall.return_value = []

    # Patch the active connection
    service._get_active_db_connection = MagicMock(return_value=mock_conn)

    # Act
    result = service.get_all_subcategory_weight_configs()

    # Assert
    mock_logger.error.assert_not_called()
    assert result == {"subcategory_weight_configs": []}


def test_get_all_subcategory_weight_configs_exception(service, mock_db_connector, mock_logger):
    # Arrange
    service.logger = mock_logger
    mock_conn = MagicMock()
    mock_conn.cursor.side_effect = Exception("DB error")

    # Patch the active connection
    service._get_active_db_connection = MagicMock(return_value=mock_conn)

    # Act
    result = service.get_all_subcategory_weight_configs()

    # Assert
    assert result == {"error": "Error fetching subcategory weight configurations"}
    mock_logger.error.assert_called_once_with("Error fetching subcategory weight configurations: DB error")
    mock_conn.close.assert_called_once()
    
def test_update_subcategory_weight_config_success(service, mock_db_connector, mock_logger):
    # Arrange
    subcategory_id = 1
    updates = {"job_title_score_weight": 0.2, "soft_skills_score_weight": 0.3}
    service.logger = mock_logger

    # Mock the connection and cursor
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_cursor.fetchone.return_value = [0.0] * 9 + [{}]  # Simulate all weights and params field

    # Setup the cursor context manager
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_db_connector.connection = mock_conn
    service._get_active_db_connection = lambda: mock_conn  # Override DB connector method

    # Act
    result = service.update_subcategory_weight_config(subcategory_id, updates, updated_by="user1")

    # Assert
    assert result == {"success": True}
    mock_logger.info.assert_any_call(f"Updating subcategory_weight_config for subcategory_id: {subcategory_id}")
    mock_logger.info.assert_any_call("subcategory_weight_config updated successfully.")
    mock_conn.commit.assert_called_once()
    mock_conn.close.assert_called_once()    
    
def test_update_subcategory_weight_config_missing_subcategory_id(service):
    # Arrange
    mock_logger = Mock()
    service.logger = mock_logger

    # Act
    result = service.update_subcategory_weight_config(None, {"job_title_score_weight": 0.2})

    # Assert
    assert result == {"error": "subcategory_id is required."}
    
    
def test_update_subcategory_weight_config_not_found(service, mock_db_connector, mock_logger):
    # Arrange
    subcategory_id = 2
    updates = {"job_title_score_weight": 0.2}
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
 
    #mock_db_connector.connection.cursor.return_value = mock_cursor
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_cursor.fetchone.return_value = None
    
    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger
    
    # Act
    result = service.update_subcategory_weight_config(subcategory_id, updates)

    # Assert
    assert result == {"error": f"No weight config found for subcategory_id {subcategory_id}"}
    mock_logger.error.assert_not_called()
    mock_conn.close.assert_called_once()
    

def test_update_subcategory_weight_config_sum_exceeds_one(service, mock_db_connector, mock_logger):
    # Arrange
    subcategory_id = 3
    updates = {"job_title_score_weight": 0.6, "soft_skills_score_weight": 0.6}
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    # Existing values (all 0 except params)
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_cursor.fetchone.return_value = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, {}]
    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger

    # Act
    result = service.update_subcategory_weight_config(subcategory_id, updates)

    # Assert
    assert "error" in result
    assert "Sum of score weights exceeds 100%" in result["error"]
    mock_logger.error.assert_called()
    mock_conn.close.assert_called_once()

    
def test_update_subcategory_weight_config_exception(service, mock_db_connector, mock_logger):
    # Arrange
    subcategory_id = 4
    updates = {"job_title_score_weight": 0.2}
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_cursor.fetchone.side_effect = Exception("DB error")


    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger

    # Act
    result = service.update_subcategory_weight_config(subcategory_id, updates)

    # Assert
    assert result == {"error": "Failed to update subcategory weight config"}
    mock_logger.error.assert_called()
    mock_conn.rollback.assert_called_once()
        
#Method: - get_weights_for_subcategory 
def test_get_weights_for_subcategory_success(service, mock_logger, mock_db_connector):
    category_id = 1
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

    mock_cursor.fetchall.return_value = [
        (10, 1, 100, "high", 0.8),
        (11, 1, 101, "low", 0.2)
    ]

    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger
    service.schema = "test_schema"

    result = service.get_weights_for_subcategory(category_id)

    assert result == {
        "weights": [
            {"id": 10, "subcategory_id": 1, "attribute_type_id": 100, "weight_level": "high", "weight_value": 0.8},
            {"id": 11, "subcategory_id": 1, "attribute_type_id": 101, "weight_level": "low", "weight_value": 0.2}
        ]
    }

    mock_logger.info.assert_called_with("Fetching all weights.")
    mock_cursor.execute.assert_called_once()
    query_called, params_called = mock_cursor.execute.call_args[0]
    assert "SELECT * from test_schema.weights WHERE subcategory_id = %s" in query_called
    assert params_called == (category_id,)
    assert mock_conn.close.called

def test_get_weights_for_subcategory_empty(service, mock_logger, mock_db_connector):
    category_id = 2
    mock_conn = mock_db_connector.connection
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger

    result = service.get_weights_for_subcategory(category_id)

    assert result == {"weights": []}
    mock_logger.info.assert_called_with("Fetching all weights.")
    assert mock_conn.close.called

def test_get_weights_for_subcategory_exception(service, mock_logger, mock_db_connector):
    category_id = 3
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    mock_cursor.execute.side_effect = Exception("DB error")
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

    service._get_active_db_connection = lambda: mock_conn
    service.logger = mock_logger

    result = service.get_weights_for_subcategory(category_id)

    assert result == {"error": "Error fetching weights"}
    mock_logger.error.assert_called_once()
    assert mock_conn.close.called
