# Web Framework and API
fastapi==0.115.8
uvicorn==0.34.0
pydantic>=2.7.0
pydantic-settings>=2.0.0
python-multipart>=0.0.5
requests>=2.31.0

# Azure and Cloud Services
azure-identity>=1.15.0
azure-appconfiguration>=1.5.0
azure-keyvault-secrets>=4.8.0
azure-servicebus>=7.10.0
msal>=1.32.0
msal-extensions>=1.3.0

# Database and Data Processing
pandas>=2.1.0
numpy>=1.24.0
pgvector==0.4.1
psycopg2-binary==2.9.10
pyodbc>=5.2.0

# Machine Learning and NLP
openai>=1.1.0
nltk>=3.8.1
scikit-learn>=1.3.0
beautifulsoup4>=4.12.0

# Monitoring and Telemetry
prometheus-client>=0.16.0
opentelemetry-api>=1.24.0
opentelemetry-sdk>=1.24.0
opentelemetry-instrumentation>=0.46b0
opentelemetry-instrumentation-requests>=0.46b0
opentelemetry-instrumentation-sqlalchemy>=0.46b0
opentelemetry-instrumentation-fastapi>=0.46b0
opentelemetry-instrumentation-asgi>=0.46b0
opentelemetry-exporter-otlp>=1.24.0
opentelemetry-exporter-otlp-proto-http>=1.24.0
opentelemetry-exporter-otlp-proto-grpc>=1.24.0
opentelemetry-semantic-conventions>=0.46b0
importlib-metadata>=7.0.0  # Required to replace pkg_resources

# Utilities
python-dotenv>=0.19.0
phonenumbers>=8.12.0
tenacity==9.1.2
pyyaml>=6.0.1
sendgrid>=6.12.0

# Note: For local development, install the local package separately:
# pip install -e ../

# Add any other dependencies from the main project that are needed 