import json
import csv
from collections import defaultdict
import mysql.connector
from mysql.connector import Error

input_file = '/mnt/incoming/generated/reclassify_results_090-new-1.json'
output_file = '/mnt/incoming/generated/reclassify_results_090-new-1_summary.csv'

# Database connection configuration
db_config = {
    'host': 'your_host',
    'user': 'your_user',
    'password': 'your_password',
    'database': 'your_database'
}

try:
    # Establish database connection
    connection = mysql.connector.connect(**db_config)
    cursor = connection.cursor()

    def classify_score(score):
        score = int(score)
        if score <= 7:
            return "high"
        elif score == 15:
            return "medium"
        elif score == 16 or score == 17:
            return "low"
        elif 8 <= score <= 14:
            return "other"
        else:
            return "unknown"

    # Dictionary: sub_cat -> {'high': X, 'medium': Y, 'low': Z, 'other': W}
    subcat_counts = defaultdict(lambda: {'high': 0, 'medium': 0, 'low': 0, 'other': 0})

    with open(input_file, 'r') as f:
        data = json.load(f)

    for entry in data.values():
        sub_cats = entry.get("sub_cats", [])
        scores = entry.get("scores", [])
        for sub_cat, score in zip(sub_cats, scores):
            label = classify_score(score)
            if label in subcat_counts[sub_cat]:
                subcat_counts[sub_cat][label] += 1

    # Write to CSV
    #Write to CSV in sorted order of sub_cat
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['sub_cat', 'high', 'medium', 'low', 'other'])
        for sub_cat in sorted(subcat_counts.keys()):
            counts = subcat_counts[sub_cat]
            writer.writerow([
                sub_cat,
                counts['high'],
                counts['medium'],
                counts['low'],
                counts['other']
            ])

    print(f"Summary written to {output_file}")

    # ... rest of the code ...

    query = f"""SELECT COUNT(*) as total_count 
    FROM contact 
    WHERE statecode = 0 
    AND recruit_iscandidatecontact = 1 
    AND (recruit_workpref_contractor = 1 OR recruit_workpref_temp = 1) 
    AND (recruit_cvurl IS NOT NULL OR recruit_cvurl <> '') 
    AND recruit_mobilehome IS NOT NULL
    AND (
        -- Numbers that don't match US format
        (recruit_mobilehome NOT LIKE '+1%' AND LEN(recruit_mobilehome) != 10)
        OR 
        -- Numbers with +1 but wrong length or format
        (recruit_mobilehome LIKE '+1%' AND (
            LEN(recruit_mobilehome) != 12 
            OR SUBSTRING(recruit_mobilehome, 2, 1) NOT BETWEEN '2' AND '9'
        ))
    )"""

    print("Querying for non-USA phone numbers...")
    cursor.execute(query)
    result = cursor.fetchone()
    total_count = result[0] if result else 0
    print(f"Total contacts with non-USA phone numbers: {total_count}")

except Error as e:
    print(f"Error connecting to database: {e}")
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        print("Database connection closed.")