from multiprocessing import context
from behave import given, when, then
from selenium import webdriver
from features.pages.recruiterLogin_page import Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, open_recruiter_portal, navigated_to_portal
import sys
import os
from features.pages.recruiterLogin_page import generate_uuid_step
from features.pages.recruiterLogin_page import key_encrypts
from features.pages.recruiterLogin_page import insert_token_in_db

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

@when("user generates a unique UUID as the regression key")
def step_user_generates_uuid(context):
   generate_uuid_step(context)

@when("encrypts the key using the cryptographic library")
def step_encrypts_key(context):
    key_encrypts(context)

@when(u'inserts the encrypted key into the database with a 30-minute expiry and is_key_allowed set to true')
def step_insert_encrypted_key(context):
    insert_token_in_db(context)

@when(u'opens the recruiter portal URL with the regression_key as a query parameter')
def step_open_recruiter_portal(context):
    open_recruiter_portal(context)

@then(u'the user should be navigated to the portal without AD authentication')
def navigated_to_portal_without_ad_authentication(context):
   navigated_to_portal(context)
