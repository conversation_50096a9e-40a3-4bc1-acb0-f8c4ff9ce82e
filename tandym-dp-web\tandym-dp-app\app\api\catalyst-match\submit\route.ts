import { NextRequest } from "next/server";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://0.0.0.0:8005";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    const response = await fetch(`${BASE_URL}/catalyst-match/submit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    if (!response.ok) {
      return Response.json(
        { error: data.detail || "Failed to submit form" },
        { status: response.status }
      );
    }

    return Response.json(data);
  } catch (error) {
    console.error("Error submitting CatalystMatch form:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 