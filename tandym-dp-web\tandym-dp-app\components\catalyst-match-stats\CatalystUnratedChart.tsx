"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BarChart3,
  Target,
  Bookmark,
  Building2,
  Hash,
  PieChart,
} from "lucide-react";
import {
  CatalystMatchResult,
  formatNumber,
} from "@/app/stats/catalystmatch/helper";
import {
  Too<PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CatalystUnratedChartProps {
  unratedResults: CatalystMatchResult[];
}

type ChartViewMode = "summary" | "stacked";

const CatalystUnratedChart: React.FC<CatalystUnratedChartProps> = ({
  unratedResults,
}) => {
  const [viewMode, setViewMode] = useState<ChartViewMode>("summary");
  if (!unratedResults || unratedResults.length === 0) {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Unrated Vacancies
          </CardTitle>
          <div className="text-sm text-gray-500">
            No unrated data available for the selected time range
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-12 text-gray-500">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No unrated data available
              </h3>
              <p className="text-sm text-gray-600">
                No unrated vacancies were found for the selected time range.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group unrated results by GTM and calculate totals
  const unratedGtmData = unratedResults.reduce(
    (acc, result) => {
      if (!acc[result.category_name]) {
        acc[result.category_name] = {
          total: 0,
          total_shortlisted: 0,
          subcategories: [],
        };
      }
      acc[result.category_name].total += result.total_found;
      acc[result.category_name].total_shortlisted +=
        Number(result.shortlisted) || 0;

      // Check if subcategory already exists
      const existingSubcategory = acc[result.category_name].subcategories.find(
        (sub) => sub.name === result.subcategory_name
      );

      if (existingSubcategory) {
        // Add to existing subcategory count
        existingSubcategory.count += result.total_found;
        existingSubcategory.shortlisted += Number(result.shortlisted) || 0;
      } else {
        // Add new subcategory
        acc[result.category_name].subcategories.push({
          name: result.subcategory_name,
          count: result.total_found,
          shortlisted: Number(result.shortlisted) || 0,
          percentage: 0,
        });
      }
      return acc;
    },
    {} as Record<
      string,
      {
        total: number;
        total_shortlisted: number;
        subcategories: Array<{
          name: string;
          count: number;
          shortlisted: number;
          percentage: number;
        }>;
      }
    >
  );

  // Calculate percentages and sort by total
  const sortedUnratedGtmData = Object.entries(unratedGtmData)
    .map(([gtm, data]) => ({
      gtm,
      total: data.total,
      total_shortlisted: data.total_shortlisted,
      subcategories: data.subcategories.map((sub) => ({
        ...sub,
        percentage: data.total > 0 ? (sub.count / data.total) * 100 : 0,
      })),
    }))
    .sort((a, b) => b.total - a.total)
    .filter((item) => item.total > 0);

  // Color-blind friendly color palette (same as aggregated chart)
  const colorPalette = [
    "#3b82f6", // Blue
    "#10b981", // Green
    "#f59e0b", // Amber
    "#ef4444", // Red
    "#8b5cf6", // Purple
    "#06b6d4", // Cyan
    "#84cc16", // Lime
    "#f97316", // Orange
    "#ec4899", // Pink
    "#6366f1", // Indigo
  ];

  const renderStackedHistogram = () => {
    const maxUnrated = Math.max(
      ...sortedUnratedGtmData.map((item) => item.total)
    );

    return (
      <div className="space-y-4">
        {/* Stacked Histogram */}
        <div className="space-y-4">
          {sortedUnratedGtmData.map((item, itemIndex) => (
            <div key={item.gtm} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-sm border border-gray-300"
                    style={{
                      backgroundColor:
                        colorPalette[itemIndex % colorPalette.length],
                    }}
                  ></div>
                  <span className="font-medium">{item.gtm}</span>
                </div>
                <span className="text-gray-600 font-semibold">
                  {formatNumber(item.total)} unrated
                </span>
              </div>

              <div className="relative h-10 bg-gray-100 rounded-md overflow-hidden border border-gray-200">
                {item.subcategories.map((subcategory, subIndex) => {
                  const width =
                    maxUnrated > 0 ? (subcategory.count / maxUnrated) * 100 : 0;
                  const left = item.subcategories
                    .slice(0, subIndex)
                    .reduce(
                      (acc, sub) =>
                        acc +
                        (maxUnrated > 0 ? (sub.count / maxUnrated) * 100 : 0),
                      0
                    );

                  // Add diagonal stripes pattern for better distinction
                  const stripePattern =
                    subIndex % 2 === 0
                      ? "repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,0.3) 2px, rgba(255,255,255,0.3) 4px)"
                      : "none";

                  return (
                    <TooltipProvider key={subcategory.name}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className="absolute h-full transition-all duration-300 hover:opacity-90 cursor-pointer border-r border-white/50"
                            style={{
                              left: `${left}%`,
                              width: `${width}%`,
                              backgroundColor:
                                colorPalette[itemIndex % colorPalette.length],
                              opacity: 0.85 + subIndex * 0.05, // Vary opacity for subcategories
                              backgroundImage: stripePattern,
                            }}
                          />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-sm border border-gray-300"
                                style={{
                                  backgroundColor:
                                    colorPalette[
                                      itemIndex % colorPalette.length
                                    ],
                                }}
                              ></div>
                              <p className="font-semibold">{item.gtm}</p>
                            </div>
                            <p className="font-medium text-blue-600">
                              {subcategory.name}
                            </p>
                            <div className="text-sm space-y-1">
                              <div className="flex justify-between">
                                <span>Unrated:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.count)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Shortlisted:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.shortlisted)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Percentage:</span>
                                <span className="font-semibold">
                                  {(
                                    (subcategory.count / item.total) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </span>
                              </div>
                            </div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Compact Legend */}
        <div className="bg-gray-50 p-2 rounded-md">
          <div className="flex flex-wrap gap-3 text-xs">
            {sortedUnratedGtmData.map((item, itemIndex) => (
              <div key={item.gtm} className="flex items-center gap-1">
                <div
                  className="w-2 h-2 rounded-sm border border-gray-300"
                  style={{
                    backgroundColor:
                      colorPalette[itemIndex % colorPalette.length],
                  }}
                ></div>
                <span className="text-gray-600 font-medium">{item.gtm}</span>
                <span className="text-gray-500">
                  ({formatNumber(item.total)})
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Calculate overall statistics
  const totalUnrated = sortedUnratedGtmData.reduce(
    (sum, item) => sum + item.total,
    0
  );
  const totalShortlisted = sortedUnratedGtmData.reduce(
    (sum, item) => sum + item.total_shortlisted,
    0
  );

  return (
    <div className="space-y-4">
      {/* View Mode Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "summary" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("summary")}
            className="flex items-center gap-2"
          >
            <PieChart className="h-4 w-4" />
            Summary
          </Button>
          <Button
            variant={viewMode === "stacked" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("stacked")}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Stacked
          </Button>
        </div>
        <div className="text-sm text-gray-500">
          {sortedUnratedGtmData.length} categories with{" "}
          {formatNumber(totalUnrated)} total unrated matches
        </div>
      </div>

      {/* Color Legend */}
      <div className="text-sm text-gray-600 font-medium">
        <span className="text-blue-600 font-bold">●</span> Unrated Matches •{" "}
        <span className="text-purple-600 font-bold">●</span> Shortlisted
      </div>

      {/* Content */}
      <div>
        {viewMode === "summary" && (
          <div className="space-y-6">
            {/* Summary Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <Target className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-semibold text-blue-900">
                    {formatNumber(totalUnrated)}
                  </div>
                  <div className="text-sm text-blue-600">Total Unrated</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                <Bookmark className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="font-semibold text-purple-900">
                    {formatNumber(totalShortlisted)}
                  </div>
                  <div className="text-sm text-purple-600">
                    Total Shortlisted
                  </div>
                </div>
              </div>
            </div>

            {/* Category Breakdown */}
            <div className="space-y-4">
              {sortedUnratedGtmData.map((item, index) => (
                <div
                  key={`${item.gtm}-${index}`}
                  className="space-y-3 p-4 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-600" />
                        <span className="text-sm font-bold text-gray-700">
                          {item.gtm}
                        </span>
                        <span className="text-sm text-gray-500">
                          ({formatNumber(item.total)} unrated)
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 space-y-1">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Target className="h-3 w-3 text-blue-600" />
                            {formatNumber(item.total)} unrated
                          </span>
                          <span className="flex items-center gap-1">
                            <Bookmark className="h-3 w-3 text-purple-600" />
                            {formatNumber(item.total_shortlisted)} shortlisted
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Subcategory Breakdown */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-700 mb-2">
                      Subcategory Distribution:
                    </div>
                    <div className="relative h-8 bg-white rounded-md overflow-hidden border border-gray-200">
                      {item.subcategories.map((sub, subIndex) => (
                        <div
                          key={sub.name}
                          className="absolute h-full flex items-center justify-center"
                          style={{
                            left: `${item.subcategories
                              .slice(0, subIndex)
                              .reduce((sum, s) => sum + s.percentage, 0)}%`,
                            width: `${sub.percentage}%`,
                            backgroundColor:
                              colorPalette[subIndex % colorPalette.length],
                          }}
                          title={`${sub.name}: ${formatNumber(
                            sub.count
                          )} unrated (${sub.percentage.toFixed(1)}%)`}
                        >
                          {sub.percentage > 8 && (
                            <span className="text-xs font-medium text-white px-2 truncate">
                              {sub.name}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Subcategory Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-3">
                      {item.subcategories.map((sub, subIndex) => (
                        <div
                          key={sub.name}
                          className="flex items-center justify-between p-2 bg-white rounded border border-gray-200"
                          style={{
                            borderLeft: `4px solid ${
                              colorPalette[subIndex % colorPalette.length]
                            }`,
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <Hash className="h-3 w-3 text-gray-500" />
                            <span className="text-xs font-medium text-gray-700">
                              {sub.name}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600">
                            {formatNumber(sub.count)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {viewMode === "stacked" && (
          <div className="space-y-4">{renderStackedHistogram()}</div>
        )}
      </div>
    </div>
  );
};

export default CatalystUnratedChart;
