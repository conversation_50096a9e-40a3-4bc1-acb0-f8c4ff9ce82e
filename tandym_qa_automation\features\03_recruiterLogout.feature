# @logoutFeature
# Feature: Validate the Logout functionality of the recruiter portal
 
#      @tc:4781
#      @automation @recruiterPortal
# Scenario: Validate the logout functionality
#     When user clicks on the user profile icon
#     And user clicks on the Logout button

# @tc:4401 @automation @recruiterPortal
# Scenario: Validate that is_active is set to false after logout
#  When user should set is_active to false for the regression key in the database
