#!/usr/bin/env python3
"""
Contact ID Email Processor

Processes emails from ContactId_Queue folder by:
1. Fetching emails from contactid_queue folder
2. Extracting sender email addresses  
3. Looking up Contact IDs in Dataverse
4. Running resume parsing if Contact ID exists
5. Moving emails to appropriate folders based on results
6. Comprehensive logging of all actions
"""

import os
import sys
import json
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Tuple

# Optional import for SharePoint authentication handled in get_sharepoint_token()

# Add parent directory to path to import common modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from common.secrets_env import load_secrets_env_variables
from common.appLogger import AppLogger
from azure_client import AzureEmailFetcher

# Import dataverse helper modules
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_common import read_fields_from_dataverse

# Import resume parsing function
from generator.parse_resume_to_db import parse_resume_and_add_to_db

# Import SharePoint file functions
from generator.get_files_from_sharepoint import get_file_metadata_from_sharepoint

# Configuration constants
CONTACTID_QUEUE_FOLDER = 'ContactId_Queue'
CAT_PROCESSED_FOLDER = 'Cat_Processed'
CAT_ERRORED_FOLDER = 'Cat_Errored'
MAILBOX = '<EMAIL>'
EMAIL_AGE_THRESHOLD_DAYS = 10  # Move emails older than this to error folder if no contact found

# Default environment for Dataverse operations
DEFAULT_DATAVERSE_ENV = Environment.PROD


class ContactIdProcessor:
    """Handles Contact ID lookup and email processing workflow"""
    
    def __init__(self, logger):
        self.logger = logger
        self.email_fetcher = AzureEmailFetcher(logger=logger)
        
        # Initialize Dataverse credentials and token once
        try:
            self.credentials = get_dataverse_credentials_for_env(DEFAULT_DATAVERSE_ENV)
            self.token = get_token_for_env(DEFAULT_DATAVERSE_ENV)
            self.dataverse_url = self.credentials["RESOURCE_URL"]
            
            if not self.token:
                raise Exception(f"Failed to get Dataverse token for environment: {DEFAULT_DATAVERSE_ENV}")
            
            # Log credentials status for debugging
            status = self.get_credentials_status()
            self.logger.debug(f"Credentials Status: {status}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Dataverse credentials: {e}")
            raise
    

    
    def get_credentials_status(self) -> Dict[str, str]:
        """
        Get the current status of credentials and token for debugging
        
        Returns:
            Dictionary with credential status information
        """
        return {
            'dataverse_url': self.dataverse_url if hasattr(self, 'dataverse_url') else 'Not initialized',
            'credentials_initialized': 'Yes' if hasattr(self, 'credentials') and self.credentials else 'No',
            'token_manager_available': 'Yes' if hasattr(self, 'token') and self.token else 'No',
            'environment': DEFAULT_DATAVERSE_ENV.name if hasattr(DEFAULT_DATAVERSE_ENV, 'name') else str(DEFAULT_DATAVERSE_ENV)
        }
        
    def fetch_queue_emails(self, count: int = 50, sender_filter: Optional[List[str]] = None) -> List[Dict]:
        """
        Fetch emails from ContactId_Queue folder sorted by most recent (latest first)
        
        Args:
            count: Number of emails to fetch
            sender_filter: List of sender email addresses to filter by (optional)
            
        Returns:
            List of email dictionaries ordered by receivedDateTime desc (latest first)
        """
        try:
            
            # Find the ContactId_Queue folder
            queue_folder = self.email_fetcher.find_folder_by_name(MAILBOX, CONTACTID_QUEUE_FOLDER)
            if not queue_folder:
                self.logger.warning(f" {CONTACTID_QUEUE_FOLDER} folder not found")
                return []
            
            queue_folder_id = queue_folder['id']
            # self.logger.debug(f" Found {CONTACTID_QUEUE_FOLDER} folder ID: {queue_folder_id}")
            
            # Fetch emails from the queue folder with optional sender filtering
            emails = self.email_fetcher.fetch_emails(
                mailbox=MAILBOX,
                count=count,
                folder_id=queue_folder_id,
                sender_filter=sender_filter
            )
            
            return emails
            
        except Exception as e:
            self.logger.error(f" Failed to fetch emails from {CONTACTID_QUEUE_FOLDER}: {e}")
            return []
    
    def extract_sender_email(self, email: Dict) -> str:
        """
        Extract sender email address from email object
        
        Args:
            email: Email dictionary
            
        Returns:
            Sender email address or empty string if not found
        """
        try:
            from_info = email.get('from', {})
            sender_email = from_info.get('emailAddress', {}).get('address', '')
            return sender_email.lower().strip() if sender_email else ''
        except Exception as e:
            self.logger.error(f" Failed to extract sender email from email {email.get('id', 'Unknown')}: {e}")
            return ''
    
    def lookup_contact_id_in_dataverse(self, sender_email: str, email_received_datetime: Optional[str] = None) -> Optional[Dict[str, str]]:
        """
        Query Dataverse system to check if Contact ID exists for email.
        If multiple contacts found with same email, returns the latest updated one.
        
        Args:
            sender_email: Email address to look up
            email_received_datetime: ISO datetime string when the email was received
            
        Returns:
            Dictionary with contact_id, resume_url, created_on, modified_on if found and created before email, None otherwise
        """
        try:
            self.logger.debug(f" Looking up Contact ID for email: {sender_email}")
            
            # Fields to retrieve from contact entity
            fields = [
                'contactid',           # Contact ID we need
                'emailaddress2',       # Email field to match against  
                'createdon',           # Creation date to compare with email received date
                'modifiedon',          # Last modified date to get the latest updated contact
                'recruit_cvurl',       # Resume URL to fetch if needed
                'statecode',           # Status (active/inactive)
                'recruit_iscandidatecontact'  # Whether this is a candidate contact
            ]
            
            # Build where clause following the pattern from dv_contact.py
            where_clause = f"(statecode eq 0) and (recruit_iscandidatecontact eq true) and (emailaddress2 eq '{sender_email}')"
            
            # Use the existing read_fields_from_dataverse function
            response = read_fields_from_dataverse(
                token=self.token,
                dataverse_url=self.dataverse_url,
                table_name="contact",
                fields=fields,
                whereClause=where_clause,
                logger=self.logger
            )
            
            if response is None or len(response.get('value', [])) == 0:
                self.logger.info(f" No Contact ID found for email: {sender_email}")
                return None
            
            contacts = response.get('value', [])
            
            # Sort contacts by modifiedon desc to get the latest updated contact first
            try:
                contacts.sort(key=lambda x: x.get('modifiedon', ''), reverse=True)
            except Exception as sort_error:
                self.logger.debug(f"Failed to sort contacts by modifiedon: {sort_error}")
                # Continue with original order if sorting fails
            
            # Log if multiple contacts found
            if len(contacts) > 1:
                self.logger.info(f" Found {len(contacts)} contacts for email {sender_email}, selecting latest updated")
                for i, contact in enumerate(contacts):
                    modified_on = contact.get('modifiedon', 'Unknown')
                    created_on = contact.get('createdon', 'Unknown')
                    contact_id = contact.get('contactid', 'Unknown')
                    if i == 0:
                        self.logger.info(f"   ✅ Contact {i+1} (SELECTED): ID={contact_id[:20]}..., Modified={modified_on}, Created={created_on}")
                    else:
                        self.logger.info(f"   Contact {i+1}: ID={contact_id[:20]}..., Modified={modified_on}, Created={created_on}")
            
            # Get the latest updated contact (first in sorted results)
            contact = contacts[0]
            contact_id = contact.get('contactid')
            created_on = contact.get('createdon')
            modified_on = contact.get('modifiedon')
            resume_url = contact.get('recruit_cvurl', '')
            
            if not contact_id:
                self.logger.warning(f" Contact found but no contactid field for email: {sender_email}")
                return None
            
            self.logger.info(f" Contact ID found for {sender_email} - Contact ID: {contact_id}")
            
            # Check if we need to compare creation date with email received date
            if email_received_datetime and created_on:
                try:
                    # Parse creation date (Dataverse format: 2024-12-10T15:30:00Z)
                    contact_created = datetime.fromisoformat(created_on.replace('Z', '+00:00'))
                    
                    # Parse email received date
                    email_received = datetime.fromisoformat(email_received_datetime.replace('Z', '+00:00'))
                    
                    if contact_created > email_received:
                        self.logger.info(f"Contact created AFTER email received - Contact: {contact_created}, Email: {email_received}")
                    else:
                        self.logger.info(f"Contact created BEFORE email received - Contact: {contact_created}, Email: {email_received}")
                        
                        # Get actual file modification time from SharePoint
                        file_metadata = None
                        if resume_url:
                            file_metadata = self.get_resume_file_metadata(resume_url)
                        else:
                            self.logger.info(f"No resume URL found for contact {contact_id}")
                        
                        if file_metadata and file_metadata.get('lastModifiedDateTime'):
                            try:
                                # Handle SharePoint datetime objects and string formats
                                modified_value = file_metadata['lastModifiedDateTime']
                                
                                if isinstance(modified_value, datetime):
                                    # Already a datetime object from SharePoint
                                    from datetime import timezone
                                    if modified_value.tzinfo is None:
                                        # Make timezone-aware if needed
                                        file_modified = modified_value.replace(tzinfo=timezone.utc)
                                    else:
                                        file_modified = modified_value
                                elif isinstance(modified_value, str):
                                    # String format - handle various timezone formats
                                    if 'Z' in modified_value or '+' in modified_value:
                                        # SharePoint/ISO date with timezone info
                                        file_modified = datetime.fromisoformat(modified_value.replace('Z', '+00:00'))
                                    else:
                                        # Date without timezone info - assume UTC
                                        from datetime import timezone
                                        file_modified_naive = datetime.fromisoformat(modified_value)
                                        file_modified = file_modified_naive.replace(tzinfo=timezone.utc)
                                else:
                                    self.logger.debug(f" Unexpected date format: {type(modified_value)} - {modified_value}")
                                    return None
                                                                
                                if file_modified < email_received:
                                    self.logger.info(f" Resume file NOT updated after email - File: {file_modified}, Email: {email_received}")
                                    # Return None to indicate this contact should not be processed
                                    return None
                                    
                            except Exception as file_date_error:
                                self.logger.info(f"Failed to parse {sender_email}")
                                self.logger.debug(f" Failed to parse file modification date: {file_date_error}")
                        else:
                            self.logger.info(f"No file_metadata found for contact {sender_email}")
                            return None
                       
                except Exception as date_error:
                    self.logger.debug(f" Failed to parse dates for comparison: {date_error}")
                    # If date parsing fails, proceed without date check
            
            # Return contact information
            result = {
                'contact_id': contact_id,
                'resume_url': resume_url or '',
                'created_on': created_on or '',
                'modified_on': modified_on or '',
                'resume_modified_after_email': False,  # Will be updated if file check passes
                'file_metadata': None  # Will be updated if file metadata is available
            }

            self.logger.info(f"successfully found Contact ID for {sender_email} - Contact ID: {contact_id}")
            
            return result
                
        except Exception as e:
            self.logger.error(f" Dataverse lookup failed for {sender_email}: {e}")
            # On connection failure, return None but log as connection error
            self.logger.warning(f" Connection failure - will retry later for email: {sender_email}")
            raise  # Re-raise to handle as connection failure
    

    
    def get_resume_file_metadata(self, resume_url: str) -> Optional[Dict]:
        """
        Get file metadata from SharePoint using existing SharePoint functions
        
        Args:
            resume_url: SharePoint URL to the resume file
            
        Returns:
            Dictionary with file metadata (created, modified) or None if error
        """
        if not resume_url:
            self.logger.debug(f" No resume URL provided")
            return None
            
        try:
            self.logger.debug(f" Fetching file metadata for: {resume_url}")
            
            # Extract relative path from full SharePoint URL
            # Convert from: https://execusearchgroup.sharepoint.com/sites/Mercury/contact/Name_ID/CV/cv.pdf
            # To: /sites/Mercury/contact/Name_ID/CV/cv.pdf
            if resume_url.startswith('https://'):
                import urllib.parse
                parsed_url = urllib.parse.urlparse(resume_url)
                cv_url = parsed_url.path  # This gives us the relative path
            else:
                cv_url = resume_url  # Already a relative path
            
            # Map environment to SharePoint environment number
            # Environment.PROD = 2, Environment.UAT = 1, Environment.SANDBOX = 0
            sharepoint_env = DEFAULT_DATAVERSE_ENV.value
            
            self.logger.debug(f"Using SharePoint environment: {sharepoint_env}, path: {cv_url}")
            
            # Use existing SharePoint function to get metadata
            filedata = get_file_metadata_from_sharepoint(sharepoint_env, cv_url, logger=self.logger)
            
            if filedata and ('created' in filedata or 'modified' in filedata):
                # Convert to the expected format
                metadata = {
                    'lastModifiedDateTime': filedata.get('modified'),
                    'createdDateTime': filedata.get('created'),
                    'source': 'sharepoint'
                }
                
                self.logger.debug(f"SharePoint metadata retrieved: created={metadata['createdDateTime']}, modified={metadata['lastModifiedDateTime']}")
                return metadata
            else:
                self.logger.debug(f"No metadata returned from SharePoint for: {cv_url}")
                return None
                
        except Exception as e:
            self.logger.debug(f"Error fetching file metadata from {resume_url}: {e}")
            return None
    
    def parse_resume(self, email: Dict, contact_id: str) -> Tuple[bool, str]:
        """
        Run resume parser using the existing parse_resume_and_add_to_db function
        
        Args:
            email: Email dictionary containing attachment
            contact_id: Contact ID for the sender
            
        Returns:
            Tuple of (success: bool, result_message: str)
        """
        try:
            email_id = email.get('id', 'Unknown')
            self.logger.info(f"Starting resume parsing for email: {email_id}, Contact ID: {contact_id}")
            
            # Check if email has attachments
            if not email.get('hasAttachments', False):
                error_msg = "No attachments found in email"
                self.logger.warning(f"{error_msg} - Email ID: {email_id}")
                return False, error_msg
            
            # Call the existing resume parsing function
            self.logger.info(f"📄 Calling parse_resume_and_add_to_db for Contact ID: {contact_id}")
            
            try:
                # Call the resume parser with skip=False to force processing
                parse_resume_and_add_to_db(
                    token=self.token,
                    dataverse_url=self.dataverse_url,
                    contactid=contact_id,
                    skip=False,  # Always process, don't skip
                    logger=self.logger
                )
                
                # If we get here without exception, parsing was successful
                success_msg = f"Resume parsed and saved to database successfully for Contact ID: {contact_id}"
                self.logger.info(f"{success_msg}")
                return True, success_msg
                
            except Exception as parsing_error:
                error_msg = f"Resume parsing failed: {parsing_error}"
                self.logger.error(f"Resume parsing failed for Contact ID {contact_id}: {error_msg}")
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Resume parsing system error: {e}"
            self.logger.error(f"{error_msg} - Email ID: {email_id}")
            return False, error_msg
    
    def move_email_to_folder(self, email_id: str, target_folder_name: str) -> bool:
        """
        Move email to specified folder, creating folder if needed
        
        Args:
            email_id: ID of email to move
            target_folder_name: Name of destination folder
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.debug(f" Moving email {email_id} to folder: {target_folder_name}")
            
            # Ensure target folder exists
            folder_id = self.email_fetcher.ensure_folder_exists(MAILBOX, target_folder_name)
            
            # Move email to target folder
            success = self.email_fetcher.move_email(MAILBOX, email_id, folder_id)
            
            if success:
                self.logger.info(f"Successfully moved email {email_id} to {target_folder_name}")
                return True
            else:
                self.logger.error(f"Failed to move email {email_id} to {target_folder_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error moving email {email_id} to {target_folder_name}: {e}")
            return False
    
    def process_single_email(self, email: Dict, live_mode: bool = False) -> Dict:
        """
        Process a single email through the complete workflow
        
        Args:
            email: Email dictionary to process
            live_mode: If False (test mode), skip resume parsing and email moving
            
        Returns:
            Processing result dictionary with detailed information
        """
        email_id = email.get('id', 'Unknown')
        
        # Extract basic email info
        sender_email = self.extract_sender_email(email)
        received_dt = email.get('receivedDateTime', '')
        
        result = {
            'email_id': email_id,
            'sender_email': sender_email,
            'received_datetime': received_dt,
            'contact_id': None,
            'resume_url': None,
            'created_on': None,
            'modified_on': None,
            'resume_modified_after_email': False,
            'file_metadata': None,
            'final_folder': None,
            'status': 'pending',
            'error_message': None
        }
        
        try:
            # Step 1: Extract sender email
            if not sender_email:
                result['status'] = 'skipped'
                result['error_message'] = 'Could not extract sender email'
                self.logger.warning(f" Could not extract sender email - skipping email {email_id}")
                return result
            
            # Step 2: Dataverse lookup
            
            try:
                contact_data = self.lookup_contact_id_in_dataverse(sender_email, received_dt)
                
                if contact_data is None:
                    # Check if email is older than threshold days and no contact found
                    if received_dt:
                        try:
                            email_received = datetime.fromisoformat(received_dt.replace('Z', '+00:00'))
                            current_time = datetime.now(email_received.tzinfo)
                            days_old = (current_time - email_received).days
                            
                            if days_old > EMAIL_AGE_THRESHOLD_DAYS:
                                # Email is older than threshold and no contact found - move to error folder
                                if live_mode:
                                    error_folder = "Error_no_contact_found"
                                    move_success = self.move_email_to_folder(email_id, error_folder)
                                    if move_success:
                                        result['status'] = 'moved_to_error'
                                        result['final_folder'] = error_folder
                                        result['error_message'] = f'No contact found, email {days_old} days old (threshold: {EMAIL_AGE_THRESHOLD_DAYS})'
                                        self.logger.info(f"  Moved email {email_id} to {error_folder} - {days_old} days old, no contact (threshold: {EMAIL_AGE_THRESHOLD_DAYS})")
                                        return result
                                    else:
                                        self.logger.warning(f"  Failed to move old email {email_id} to {error_folder}")
                                else:
                                    # Test mode - just log what would happen
                                    result['status'] = 'would_move_to_error'
                                    result['final_folder'] = 'Error_no_contact_found'
                                    result['error_message'] = f'Would move: No contact found, email {days_old} days old (threshold: {EMAIL_AGE_THRESHOLD_DAYS})'
                                    self.logger.info(f"  Would move email {email_id} to Error_no_contact_found - {days_old} days old, no contact (threshold: {EMAIL_AGE_THRESHOLD_DAYS})")
                                    return result
                        except Exception as date_error:
                            self.logger.debug(f"Failed to parse email date for age check: {date_error}")
                    
                    # Either no Contact ID found OR resume not updated after email (skipped)
                    result['status'] = 'skipped'
                    result['final_folder'] = CONTACTID_QUEUE_FOLDER
                    result['error_message'] = 'Contact not found or resume not updated after email'
                    self.logger.info(f"  Skipped email {email_id} from {sender_email}")
                    return result
                    
                elif contact_data:
                    result['contact_id'] = contact_data['contact_id']
                    result['resume_url'] = contact_data['resume_url']
                    result['created_on'] = contact_data['created_on']
                    result['modified_on'] = contact_data['modified_on']
                    result['resume_modified_after_email'] = contact_data['resume_modified_after_email']
                    result['file_metadata'] = contact_data.get('file_metadata')
                    
                    if not result['contact_id']:
                        # No Contact ID found - leave in queue
                        result['status'] = 'skipped'
                        result['final_folder'] = CONTACTID_QUEUE_FOLDER
                        result['error_message'] = 'Contact ID is empty'
                        self.logger.info(f" Contact ID not found – skipped email {email_id} from {sender_email}")
                        return result
                
            except Exception as e:
                # Connection failure - leave in queue for retry
                result['status'] = 'connection_failure'
                result['error_message'] = f"Dataverse connection failure: {e}"
                result['final_folder'] = CONTACTID_QUEUE_FOLDER
                self.logger.warning(f" Connection failure – retry later for email {email_id}")
                return result
            
            # Step 3: Resume parsing (Contact ID found)
            self.logger.debug(f" Contact ID found: {result['contact_id']} for email {email_id}")
            
            # Check if we should skip resume parsing and moving in test mode
            if not live_mode:
                result['status'] = 'test_mode_skipped'
                result['final_folder'] = CONTACTID_QUEUE_FOLDER  # Email remains in queue
                return result
            
            # Ensure we have a valid contact_id before proceeding
            contact_id_for_parsing = result.get('contact_id')
            if not contact_id_for_parsing:
                result['status'] = 'error'
                result['error_message'] = 'Contact ID is empty or None'
                result['final_folder'] = CONTACTID_QUEUE_FOLDER
                self.logger.error(f" Contact ID is empty for email {email_id}")
                return result
            
            parsing_success, parsing_message = self.parse_resume(email, contact_id_for_parsing)
            
            # Step 4: Move email based on parsing result
            if parsing_success:
                # Parsing succeeded - move to cat_processed
                target_folder = CAT_PROCESSED_FOLDER
                move_success = self.move_email_to_folder(email_id, target_folder)
                
                if move_success:
                    result['status'] = 'processed'
                    result['final_folder'] = target_folder
                else:
                    result['status'] = 'move_failed'
                    result['final_folder'] = CONTACTID_QUEUE_FOLDER
                    result['error_message'] = f"Failed to move email to {target_folder}"
                    self.logger.warning(f" Failed to move processed email {email_id} to {target_folder}")
                    
            else:
                # Parsing failed - move to cat_errored
                target_folder = CAT_ERRORED_FOLDER
                move_success = self.move_email_to_folder(email_id, target_folder)
                
                if move_success:
                    result['status'] = 'parsing_failed'
                    result['final_folder'] = target_folder
                else:
                    result['status'] = 'move_failed'
                    result['final_folder'] = CONTACTID_QUEUE_FOLDER
                    result['error_message'] = f"Failed to move email to {target_folder}"
                    self.logger.warning(f" Failed to move failed email {email_id} to {target_folder}")
            
            return result
            
        except Exception as e:
            result['status'] = 'error'
            result['error_message'] = f"Unexpected error: {e}"
            result['final_folder'] = CONTACTID_QUEUE_FOLDER
            self.logger.error(f"❌ Unexpected error processing email {email_id}: {e}")
            return result


def display_processing_summary(results: List[Dict], logger):
    """Display comprehensive processing statistics as a single JSON object"""
    total = len(results)
    
    if not results:
        summary = {
            "processing_statistics": {
                "total_emails_processed": 0,
                "message": "No emails processed"
            }
        }
        logger.info(f"PROCESSING STATISTICS: {json.dumps(summary, indent=2)}")
        return
    
    # Calculate detailed statistics
    processed = len([r for r in results if r['status'] == 'processed'])
    parsing_failed = len([r for r in results if r['status'] == 'parsing_failed'])
    skipped = len([r for r in results if r['status'] == 'skipped'])
    test_mode_skipped = len([r for r in results if r['status'] == 'test_mode_skipped'])
    connection_failures = len([r for r in results if r['status'] == 'connection_failure'])
    errors = len([r for r in results if r['status'] == 'error'])
    move_failures = len([r for r in results if r['status'] == 'move_failed'])
    moved_to_error = len([r for r in results if r['status'] == 'moved_to_error'])
    would_move_to_error = len([r for r in results if r['status'] == 'would_move_to_error'])
    contacts_found = len([r for r in results if r.get('contact_id')])
    resumes_found = len([r for r in results if r.get('resume_url')])
    resumes_updated_after_email = len([r for r in results if r.get('resume_modified_after_email', False)])
    
    # Break down skip reasons
    skipped_no_contact = len([r for r in results if r['status'] == 'skipped' and 'not found' in r.get('error_message', '')])
    skipped_old_resume = len([r for r in results if r['status'] == 'skipped' and 'not updated after email' in r.get('error_message', '')])
    
    # Calculate percentages
    contact_percentage = int(contacts_found/total*100) if total > 0 else 0
    resume_percentage = int(resumes_found/total*100) if total > 0 else 0
    success_percentage = int(processed/total*100) if total > 0 else 0
    
    # Build comprehensive summary
    summary = {
        "processing_statistics": {
            "overview": {
                "total_emails_processed": total,
                "contact_ids_found": contacts_found,
                "contact_ids_found_percentage": contact_percentage,
                "resume_urls_found": resumes_found,
                "resume_urls_found_percentage": resume_percentage,
                "resumes_updated_after_email": resumes_updated_after_email
            },
            "processing_outcomes": {
                "successfully_processed": processed,
                "success_percentage": success_percentage,
                "test_mode_skipped": test_mode_skipped,
                "parsing_failed": parsing_failed,
                "total_skipped": skipped,
                "skip_breakdown": {
                    "no_contact_id": skipped_no_contact,
                    "resume_not_updated": skipped_old_resume
                }
            },
            "error_categories": {
                "connection_failures": connection_failures,
                "move_failures": move_failures,
                "processing_errors": errors,
                "moved_to_error_folder": moved_to_error,
                "would_move_to_error_folder": would_move_to_error,
                "total_failures": connection_failures + move_failures + errors + parsing_failed
            }
        }
    }
    
    # Output simple summary
    total_moved_error = moved_to_error + would_move_to_error
    logger.info(f"PROCESSING_STATS: Total={total}, ContactIDs={contacts_found}, Resumes={resumes_found}, Processed={processed}, Skipped={skipped}, MovedToError={total_moved_error}, Failed={connection_failures + move_failures + errors + parsing_failed}")


def display_failed_processing_details(results: List[Dict], logger):
    """Display failed processing details in main.py style format"""
    failed_results = [r for r in results if r['status'] in ['parsing_failed', 'error', 'move_failed', 'connection_failure', 'moved_to_error', 'would_move_to_error']]
    
    if not failed_results:
        return
    
    # Display header
    logger.warning(f"\n FAILED PROCESSING DETAILS ({len(failed_results)} emails):")
    
    # Show first 5 failures with key details
    for i, result in enumerate(failed_results[:5], 1):
        sender = result['sender_email']
        status = result['status'].replace('_', ' ').title()
        error = result.get('error_message', 'No error details')[:60]
        email_id_short = result.get('email_id', 'Unknown')[:20] + "..."
        
        logger.warning(f"  {i}.  Email {sender}")
        if result.get('resume_modified_after_email'):
            logger.warning(f"      Resume updated after email received")
        logger.warning(f"Status: {status}")
        logger.warning(f"Error: {error}")
    
    if len(failed_results) > 5:
        logger.warning(f"     ... and {len(failed_results) - 5} more failed emails")


def display_final_summary(results: List[Dict], sender_filter: Optional[List[str]], live_mode: bool, logger):
    """Display final summary as a single JSON object instead of multiple log lines"""
    total = len(results)
    
    # Calculate key metrics
    contacts_found = len([r for r in results if r.get('contact_id')])
    resumes_found = len([r for r in results if r.get('resume_url')])
    resumes_updated_after_email = len([r for r in results if r.get('resume_modified_after_email', False)])
    processed = len([r for r in results if r['status'] == 'processed'])
    test_mode_skipped = len([r for r in results if r['status'] == 'test_mode_skipped'])
    parsing_failed = len([r for r in results if r['status'] == 'parsing_failed'])
    skipped = len([r for r in results if r['status'] == 'skipped'])
    
    # Break down skip reasons for final summary
    skipped_no_contact = len([r for r in results if r['status'] == 'skipped' and 'not found' in r.get('error_message', '')])
    skipped_old_resume = len([r for r in results if r['status'] == 'skipped' and 'not updated after email' in r.get('error_message', '')])
    
    contact_pct = int(contacts_found/total*100) if total > 0 else 0
    resume_pct = int(resumes_found/total*100) if total > 0 else 0
    
    # Build summary JSON
    summary = {
        "mode": "TEST" if not live_mode else "LIVE",
        "sender_filter": sender_filter,
        "totals": {
            "emails_processed": total,
            "contact_ids_found": contacts_found,
            "contact_ids_found_percentage": contact_pct,
            "resume_urls_found": resumes_found,
            "resume_urls_found_percentage": resume_pct,
            "resumes_updated_after_email": resumes_updated_after_email
        },
        "processing_results": {
            "successfully_processed": processed,
            "test_mode_skipped": test_mode_skipped,
            "parsing_failed": parsing_failed,
            "skipped_no_contact": skipped_no_contact,
            "skipped_old_resume": skipped_old_resume
        },
        "next_steps": [],
        "insights": []
    }
    
    # Add next steps and insights based on mode and results
    if total == 0:
        summary["result"] = "No emails found to process"
        if sender_filter:
            summary["next_steps"].append("Try without --sender filter to see emails from other senders")
    else:
        # Next steps
        if not live_mode and contacts_found > 0:
            summary["next_steps"].append(f"Use --live-not-a-test to process {contacts_found} emails with Contact IDs")
        elif sender_filter and contacts_found == 0:
            summary["next_steps"].append("Remove --sender filter to see all available emails")
        elif contacts_found == 0 and total > 0:
            if skipped_no_contact > 0:
                summary["insights"].append("No Contact IDs found - these emails may need manual review")
            elif skipped_old_resume > 0:
                summary["insights"].append("Resumes not updated after emails - candidates may not be actively responding")
        elif skipped_old_resume > 0 and resumes_updated_after_email == 0:
            summary["insights"].append("All resumes are older than emails - consider follow-up outreach")
    
    # Output as single JSON log entry
    logger.info(f"FINAL_SUMMARY: {json.dumps(summary)}")


def process_contactid_queue(logger, count: int = 50, live_mode: bool = False, sender_filter: Optional[List[str]] = None):
    """
    Main function to process emails from ContactId_Queue
    
    Args:
        logger: Logger instance
        count: Number of emails to process
        live_mode: If False (default), skip resume parsing and email moving (test mode)
        sender_filter: List of sender email addresses to filter by (optional)
    """
    try:
        # Single startup message with key configuration
        mode_text = "TEST" if not live_mode else "LIVE"
        filter_text = f" (sender: {', '.join(sender_filter)})" if sender_filter else ""
        
        # logger.info(f" Starting Contact ID processing: {count} emails in {mode_text} mode{filter_text}")
        
        if not live_mode:
            logger.debug("  Use --live-not-a-test flag to enable full processing")
        
        # Initialize processor (credentials and token are fetched once here)
        processor = ContactIdProcessor(logger)
        
        # Step 1: Fetch emails from queue
        emails = processor.fetch_queue_emails(count, sender_filter)
        
        if not emails:
            # logger.info(f" No emails to process")
            return []
        
        # Step 2: Process each email
        results = []
        email_count = len(emails)
        
        for i, email in enumerate(emails, 1):
            sender_email = processor.extract_sender_email(email)
            
            # Progress logging for larger batches
            if email_count > 5 and (i == 1 or i % 10 == 0 or i == email_count):
                logger.info(f"📧 Processing email {i}/{email_count}")
            elif email_count <= 5:
                logger.debug(f"📧 Processing email {i}/{email_count}: {sender_email}")
            
            result = processor.process_single_email(email, live_mode)
            results.append(result)
        
        # Step 3: Display summary
        display_processing_summary(results, logger)
        display_failed_processing_details(results, logger)
        display_final_summary(results, sender_filter, live_mode, logger)
        
        logger.info("Contact ID processing completed")
        return results
        
    except Exception as e:
        logger.error(f" Critical error in Contact ID processing: {e}")
        return []


def main():
    """Main function with command line interface"""
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Contact ID Email Processor - Process emails from ContactId_Queue folder"
    )
    
    parser.add_argument(
        "--count", 
        type=int, 
        default=50, 
        help="Number of emails to process from queue (default: 50)"
    )
    
    parser.add_argument(
        "--live-not-a-test", 
        action="store_true", 
        help="Enable LIVE mode - full processing with resume parsing and email moving"
    )
    
    parser.add_argument(
        "--sender", 
        type=str, 
        help="Process emails only from specific sender address"
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Validate count
    if args.count <= 0:
        print(" Error: count must be a positive number")
        sys.exit(1)
    
    # Initialize logger
    logger_config = {
        "name": "contact_queue_processor",
        "log_level": "INFO",
        "log_to_stdout": True,
        "use_syslog": False,
        "log_file": f"/mnt/incoming/logs/contact_queue_processor.log",
    }
    logger = AppLogger(logger_config)
    
    # Configuration
    count = args.count
    live_mode = args.live_not_a_test
    sender_filter = [args.sender] if args.sender else None
    
    # Config summary for command line startup
    mode_text = "TEST" if not live_mode else "LIVE"
    filter_text = f" for {', '.join(sender_filter)}" if sender_filter else ""
    logger.info(f"Contact ID Processor: {count} emails, {mode_text} mode{filter_text}")
    
    # Load secrets and start processing
    try:
        load_secrets_env_variables()        
        # Main processing
        process_contactid_queue(logger, count, live_mode, sender_filter)
        
    except Exception as e:
        logger.error(f" Critical error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 