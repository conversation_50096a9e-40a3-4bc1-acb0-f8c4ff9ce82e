from common.db.postgres_connector import PostgresConnector
from common.appLogger import AppLogger
from dataverse_helper.token_manager import Environment
import psycopg2
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
import json
import uuid

class CatalystMatchService:
    """Service for handling CatalystMatch form submissions."""
    
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector, env: Environment = Environment.PROD):
        """Initialize with logger and database environment."""
        self.logger = logger
        self.db = db_connector
        self.schema = self.db.schema
        self.env = env

    def _get_active_db_connection(self):
        """
        Ensures an active DB connection is available, attempting to (re)connect if necessary.
        """
        conn = self.db.connection
        if not conn or conn.closed:
            self.logger.info(f"DB connection for {self.db.config.get('dbname', 'N/A')} is None or closed. Attempting to connect.")
            conn = self.db.connect()
            if not conn:
                self.logger.error(f"Failed to establish initial DB connection to {self.db.config.get('dbname', 'N/A')}.")
                raise Exception("Database service unavailable at the moment.")
        else:
            try:
                with conn.cursor() as cur_ping:
                    cur_ping.execute("SELECT 1")
                self.logger.debug(f"Existing DB connection to {self.db.config.get('dbname', 'N/A')} is active.")
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as ping_error:
                self.logger.warning(f"DB connection test failed for {self.db.config.get('dbname', 'N/A')} ('{ping_error}'). Attempting to reconnect.")
                try:
                    self.db.close()
                except Exception as close_e:
                    self.logger.warning(f"Error closing stale DB connection: {close_e}")
                conn = self.db.connect()
                if not conn:
                    self.logger.error(f"Failed to re-establish DB connection to {self.db.config.get('dbname', 'N/A')}.")
                    raise Exception("Database service unavailable after reconnect attempt.")
        
        if not conn or conn.closed:
            self.logger.error(f"DB connection to {self.db.config.get('dbname', 'N/A')} is still unavailable.")
            raise Exception("Database connection unavailable.")
        
        return conn

    def submit_catalyst_match_form(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and store CatalystMatch form submission.
        
        Args:
            form_data: Dictionary containing form data
            
        Returns:
            Dictionary with submission result
        """
        try:
            conn = self._get_active_db_connection()
            
            # Generate a unique submission ID
            submission_id = str(uuid.uuid4())
            submission_time = datetime.now(timezone.utc)
            
            # Log the form submission
            self.logger.info(f"Processing CatalystMatch form submission: {submission_id}")
            
            # For now, we'll just log the data and return success
            # In a real implementation, you would store this in a database table
            self.logger.info(f"CatalystMatch form data: {json.dumps(form_data, indent=2)}")
            
            # Here you would typically insert into a database table like:
            # INSERT INTO catalyst_match_submissions (id, required_skills, preferred_skills, ...)
            # VALUES (%s, %s, %s, ...)
            
            return {
                "success": True,
                "submission_id": submission_id,
                "message": "CatalystMatch form submitted successfully",
                "timestamp": submission_time.isoformat(),
                "data": form_data
            }
            
        except Exception as e:
            self.logger.error(f"Error processing CatalystMatch form submission: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to process CatalystMatch form submission"
            } 