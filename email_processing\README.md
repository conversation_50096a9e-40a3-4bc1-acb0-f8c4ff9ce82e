# Azure Email Processor

**Advanced email processing system with Mercury CV forwarding, fault-tolerant single-loop processing, and comprehensive error handling.**

## 🎯 **Purpose**

**Primary Functions**:

- Fetch and process emails from `<EMAIL>` inbox
- Forward emails with attachments to Mercury CV processing (`<EMAIL>`)
- Move emails without attachments to organized folders
- Provide detailed processing statistics and error tracking

This is **production-ready code** with comprehensive safety features including test mode, fault tolerance, and detailed logging.

## 🌟 **Key Features**

### **✨ Advanced Email Processing**

- ✅ **Test vs Live Mode**: Safe development with filtered test addresses
- ✅ **Mercury CV Forwarding**: Automated forwarding to `<EMAIL>`
- ✅ **Attachment Processing**: Move emails without attachments to designated folders
- ✅ **Single-Loop Processing**: Fault-tolerant individual email handling
- ✅ **Comprehensive Statistics**: Detailed processing reports and error tracking

### **🛡️ Safety & Reliability**

- ✅ **Test Mode Default**: Only processes emails from specific test addresses
- ✅ **Fault Tolerance**: Individual email failures don't affect the batch
- ✅ **Detailed Error Tracking**: Email ID-level error reporting
- ✅ **Reprocessing Capability**: Failed emails remain in inbox for retry

## 📂 **File Structure**

```
email_processing/
├── main.py                      # 🎯 Main application (production email processing)
├── contact_queue_processor.py   # 🔄 Contact ID processing workflow
├── azure_client.py              # 🔗 Azure API client with forwarding capabilities
├── config.py                    # ⚙️ Configuration & logger setup
├── README.md                    # 📝 This comprehensive documentation
└── tools/                       # 🔨 One-time utilities
    ├── test_credentials.py
    ├── list_folders.py
    ├── create_folder.py
    ├── folder_manager.py
    └── README.md
```

## 🚀 **Quick Start**

### **1. Set Up Environment Variables**

Add GPG passphrases to your `.bashrc`:

```bash
export GPG_PASS_DEV_SECRETS="your_dev_passphrase"
export GPG_PASS_EXT_API_KEYS="your_ext_passphrase"
export GPG_PASS_UAT_SECRETS="your_uat_passphrase"
export GPG_PASS_PROD_SECRETS="your_prod_passphrase"
```

Then reload:

```bash
source ~/.bashrc
```

### **2. Basic Usage (Test Mode - Safe by Default)**

```bash
# Test mode (default) - only processes test addresses
python main.py --count 5

# Test with attachment processing
python main.py --move-no-attachments --count 10

# Test with Mercury CV forwarding
python main.py --fwd-mercurycv-no-move-2-q --count 3
```

### **3. Production Usage (Live Mode)**

```bash
# Live mode - processes ALL emails (use with caution)
python main.py --live-not-a-test --count 20

# Production with full processing
python main.py --live-not-a-test --move-no-attachments --fwd-mercurycv-move-2-q --count 50
```

## 📖 **Complete Command Reference**

### **Core Arguments**

| Argument            | Description                                    |
| ------------------- | ---------------------------------------------- |
| `--help`            | Show comprehensive help information            |
| `--count <N>`       | Fetch N emails (default: 10)                   |
| `--live-not-a-test` | **REQUIRED for production** - fetch ALL emails |

### **Processing Options**

| Argument                      | Description                                                     |
| ----------------------------- | --------------------------------------------------------------- |
| `--move-no-attachments`       | Move emails without attachments to 'Invalid Attachment' folder  |
| `--fwd-mercurycv-move-2-q`    | Forward emails with attachments to Mercury CV AND move to queue |
| `--fwd-mercurycv-no-move-2-q` | Forward emails with attachments to Mercury CV but keep in inbox |

### **Usage Examples**

```bash
# Safe development testing
python main.py --count 5
python main.py --move-no-attachments
python main.py --fwd-mercurycv-no-move-2-q

# Production operations (explicit live mode required)
python main.py --live-not-a-test --count 50
python main.py --live-not-a-test --move-no-attachments --fwd-mercurycv-move-2-q
```

## 🧪 **Test vs Live Mode**

### **Test Mode (Default - Safe)**

- **Automatic**: No special flag needed
- **Filtered Emails**: Only processes emails from specific test addresses:
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
- **Safety**: Prevents accidental processing of production emails

### **Live Mode (Production)**

- **Explicit Flag Required**: `--live-not-a-test`
- **All Emails**: Processes ALL emails in the inbox
- **Use with Caution**: No filtering, full production processing

## 🔄 **Processing Flow**

### **Single-Loop Architecture**

```
STEP 1: Fetch emails from inbox (filtered by mode)
    ↓
STEP 2: Process each email individually through ALL required steps:
    • Attachment checking (if --move-no-attachments)
    • Mercury CV forwarding (if --fwd-mercurycv-*)
    • Queue moving (if --fwd-mercurycv-move-2-q)
    ↓
STEP 3: Display remaining emails with processing statistics
```

### **Fault Tolerance**

- **Individual Processing**: Each email processed separately
- **Failure Isolation**: One failed email doesn't affect others
- **Detailed Tracking**: Email ID-level error reporting
- **Reprocessing**: Failed emails remain in inbox for retry

### **Sample Output**

```
📊 PROCESSING STATISTICS:
  📧 Total emails processed: 15
  📎 With attachments: 10
  📄 Without attachments: 5
  📁 Moved to Invalid Attachment folder: 4
  📤 Forwarded to Mercury CV: 8
  📁 Moved to ContactId_Queue: 6
  ❌ Failed processing: 2
  📋 Remaining in inbox for display: 7

❌ FAILED PROCESSING DETAILS:
  1. 📧 Email Details:
     Email ID: AAMkAGE3NzlhMzg0LTQwYjAtNGM...
     Subject: 'Resume - Senior Developer Position...'
     From: <EMAIL>
     Has Attachments: Yes
     Received: 2025-07-31T10:30:00Z
     ❌ Failed Step: forward_to_mercury_cv
     🔍 Error Details: Network timeout while forwarding email
     📍 Current Status: Remains in inbox for potential reprocessing
```

## ⚙️ **Configuration & Setup**

### **Required Azure Permissions**

Your Azure App Registration must have these **Application Permissions**:

- ✅ **Mail.ReadWrite** (for reading and moving emails)
- ✅ **Mail.Send** (for forwarding emails to Mercury CV)

### **Azure Credentials (GPG Encrypted)**

Required environment variables (loaded from GPG files in `/etc/`):

- `MAILBOX_RESUMES_CLIENT_ID`
- `MAILBOX_RESUMES_CLIENT_SECRET`
- `MAILBOX_RESUMES_TENANT_ID`

### **GPG Environment Variables**

Set these in your `.bashrc` or shell environment:

```bash
export GPG_PASS_DEV_SECRETS="your_dev_passphrase"
export GPG_PASS_EXT_API_KEYS="your_ext_passphrase"
export GPG_PASS_UAT_SECRETS="your_uat_passphrase"
export GPG_PASS_PROD_SECRETS="your_prod_passphrase"
```

### **Logging Configuration**

- **Log File**: `/mnt/incoming/logs/fetch_email.log`
- **Format**: JSON structured logging with timestamps
- **Console Output**: Detailed progress information
- **Error Tracking**: Email ID-level error details

## 🔧 **Mercury CV Integration**

### **Forwarding Process**

1. **Email Identification**: Finds emails with attachments
2. **API Call**: Uses Microsoft Graph API `/messages/{id}/forward` endpoint
3. **Destination**: Forwards to `<EMAIL>`
4. **Comment**: Includes automated processing metadata
5. **Error Handling**: Detailed logging and failure tracking

### **Queue Management**

- **Queue Folder**: `ContactId_Queue` (created automatically)
- **Conditional Moving**: Only if `--fwd-mercurycv-move-2-q` flag is used
- **Folder Creation**: Automatically creates folders if they don't exist

### **API Reference**

- **Endpoint**: `POST /v1.0/users/{mailbox}/messages/{id}/forward`
- **Authentication**: Bearer token with Application credentials
- **Required Permission**: `Mail.Send`
- **Response**: 202 Accepted on success

## 🆔 **Contact ID Processing Workflow**

### **Overview**

The `contact_queue_processor.py` provides a complete workflow for processing emails that have been forwarded to the `ContactId_Queue` folder. It performs Contact ID lookups in Dataverse and conditional resume parsing.

### **Processing Workflow**

```
STEP 1: Fetch emails from ContactId_Queue folder (sorted by most recent)
    ↓
STEP 2: Extract sender email addresses from each email
    ↓
STEP 3: Look up Contact IDs in Dataverse system
    ↓
STEP 4: Conditional processing based on Contact ID existence:

    📧 If Contact ID NOT found:
        • Leave email in ContactId_Queue
        • Log: "Contact ID not found – skipped email <message-id>"

    ✅ If Contact ID IS found:
        • Proceed to resume parsing
        • Parse resume attachment using configured parser

        📄 If parsing SUCCEEDS:
            • Move email to 'Cat_Processed' folder
            • Log: "Resume parsed successfully – email <message-id> moved to Cat_Processed"

        ❌ If parsing FAILS:
            • Move email to 'Cat_Errored' folder
            • Log: "Resume parsing failed – email <message-id> moved to Cat_Errored"

�� Connection failure handling:
    • Leave email in ContactId_Queue for retry
    • Log: "Connection failure – retry later for email <message-id>"
```

### **Usage Examples**

```bash
# Basic Contact ID processing (TEST mode - no parsing/moving)
python contact_queue_processor.py --count 50

# Process emails from specific sender (TEST mode)
python contact_queue_processor.py --sender <EMAIL> --count 10

# LIVE mode - full processing with resume parsing and email moving
python contact_queue_processor.py --live-not-a-test --count 20

# LIVE mode with specific sender
python contact_queue_processor.py --live-not-a-test --sender <EMAIL> --count 5

# Large batch in LIVE mode
python contact_queue_processor.py --live-not-a-test --count 100
```

### **Command Line Options**

| Argument            | Description                                         |
| ------------------- | --------------------------------------------------- |
| `--help`            | Show comprehensive help information                 |
| `--count <N>`       | Process N emails from ContactId_Queue (default: 50) |
| `--live-not-a-test` | Enable LIVE mode - full processing with resume parsing and email moving |
| `--sender <email>`  | Process emails only from specific sender address    |

### **Operating Modes**

#### **🧪 TEST Mode (Default)**
- **Purpose**: Safe testing and Contact ID validation
- **Behavior**: 
  - ✅ Fetches emails from ContactId_Queue (latest received first)
  - ✅ Performs Contact ID lookup in Dataverse  
  - ✅ Logs all date information and resume URLs
  - ❌ **SKIPS** resume parsing (step 4)
  - ❌ **SKIPS** email moving (step 5)
  - 📧 Emails remain in ContactId_Queue folder
- **Usage**: `python contact_queue_processor.py --count 20`

#### **🔥 LIVE Mode**
- **Purpose**: Full production processing with AI resume parsing
- **Behavior**:
  - ✅ All TEST mode features PLUS
  - ✅ **RUNS** AI-powered resume parsing 
  - ✅ **MOVES** emails to Cat_Processed or Cat_Errored folders
  - 🗃️ **STORES** parsed data in PostgreSQL database
- **Usage**: `python contact_queue_processor.py --live-not-a-test --count 10`

#### **🔍 Sender Filtering**
- **Purpose**: Process emails from specific senders only
- **Works with both TEST and LIVE modes**
- **Examples**:
  - `python contact_queue_processor.py --sender <EMAIL> --count 5`
  - `python contact_queue_processor.py --live-not-a-test --sender <EMAIL>`

### **Folder Structure**

| Folder              | Purpose        | Email State                              |
| ------------------- | -------------- | ---------------------------------------- |
| **ContactId_Queue** | Source folder  | Emails waiting for Contact ID processing |
| **Cat_Processed**   | Success folder | Successfully parsed resumes              |
| **Cat_Errored**     | Error folder   | Failed resume parsing                    |

### **Integration Points**

#### **Dataverse Integration**

- **Purpose**: Look up Contact IDs by sender email address
- **Implementation**: ✅ **IMPLEMENTED** - Full Dataverse integration using existing dataverse_helper patterns
- **Location**: `lookup_contact_id_in_dataverse()` method
- **Features**:
  - Email-to-Contact ID lookup using `emailaddress2` field
  - Creation date comparison with email received date
  - Resume URL retrieval (`recruit_cvurl` field)
  - Proper filtering for active candidate contacts
  - Environment-based authentication (PROD default)

#### **Resume Parser Integration**

- **Purpose**: Parse resume attachments and extract data
- **Implementation**: ✅ **FULLY IMPLEMENTED** - Complete AI-powered resume processing
- **Location**: `parse_resume()` method calls `parse_resume_and_add_to_db()`
- **Features**:
  1. **SharePoint Download**: Automatically downloads resume files from SharePoint
  2. **AI Processing**: Uses OpenAI to extract skills, experience, and job titles
  3. **Skills Matching**: Matches extracted data against 81+ subcategories
  4. **Database Storage**: Stores processed data in PostgreSQL database
  5. **Classification**: Automatically classifies candidates by subcategories

```python
# Actual implementation now calls:
parse_resume_and_add_to_db(
    token=token,
    dataverse_url=dataverse_url,
    contactid=contact_id,
    skip=False,
    logger=self.logger
)
```

### **⚡ Performance Optimizations**

#### **Dataverse Credentials Caching**

The system optimizes Dataverse operations by caching credentials and tokens:

1. **Single Initialization**: Credentials and tokens are fetched once during `ContactIdProcessor` initialization
2. **Reuse Across Operations**: Same credentials used for all contact lookups and resume parsing operations
3. **Smart Refresh**: Token is automatically refreshed if needed for long-running processes
4. **Reduced Overhead**: Eliminates redundant authentication calls for each email processed

```python
# Before: Called for every email (inefficient)
credentials = get_dataverse_credentials_for_env(env)  # ❌ Repeated calls
token = get_token_for_env(env)                       # ❌ Repeated calls

# After: Called once during initialization (efficient)
class ContactIdProcessor:
    def __init__(self, logger):
        self.credentials = get_dataverse_credentials_for_env(env)  # ✅ One-time call
        self.token = get_token_for_env(env)                       # ✅ One-time call
```

#### **Token Management**

- **Automatic Refresh**: `refresh_token_if_needed()` method ensures valid tokens
- **Status Monitoring**: `get_credentials_status()` provides debugging information
- **Error Handling**: Graceful handling of token expiration and refresh failures

### **Comprehensive Logging**

The Contact ID processor logs all required details:

- **Message ID**: Unique email identifier
- **Sender Email**: Extracted from email headers
- **Contact ID**: If found in Dataverse lookup
- **Resume Parsing Result**: Success/failure/exception details
- **Final Folder**: Where email was moved
- **Timestamps**: For each major action (lookup, parsing, moving)

### **Error Handling**

- **Connection Failures**: Emails remain in ContactId_Queue for retry
- **Parsing Failures**: Emails moved to Cat_Errored folder
- **Missing Contact IDs**: Emails remain in ContactId_Queue (logged as skipped)
- **System Errors**: Comprehensive error logging with email ID tracking

### **Monitoring & Troubleshooting**

```bash
# Check processing logs
tail -f /mnt/incoming/logs/contact_queue_processor.log

# Test Dataverse connectivity
python contact_queue_processor.py --count 1

# Verify folder structure
cd tools && python list_folders.py
```

## 🛠️ **Troubleshooting**

### **Quick Diagnostics**

```bash
# 1. Test GPG environment variables
env | grep GPG_PASS

# 2. Test Azure credentials
cd tools && python test_credentials.py

# 3. Test basic email fetching
python main.py --count 1

# 4. Test with detailed logging
python main.py --count 1 --fwd-mercurycv-no-move-2-q
```

### **Common Issues & Solutions**

| Issue                                | Cause                             | Solution                                        |
| ------------------------------------ | --------------------------------- | ----------------------------------------------- |
| `failed to read passphrase`          | GPG environment variables not set | Set `GPG_PASS_*` variables in `.bashrc`         |
| `Missing required Azure credentials` | GPG decryption failed             | Check GPG files in `/etc/` and passphrases      |
| `403 Forbidden` when forwarding      | Missing `Mail.Send` permission    | Add `Mail.Send` application permission in Azure |
| `No emails found in test mode`       | No emails from test addresses     | Use `--live-not-a-test` or send test emails     |
| `name 'load_secrets' is not defined` | Import error                      | Use `load_secrets_env_variables()` function     |

### **Error Patterns**

- **Credential Issues**: Check GPG setup and Azure permissions
- **Permission Issues**: Verify `Mail.Send` permission is granted
- **Network Issues**: Check Azure connectivity and API limits
- **Processing Issues**: Review detailed error logs with Email IDs

## 🔗 **Integration Points**

### **Microsoft Graph API**

- **Base URL**: `https://graph.microsoft.com/v1.0`
- **Authentication**: Client credentials flow
- **Endpoints Used**:
  - `/users/{mailbox}/mailFolders/{folderId}/messages` (fetch)
  - `/users/{mailbox}/messages/{id}/forward` (forward)
  - `/users/{mailbox}/messages/{id}/move` (move)
  - `/users/{mailbox}/mailFolders` (folder management)

### **Tandym Systems**

- **Secrets Management**: Common GPG pattern with `common/secrets_env.py`
- **Logging**: Standard `common/appLogger.py` configuration
- **Error Handling**: Consistent patterns across Tandym services

## 🏆 **Production Features**

### **Reliability**

- ✅ **Single-loop processing** for efficiency
- ✅ **Individual email error handling** for fault tolerance
- ✅ **Comprehensive logging** with Email ID tracking
- ✅ **Automatic folder creation** for organization
- ✅ **Detailed statistics** for monitoring

### **Safety**

- ✅ **Test mode by default** prevents production accidents
- ✅ **Explicit live mode flag** required for production
- ✅ **Failed email preservation** for reprocessing
- ✅ **Detailed error reporting** for troubleshooting

### **Monitoring**

- ✅ **Processing statistics** for operational insights
- ✅ **Failed processing details** with Email IDs
- ✅ **Step-by-step tracking** for debugging
- ✅ **JSON logging** for log aggregation systems

## 📋 **Development Notes**

### **Code Architecture**

- **Single Responsibility**: Each function has one clear purpose
- **Modular Design**: Reusable components for different processing steps
- **Error Isolation**: Individual email failures don't cascade
- **Comprehensive Logging**: Every operation logged with context

### **Performance Optimizations**

- **Client-side filtering**: Avoids server-side API limitations
- **Single API call per email**: Minimizes Graph API requests
- **Batch statistics**: Efficient tracking across all operations
- **Lazy folder creation**: Only creates folders when needed

### **Future Enhancements**

- Additional forwarding destinations
- Custom processing rules based on email content
- Integration with other Tandym processing pipelines
- Enhanced filtering and categorization options

---

## 💡 **Quick Reference Card**

```bash
# Safe development (test mode)
python main.py --count 5
python main.py --move-no-attachments
python main.py --fwd-mercurycv-no-move-2-q

# Production operations (live mode)
python main.py --live-not-a-test --count 50
python main.py --live-not-a-test --move-no-attachments --fwd-mercurycv-move-2-q

# Contact ID processing workflow
python contact_queue_processor.py --count 50
python contact_queue_processor.py --count 100

# Troubleshooting
env | grep GPG_PASS
python tools/test_credentials.py
python main.py --help
python contact_queue_processor.py --help
```

**Remember**: Test mode is the default for safety. Use `--live-not-a-test` only when you want to process ALL emails in production!
