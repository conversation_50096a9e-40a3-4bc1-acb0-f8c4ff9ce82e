#!/usr/bin/env python3
"""
Channel Setup Utility for Dataverse Service Bus Messages

This module provides functionality to create Service Bus queues and topics
based on the application configuration.
"""

import sys
import os
import json
from typing import Dict, Any, List, Optional

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

# Also add current directory to path in case we're running from project root
CURRENT_DIR = os.getcwd()
if CURRENT_DIR not in sys.path:
    sys.path.insert(0, CURRENT_DIR)

from common.appLogger import AppLogger
from azure.servicebus.management import ServiceBusAdministrationClient
from azure.core.exceptions import ResourceNotFoundError, ServiceRequestError, ClientAuthenticationError

class ChannelSetup:
    """
    Utility class for setting up Service Bus channels based on configuration.
    """
    
    def __init__(self, logger: Optional[AppLogger] = None):
        """
        Initialize the channel setup utility.
        
        Args:
            logger: Logger instance for outputting information
        """
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
    
    def check_service_bus_connection(self, connection_string: str) -> Optional[ServiceBusAdministrationClient]:
        """Test if the service bus connection is valid"""
        try:
            client = ServiceBusAdministrationClient.from_connection_string(connection_string)
            # Try to list topics to test the connection
            list(client.list_topics())
            return client
        except (ServiceRequestError, ClientAuthenticationError) as e:
            self.logger.error(f"Failed to connect to Service Bus: {e}")
            return None
    
    def queue_exists(self, client: ServiceBusAdministrationClient, queue_name: str) -> bool:
        """Check if a queue exists"""
        try:
            client.get_queue(queue_name=queue_name)
            return True
        except ResourceNotFoundError:
            return False
        except Exception as e:
            self.logger.error(f"Error checking if queue '{queue_name}' exists: {e}")
            return False
    
    def topic_exists(self, client: ServiceBusAdministrationClient, topic_name: str) -> bool:
        """Check if a topic exists"""
        try:
            client.get_topic(topic_name=topic_name)
            return True
        except ResourceNotFoundError:
            return False
        except Exception as e:
            self.logger.error(f"Error checking if topic '{topic_name}' exists: {e}")
            return False
    
    def subscription_exists(self, client: ServiceBusAdministrationClient, topic_name: str, subscription_name: str) -> bool:
        """Check if a subscription exists"""
        try:
            client.get_subscription(topic_name=topic_name, subscription_name=subscription_name)
            return True
        except ResourceNotFoundError:
            return False
        except Exception as e:
            self.logger.error(f"Error checking if subscription '{subscription_name}' exists on topic '{topic_name}': {e}")
            return False
    
    def create_queue(self, connection_string: str, queue_name: str) -> bool:
        """
        Create a queue if it doesn't exist.
        
        Args:
            connection_string: Service Bus connection string
            queue_name: Name of the queue to create
            
        Returns:
            True if successful, False otherwise
        """
        try:
            admin_client = self.check_service_bus_connection(connection_string)
            if not admin_client:
                self.logger.error("Failed to establish connection to Service Bus.")
                return False
            
            if not self.queue_exists(admin_client, queue_name):
                try:
                    admin_client.create_queue(queue_name=queue_name)
                    self.logger.info(f"Created queue '{queue_name}'")
                except Exception as e:
                    self.logger.error(f"Failed to create queue '{queue_name}': {e}")
                    return False
            else:
                self.logger.info(f"Queue '{queue_name}' already exists.")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating queue: {e}")
            return False
    
    def create_topic_and_subscription(self, connection_string: str, topic_name: str, subscription_name: str = "sandbox-test") -> bool:
        """
        Create a topic and subscription if they don't exist.
        
        Args:
            connection_string: Service Bus connection string
            topic_name: Name of the topic to create
            subscription_name: Name of the subscription to create
            
        Returns:
            True if successful, False otherwise
        """
        try:
            admin_client = self.check_service_bus_connection(connection_string)
            if not admin_client:
                self.logger.error("Failed to establish connection to Service Bus.")
                return False
            
            # Check if topic exists, create if it doesn't
            if not self.topic_exists(admin_client, topic_name):
                try:
                    admin_client.create_topic(topic_name=topic_name)
                    self.logger.info(f"Created topic '{topic_name}'")
                except Exception as e:
                    self.logger.error(f"Failed to create topic '{topic_name}': {e}")
                    return False
            else:
                self.logger.info(f"Topic '{topic_name}' already exists.")
            
            # Check if subscription exists, create if it doesn't
            if not self.subscription_exists(admin_client, topic_name, subscription_name):
                try:
                    admin_client.create_subscription(topic_name=topic_name, subscription_name=subscription_name)
                    self.logger.info(f"Created subscription '{subscription_name}' on topic '{topic_name}'")
                except Exception as e:
                    self.logger.error(f"Failed to create subscription '{subscription_name}' on topic '{topic_name}': {e}")
                    return False
            else:
                self.logger.info(f"Subscription '{subscription_name}' on topic '{topic_name}' already exists.")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating topic and subscription: {e}")
            return False
    
    def setup_channels_from_config(self, connection_string: str, config: Dict[str, Any]) -> bool:
        """
        Set up channels (queues or topics) based on the configuration.
        
        Args:
            connection_string: Service Bus connection string
            config: Application configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            processors = config.get('processors', [])
            if not processors:
                self.logger.warning("No processors configured, skipping channel setup")
                return True
            
            success = True
            
            for processor_config in processors:
                if not processor_config.get('enabled', True):
                    self.logger.info(f"Skipping disabled processor: {processor_config.get('type', 'unknown')}")
                    continue
                
                channel_type = processor_config.get('channel_type', 'queue')
                channel_name = processor_config.get('channel_name')
                processor_type = processor_config.get('type', 'unknown')
                
                if not channel_name:
                    self.logger.error(f"No channel name specified for processor {processor_type}")
                    success = False
                    continue
                
                self.logger.info(f"Setting up {channel_type} '{channel_name}' for processor {processor_type}")
                
                if channel_type.lower() == 'queue':
                    if not self.create_queue(connection_string, channel_name):
                        success = False
                elif channel_type.lower() == 'topic':
                    subscription_name = processor_config.get('subscription_name', 'sandbox-test')
                    if not self.create_topic_and_subscription(connection_string, channel_name, subscription_name):
                        success = False
                else:
                    self.logger.error(f"Unsupported channel type: {channel_type}")
                    success = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error setting up channels from config: {e}")
            return False

def setup_channels(connection_string: str, config_file: Optional[str] = None) -> bool:
    """
    Set up Service Bus channels based on configuration.
    
    Args:
        connection_string: Service Bus connection string
        config_file: Path to configuration file (optional)
        
    Returns:
        True if successful, False otherwise
    """
    setup = ChannelSetup()
    
    # Load configuration
    config = {}
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            setup.logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            setup.logger.error(f"Error loading config file: {e}")
            return False
    else:
        setup.logger.warning("No config file provided, using default configuration")
        # Use default configuration
        config = {
            "processors": [
                {
                    "type": "contact",
                    "enabled": True,
                    "channel_type": "queue",
                    "channel_name": "contact-updates"
                },
                {
                    "type": "crimson_vacancy",
                    "enabled": True,
                    "channel_type": "queue",
                    "channel_name": "vacancy-updates"
                }
            ]
        }
    
    return setup.setup_channels_from_config(connection_string, config)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Set up Service Bus channels")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--connection-string", help="Service Bus connection string")
    
    args = parser.parse_args()
    
    connection_string = args.connection_string or os.getenv('SERVICE_BUS_CONNECTION_STRING')
    if not connection_string:
        print("Error: SERVICE_BUS_CONNECTION_STRING environment variable not set or --connection-string not provided")
        exit(1)
    
    success = setup_channels(connection_string, args.config)
    exit(0 if success else 1) 