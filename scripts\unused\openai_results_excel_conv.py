import json
import pandas as pd
import sys
import os

#directory = "/mnt/incoming/match-results/openai-match-results/"  # Change this to your desired directory


def process_json_to_excel(file_name):
    # Define the directory
    directory = "/mnt/incoming/match-results/openai-match-results/"  # Change this to your desired directory
    file_path = os.path.join(directory, file_name)
    
    # Load JSON data
    with open(file_path, 'r') as file:
        data = json.load(file)

    # Extract vacancy details
    vacancy_refno = data['vacancy']['refno']
    vacancy_id = data['vacancy']['id']

    # Prepare data for DataFrame
    headers = [
         "Candidate Email", "Resume URL", "Candidate ID", "Overall Score", "Job Title Score", 
    "Technical Skills", "Tools & Platforms Score", "Degrees & Certifications", "Soft Skills Score", "Relevant Experience Score", "Industry Experience Score", "Overall Experience Score",
    "thumbs up/down"
    ]
    rows = []

    rows.append(headers)  # Add headers in one row
    

    # Extract candidate details
    for candidate in data['candidates']:
        row = [
            candidate['email'], 
            candidate['resume_url'],
            candidate['contactid'],
            candidate['classification score']['overallscore'],
            candidate['classification score']['jobtitlescore'],
            candidate['classification score']['technical skills'],
            candidate['classification score']['toolsplatformsscore'],
            candidate['classification score']['degrees and certifications'],
            candidate['classification score']['softskillsscore'],
            candidate['classification score']['relevantexperiencescore'],
            candidate['classification score']['industryexperiencescore'],
            candidate['classification score']['overallexperiencescore']
        ]
        rows.append(row)
    
    # Convert to DataFrame
    row.append(candidate.get('thumbs up/down', None))  # Add a placeholder value


    # Convert to DataFrame
    df = pd.DataFrame(rows)
    df.columns = [
    "Candidate Email", "Resume URL", "Candidate ID", "Overall Score", "Job Title Score", 
    "Technical Skills", "Tools & Platforms Score", "Degrees & Certifications", "Soft Skills Score", "Relevant Experience Score", "Industry Experience Score", "Overall Experience Score",
    "thumbs up/down"]
    df["Overall Score"] = pd.to_numeric(df["Overall Score"], errors="coerce")

    df_sorted = df.sort_values(by="Overall Score", ascending=False)

    # Define output file path
    output_file = os.path.join(directory, file_name.replace('.json', '.xlsx'))
    
    # Save to Excel
    #df_sorted.to_excel(output_file, index=False, header=False)


    with pd.ExcelWriter(output_file, engine="xlsxwriter") as writer:
        
        # Create a DataFrame for vacancy details
        vacancy_df = pd.DataFrame({
            "Vacancy Ref No": [vacancy_refno],
            "Vacancy ID": [vacancy_id]
        })

        # Write vacancy details as the first sheet
        vacancy_df.to_excel(writer, sheet_name="Vacancy Details", index=False)

        # Write sorted candidate data as the second sheet
        df_sorted.to_excel(writer, sheet_name="Sorted Candidates", index=False)


    print(f"Excel file '{output_file}' has been created successfully.")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_name = sys.argv[1]
    process_json_to_excel(file_name)