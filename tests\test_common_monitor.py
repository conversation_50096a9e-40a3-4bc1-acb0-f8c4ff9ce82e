#!/usr/bin/env python3
"""
Test script for Common Monitor Runner

This script tests the configuration loading and monitor initialization
without actually starting the monitors.
"""

from catalyst_match.config_helper import get_max_jobs, get_all_max_jobs
from catalyst_match.run_common_monitor import CommonMonitorRunner
from common.db.config_postgres import PostgresEnvironment


def test_config_loading():
    """Test that configuration is loaded correctly."""
    print("=== Testing Configuration Loading ===")
    
    # Test individual job types
    catalyst_jobs = get_max_jobs("catalyst_match")
    why_fit_jobs = get_max_jobs("why_fit")
    
    print(f"Catalyst Match max jobs: {catalyst_jobs}")
    print(f"Why Fit max jobs: {why_fit_jobs}")
    
    # Test getting all max jobs
    all_jobs = get_all_max_jobs()
    print(f"All max jobs: {all_jobs}")
    
    # Verify the values
    assert catalyst_jobs == 1, f"Expected catalyst_match max jobs to be 1, got {catalyst_jobs}"
    assert why_fit_jobs == 3, f"Expected why_fit max jobs to be 3, got {why_fit_jobs}"
    assert all_jobs == {"catalyst_match": 1, "why_fit": 3}, f"Expected all jobs to match, got {all_jobs}"
    
    print("✅ Configuration loading test passed!")


def test_common_monitor_initialization():
    """Test that CommonMonitorRunner initializes correctly."""
    print("\n=== Testing Common Monitor Initialization ===")
    
    # Test with DEV environment
    runner = CommonMonitorRunner(env=PostgresEnvironment.DEV)
    
    # Check that configuration was loaded correctly
    assert runner.catalyst_max_jobs == 1, f"Expected catalyst_max_jobs to be 1, got {runner.catalyst_max_jobs}"
    assert runner.why_fit_max_jobs == 3, f"Expected why_fit_max_jobs to be 3, got {runner.why_fit_max_jobs}"
    
    print(f"✅ CommonMonitorRunner initialized successfully!")
    print(f"   Catalyst max jobs: {runner.catalyst_max_jobs}")
    print(f"   Why Fit max jobs: {runner.why_fit_max_jobs}")
    print(f"   Environment: {runner.env.name}")


def test_status_reporting():
    """Test status reporting functionality."""
    print("\n=== Testing Status Reporting ===")
    
    runner = CommonMonitorRunner(env=PostgresEnvironment.DEV)
    status = runner.get_status()
    
    # Check status structure
    assert "running" in status, "Status should contain 'running'"
    assert "catalyst_monitor" in status, "Status should contain 'catalyst_monitor'"
    assert "job_processor" in status, "Status should contain 'job_processor'"
    
    # Check that monitors are not running initially
    assert not status["running"], "Runner should not be running initially"
    assert not status["catalyst_monitor"]["running"], "Catalyst monitor should not be running initially"
    assert not status["job_processor"]["running"], "Job processor should not be running initially"
    
    print("✅ Status reporting test passed!")
    print(f"   Status: {status}")


def main():
    """Run all tests."""
    print("🧪 Testing Common Monitor Runner")
    print("=" * 50)
    
    try:
        test_config_loading()
        test_common_monitor_initialization()
        test_status_reporting()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
