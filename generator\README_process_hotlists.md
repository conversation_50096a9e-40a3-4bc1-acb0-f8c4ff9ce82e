# Hotlist Table Analyzer

This script analyzes the `mercury_hotlist` table from Dataverse to provide insights about hotlist records.

## Features

- **Complete Data Retrieval**: Fetches all records from the hotlist table (handles pagination for large datasets)
- **Grouping Analysis**: Groups records by `_createdby_value` and lists all `mercury_name` values for each creator
- **Mismatch Detection**: Identifies records where `_createdby_value` and `_owninguser_value` are different
- **Comprehensive Logging**: Detailed logging with optional log file output
- **JSON Export**: Option to save analysis results to a JSON file

## Prerequisites

1. **Environment Variables**: Ensure the appropriate Dataverse environment variables are set:
   - For PROD: `AZURE_DATAVERSE_PROD_TENANT_ID`, `AZURE_DATAVERSE_PROD_CLIENT_ID`, `AZURE_DATAVERSE_PROD_CLIENT_SECRET`
   - For UAT: `AZURE_DATAVERSE_UAT_TENANT_ID`, `AZURE_DATAVERSE_UAT_CLIENT_ID`, `AZURE_DATAVERSE_UAT_CLIENT_SECRET`
   - For SANDBOX: `AZURE_DATAVERSE_SANDBOX_TENANT_ID`, `AZURE_DATAVERSE_SANDBOX_CLIENT_ID`, `AZURE_DATAVERSE_SANDBOX_CLIENT_SECRET`
   - For AZ_APP_VAULT: `AZURE_DATAVERSE_TENANT_ID`, `AZURE_DATAVERSE_CLIENT_ID`, `AZURE_DATAVERSE_CLIENT_SECRET`, `AZURE_DATAVERSE_RESOURCE_URL`

2. **Python Dependencies**: The script uses the existing project dependencies from the codebase.

## Usage

### Basic Usage

```bash
# Run with default settings (PROD environment)
python hotlist_analyzer.py

# Run with specific environment
python hotlist_analyzer.py --env UAT

# Run with output file
python hotlist_analyzer.py --output-file results.json

# Run with log file
python hotlist_analyzer.py --log-file hotlist_analysis.log
```

### Command Line Options

- `--env`: Dataverse environment (SANDBOX, UAT, PROD, AZ_APP_VAULT). Default: PROD
- `--output-file`: Path to save analysis results as JSON
- `--log-file`: Path to save detailed logs

### Examples

```bash
# Analyze PROD environment and save results
python hotlist_analyzer.py --env PROD --output-file prod_hotlist_analysis.json --log-file prod_analysis.log

# Quick analysis of SANDBOX environment
python hotlist_analyzer.py --env SANDBOX

# Analyze UAT with logging
python hotlist_analyzer.py --env UAT --log-file uat_analysis.log
```

## Output

### Console Output

The script provides detailed console output including:

1. **Summary Statistics**:
   - Total number of records
   - Number of unique `_createdby_value` entries
   - Number of records with mismatched `_createdby_value` and `_owninguser_value`

2. **Grouped Analysis**:
   - For each `_createdby_value`, shows:
     - Count of records
     - List of all `mercury_name` values

3. **Mismatch Details**:
   - Detailed list of records where `_createdby_value` ≠ `_owninguser_value`
   - Shows `mercury_hotlistid`, `mercury_name`, and both user values

### JSON Output (if --output-file specified)

The JSON file contains:

```json
{
  "analysis_timestamp": "2025-01-27T10:30:00.123456",
  "results": {
    "total_records": 5000,
    "unique_created_by_values": 150,
    "mismatch_count": 25,
    "created_by_groups": {
      "user-guid-1": {
        "count": 50,
        "mercury_names": ["Hotlist 1", "Hotlist 2", ...]
      }
    },
    "mismatch_records": [
      {
        "mercury_hotlistid": "hotlist-guid-1",
        "mercury_name": "Mismatched Hotlist",
        "_createdby_value": "user-guid-1",
        "_owninguser_value": "user-guid-2"
      }
    ]
  }
}
```

## Error Handling

The script includes comprehensive error handling:

- **Connection Issues**: Logs and handles Dataverse connection failures
- **Authentication Errors**: Handles token acquisition and refresh issues
- **Data Retrieval Errors**: Manages pagination failures and partial data retrieval
- **File I/O Errors**: Handles issues with log and output file creation

## Logging

The script uses the project's `AppLogger` class with the following features:

- **Console Output**: Always logs to stdout for immediate feedback
- **File Logging**: Optional log file with backup mode (creates timestamped backups)
- **Log Levels**: INFO level by default, includes debug information for troubleshooting
- **Structured Format**: Includes timestamps, file/line numbers, and thread information

## Performance Considerations

- **Pagination**: Automatically handles large datasets by using Dataverse's pagination
- **Memory Usage**: Processes records in chunks to manage memory efficiently
- **Network Efficiency**: Uses appropriate page sizes and connection pooling

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify environment variables are set correctly
   - Check that the service principal has appropriate permissions

2. **Connection Timeouts**:
   - The script includes retry logic for transient failures
   - Check network connectivity to Dataverse

3. **Permission Errors**:
   - Ensure the service principal has read access to the `mercury_hotlist` table

### Debug Mode

For troubleshooting, you can modify the logger configuration in the script:

```python
logger_config = {
    "name": "hotlist_analyzer",
    "log_level": "DEBUG",  # Change from INFO to DEBUG
    "log_to_stdout": True,
    "use_json": False
}
```

## Security Notes

- The script uses service principal authentication
- Credentials are loaded from environment variables
- No sensitive data is logged (only GUIDs and names)
- Output files should be stored securely if they contain sensitive information 