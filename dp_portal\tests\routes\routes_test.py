import pytest
from unittest.mock import Mock
from fastapi.testclient import TestClient
from dp_portal.apis.routes import Routes 
from fastapi import FastAPI

@pytest.fixture
def mock_entitlement_service():
    return Mock()

@pytest.fixture
def mock_historical_service():
    return Mock()

@pytest.fixture
def mock_regression_key_service():
    return Mock()
        
@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_postgress_db_connector():
    return Mock()

@pytest.fixture
def test_client(mock_logger, mock_postgress_db_connector, mock_entitlement_service, mock_historical_service, mock_regression_key_service):
    app = FastAPI()
    router_instance = Routes(
        logger=mock_logger,
        postgress_db_connector=mock_postgress_db_connector,
        entitlement_service=mock_entitlement_service,
        historical_service=mock_historical_service,
        regression_key_service=mock_regression_key_service
    )
    
    app.include_router(router_instance.router)
    return TestClient(app, raise_server_exceptions=False)

def test_get_entitlement_success(test_client, mock_entitlement_service):
    # Arrange
    mock_entitlement_service.get_entitlement.return_value = {"entitled": True}
    params = {"email_id": "<EMAIL>", "portal_name": "main"}

    # Act
    response = test_client.get("/api/entitlement", params=params)

    # Assert
    assert response.status_code == 200
    assert response.json() == {"entitled": True}
    mock_entitlement_service.get_entitlement.assert_called_once_with("<EMAIL>", "main")

def test_get_entitlement_missing_email(test_client):
    # Arrange
    params = {"portal_name": "main"}  # Missing required 'email_id'

    # Act
    response = test_client.get("/api/entitlement", params=params)

    # Assert
    assert response.status_code == 422  # FastAPI raises 422 for missing query parameters
    response_json = response.json()
    assert response_json["detail"][0]["loc"] == ["query", "email_id"]
    assert response_json["detail"][0]["msg"] == "Field required"
    
    
def test_get_entitlement_missing_portal_name(test_client):
    # Act
    response = test_client.get("/api/entitlement", params={"email_id": "<EMAIL>"})

    # Assert
    assert response.status_code == 422  # FastAPI validation error
    response_json = response.json()
    assert response_json["detail"][0]["loc"] == ["query", "portal_name"]
    assert response_json["detail"][0]["msg"] == "Field required"
    assert response_json["detail"][0]["type"] == "missing"

def test_get_entitlement_service_exception(test_client, mock_entitlement_service):
    # Arrange
    mock_entitlement_service.get_entitlement.side_effect = Exception("Service error")
    params = {"email_id": "<EMAIL>", "portal_name": "main"}

    # Act
    response = test_client.get("/api/entitlement", params=params)

    # Assert
    assert response.status_code == 500  # Unhandled exception returns 500
    assert "Internal Server Error" in response.text    
    
def test_add_historical_data_success(test_client, mock_historical_service):
    # Arrange
    mock_historical_service.insert_historical_log.return_value = {
        "message": "Historical log inserted successfully"
    }
    params = {
        "email_id": "<EMAIL>",
        "portal_name": "main",
        "feature": "Search"
    }
    payload = {"source": "search_engine", "duration": 150}

    # Act
    response = test_client.post("/api/add_historical_data", params=params, json=payload)

    # Assert
    assert response.status_code == 200
    assert response.json() == {
        "error": False,
        "code": "TR_HL_01",
        "message": "Successful"
    }
    mock_historical_service.insert_historical_log.assert_called_once_with(
        "<EMAIL>", "main", "Search", payload
    )

def test_add_historical_data_failure(test_client, mock_historical_service):
    # Arrange
    mock_historical_service.insert_historical_log.return_value = {
        "message": "Some error inserting log"
    }
    params = {
        "email_id": "<EMAIL>",
        "portal_name": "main",
        "feature": "Search"
    }
    payload = {"error_code": "123"}

    # Act
    response = test_client.post("/api/add_historical_data", params=params, json=payload)

    # Assert
    assert response.status_code == 200
    assert response.json() == {
        "error": True,
        "code": "TR_HL_ERR",
        "message": "Error inserting historical log"
    }
    mock_historical_service.insert_historical_log.assert_called_once()


def test_add_historical_data_with_empty_portal(test_client, mock_historical_service):
    # Arrange
    mock_historical_service.insert_historical_log.return_value = {
        "message": "Historical log inserted successfully"
    }
    params = {
        "email_id": "<EMAIL>",
        "portal_name": "",
        "feature": "Search"
    }
    payload = {"meta": "test"}

    # Act
    response = test_client.post("/api/add_historical_data", params=params, json=payload)

    # Assert
    assert response.status_code == 200
    assert response.json()["error"] is False
    mock_historical_service.insert_historical_log.assert_called_once_with(
        "<EMAIL>", None, "Search", payload
    )


def test_add_historical_data_exception(test_client, mock_historical_service):
    # Arrange
    mock_historical_service.insert_historical_log.side_effect = Exception("DB failure")
    params = {
        "email_id": "<EMAIL>",
        "portal_name": "main",
        "feature": "Search"
    }
    payload = {"meta": "testing exception"}

    # Act
    response = test_client.post("/api/add_historical_data", params=params, json=payload)

    # Assert
    # If no try/except is used in your route method, it will return 500 by default
    assert response.status_code == 500
    
