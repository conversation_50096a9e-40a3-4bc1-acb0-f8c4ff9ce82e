#!/usr/bin/env python3
"""
Simple Real Email Test for AppLogger Email Notifications

This script tests the core functionality: calling logger.error() sends an email.
"""

import os
import sys

# Add project root to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.appLogger import App<PERSON>ogger
from common.secrets_env import load_secrets_env_variables
from utils.email.sendgrid_helper import SendGridEmailHelper

def test_sendgrid_direct():
    """
    Test SendGrid directly to see the actual API response.
    """
    print("Testing SendGrid API Directly")
    print("=" * 50)
    
    # Load secrets and environment variables
    try:
        load_secrets_env_variables()
        print("✓ Secrets loaded successfully")
    except Exception as e:
        print(f"⚠ Warning: Could not load secrets: {e}")
        print("Continuing with existing environment variables...")
    
    # Check prerequisites
    sendgrid_key = os.getenv("SENDGRID_API_KEY")
    if not sendgrid_key:
        print("❌ SENDGRID_API_KEY environment variable not set!")
        print("Please set it: export SENDGRID_API_KEY='your_api_key'")
        return False
    
    print(f"✓ SENDGRID_API_KEY found (length: {len(sendgrid_key)})")
    print(f"✓ API Key starts with: {sendgrid_key[:10]}...")
    
    # Create a simple logger
    logger_config = {
        "name": "sendgrid_test",
        "log_level": "INFO",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Initialize SendGrid helper
    sendgrid_helper = SendGridEmailHelper(logger=logger)
    
    # Test simple email
    print("\nSending test email via SendGrid...")
    result = sendgrid_helper.send_email(
        subject="TEST: SendGrid Direct Test",
        html_content="<h1>Test Email</h1><p>This is a test email sent directly via SendGrid.</p>",
        plain_text="Test Email\n\nThis is a test email sent directly via SendGrid.",
        to_emails=["<EMAIL>"]
    )
    
    print("\n" + "=" * 50)
    print("SendGrid API Response:")
    print("=" * 50)
    print(f"Result: {result}")
    
    if result.get('error'):
        print(f"❌ Error: {result.get('error')}")
        return False
    elif result.get('status_code'):
        print(f"✓ Status Code: {result.get('status_code')}")
        print(f"✓ Response Body: {result.get('body', 'No body')}")
        print(f"✓ Response Headers: {result.get('headers', 'No headers')}")
        
        if result.get('status_code') == 202:
            print("✓ Email accepted by SendGrid (status 202)")
        else:
            print(f"⚠ Unexpected status code: {result.get('status_code')}")
    else:
        print("⚠ No status code in response")
    
    return True

def test_logger_error_sends_email():
    """
    Simple test: call logger.error() and verify it sends an email.
    """
    print("\nTesting AppLogger Error Email Notification")
    print("=" * 50)
    
    # Load secrets and environment variables
    try:
        load_secrets_env_variables()
        print("✓ Secrets loaded successfully")
    except Exception as e:
        print(f"⚠ Warning: Could not load secrets: {e}")
        print("Continuing with existing environment variables...")
    
    # Check prerequisites
    if not os.getenv("SENDGRID_API_KEY"):
        print("❌ SENDGRID_API_KEY environment variable not set!")
        print("Please set it: export SENDGRID_API_KEY='your_api_key'")
        return False
    
    # Create logger with email notifications enabled
    config = {
        "name": "simple_email_test",
        "log_level": "ERROR",
        "log_to_stdout": True,
        "email_notifications": {
            "enabled": True,
            "to_emails": ["<EMAIL>"],
            "subject_prefix": "LOGGER ERROR TEST"
        }
    }
    
    logger = AppLogger(config)
    
    print("✓ Logger configured with email notifications")
    print(f"✓ Target email: <EMAIL>")
    print(f"✓ Subject prefix: LOGGER ERROR TEST")
    
    # Test the core functionality: logger.error() sends email
    print("\nCalling logger.error() - this should send an email...")
    logger.error("This is a test error message from AppLogger email notification system")
    
    # Test with exception to demonstrate enhanced error information
    print("\nCalling logger.error() with exception - this should send an email with traceback...")
    try:
        # Intentionally cause an exception
        result = 1 / 0
    except Exception as e:
        logger.error("Division by zero error occurred during testing", exc_info=True)
    
    print("\n✓ Error logged. Check your email inbox!")
    print("You should receive an email with subject: 'LOGGER ERROR TEST - simple_email_test'")
    
    return True

def main():
    """Main test function."""
    print("Simple Real Email Test for AppLogger")
    print("=" * 60)
    print("This test will send <NAME_EMAIL>")
    print("=" * 60)
    
    # Check if user wants to proceed
    response = input("\nDo you want to proceed with sending real emails? (yes/no): ").lower().strip()
    if response not in ['yes', 'y', '']:  # Empty string (just Enter) is treated as yes
        print("Test cancelled.")
        return
    
    print("\nStarting tests...")
    
    # Run SendGrid direct test first
    sendgrid_success = test_sendgrid_direct()
    
    # Run AppLogger test
    logger_success = test_logger_error_sends_email()
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"SendGrid Direct Test: {'✓ PASSED' if sendgrid_success else '❌ FAILED'}")
    print(f"Logger Error Email: {'✓ PASSED' if logger_success else '❌ FAILED'}")
    
    if sendgrid_success and logger_success:
        print("\n🎉 All tests completed! Check your email inbox.")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
    
    print("\nNote: Emails were <NAME_EMAIL>")
    print("If you don't receive emails, check:")
    print("1. SendGrid API key is valid")
    print("2. Email address is correct")
    print("3. Check spam/junk folder")
    print("4. Check SendGrid dashboard for delivery status")

if __name__ == "__main__":
    main()
