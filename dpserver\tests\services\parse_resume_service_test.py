import pytest
from unittest.mock import Mock, patch
from dpserver.services.parse_resume_service import ParseResumeService

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_cursor():
    return Mock()

@pytest.fixture
def mock_connection(mock_cursor):
    conn = Mock()
    conn.cursor.return_value = mock_cursor
    return conn

@pytest.fixture
def mock_db_connector(mock_connection):
    connector = Mock()
    connector.schema = "test_schema"
    connector.connection = mock_connection
    return connector

@pytest.fixture
def parse_resume_service(mock_logger, mock_db_connector):
    return ParseResumeService(
        logger=mock_logger,
        db_connector=mock_db_connector,
        sp_env="uat", 
        db_env="dev"
    )

def test_get_candidate_success(parse_resume_service, mock_cursor, mock_db_connector):
    contact_id = "1234"
    candidate_id = "candidate-uuid"
    resume_data = {"name": "<PERSON>"}

    mock_cursor.fetchone.return_value = (candidate_id, resume_data)

    result = parse_resume_service.get_candidate(contact_id)

    mock_db_connector.connect.assert_called_once()
    mock_cursor.execute.assert_called_once()
    
    query_used = mock_cursor.execute.call_args[0][0]
    assert "SELECT id, resume_data" in query_used
    assert "FROM test_schema.candidates" in query_used
    assert "WHERE contact_id = %s" in query_used

    assert mock_cursor.execute.call_args[0][1] == (contact_id,)

    mock_cursor.close.assert_called_once()

    assert result == {
        "candidate_id": candidate_id,
        "status": 0,
        "description": "Success",
        "candidate": resume_data
    }

def test_get_candidate_exception(parse_resume_service, mock_cursor, mock_db_connector):
    contact_id = "error-id"
    mock_cursor.execute.side_effect = Exception("DB Error")

    result = parse_resume_service.get_candidate(contact_id)

    mock_db_connector.connect.assert_called_once()
    parse_resume_service.logger.error.assert_called_once()
    parse_resume_service.db_connector.connection.rollback.assert_called_once()
    mock_cursor.close.assert_not_called()

    assert result == {
        "candidate_id": None,
        "status": 500,
        "description": "Error fetching candidate."
    }