{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/serverActions.ts"], "sourcesContent": ["// api/serverActions.ts\r\n\"use server\";\r\nimport \"server-only\";\r\nimport { cookies } from \"next/headers\";\r\nimport { API_ENDPOINTS } from \"./config\";\r\nimport { postData } from \"./post\";\r\nimport { updateData } from \"./put\";\r\nimport { EntitlementResponse } from \"./types\";\r\n\r\n// Golden rule: Never return Request, Response, NextRequest, NextResponse, IncomingMessage, ClientRequest, Socket, or raw Axios error objects\r\ntype Ok<T>  = { data: { success: true; data?: T; message?: string } };\r\ntype Err    = { data: { success: false; message: string; code?: number; details?: unknown } };\r\ntype Result<T> = Ok<T> | Err;\r\n\r\nfunction errMsg(e: unknown) {\r\n  // strings only; never return the raw error object\r\n  try {\r\n    if (e && typeof e === \"object\" && \"message\" in (e as any)) return String((e as any).message);\r\n    return String(e);\r\n  } catch { return \"Unknown error\"; }\r\n}\r\n\r\nexport async function fetchAllSubCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.subCategories);\r\n    const data = await response.json();\r\n    return data?.subcategories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function deleteAttributeTitleById(attributeId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.deleteAttribute.replace(\r\n        \":attribute_id\",\r\n        attributeId.toString()\r\n      ),\r\n      { method: \"DELETE\" }\r\n    );\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchAttributeBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchAttributesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateAttributeWeight(\r\n  subCategoryId: number,\r\n  updatedData: any\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateAttributeWeight.replace(\r\n    \":sub_category_id\",\r\n    subCategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await postData(url, updatedData);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Attribute weight updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryOfAttribute(\r\n  attributeId: number,\r\n  data: { new_subcategory_id: number }\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update attribute subcategory\");\r\n  }\r\n}\r\n\r\nexport async function updateAttributeApprovalStatus(\r\n  attributeId: number,\r\n  data: { is_approved: boolean }\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Attribute approval status updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function fetchWeightsBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchWeightsBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacancies() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacancies);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacanciesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getVacancyByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`Error while fetching vacancy with ${vacancyId}`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function fetchCandidatesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCandidatesByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateId(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\ninterface RecruiterPostReview {\r\n  hiring_decision: string;\r\n  review_message: string;\r\n  candidate_contact_id: string;\r\n  recruiter_email: string;\r\n}\r\n\r\nexport async function updateRecruiterReview(data: any) {\r\n  const url = API_ENDPOINTS.updateCandidatesReviewData;\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function updateWhyFit(data: any) {\r\n  try {\r\n    const url = API_ENDPOINTS.updateWhyFitData;\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error while updating whyfit: \", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function fetchEntitlements(\r\n  email_id: string,\r\n  saveHistoryLogs?: boolean\r\n): Promise<EntitlementResponse | false> {\r\n  try {\r\n    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === \"true\";\r\n    if (!isEntitlementEnabled) {\r\n      const response = {\r\n        error: false,\r\n        code: \"TR_01\",\r\n        message: \"Successful\",\r\n        entitlement: {\r\n          Work_force_Index: true,\r\n          Sub_Catregory: true,\r\n          Vacancy: true,\r\n          Search_Match: true,\r\n          Sc_Score_Config: true,\r\n          candidate_tunning_page: true,\r\n          Shorting_Listing: true,\r\n          Historical_Logs: true,\r\n          Regenerate: true,\r\n          Update_Availability: true,\r\n        },\r\n      };\r\n      if (saveHistoryLogs) {\r\n        // Save entitlement in cookies\r\n        const cookieStore = await cookies();\r\n        cookieStore.set(\"entitlement\", JSON.stringify(response.entitlement), {\r\n          secure: true,\r\n        });\r\n      }\r\n      return response;\r\n    }\r\n    const portal_name = \"recruiter\";\r\n    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(\r\n      email_id\r\n    )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n    const response = await fetch(url);\r\n    if (!response.ok) {\r\n      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);\r\n    }\r\n\r\n    const data: EntitlementResponse = await response.json();\r\n    if (saveHistoryLogs) {\r\n      // Save entitlement in cookies\r\n      (await cookies()).set(\"entitlement\", JSON.stringify(data.entitlement), {\r\n        secure: true,\r\n      });\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching entitlement data:\", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function getAllSubcategoryWeightConfigs() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);\r\n    const data = await response.json();\r\n    return data?.subcategory_weight_configs || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryWeightConfig(\r\n  subcategoryId: number,\r\n  data: any\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(\r\n    \":subcategory_id\",\r\n    subcategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Subcategory weight config updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function postVacanciesShortlisted(data: any) {\r\n  const url = API_ENDPOINTS.vacanciesShortlisted;\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"error::\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function postHistoricalLogs(\r\n  email: string,\r\n  portalName: string,\r\n  featureName: string,\r\n  metaData: any = null\r\n) {\r\n  const url = API_ENDPOINTS.saveHistoryLogs\r\n    .replace(\"{email_id}\", email)\r\n    .replace(\"{portal_name}\", portalName)\r\n    .replace(\"{feature}\", featureName);\r\n\r\n  try {\r\n    const response = await postData(url, metaData);\r\n\r\n    if (!response || response.status >= 400) {\r\n      console.error(\r\n        `[postHistoricalLogs] Failed request: ${response.status} ${response.statusText}`\r\n      );\r\n      return null;\r\n    }\r\n\r\n    if (!response.data) {\r\n      console.warn(`[postHistoricalLogs] Response missing data:`, response);\r\n      throw new Error(`No data in response for historical logs.`);\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`[postHistoricalLogs] Exception for ${email}:`, error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport async function fetchCatalystMatchStatus(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCatalystMatchStatus.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    if (!response.ok) {\r\n      const errorData = await response.json(); // Still extract JSON body\r\n      const error = {\r\n        error: true,\r\n        status: response.status,\r\n        message: errorData.detail.error.message || \"Unknown error\",\r\n      };\r\n      throw new Error(JSON.stringify(error));\r\n    }\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    if (\r\n      typeof error === \"object\" &&\r\n      error !== null &&\r\n      \"message\" in error &&\r\n      typeof (error as any).message === \"string\"\r\n    ) {\r\n      throw new Error(\r\n        JSON.stringify({\r\n          error: true,\r\n          status: JSON.parse((error as any).message).status || 500,\r\n          message:\r\n            JSON.parse((error as any).message).message ||\r\n            \"Failed to fetch Catalyst Match Status\",\r\n        })\r\n      );\r\n    }\r\n  }\r\n}\r\n\r\nexport async function regenerateCatalystMatch(vacancy_id: string, data: any) {\r\n  const url = API_ENDPOINTS.regenerateCatalystMatch.replace(\r\n    \":vacancy_id\",\r\n    vacancy_id\r\n  );\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function submitCatalystMatchForm(payload: any): Promise<Result<null>> {\r\n  try {\r\n    const resp = await fetch(API_ENDPOINTS.submitCatalystMatch, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to submit\", code: resp.status } };\r\n    }\r\n    return { data: { success: true, message: body?.message || \"Saved\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function getVacancyTemplate(\r\n  { vacancy_id }: { vacancy_id: string }\r\n): Promise<Result<any>> {\r\n  try {\r\n    const resp = await fetch(API_ENDPOINTS.getVacancyTemplate, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify({ vacancy_id }),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to load template\", code: resp.status } };\r\n    }\r\n\r\n    const safe = JSON.parse(JSON.stringify(body?.data ?? body));\r\n    return { data: { success: true, data: safe, message: body?.message || \"Template retrieved successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\n\r\n\r\nexport async function saveVacancyTemplate(\r\n  templateData: any, \r\n  regenerate: boolean = false\r\n): Promise<Result<any>> {\r\n  try {\r\n    // Convert camelCase to snake_case and structure the payload correctly\r\n    const templatePayload = {\r\n      vacancy_id: templateData.vacancy_id,\r\n      required_skills: templateData.requiredSkills || [],\r\n      preferred_skills: templateData.preferredSkills || [],\r\n      recency_must_have_skills: templateData.recencyMustHaveSkills || \"Current +1\",\r\n      additional_job_titles: templateData.additionalJobTitles || [],\r\n      city: templateData.city || \"\",\r\n      state: templateData.state || \"\",\r\n      miles: templateData.miles || \"50\",\r\n      years_experience: templateData.yearsExperience || \"\",\r\n      degrees: templateData.degrees || [],\r\n      certifications_licenses: templateData.certificationsLicenses || [],\r\n      industry: templateData.industry || \"No\",\r\n      confidential: templateData.confidential || \"No\",\r\n      enable_catalyst_match: templateData.enableCatalystMatch || false,\r\n    };\r\n\r\n    const payload = {\r\n      template_data: templatePayload,\r\n      regenerate: regenerate\r\n    };\r\n\r\n    const resp = await fetch(API_ENDPOINTS.saveVacancyTemplate, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to save template\", code: resp.status } };\r\n    }\r\n\r\n    const safe = JSON.parse(JSON.stringify(body?.data ?? body));\r\n    return { data: { success: true, data: safe, message: body?.message || \"Template saved successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsBD;IAWA;IAgBA;IAgBA;IAkBA;IAiBA;IAkBA;IAgBA;IAWA;IAgBA;IAgBA;IAuBA;IAWA;IAUA;IAwDA;IAWA;IAkBA;IAWA;IAiCA;IAuCA;IAaA;IAmBA;IAyBA"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/catalyst-match/IframeAuthGate.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { fetchEntitlements } from \"@/api/serverActions\";\r\n\r\ninterface IframeAuthGateProps {\r\n  children: React.ReactNode;\r\n  onAuthComplete?: (isAuthenticated: boolean) => void; // callback\r\n}\r\n\r\nexport default function IframeAuthGate({\r\n  children,\r\n  onAuthComplete,\r\n}: IframeAuthGateProps) {\r\n  const { data: session, status } = useSession();\r\n  const [authComplete, setAuthComplete] = useState(false);\r\n  const popupRef = useRef<Window | null>(null);\r\n  const emailId = session?.user?.email || null;\r\n  const isIframe = typeof window !== \"undefined\" && window.self !== window.top;\r\n  localStorage?.setItem(\"email\", session?.user?.email || \"null\");\r\n\r\n  useEffect(() => {\r\n    const handler = async (event: MessageEvent) => {\r\n      console.log(\"Received message event:\", event);\r\n      if (\r\n        event.data === \"authcomplete\" ||\r\n        event.data?.type === \"authcomplete\"\r\n      ) {\r\n        onAuthComplete?.(true);\r\n        setAuthComplete(true);\r\n        return <>{children}</>;\r\n        // Try fetching session manually\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"message\", handler);\r\n    return () => window.removeEventListener(\"message\", handler);\r\n  }, [onAuthComplete]);\r\n  useEffect(() => {\r\n    window.parent.frames.postMessage(\r\n      {\r\n        status: status,\r\n        email: emailId,\r\n        timestamp: Date.now(),\r\n      },\r\n      \"*\"\r\n    );\r\n\r\n    if (status !== \"authenticated\") {\r\n      setAuthComplete(false);\r\n    }\r\n    if (status === \"authenticated\") {\r\n      onAuthComplete?.(true);\r\n      setAuthComplete(true);\r\n      const userEmail = emailId || session?.user?.email || \"\";\r\n\r\n      fetchEntitlements(userEmail)\r\n        .then((res) => {\r\n          if (res === false) {\r\n            console.error(\"No entitlement response\");\r\n            return;\r\n          }\r\n\r\n          if (res.error) {\r\n            console.error(\"Entitlement error:\", res.error);\r\n            return;\r\n          }\r\n          localStorage.setItem(\"emailId\", userEmail);\r\n          setAuthComplete(true);\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Failed to fetch entitlements:\", error);\r\n        })\r\n        .finally(() => {});\r\n      // Send message to parent window\r\n    }\r\n  }, [status, onAuthComplete]);\r\n\r\n  useEffect(() => {\r\n    if (authComplete && isIframe) {\r\n      // Notify parent window that auth is complete\r\n      window.parent.postMessage({ type: \"authComplete\" }, \"*\");\r\n      window.parent.postMessage({ type: \"authenticated\" }, \"*\");\r\n      setAuthComplete(true);\r\n      onAuthComplete?.(true);\r\n    }\r\n  }, [authComplete, isIframe]);\r\n\r\n  const openLoginPopup = () => {\r\n    const width = 600;\r\n    const height = 700;\r\n    const left = window.innerWidth / 2 - width / 2;\r\n    const top = window.innerHeight / 2 - height / 2;\r\n\r\n    popupRef.current = window.open(\r\n      \"/api/auth/signin/azure-ad?callbackUrl=/authcomplete\",\r\n      \"MicrosoftLogin\",\r\n      `width=${width},height=${height},left=${left},top=${top}`\r\n    );\r\n  };\r\n\r\n  if (!authComplete) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-screen bg-white text-center\">\r\n        <h1 className=\"text-2xl font-bold mb-4\">\r\n          For your security, please authenticate before proceeding.\r\n        </h1>\r\n        <button\r\n          onClick={openLoginPopup}\r\n          className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded\"\r\n        >\r\n          Secure this Session\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWe,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,EACM;;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACvC,MAAM,UAAU,SAAS,MAAM,SAAS;IACxC,MAAM,WAAW,aAAkB,eAAe,OAAO,IAAI,KAAK,OAAO,GAAG;IAC5E,cAAc,QAAQ,SAAS,SAAS,MAAM,SAAS;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;oDAAU,OAAO;oBACrB,QAAQ,GAAG,CAAC,2BAA2B;oBACvC,IACE,MAAM,IAAI,KAAK,kBACf,MAAM,IAAI,EAAE,SAAS,gBACrB;wBACA,iBAAiB;wBACjB,gBAAgB;wBAChB,qBAAO;sCAAG;;oBACV,gCAAgC;oBAClC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;mCAAG;QAAC;KAAe;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,CAC9B;gBACE,QAAQ;gBACR,OAAO;gBACP,WAAW,KAAK,GAAG;YACrB,GACA;YAGF,IAAI,WAAW,iBAAiB;gBAC9B,gBAAgB;YAClB;YACA,IAAI,WAAW,iBAAiB;gBAC9B,iBAAiB;gBACjB,gBAAgB;gBAChB,MAAM,YAAY,WAAW,SAAS,MAAM,SAAS;gBAErD,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,WACf,IAAI;gDAAC,CAAC;wBACL,IAAI,QAAQ,OAAO;4BACjB,QAAQ,KAAK,CAAC;4BACd;wBACF;wBAEA,IAAI,IAAI,KAAK,EAAE;4BACb,QAAQ,KAAK,CAAC,sBAAsB,IAAI,KAAK;4BAC7C;wBACF;wBACA,aAAa,OAAO,CAAC,WAAW;wBAChC,gBAAgB;oBAClB;+CACC,KAAK;gDAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,iCAAiC;oBACjD;+CACC,OAAO;gDAAC,KAAO;;YAClB,gCAAgC;YAClC;QACF;mCAAG;QAAC;QAAQ;KAAe;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,gBAAgB,UAAU;gBAC5B,6CAA6C;gBAC7C,OAAO,MAAM,CAAC,WAAW,CAAC;oBAAE,MAAM;gBAAe,GAAG;gBACpD,OAAO,MAAM,CAAC,WAAW,CAAC;oBAAE,MAAM;gBAAgB,GAAG;gBACrD,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;mCAAG;QAAC;QAAc;KAAS;IAE3B,MAAM,iBAAiB;QACrB,MAAM,QAAQ;QACd,MAAM,SAAS;QACf,MAAM,OAAO,OAAO,UAAU,GAAG,IAAI,QAAQ;QAC7C,MAAM,MAAM,OAAO,WAAW,GAAG,IAAI,SAAS;QAE9C,SAAS,OAAO,GAAG,OAAO,IAAI,CAC5B,uDACA,kBACA,CAAC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK;IAE7D;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAGxC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBAAO;kBAAG;;AACZ;GA5GwB;;QAIY,iJAAA,CAAA,aAAU;;;KAJtB"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Loading.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Loading = ({ height }: { height?: string }) => {\r\n  return (\r\n    <div\r\n      className={`flex justify-center items-center ${\r\n        height ? height : \"h-[100vh]\"\r\n      } w-full`}\r\n    >\r\n      <svg\r\n        className=\"ml-2 mt-0.5 size-10 animate-spin\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n      >\r\n        <circle\r\n          className=\"opacity-25\"\r\n          cx=\"12\"\r\n          cy=\"12\"\r\n          r=\"10\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"4\"\r\n        ></circle>\r\n        <path\r\n          className=\"opacity-75\"\r\n          fill=\"currentColor\"\r\n          d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n        ></path>\r\n      </svg>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAuB;IAC9C,qBACE,6LAAC;QACC,WAAW,CAAC,iCAAiC,EAC3C,SAAS,SAAS,YACnB,OAAO,CAAC;kBAET,cAAA,6LAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;;;;;;AAKZ;KA7BM;uCA+BS"}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/input.tsx"], "sourcesContent": ["import { cn } from \"@/library/utils\";\r\nimport * as React from \"react\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/library/utils';\r\n\r\nconst buttonVariants = cva(\r\n  'inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primaryButton text-primaryButton-foreground shadow hover:bg-primaryButton/90 active:bg-primaryButton/80',\r\n        destructive:\r\n          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',\r\n        outline:\r\n          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2',\r\n        sm: 'h-8 rounded-md px-3 text-xs',\r\n        lg: 'h-10 rounded-md px-8',\r\n        icon: 'h-9 w-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : 'button';\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = 'Button';\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,sRACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/VacancyDetails.tsx"], "sourcesContent": ["import { X } from \"lucide-react\";\r\nimport React from \"react\";\r\nimport { Vacancy } from \"../CandidateTable/helper\";\r\nimport { Button } from \"../ui/button\";\r\n\r\nconst SkillBadge = ({ skill, weight }: { skill: string; weight: string }) => {\r\n  const getBadgeStyle = (weight: string) => {\r\n    switch (weight) {\r\n      case \"high\":\r\n        return \"bg-green-200/50 text-green-800 border border-green-300 shadow-md\";\r\n      case \"medium\":\r\n        return \"bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md\";\r\n      case \"normal\":\r\n        return \"bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md\";\r\n      case \"low\":\r\n        return \"bg-red-200/50 text-red-800 border border-red-300 shadow-md\";\r\n      default:\r\n        return \"bg-gray-200 text-gray-700 border border-gray-300\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <span\r\n      className={`px-2 py-0.5 rounded-full text-xs font-semibold flex items-center gap-1 ${getBadgeStyle(\r\n        weight\r\n      )}`}\r\n    >\r\n      <span className=\"text-[12px] capitalize\">{skill}</span>\r\n      <span className=\"text-[10px] opacity-80 font-semibold\">({weight})</span>\r\n    </span>\r\n  );\r\n};\r\n\r\ninterface VacancyDetailsProps {\r\n  vacancy: Vacancy | null;\r\n  setActiveVacancy: (vacancy: Vacancy | null) => void;\r\n  mercuryPortal?: boolean;\r\n}\r\n\r\nexport default function VacancyDetails({\r\n  vacancy,\r\n  setActiveVacancy,\r\n  mercuryPortal,\r\n}: VacancyDetailsProps) {\r\n  const isNotEmptyArray = (arr?: any[]) => Array.isArray(arr) && arr.length > 0;\r\n  if (!vacancy) return null;\r\n\r\n  const SkillSection = ({ title, items }: { title: string; items?: any[] }) => {\r\n    if (!isNotEmptyArray(items)) return null;\r\n\r\n    return (\r\n      <div className=\"mb-3\">\r\n        <h4 className=\"text-lg font-semibold text-gray-800 mb-1\">{title}</h4>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {items?.map((skill, index) => (\r\n            <SkillBadge\r\n              key={index}\r\n              skill={typeof skill?.name === \"string\" ? skill?.name : \"Unknown\"}\r\n              weight={\r\n                typeof skill?.weight === \"string\" ? skill?.weight : \"normal\"\r\n              }\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const stringOrArray = (value: any) => {\r\n    if (value === null || value === undefined) {\r\n      return \"N/A\";\r\n    } else if (Array.isArray(value)) {\r\n      return value.length ? value.join(\", \") : \"N/A\";\r\n    } else if (typeof value === \"string\" && value.trim() !== \"\") {\r\n      return value;\r\n    } else {\r\n      return \"N/A\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      onClick={(e: React.MouseEvent<HTMLElement>) => {\r\n        e.stopPropagation();\r\n      }}\r\n      className={`absolute w-[55%] left-[calc((100vw-900px)/2)] ${\r\n        mercuryPortal ? \"top-[5%]\" : \"top-[17%]\"\r\n      } bg-white shadow-lg border p-4 text-xs rounded-md z-[999] p-5`}\r\n    >\r\n      {/* Close Button */}\r\n      <Button\r\n        className=\"absolute top-3.5 right-3 text-gray-600 hover:text-black\"\r\n        variant=\"ghost\"\r\n        onClick={() => setActiveVacancy(null)}\r\n      >\r\n        <X size={18} />\r\n      </Button>\r\n\r\n      <h3 className=\"text-base font-bold text-gray-800 mb-3 border-b pb-2\">\r\n        Vacancy Details\r\n      </h3>\r\n\r\n      <div className=\"max-h-[70vh] overflow-y-auto\">\r\n        <div className=\"mb-3\">\r\n          <p>\r\n            <strong className=\"text-gray-800 text-md\">Ref No:</strong>{\" \"}\r\n            <span className=\"text-gray-800\">{vacancy?.refno}</span>\r\n          </p>\r\n        </div>\r\n        <div className=\"mb-3\">\r\n          <p>\r\n            <strong className=\"text-gray-800 text-md\">Vancancy Id:</strong>{\" \"}\r\n            <span className=\"text-gray-800\">\r\n              {vacancy?.vacancy_id ? vacancy?.vacancy_id : \"N/A\"}\r\n            </span>\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800\">Job Title</h4>\r\n          <p className=\"text-gray-800\">\r\n            {stringOrArray(vacancy?.vacancy_data?.[\"job title\"])}\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800\">Location</h4>\r\n          <p className=\"text-gray-800\">\r\n            {Array.isArray(vacancy?.vacancy_data?.job_template?.job_location) &&\r\n            vacancy.vacancy_data.job_template.job_location.length\r\n              ? vacancy.vacancy_data.job_template.job_location\r\n                  .map((loc: any) =>\r\n                    [loc?.city, loc?.state].filter(Boolean).join(\", \")\r\n                  )\r\n                  .join(\" | \")\r\n              : \"N/A\"}\r\n          </p>\r\n        </div>\r\n\r\n        <SkillSection\r\n          title=\"Soft Skills\"\r\n          items={vacancy?.vacancy_data?.[\"soft skills\"]}\r\n        />\r\n        <SkillSection\r\n          title=\"Technical Skills\"\r\n          items={vacancy?.vacancy_data?.[\"technical skills\"]}\r\n        />\r\n        <SkillSection\r\n          title=\"Tools & Platforms\"\r\n          items={vacancy?.vacancy_data?.[\"tools and platforms\"]}\r\n        />\r\n\r\n        {/* Degrees & Certifications */}\r\n        {vacancy?.vacancy_data?.[\"degrees and certifications\"]?.length ? (\r\n          <div className=\"mb-3\">\r\n            <h4 className=\"text-lg font-semibold text-gray-700 mb-3\">\r\n              Degrees & Certifications\r\n            </h4>\r\n            <ul className=\"text-gray-800 list-disc list-inside\">\r\n              {vacancy.vacancy_data[\"degrees and certifications\"].map(\r\n                (degree, index) => (\r\n                  <li key={index} className=\"text-xs capitalize\">\r\n                    {degree?.name || \"N/A\"}\r\n                  </li>\r\n                )\r\n              )}\r\n            </ul>\r\n          </div>\r\n        ) : null}\r\n\r\n        {/* Years Of Experience */}\r\n        {vacancy?.vacancy_data?.[\"years of experience\"]?.length ? (\r\n          <div className=\"mb-3\">\r\n            <h4 className=\"text-lg font-semibold text-gray-700 mb-3\">\r\n              Relavant Years Of Experience\r\n            </h4>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {vacancy?.vacancy_data?.[\"years of experience\"]\r\n                ? vacancy?.vacancy_data?.[\"years of experience\"].map(\r\n                    (exp, index) => (\r\n                      <SkillBadge\r\n                        key={index}\r\n                        skill={exp?.years || \"Unknown\"}\r\n                        weight={exp?.weight || \"normal\"}\r\n                      />\r\n                    )\r\n                  )\r\n                : \"N/A\"}\r\n            </div>\r\n          </div>\r\n        ) : null}\r\n\r\n        <div className=\"mb-1\">\r\n          <h4 className=\"text-lg font-semibold text-gray-700 mb-1\">\r\n            Job Description\r\n          </h4>\r\n          {vacancy?.vacancy_data?.job_description.trim() ? (\r\n            <p\r\n              dangerouslySetInnerHTML={{\r\n                __html: vacancy?.vacancy_data?.job_description,\r\n              }}\r\n              className=\"text-gray-700 mt-1 mb-2\"\r\n            ></p>\r\n          ) : (\r\n            \"N/A\"\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,KAAK,EAAE,MAAM,EAAqC;IACtE,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uEAAuE,EAAE,cACnF,SACC;;0BAEH,6LAAC;gBAAK,WAAU;0BAA0B;;;;;;0BAC1C,6LAAC;gBAAK,WAAU;;oBAAuC;oBAAE;oBAAO;;;;;;;;;;;;;AAGtE;KA1BM;AAkCS,SAAS,eAAe,EACrC,OAAO,EACP,gBAAgB,EAChB,aAAa,EACO;IACpB,MAAM,kBAAkB,CAAC,MAAgB,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,GAAG;IAC5E,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,KAAK,EAAoC;QACtE,IAAI,CAAC,gBAAgB,QAAQ,OAAO;QAEpC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,OAAO,sBAClB,6LAAC;4BAEC,OAAO,OAAO,OAAO,SAAS,WAAW,OAAO,OAAO;4BACvD,QACE,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS;2BAHjD;;;;;;;;;;;;;;;;IAUjB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,OAAO,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ;QAC3C,OAAO,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAI;YAC3D,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QACC,SAAS,CAAC;YACR,EAAE,eAAe;QACnB;QACA,WAAW,CAAC,8CAA8C,EACxD,gBAAgB,aAAa,YAC9B,6DAA6D,CAAC;;0BAG/D,6LAAC,8HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,SAAQ;gBACR,SAAS,IAAM,iBAAiB;0BAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,MAAM;;;;;;;;;;;0BAGX,6LAAC;gBAAG,WAAU;0BAAuD;;;;;;0BAIrE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAO,WAAU;8CAAwB;;;;;;gCAAiB;8CAC3D,6LAAC;oCAAK,WAAU;8CAAiB,SAAS;;;;;;;;;;;;;;;;;kCAG9C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAO,WAAU;8CAAwB;;;;;;gCAAsB;8CAChE,6LAAC;oCAAK,WAAU;8CACb,SAAS,aAAa,SAAS,aAAa;;;;;;;;;;;;;;;;;kCAKnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CACV,cAAc,SAAS,cAAc,CAAC,YAAY;;;;;;;;;;;;kCAIvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CACV,MAAM,OAAO,CAAC,SAAS,cAAc,cAAc,iBACpD,QAAQ,YAAY,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GACjD,QAAQ,YAAY,CAAC,YAAY,CAAC,YAAY,CAC3C,GAAG,CAAC,CAAC,MACJ;wCAAC,KAAK;wCAAM,KAAK;qCAAM,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,OAE9C,IAAI,CAAC,SACR;;;;;;;;;;;;kCAIR,6LAAC;wBACC,OAAM;wBACN,OAAO,SAAS,cAAc,CAAC,cAAc;;;;;;kCAE/C,6LAAC;wBACC,OAAM;wBACN,OAAO,SAAS,cAAc,CAAC,mBAAmB;;;;;;kCAEpD,6LAAC;wBACC,OAAM;wBACN,OAAO,SAAS,cAAc,CAAC,sBAAsB;;;;;;oBAItD,SAAS,cAAc,CAAC,6BAA6B,EAAE,uBACtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAG,WAAU;0CACX,QAAQ,YAAY,CAAC,6BAA6B,CAAC,GAAG,CACrD,CAAC,QAAQ,sBACP,6LAAC;wCAAe,WAAU;kDACvB,QAAQ,QAAQ;uCADV;;;;;;;;;;;;;;;+BAOf;oBAGH,SAAS,cAAc,CAAC,sBAAsB,EAAE,uBAC/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc,CAAC,sBAAsB,GAC3C,SAAS,cAAc,CAAC,sBAAsB,CAAC,IAC7C,CAAC,KAAK,sBACJ,6LAAC;wCAEC,OAAO,KAAK,SAAS;wCACrB,QAAQ,KAAK,UAAU;uCAFlB;;;;gDAMX;;;;;;;;;;;+BAGN;kCAEJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;4BAGxD,SAAS,cAAc,gBAAgB,uBACtC,6LAAC;gCACC,yBAAyB;oCACvB,QAAQ,SAAS,cAAc;gCACjC;gCACA,WAAU;;;;;uCAGZ;;;;;;;;;;;;;;;;;;;AAMZ;MA3KwB"}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/VacancyItem.tsx"], "sourcesContent": ["import { Vacancy } from \"@/app/candidates/helper\";\r\nimport { Info } from \"lucide-react\";\r\nimport React, { useRef } from \"react\";\r\nimport VacancyDetails from \"./VacancyDetails\";\r\n\r\nconst VacancyItem = ({\r\n  vacancy,\r\n  selectedVacancy,\r\n  handleVacancyClick,\r\n  setSearch,\r\n  activeVacancy,\r\n  setActiveVacancy,\r\n}: {\r\n  vacancy: Vacancy;\r\n  selectedVacancy: Vacancy | null;\r\n  handleVacancyClick: (vacancy: Vacancy) => void;\r\n  setSearch: React.Dispatch<React.SetStateAction<string>>;\r\n  activeVacancy: Vacancy | null;\r\n  setActiveVacancy: React.Dispatch<React.SetStateAction<Vacancy | null>>;\r\n}) => {\r\n  // Inside your component\r\n  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const isSelected = selectedVacancy?.refno === vacancy?.refno;\r\n\r\n  return (\r\n    <>\r\n      <li\r\n        className={`flex justify-between items-center p-2 border-b cursor-pointer hover:bg-gray-100 ${\r\n          isSelected\r\n            ? \"bg-gray-900 text-white hover:bg-gray-900 rounded-md\"\r\n            : \"\"\r\n        }`}\r\n        onClick={() => {\r\n          handleVacancyClick(vacancy);\r\n          setActiveVacancy(null);\r\n          setSearch(\"\");\r\n        }}\r\n      >\r\n        <span>{vacancy?.refno}</span>\r\n\r\n        <span\r\n          className=\"flex items-center\"\r\n          onClick={(e: React.MouseEvent<HTMLSpanElement>) => {\r\n            e.stopPropagation();\r\n          }}\r\n        >\r\n          <span className=\"group ml-2\">\r\n            <Info\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                isSelected ? \"text-black fill-white\" : \"text-white fill-black\"\r\n              }`}\r\n              onMouseEnter={() => {\r\n                hoverTimeout.current = setTimeout(() => {\r\n                  setActiveVacancy(vacancy);\r\n                }, 200);\r\n              }}\r\n              onMouseLeave={() => {\r\n                if (hoverTimeout.current) {\r\n                  clearTimeout(hoverTimeout.current);\r\n                  hoverTimeout.current = null;\r\n                }\r\n              }}\r\n              onClick={(e: React.MouseEvent<SVGSVGElement>) => {\r\n                e.stopPropagation();\r\n              }}\r\n            />\r\n            {activeVacancy?.refno === vacancy?.refno && (\r\n              <VacancyDetails\r\n                vacancy={activeVacancy}\r\n                setActiveVacancy={setActiveVacancy}\r\n              />\r\n            )}\r\n          </span>\r\n        </span>\r\n      </li>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VacancyItem;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAFA;;;;;;AAIA,MAAM,cAAc,CAAC,EACnB,OAAO,EACP,eAAe,EACf,kBAAkB,EAClB,SAAS,EACT,aAAa,EACb,gBAAgB,EAQjB;;IACC,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEnD,MAAM,aAAa,iBAAiB,UAAU,SAAS;IAEvD,qBACE;kBACE,cAAA,6LAAC;YACC,WAAW,CAAC,gFAAgF,EAC1F,aACI,wDACA,IACJ;YACF,SAAS;gBACP,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAU;YACZ;;8BAEA,6LAAC;8BAAM,SAAS;;;;;;8BAEhB,6LAAC;oBACC,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;oBACnB;8BAEA,cAAA,6LAAC;wBAAK,WAAU;;0CACd,6LAAC,qMAAA,CAAA,OAAI;gCACH,MAAM;gCACN,WAAW,CAAC,eAAe,EACzB,aAAa,0BAA0B,yBACvC;gCACF,cAAc;oCACZ,aAAa,OAAO,GAAG,WAAW;wCAChC,iBAAiB;oCACnB,GAAG;gCACL;gCACA,cAAc;oCACZ,IAAI,aAAa,OAAO,EAAE;wCACxB,aAAa,aAAa,OAAO;wCACjC,aAAa,OAAO,GAAG;oCACzB;gCACF;gCACA,SAAS,CAAC;oCACR,EAAE,eAAe;gCACnB;;;;;;4BAED,eAAe,UAAU,SAAS,uBACjC,6LAAC,8IAAA,CAAA,UAAc;gCACb,SAAS;gCACT,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAQlC;GA1EM;KAAA;uCA4ES"}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/CandidateResume.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { ResumeData, WorkExperience } from \"@/app/candidates/helper\";\r\nimport Loading from \"../Loading\";\r\nimport { DownloadIcon, ExternalLink } from \"lucide-react\";\r\nimport { isParsedResume } from \"@/api/config\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Vacancy } from \"../CandidateTable/helper\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\n\r\nconst SkillBadge = ({\r\n  skill,\r\n  styleClass,\r\n}: {\r\n  skill: string;\r\n  styleClass?: string;\r\n}) => {\r\n  return (\r\n    <span\r\n      className={`px-3 py-1 rounded-full text-xs font-semibold text-gray-800 border border-gray-300 shadow-sm ${\r\n        styleClass ?? \"\"\r\n      }`}\r\n    >\r\n      {skill}\r\n    </span>\r\n  );\r\n};\r\n\r\nconst CandidateResume = ({\r\n  vacancy,\r\n  selectedResume,\r\n  setSelectedResume,\r\n  isResumeModalOpen,\r\n  setIsResumeModalOpen,\r\n  isLoading,\r\n}: {\r\n  vacancy: Vacancy | null;\r\n  selectedResume: ResumeData | null;\r\n  setSelectedResume: React.Dispatch<React.SetStateAction<null | ResumeData>>;\r\n  isResumeModalOpen: boolean;\r\n  setIsResumeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;\r\n  isLoading: boolean;\r\n}) => {\r\n  const cv = selectedResume?.candidate?.resume_file;\r\n\r\n  const legendSkillWeights = [\"high\", \"medium\", \"normal\"];\r\n\r\n  const getSharePointURL = (url: string): string => {\r\n    if (!url) return \"\";\r\n\r\n    const lowerUrl = url.toLowerCase();\r\n    const baseURL = lowerUrl.split(\"/contact\")[0] || \"\";\r\n\r\n    const idMatch = lowerUrl.match(/\\/sites.*$/);\r\n    if (!idMatch || !idMatch[0]) return \"\";\r\n\r\n    const encodedFileName = encodeURIComponent(idMatch[0]);\r\n    return `${baseURL}/_layouts/15/embed.aspx?Id=${encodedFileName}`;\r\n  };\r\n\r\n  const resultURL = cv ? getSharePointURL(cv) : \"\";\r\n\r\n  // Add getHighlightStyle function for color based on weight\r\n  const getHighlightStyle = (weight: string) => {\r\n    switch (weight) {\r\n      case \"high\":\r\n        return \"p-1 bg-green-200/50 text-green-800 border border-green-300 shadow-md\";\r\n      case \"medium\":\r\n        return \"p-1 bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md\";\r\n      case \"normal\":\r\n        return \"p-1 bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md\";\r\n      case \"low\":\r\n        return \"p-1 bg-red-200/50 text-red-800 border border-red-300 shadow-md\";\r\n      default:\r\n        return \"p-1 bg-gray-200 text-gray-700 border border-gray-300\";\r\n    }\r\n  };\r\n\r\n  const getFullWorkExperience = React.useMemo(() => {\r\n    if (!selectedResume) return null;\r\n\r\n    const workExperience = selectedResume?.candidate?.[\"work experience\"] ?? [];\r\n    if (!Array.isArray(workExperience) || workExperience.length === 0) {\r\n      return <p className=\"text-gray-500\">No work experience available</p>;\r\n    }\r\n\r\n    return (\r\n      <div className=\"mb-6 p-4 bg-gray-100 rounded-lg\">\r\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\r\n          Full Work Experience\r\n        </h3>\r\n        <ul className=\"list-disc ml-5 space-y-3\">\r\n          {workExperience.map((exp: WorkExperience, index: number) => {\r\n            const description = exp.description || \"\";\r\n            const softSkills = vacancy?.vacancy_data?.[\"soft skills\"] || [];\r\n            const technicalSkills =\r\n              vacancy?.vacancy_data?.[\"technical skills\"] || [];\r\n            const toolsAndPlatforms =\r\n              vacancy?.vacancy_data?.[\"tools and platforms\"] || [];\r\n            const allSkills = [\r\n              ...softSkills,\r\n              ...technicalSkills,\r\n              ...toolsAndPlatforms,\r\n            ];\r\n\r\n            // Highlighting logic\r\n            const highlightedDescription = (() => {\r\n              if (allSkills.length === 0) return description;\r\n              const regex = new RegExp(\r\n                allSkills\r\n                  .map((skill) => {\r\n                    const name =\r\n                      typeof skill?.name === \"string\" ? skill.name : \"\";\r\n                    return `\\\\b${name.replace(\r\n                      /[.*+?^${}()|[\\]\\\\]/g,\r\n                      \"\\\\$&\"\r\n                    )}\\\\b`;\r\n                  })\r\n                  .join(\"|\"),\r\n                \"gi\"\r\n              );\r\n\r\n              const result: (string | React.ReactNode)[] = [];\r\n              let lastIndex = 0;\r\n              let match: RegExpExecArray | null;\r\n              while ((match = regex.exec(description)) !== null) {\r\n                if (match.index > lastIndex) {\r\n                  result.push(description.slice(lastIndex, match.index));\r\n                }\r\n                const matchedSkill = allSkills.find((skill) => {\r\n                  const skillName =\r\n                    typeof skill === \"string\" ? skill : skill.name;\r\n                  return (\r\n                    skillName &&\r\n                    match &&\r\n                    match[0] &&\r\n                    skillName.toLowerCase() === match[0].toLowerCase()\r\n                  );\r\n                });\r\n                const weight =\r\n                  typeof matchedSkill === \"object\" && matchedSkill !== null\r\n                    ? matchedSkill.weight\r\n                    : undefined;\r\n                const colorClass = weight\r\n                  ? getHighlightStyle(weight)\r\n                  : \"bg-gray-200 text-gray-700 border border-gray-300\";\r\n                result.push(\r\n                  <span\r\n                    key={match.index + \"-\" + match[0]}\r\n                    className={colorClass}\r\n                  >\r\n                    {match[0]}\r\n                  </span>\r\n                );\r\n                lastIndex = regex.lastIndex;\r\n              }\r\n              if (lastIndex < description.length) {\r\n                result.push(description.slice(lastIndex));\r\n              }\r\n              return result;\r\n            })();\r\n\r\n            return (\r\n              <li key={index} className=\"p-3 bg-white rounded-md shadow\">\r\n                <p className=\"font-semibold text-lg\">\r\n                  {exp.title} at {exp.company}\r\n                </p>\r\n                <p className=\"text-gray-600 text-sm\">\r\n                  {exp.start_date} - {exp.end_date || \"Present\"}\r\n                </p>\r\n                <p className=\"text-gray-700 max-h-[150px] overflow-y-auto\">\r\n                  {highlightedDescription}\r\n                </p>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      </div>\r\n    );\r\n  }, [selectedResume, vacancy]);\r\n\r\n  const getSkillBadgeHighlightClassName = (skill: string) => {\r\n    if (!skill) return \"\";\r\n    const skillLower = typeof skill === \"string\" ? skill.toLowerCase() : \"\";\r\n    const softSkills = vacancy?.vacancy_data?.[\"soft skills\"] || [];\r\n    const technicalSkills = vacancy?.vacancy_data?.[\"technical skills\"] || [];\r\n    const toolsAndPlatforms =\r\n      vacancy?.vacancy_data?.[\"tools and platforms\"] || [];\r\n    const allSkills = [...softSkills, ...technicalSkills, ...toolsAndPlatforms];\r\n    const matchedSkill = allSkills.find(\r\n      (skill) =>\r\n        typeof skill === \"object\" && skill.name?.toLowerCase?.() === skillLower\r\n    );\r\n    return matchedSkill ? getHighlightStyle(matchedSkill.weight) : \"\";\r\n  };\r\n\r\n  const getParsedResume = () => {\r\n    return (\r\n      <>\r\n        {cv ? (\r\n          <>\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between my-4\">\r\n                <h3 className=\"text-xl font-bold text-gray-900\">CV</h3>\r\n                <a\r\n                  href={cv}\r\n                  target=\"_blank\"\r\n                  download\r\n                  onClick={() => {\r\n                    getAppInsights()?.trackEvent({\r\n                      name: \"FE_DownloadCVClicked\",\r\n                      properties: { resume: cv },\r\n                    });\r\n                  }}\r\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg transition-all flex items-center gap-2 z-10\"\r\n                >\r\n                  Download CV <DownloadIcon />\r\n                </a>\r\n              </div>\r\n              {resultURL ? (\r\n                <iframe\r\n                  className=\"cv-renderer\"\r\n                  src={resultURL}\r\n                  width=\"100%\"\r\n                  height=\"600px\"\r\n                />\r\n              ) : (\r\n                <p>Resume URL is not available</p>\r\n              )}\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <div className=\"flex relative justify-center items-center text-3xl my-5\">\r\n            Resume is not available\r\n          </div>\r\n        )}\r\n      </>\r\n    );\r\n  };\r\n\r\n  const handleResumeLinkClick = () => {\r\n    getAppInsights()?.trackEvent({\r\n      name: \"FE_ResumeLinkClicked\",\r\n      properties: { resume: cv },\r\n    });\r\n  };\r\n\r\n  const hasCandidateData =\r\n    selectedResume?.candidate &&\r\n    typeof selectedResume.candidate === \"object\" &&\r\n    Object.keys(selectedResume.candidate).length > 0;\r\n\r\n  const SkillSection = ({\r\n    title,\r\n    skills,\r\n  }: {\r\n    title: string;\r\n    skills: string[];\r\n  }) => (\r\n    <p className=\"flex items-start mb-5\">\r\n      <strong className=\"w-[250px]\">{title}</strong>\r\n      <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n        {skills.length > 0\r\n          ? skills.map((skill, i) => (\r\n              <SkillBadge\r\n                key={i}\r\n                skill={skill}\r\n                styleClass={getSkillBadgeHighlightClassName(skill)}\r\n              />\r\n            ))\r\n          : \"N/A\"}\r\n      </span>\r\n    </p>\r\n  );\r\n\r\n  return (\r\n    <div>\r\n      {\" \"}\r\n      {/* Modal */}\r\n      {isResumeModalOpen && (\r\n        <div className=\"fixed z-50 inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center\">\r\n          <div className=\"bg-white w-[80vw] min-h-[60vh] px-8 rounded-lg max-h-[90vh] overflow-y-auto shadow-lg\">\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between text-gray-900 border-b pb-2 mb-4 sticky top-0 bg-white z-10 pt-8\">\r\n              <h2 className=\"text-2xl font-extrabold \">Candidate Profile</h2>\r\n              <div className=\"flex\">\r\n                <Button\r\n                  asChild\r\n                  className=\"text-blue-500 text-lg\"\r\n                  variant=\"link\"\r\n                >\r\n                  <a\r\n                    href={cv}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    onClick={handleResumeLinkClick}\r\n                  >\r\n                    Resume Link <ExternalLink />\r\n                  </a>\r\n                </Button>\r\n                {/* Close Button */}\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedResume(null);\r\n                    setIsResumeModalOpen(false);\r\n                  }}\r\n                  className=\"bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2 rounded-lg transition-all\"\r\n                >\r\n                  Close\r\n                </button>\r\n              </div>\r\n            </div>\r\n            {selectedResume?.candidate && hasCandidateData ? (\r\n              <>\r\n                {/* Personal Details */}\r\n                <div className=\"mb-6\">\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">👤 Name:</span>{\" \"}\r\n                    {selectedResume?.candidate?.name}\r\n                  </p>\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">✉️ Email:</span>{\" \"}\r\n                    {selectedResume?.candidate?.email}\r\n                  </p>\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">📞 Phone:</span>{\" \"}\r\n                    {selectedResume?.candidate?.phone}\r\n                  </p>\r\n                  {selectedResume?.candidate?.city &&\r\n                    selectedResume?.candidate?.state && (\r\n                      <p className=\"text-lg font-semibold text-gray-700\">\r\n                        <span className=\"text-gray-900\">📍 location:</span>{\" \"}\r\n                        {`${selectedResume?.candidate?.city}, ${selectedResume?.candidate?.state}`}\r\n                      </p>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Skills & Certifications */}\r\n                <div className=\"mb-6\">\r\n                  <p className=\"flex justify-content items-start mb-5\">\r\n                    <strong className=\"w-[250px]\"> Skills</strong>\r\n                    <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n                      {legendSkillWeights.map((weight, index) => (\r\n                        <SkillBadge\r\n                          key={index}\r\n                          skill={weight}\r\n                          styleClass={getHighlightStyle(weight)}\r\n                        />\r\n                      ))}\r\n                    </span>\r\n                  </p>\r\n                  {/* Soft Skills */}\r\n                  <SkillSection\r\n                    title=\"💻 Soft Skills:\"\r\n                    skills={selectedResume?.candidate?.[\"soft skills\"] || []}\r\n                  />\r\n\r\n                  {/* Technical Skills */}\r\n                  <SkillSection\r\n                    title=\"💻 Technical Skills:\"\r\n                    skills={\r\n                      selectedResume?.candidate?.[\"technical skills\"] || []\r\n                    }\r\n                  />\r\n\r\n                  {/* Tools & Platforms */}\r\n                  <SkillSection\r\n                    title=\"🛠 Tools & Platforms:\"\r\n                    skills={\r\n                      selectedResume?.candidate?.[\"tools and platforms\"] || []\r\n                    }\r\n                  />\r\n                </div>\r\n                {isParsedResume ? getFullWorkExperience : getParsedResume()}\r\n              </>\r\n            ) : selectedResume?.candidate &&\r\n              Object.keys(selectedResume.candidate)?.length === 0 ? (\r\n              <div className=\"flex relative top-28 pt-10 justify-center items-center text-3xl\">\r\n                Resume details not found\r\n              </div>\r\n            ) : isResumeModalOpen && isLoading ? (\r\n              <Loading height=\"h-[80vh]\" />\r\n            ) : null}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateResume;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;AACA;AAEA;AAJA;AAAA;;;;;;;;;AAMA,MAAM,aAAa,CAAC,EAClB,KAAK,EACL,UAAU,EAIX;IACC,qBACE,6LAAC;QACC,WAAW,CAAC,4FAA4F,EACtG,cAAc,IACd;kBAED;;;;;;AAGP;KAhBM;AAkBN,MAAM,kBAAkB,CAAC,EACvB,OAAO,EACP,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,SAAS,EAQV;;IACC,MAAM,KAAK,gBAAgB,WAAW;IAEtC,MAAM,qBAAqB;QAAC;QAAQ;QAAU;KAAS;IAEvD,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,WAAW,IAAI,WAAW;QAChC,MAAM,UAAU,SAAS,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI;QAEjD,MAAM,UAAU,SAAS,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO;QAEpC,MAAM,kBAAkB,mBAAmB,OAAO,CAAC,EAAE;QACrD,OAAO,GAAG,QAAQ,2BAA2B,EAAE,iBAAiB;IAClE;IAEA,MAAM,YAAY,KAAK,iBAAiB,MAAM;IAE9C,2DAA2D;IAC3D,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,6JAAA,CAAA,UAAK,CAAC,OAAO;0DAAC;YAC1C,IAAI,CAAC,gBAAgB,OAAO;YAE5B,MAAM,iBAAiB,gBAAgB,WAAW,CAAC,kBAAkB,IAAI,EAAE;YAC3E,IAAI,CAAC,MAAM,OAAO,CAAC,mBAAmB,eAAe,MAAM,KAAK,GAAG;gBACjE,qBAAO,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;YACtC;YAEA,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,6LAAC;wBAAG,WAAU;kCACX,eAAe,GAAG;8EAAC,CAAC,KAAqB;gCACxC,MAAM,cAAc,IAAI,WAAW,IAAI;gCACvC,MAAM,aAAa,SAAS,cAAc,CAAC,cAAc,IAAI,EAAE;gCAC/D,MAAM,kBACJ,SAAS,cAAc,CAAC,mBAAmB,IAAI,EAAE;gCACnD,MAAM,oBACJ,SAAS,cAAc,CAAC,sBAAsB,IAAI,EAAE;gCACtD,MAAM,YAAY;uCACb;uCACA;uCACA;iCACJ;gCAED,qBAAqB;gCACrB,MAAM,yBAAyB;6GAAC;wCAC9B,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO;wCACnC,MAAM,QAAQ,IAAI,OAChB,UACG,GAAG;qHAAC,CAAC;gDACJ,MAAM,OACJ,OAAO,OAAO,SAAS,WAAW,MAAM,IAAI,GAAG;gDACjD,OAAO,CAAC,GAAG,EAAE,KAAK,OAAO,CACvB,uBACA,QACA,GAAG,CAAC;4CACR;oHACC,IAAI,CAAC,MACR;wCAGF,MAAM,SAAuC,EAAE;wCAC/C,IAAI,YAAY;wCAChB,IAAI;wCACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,YAAY,MAAM,KAAM;4CACjD,IAAI,MAAM,KAAK,GAAG,WAAW;gDAC3B,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC,WAAW,MAAM,KAAK;4CACtD;4CACA,MAAM,eAAe,UAAU,IAAI;sIAAC,CAAC;oDACnC,MAAM,YACJ,OAAO,UAAU,WAAW,QAAQ,MAAM,IAAI;oDAChD,OACE,aACA,SACA,KAAK,CAAC,EAAE,IACR,UAAU,WAAW,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;gDAEpD;;4CACA,MAAM,SACJ,OAAO,iBAAiB,YAAY,iBAAiB,OACjD,aAAa,MAAM,GACnB;4CACN,MAAM,aAAa,SACf,kBAAkB,UAClB;4CACJ,OAAO,IAAI,eACT,AAzCyB,2LAsD9B,EAbM;gDAEC,WAAW;0DAEV,KAAK,CAAC,EAAE;+CAHJ,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE;;;;;4CAMrC,YAAY,MAAM,SAAS;wCAC7B;wCACA,IAAI,YAAY,YAAY,MAAM,EAAE;4CAClC,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC;wCAChC;wCACA,OAAO;oCACT;;gCAEA,qBACE,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;4CAAE,WAAU;;gDACV,IAAI,KAAK;gDAAC;gDAAK,IAAI,OAAO;;;;;;;sDAE7B,6LAAC;4CAAE,WAAU;;gDACV,IAAI,UAAU;gDAAC;gDAAI,IAAI,QAAQ,IAAI;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDACV;;;;;;;mCARI;;;;;4BAYb;;;;;;;;;;;;;QAIR;yDAAG;QAAC;QAAgB;KAAQ;IAE5B,MAAM,kCAAkC,CAAC;QACvC,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,aAAa,OAAO,UAAU,WAAW,MAAM,WAAW,KAAK;QACrE,MAAM,aAAa,SAAS,cAAc,CAAC,cAAc,IAAI,EAAE;QAC/D,MAAM,kBAAkB,SAAS,cAAc,CAAC,mBAAmB,IAAI,EAAE;QACzE,MAAM,oBACJ,SAAS,cAAc,CAAC,sBAAsB,IAAI,EAAE;QACtD,MAAM,YAAY;eAAI;eAAe;eAAoB;SAAkB;QAC3E,MAAM,eAAe,UAAU,IAAI,CACjC,CAAC,QACC,OAAO,UAAU,YAAY,MAAM,IAAI,EAAE,oBAAoB;QAEjE,OAAO,eAAe,kBAAkB,aAAa,MAAM,IAAI;IACjE;IAEA,MAAM,kBAAkB;QACtB,qBACE;sBACG,mBACC;0BACE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,6LAAC;oCACC,MAAM;oCACN,QAAO;oCACP,QAAQ;oCACR,SAAS;wCACP,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;4CAC3B,MAAM;4CACN,YAAY;gDAAE,QAAQ;4CAAG;wCAC3B;oCACF;oCACA,WAAU;;wCACX;sDACa,6LAAC,iNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;wBAG5B,0BACC,6LAAC;4BACC,WAAU;4BACV,KAAK;4BACL,OAAM;4BACN,QAAO;;;;;iDAGT,6LAAC;sCAAE;;;;;;;;;;;;8CAKT,6LAAC;gBAAI,WAAU;0BAA0D;;;;;;;IAMjF;IAEA,MAAM,wBAAwB;QAC5B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;YAC3B,MAAM;YACN,YAAY;gBAAE,QAAQ;YAAG;QAC3B;IACF;IAEA,MAAM,mBACJ,gBAAgB,aAChB,OAAO,eAAe,SAAS,KAAK,YACpC,OAAO,IAAI,CAAC,eAAe,SAAS,EAAE,MAAM,GAAG;IAEjD,MAAM,eAAe,CAAC,EACpB,KAAK,EACL,MAAM,EAIP,iBACC,6LAAC;YAAE,WAAU;;8BACX,6LAAC;oBAAO,WAAU;8BAAa;;;;;;8BAC/B,6LAAC;oBAAK,WAAU;8BACb,OAAO,MAAM,GAAG,IACb,OAAO,GAAG,CAAC,CAAC,OAAO,kBACjB,6LAAC;4BAEC,OAAO;4BACP,YAAY,gCAAgC;2BAFvC;;;;oCAKT;;;;;;;;;;;;IAKV,qBACE,6LAAC;;YACE;YAEA,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDACC,MAAM;gDACN,QAAO;gDACP,KAAI;gDACJ,SAAS;;oDACV;kEACa,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;sDAI7B,6LAAC;4CACC,SAAS;gDACP,kBAAkB;gDAClB,qBAAqB;4CACvB;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAKJ,gBAAgB,aAAa,iCAC5B;;8CAEE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAgB;gDAC/C,gBAAgB,WAAW;;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAiB;gDAChD,gBAAgB,WAAW;;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAiB;gDAChD,gBAAgB,WAAW;;;;;;;wCAE7B,gBAAgB,WAAW,QAC1B,gBAAgB,WAAW,uBACzB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAoB;gDACnD,GAAG,gBAAgB,WAAW,KAAK,EAAE,EAAE,gBAAgB,WAAW,OAAO;;;;;;;;;;;;;8CAMlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAO,WAAU;8DAAY;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DACb,mBAAmB,GAAG,CAAC,CAAC,QAAQ,sBAC/B,6LAAC;4DAEC,OAAO;4DACP,YAAY,kBAAkB;2DAFzB;;;;;;;;;;;;;;;;sDAQb,6LAAC;4CACC,OAAM;4CACN,QAAQ,gBAAgB,WAAW,CAAC,cAAc,IAAI,EAAE;;;;;;sDAI1D,6LAAC;4CACC,OAAM;4CACN,QACE,gBAAgB,WAAW,CAAC,mBAAmB,IAAI,EAAE;;;;;;sDAKzD,6LAAC;4CACC,OAAM;4CACN,QACE,gBAAgB,WAAW,CAAC,sBAAsB,IAAI,EAAE;;;;;;;;;;;;gCAI7D,gHAAA,CAAA,iBAAc,GAAG,wBAAwB;;2CAE1C,gBAAgB,aAClB,OAAO,IAAI,CAAC,eAAe,SAAS,GAAG,WAAW,kBAClD,6LAAC;4BAAI,WAAU;sCAAkE;;;;;mCAG/E,qBAAqB,0BACvB,6LAAC,yHAAA,CAAA,UAAO;4BAAC,QAAO;;;;;mCACd;;;;;;;;;;;;;;;;;;AAMhB;GAxWM;MAAA;uCA0WS"}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = \"Table\";\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n));\r\nTableHeader.displayName = \"TableHeader\";\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = \"TableBody\";\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = \"TableFooter\";\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = \"TableRow\";\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = \"TableHead\";\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = \"TableCell\";\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = \"TableCaption\";\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Modal.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\ninterface ModalProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  title?: string;\r\n  isOpen: boolean;\r\n  children: ReactNode;\r\n  onClose: () => void;\r\n  width?: string;\r\n  height?: string;\r\n  mercury?: boolean;\r\n}\r\n\r\nconst Modal = ({\r\n  isOpen,\r\n  title,\r\n  children,\r\n  onClose,\r\n  width = \"max-w-md\",\r\n  height,\r\n  mercury = false,\r\n  ...rest\r\n}: ModalProps) => {\r\n  if (!isOpen) return <></>;\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-20 flex justify-center items-center z-50\">\r\n      <div\r\n        className={`bg-white rounded-lg shadow-lg w-[90%] ${width} ${height} p-6 relative`}\r\n        {...rest}\r\n      >\r\n        {/* Header */}\r\n        {title ? (\r\n          <div className=\"flex justify-between items-center border-b pb-2 mb-4\">\r\n            <h2 className=\"text-xl font-semibold\">{title}</h2>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              ✖\r\n            </button>\r\n          </div>\r\n        ) : null}\r\n\r\n        {/* Content */}\r\n        <div className={mercury ? \"p-4 w-[100%] h-[100%]\" : undefined}>\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;;AAYA,MAAM,QAAQ,CAAC,EACb,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,QAAQ,UAAU,EAClB,MAAM,EACN,UAAU,KAAK,EACf,GAAG,MACQ;IACX,IAAI,CAAC,QAAQ,qBAAO;IACpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAC,sCAAsC,EAAE,MAAM,CAAC,EAAE,OAAO,aAAa,CAAC;YACjF,GAAG,IAAI;;gBAGP,sBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;2BAID;8BAGJ,6LAAC;oBAAI,WAAW,UAAU,0BAA0B;8BACjD;;;;;;;;;;;;;;;;;AAKX;KArCM;uCAuCS"}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/public/assets/info_outline_black.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 16, height: 16, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider;\r\n\r\nconst Tooltip = TooltipPrimitive.Root;\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger;\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </TooltipPrimitive.Portal>\r\n));\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,kBAAkB,uKAAiB,QAAQ;AAEjD,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,uKAAiB,MAAM;kBACtB,cAAA,6LAAC,uKAAiB,OAAO;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qXACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/AppToolTip.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport Info from \"@/public/assets/info_outline_black.svg\";\r\nimport { cn } from \"@/library/utils\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"./ui/tooltip\";\r\n\r\ninterface AppToolTipProps {\r\n  text: React.ReactNode;\r\n  direction?: \"bottom\" | \"top\" | \"left\" | \"right\" | undefined;\r\n  header?: React.ReactNode;\r\n  className?: string;\r\n  triggerClassName?: string;\r\n  iconClass?: string;\r\n}\r\n\r\nconst AppToolTip = ({\r\n  text,\r\n  direction = \"left\",\r\n  header,\r\n  className,\r\n  triggerClassName,\r\n  iconClass,\r\n}: AppToolTipProps) => {\r\n  return (\r\n    <TooltipProvider delayDuration={0}>\r\n      <Tooltip>\r\n        <TooltipTrigger\r\n          className={cn(`flex min-w-4 items-center`, triggerClassName)}\r\n        >\r\n          {header ? (\r\n            header\r\n          ) : (\r\n            <Image\r\n              id=\"info-button\"\r\n              className={`custom-target-icon ml-1 hidden size-3.5 sm:inline ${iconClass}`}\r\n              src={Info}\r\n              alt=\"c\"\r\n            />\r\n          )}\r\n        </TooltipTrigger>\r\n        <TooltipContent\r\n          className={cn(\" bg-[#646464] text-white\", className)}\r\n          side={direction}\r\n        >\r\n          {text}\r\n        </TooltipContent>\r\n      </Tooltip>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default AppToolTip;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAgBA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,YAAY,MAAM,EAClB,MAAM,EACN,SAAS,EACT,gBAAgB,EAChB,SAAS,EACO;IAChB,qBACE,6LAAC,+HAAA,CAAA,kBAAe;QAAC,eAAe;kBAC9B,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8BACN,6LAAC,+HAAA,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,yBAAyB,CAAC,EAAE;8BAE1C,SACC,uBAEA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,IAAG;wBACH,WAAW,CAAC,kDAAkD,EAAE,WAAW;wBAC3E,KAAK,sTAAA,CAAA,UAAI;wBACT,KAAI;;;;;;;;;;;8BAIV,6LAAC,+HAA<PERSON>,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;oBAC1C,MAAM;8BAEL;;;;;;;;;;;;;;;;;AAKX;KAlCM;uCAoCS"}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1851, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/WhyFitAction.tsx"], "sourcesContent": ["import { CircleX, Expand, Minimize, Copy } from \"lucide-react\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"../ui/button\";\r\nimport {\r\n  Candidate,\r\n  Vacancy,\r\n  WhyFitReasonPayload,\r\n} from \"@/app/candidates/helper\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport Modal from \"@/components/Modal\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport Loading from \"../Loading\";\r\n\r\nconst WhyFitAction = ({\r\n  candidate,\r\n  setCandidates,\r\n  selectedVacancy,\r\n  existingEdit,\r\n  setExistingEdit,\r\n  candidateEditId,\r\n  setCandidateEditId,\r\n  candidateEditId2,\r\n  setCandidateEditId2,\r\n  setExpandPopupOpen,\r\n  isTableReadOnly,\r\n}: {\r\n  candidate: Candidate;\r\n  setCandidates?: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  selectedVacancy: Vacancy;\r\n  existingEdit: boolean;\r\n  setExistingEdit: React.Dispatch<React.SetStateAction<boolean>>;\r\n  candidateEditId: string;\r\n  setCandidateEditId: React.Dispatch<React.SetStateAction<string>>;\r\n  candidateEditId2: string;\r\n  setCandidateEditId2: React.Dispatch<React.SetStateAction<string>>;\r\n  setExpandPopupOpen: React.Dispatch<React.SetStateAction<boolean>>;\r\n  isTableReadOnly: boolean; // New prop to control table read-only state\r\n}) => {\r\n  const [showCommentBox, setShowCommentBox] = useState(false);\r\n\r\n  const commentBoxRef = useRef<HTMLDivElement | null>(null);\r\n  const buttonRef = useRef<HTMLDivElement | null>(null);\r\n  const [openAbove, setOpenAbove] = useState(false);\r\n  const [confirmModal, setConfirmModal] = useState(false);\r\n  const { showNotification } = useNotification();\r\n  const [originalComment, setOriginalComment] = useState(\r\n    candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n  );\r\n  const [comment, setComment] = useState(\r\n    candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n  );\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const expandedTextareaRef = useRef<HTMLTextAreaElement | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && expandedTextareaRef.current) {\r\n      const textarea = expandedTextareaRef.current;\r\n      // Wait for DOM to paint\r\n      requestAnimationFrame(() => {\r\n        textarea.focus();\r\n        textarea.setSelectionRange(\r\n          textarea.value.length,\r\n          textarea.value.length\r\n        );\r\n      });\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  useEffect(() => {\r\n    const decision = candidate?.candidate_data?.current_fitness_reason;\r\n    if (decision) {\r\n      setComment(decision.reason ?? \"\");\r\n      setOriginalComment(decision.reason ?? \"\");\r\n    }\r\n  }, [candidate]);\r\n\r\n  useEffect(() => {\r\n    if (comment && comment !== originalComment) {\r\n      setExistingEdit(true);\r\n    } else {\r\n      setExistingEdit(false);\r\n    }\r\n  }, [comment]);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && buttonRef.current) {\r\n      const buttonRect = buttonRef.current.getBoundingClientRect();\r\n      const windowHeight = window.innerHeight;\r\n      setOpenAbove(buttonRect.bottom + 220 > windowHeight);\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  const handleReviewClick = () => {\r\n    setShowCommentBox((prev) => {\r\n      if (!prev) {\r\n        setOriginalComment(\r\n          candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n        );\r\n      }\r\n      return !prev;\r\n    });\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    if (!comment) {\r\n      showNotification(\"Nothing to copy.\", \"warning\");\r\n      return;\r\n    }\r\n    navigator.clipboard\r\n      .writeText(comment)\r\n      .then(() => {\r\n        showNotification(\"Copied to clipboard!\", \"success\");\r\n      })\r\n      .catch(() => {\r\n        showNotification(\"Failed to copy.\", \"error\");\r\n      });\r\n  };\r\n\r\n  const handleSend = async () => {\r\n    setConfirmModal(false);\r\n    setExistingEdit(false);\r\n    setShowCommentBox(false);\r\n    setCandidateEditId(\"\");\r\n    setCandidateEditId2(\"\");\r\n    setLoading(true);\r\n    const requestObj: WhyFitReasonPayload = {\r\n      candidate_contact_id: candidate?.candidate_contactid,\r\n      vacancy_refno: selectedVacancy?.refno,\r\n      fitness_reason_text: comment,\r\n      author_email: selectedVacancy.locked_by,\r\n    };\r\n    try {\r\n      let response: any = await fetch(\"/api/candidates/fitness_reason\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          Accept: \"application/json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(requestObj),\r\n      });\r\n      response = await response.json();\r\n      if (response?.status_code === 200) {\r\n        showNotification(\"Comment submitted successfully!\", \"success\");\r\n        if (setCandidates) {\r\n          setCandidates((prev) =>\r\n            prev.map((c) =>\r\n              c.candidate_contactid === candidate?.candidate_contactid\r\n                ? {\r\n                    ...c,\r\n                    candidate_data: {\r\n                      ...c.candidate_data,\r\n                      current_fitness_reason: {\r\n                        reason: comment,\r\n                        author:\r\n                          candidate.candidate_data.current_fitness_reason\r\n                            ?.author || \"\",\r\n                        timestamp:\r\n                          candidate.candidate_data.current_fitness_reason\r\n                            ?.timestamp || \"\",\r\n                      },\r\n                    },\r\n                  }\r\n                : c\r\n            )\r\n          );\r\n        }\r\n      } else {\r\n        showNotification(\r\n          \"Error submitting comment. Please try again.\",\r\n          \"error\"\r\n        );\r\n      }\r\n      setLoading(false);\r\n    } catch (error) {\r\n      setLoading(false);\r\n      console.error(error);\r\n      showNotification(\"Error submitting comment. Please try again.\", \"error\");\r\n    }\r\n  };\r\n\r\n  const handleSave = () => {\r\n    if (comment.length > 0) {\r\n      handleSend();\r\n    } else {\r\n      setConfirmModal(true);\r\n    }\r\n  };\r\n\r\n  function cancelRevertChanges() {\r\n    setCandidateEditId2(\"\");\r\n  }\r\n\r\n  function confirmDiscardChanges() {\r\n    setExistingEdit(false);\r\n    setShowCommentBox(false);\r\n    setComment(originalComment);\r\n    setCandidateEditId(\"\");\r\n    setCandidateEditId2(\"\");\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox) {\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"auto\";\r\n    }\r\n    setExpandPopupOpen(showCommentBox);\r\n\r\n    return () => {\r\n      document.body.style.overflow = \"auto\"; // Clean up on unmount\r\n    };\r\n  }, [showCommentBox]);\r\n\r\n  // for feature use\r\n  // const isEditable = IS_WHY_FIT_EDITABLE === \"false\";\r\n  const isEditable = false;\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center w-full\" ref={buttonRef}>\r\n      <div className=\"flex items-center space-x-2 relative w-full\">\r\n        <motion.div className=\"cursor-pointer w-full\">\r\n          <textarea\r\n            cols={3}\r\n            value={comment}\r\n            onChange={(e) => {\r\n              setCandidateEditId2(candidate?.candidate_contactid);\r\n              if (\r\n                existingEdit &&\r\n                candidateEditId !== candidate?.candidate_contactid\r\n              ) {\r\n                return;\r\n              }\r\n              setCandidateEditId(candidate?.candidate_contactid);\r\n              setComment(e.target.value);\r\n            }}\r\n            className=\"w-full border border-gray-300 rounded-lg p-2 my-1\"\r\n            disabled={!isEditable || isTableReadOnly}\r\n            placeholder={undefined}\r\n            title={undefined}\r\n          />\r\n          <div className=\"flex gap-1.5 mb-1\">\r\n            {comment !== originalComment && (\r\n              <>\r\n                <Button\r\n                  onClick={() => confirmDiscardChanges()}\r\n                  className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]\"\r\n                >\r\n                  Discard\r\n                </Button>\r\n\r\n                <Button\r\n                  size=\"sm\"\r\n                  onClick={() => handleSave()}\r\n                  disabled={!isEditable || comment === originalComment}\r\n                  className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]\"\r\n                >\r\n                  {loading ? <Loading /> : \"Save\"}\r\n                </Button>\r\n              </>\r\n            )}\r\n            <div className=\"absolute -top-0.5 -right-0.5\">\r\n              <AppToolTip\r\n                text=\"Click To Expand\"\r\n                header={\r\n                  <Expand\r\n                    onClick={() => handleReviewClick()}\r\n                    size={8}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-4 w-4 text-[6px]\"\r\n                  />\r\n                }\r\n              />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n      <AnimatePresence>\r\n        {showCommentBox && (\r\n          <div className=\"fixed top-0 left-0 right-0 bottom-0 h-full z-[999] bg-[#33333320] flex justify-center items-center\">\r\n            <motion.div\r\n              ref={commentBoxRef}\r\n              initial={{ opacity: 0, scale: 0.95 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.95 }}\r\n              transition={{ duration: 0.25 }}\r\n              className=\"fixed top-[40vh] left-[40vw] w-[500px] h-[300px] bg-white shadow-lg rounded-lg p-4 border\"\r\n            >\r\n              <div className=\"flex justify-between items-center mb-1\">\r\n                <p className=\"text-sm font-semibold\">Why-Fit</p>\r\n                <div className=\"flex gap-2\">\r\n                  <AppToolTip\r\n                    text=\"Copy Text\"\r\n                    header={\r\n                      <Copy\r\n                        onClick={handleCopy}\r\n                        className=\"bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5\"\r\n                      />\r\n                    }\r\n                  />\r\n                  <AppToolTip\r\n                    text=\"Click To Minimize\"\r\n                    header={\r\n                      <Minimize\r\n                        onClick={() => setShowCommentBox(false)}\r\n                        className=\"bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5\"\r\n                      />\r\n                    }\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <textarea\r\n                ref={expandedTextareaRef}\r\n                autoFocus\r\n                disabled={!isEditable}\r\n                className={`w-full ${\r\n                  isEditable ? \"h-[200px]\" : \"h-[235px]\"\r\n                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                placeholder=\"Share your thoughts...\"\r\n                value={comment}\r\n                onChange={(e) => setComment(e.target.value)}\r\n              />\r\n\r\n              {isEditable && (\r\n                <div className=\"flex justify-end gap-1.5 mt-2\">\r\n                  <Button\r\n                    onClick={confirmDiscardChanges}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Discard\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleSave}\r\n                    disabled={comment === originalComment}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Save {loading && <Loading />}\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      <AnimatePresence>\r\n        {confirmModal && (\r\n          <motion.div\r\n            ref={commentBoxRef}\r\n            initial={{ opacity: 0, y: openAbove ? -10 : 10, x: -10 }}\r\n            animate={{ opacity: 1, y: 0, x: -50 }}\r\n            exit={{ opacity: 0, y: openAbove ? -10 : 10 }}\r\n            transition={{ duration: 0.3 }}\r\n            className={`absolute z-[9] ${\r\n              openAbove ? \"bottom-10\" : \"top-10\"\r\n            } w-[300px] h-[170px] bg-white shadow-lg rounded-lg p-4 border`}\r\n          >\r\n            <div className=\"flex justify-between items-center mb-1\">\r\n              <p className=\"text-sm font-semibold\">Please confirm</p>\r\n              <CircleX\r\n                size={14}\r\n                className=\"cursor-pointer\"\r\n                onClick={() => setConfirmModal(false)}\r\n              />\r\n            </div>\r\n            <p className=\"text-sm font-semibold\">\r\n              Comments are empty. Please confirm to submit.\r\n            </p>\r\n\r\n            {/* {viewState ? null : ( */}\r\n            <div className=\"flex gap-4\">\r\n              <Button\r\n                onClick={() => handleSend()}\r\n                className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n              >\r\n                Confirm\r\n              </Button>\r\n              <Button\r\n                onClick={() => setConfirmModal(false)}\r\n                className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      <Modal\r\n        isOpen={Boolean(\r\n          candidateEditId &&\r\n            candidateEditId2 &&\r\n            candidateEditId === candidate.candidate_contactid &&\r\n            candidateEditId2 !== candidate.candidate_contactid\r\n        )}\r\n        onClose={() => setCandidateEditId2(\"\")}\r\n        title=\"Changes not saved!\"\r\n      >\r\n        <p className=\"mb-5 h-20\">\r\n          Your recent change to the why fit field has not been saved yet. Choose\r\n          &lsquo;Cancel&lsquo; to continue text changes or &lsquo;Discard\r\n          Chanages&lsquo; to revert or &lsquo;Save&lsquo; your changes.\r\n        </p>\r\n        <div className=\"flex justify-end gap-1.5\">\r\n          <Button\r\n            onClick={() => {\r\n              handleSave();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"green\",\r\n              border: \"1px solid green\",\r\n              background: \"#fff\",\r\n            }}\r\n          >\r\n            Save {loading && <Loading />}\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              confirmDiscardChanges();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"#e95151\",\r\n              border: \"1px solid #e95151\",\r\n              background: \"#fff\",\r\n            }}\r\n          >\r\n            Discard Changes\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              cancelRevertChanges();\r\n            }}\r\n            style={{ background: \"white\" }}\r\n            className=\"text-gray-900 border border-gray-900 rounded-md\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WhyFitAction;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAMA;AACA;AACA;AACA;AAVA;AAFA;AAEA;AAFA;AAAA;AAAA;;;;;;;;;;;AAcA,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,aAAa,EACb,eAAe,EACf,YAAY,EACZ,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,eAAe,EAahB;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD,WAAW,gBAAgB,wBAAwB,UAAU;IAE/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnC,WAAW,gBAAgB,wBAAwB,UAAU;IAG/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA8B;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,oBAAoB,OAAO,EAAE;gBACjD,MAAM,WAAW,oBAAoB,OAAO;gBAC5C,wBAAwB;gBACxB;8CAAsB;wBACpB,SAAS,KAAK;wBACd,SAAS,iBAAiB,CACxB,SAAS,KAAK,CAAC,MAAM,EACrB,SAAS,KAAK,CAAC,MAAM;oBAEzB;;YACF;QACF;iCAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,WAAW,gBAAgB;YAC5C,IAAI,UAAU;gBACZ,WAAW,SAAS,MAAM,IAAI;gBAC9B,mBAAmB,SAAS,MAAM,IAAI;YACxC;QACF;iCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW,YAAY,iBAAiB;gBAC1C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF;iCAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,UAAU,OAAO,EAAE;gBACvC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;gBAC1D,MAAM,eAAe,OAAO,WAAW;gBACvC,aAAa,WAAW,MAAM,GAAG,MAAM;YACzC;QACF;iCAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB;QACxB,kBAAkB,CAAC;YACjB,IAAI,CAAC,MAAM;gBACT,mBACE,WAAW,gBAAgB,wBAAwB,UAAU;YAEjE;YACA,OAAO,CAAC;QACV;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ,iBAAiB,oBAAoB;YACrC;QACF;QACA,UAAU,SAAS,CAChB,SAAS,CAAC,SACV,IAAI,CAAC;YACJ,iBAAiB,wBAAwB;QAC3C,GACC,KAAK,CAAC;YACL,iBAAiB,mBAAmB;QACtC;IACJ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,WAAW;QACX,MAAM,aAAkC;YACtC,sBAAsB,WAAW;YACjC,eAAe,iBAAiB;YAChC,qBAAqB;YACrB,cAAc,gBAAgB,SAAS;QACzC;QACA,IAAI;YACF,IAAI,WAAgB,MAAM,MAAM,kCAAkC;gBAChE,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,WAAW,MAAM,SAAS,IAAI;YAC9B,IAAI,UAAU,gBAAgB,KAAK;gBACjC,iBAAiB,mCAAmC;gBACpD,IAAI,eAAe;oBACjB,cAAc,CAAC,OACb,KAAK,GAAG,CAAC,CAAC,IACR,EAAE,mBAAmB,KAAK,WAAW,sBACjC;gCACE,GAAG,CAAC;gCACJ,gBAAgB;oCACd,GAAG,EAAE,cAAc;oCACnB,wBAAwB;wCACtB,QAAQ;wCACR,QACE,UAAU,cAAc,CAAC,sBAAsB,EAC3C,UAAU;wCAChB,WACE,UAAU,cAAc,CAAC,sBAAsB,EAC3C,aAAa;oCACrB;gCACF;4BACF,IACA;gBAGV;YACF,OAAO;gBACL,iBACE,+CACA;YAEJ;YACA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,WAAW;YACX,QAAQ,KAAK,CAAC;YACd,iBAAiB,+CAA+C;QAClE;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB;QACF,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,SAAS;QACP,oBAAoB;IACtB;IAEA,SAAS;QACP,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YACA,mBAAmB;YAEnB;0CAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,sBAAsB;gBAC/D;;QACF;iCAAG;QAAC;KAAe;IAEnB,kBAAkB;IAClB,sDAAsD;IACtD,MAAM,aAAa;IAEnB,qBACE,6LAAC;QAAI,WAAU;QAAoC,KAAK;;0BACtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,WAAU;;sCACpB,6LAAC;4BACC,MAAM;4BACN,OAAO;4BACP,UAAU,CAAC;gCACT,oBAAoB,WAAW;gCAC/B,IACE,gBACA,oBAAoB,WAAW,qBAC/B;oCACA;gCACF;gCACA,mBAAmB,WAAW;gCAC9B,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC3B;4BACA,WAAU;4BACV,UAAU,CAAC,cAAc;4BACzB,aAAa;4BACb,OAAO;;;;;;sCAET,6LAAC;4BAAI,WAAU;;gCACZ,YAAY,iCACX;;sDACE,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,IAAM;4CACf,WAAU;sDACX;;;;;;sDAID,6LAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM;4CACf,UAAU,CAAC,cAAc,YAAY;4CACrC,WAAU;sDAET,wBAAU,6LAAC,yHAAA,CAAA,UAAO;;;;uDAAM;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,4HAAA,CAAA,UAAU;wCACT,MAAK;wCACL,sBACE,6LAAC,yMAAA,CAAA,SAAM;4CACL,SAAS,IAAM;4CACf,MAAM;4CACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBACnC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBAChC,YAAY;4BAAE,UAAU;wBAAK;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,UAAU;gDACT,MAAK;gDACL,sBACE,6LAAC,qMAAA,CAAA,OAAI;oDACH,SAAS;oDACT,WAAU;;;;;;;;;;;0DAIhB,6LAAC,4HAAA,CAAA,UAAU;gDACT,MAAK;gDACL,sBACE,6LAAC,6MAAA,CAAA,WAAQ;oDACP,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6LAAC;gCACC,KAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAW,CAAC,OAAO,EACjB,6EAA2B,YAC5B,mFAAmF,CAAC;gCACrF,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;4BAG3C,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,YAAY;wCACtB,WAAU;wCACV,MAAK;;4CACN;4CACO,yBAAW,6LAAC,yHAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC,4LAAA,CAAA,kBAAe;0BACb,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG,YAAY,CAAC,KAAK;wBAAI,GAAG,CAAC;oBAAG;oBACvD,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,GAAG,CAAC;oBAAG;oBACpC,MAAM;wBAAE,SAAS;wBAAG,GAAG,YAAY,CAAC,KAAK;oBAAG;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAC,eAAe,EACzB,YAAY,cAAc,SAC3B,6DAA6D,CAAC;;sCAE/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,6LAAC,+MAAA,CAAA,UAAO;oCACN,MAAM;oCACN,WAAU;oCACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;sCAGnC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAKrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,IAAM;oCACf,WAAU;8CACX;;;;;;8CAGD,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC,uHAAA,CAAA,UAAK;gBACJ,QAAQ,QACN,mBACE,oBACA,oBAAoB,UAAU,mBAAmB,IACjD,qBAAqB,UAAU,mBAAmB;gBAEtD,SAAS,IAAM,oBAAoB;gBACnC,OAAM;;kCAEN,6LAAC;wBAAE,WAAU;kCAAY;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,YAAY;gCACd;;oCACD;oCACO,yBAAW,6LAAC,yHAAA,CAAA,UAAO;;;;;;;;;;;0CAE3B,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,YAAY;gCACd;0CACD;;;;;;0CAGD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,OAAO;oCAAE,YAAY;gCAAQ;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAhbM;;QA+ByB,4HAAA,CAAA,kBAAe;;;KA/BxC;uCAkbS"}}, {"offset": {"line": 2480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Button.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\ninterface ButtonProps {\r\n  onClick: () => void;\r\n  children: ReactNode;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n  className?: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  style?: any;\r\n}\r\n\r\nconst Button = ({\r\n  onClick,\r\n  children,\r\n  disabled,\r\n  loading,\r\n  className,\r\n  style,\r\n}: ButtonProps) => {\r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      style={style}\r\n      className={`flex justify-center ml-4 px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 disabled:bg-gray-600 ${className}`}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {loading && (\r\n        <svg\r\n          className=\"ml-2 mt-0.5 size-5 animate-spin text-white\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          ></circle>\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          ></path>\r\n        </svg>\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button;\r\n"], "names": [], "mappings": ";;;;;AAYA,MAAM,SAAS,CAAC,EACd,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,SAAS,EACT,KAAK,EACO;IACZ,qBACE,6LAAC;QACC,SAAS;QACT,OAAO;QACP,WAAW,CAAC,4GAA4G,EAAE,WAAW;QACrI,UAAU;;YAET;YACA,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;;AAMd;KAxCM;uCA0CS"}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2552, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/DiscardModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Button from \"../Button\";\r\nimport Modal from \"../Modal\";\r\n\r\ninterface DiscardModalProps {\r\n  isOpenDiscardModal: boolean;\r\n  cancelTabChange: () => void;\r\n  confirmTabChange: () => void;\r\n  loadingPost?: boolean;\r\n  saveChanges?: () => void;\r\n  message?: string;\r\n  title?: string;\r\n  confirmButtonText?: string;\r\n}\r\n\r\nconst DiscardModal = (props: DiscardModalProps) => {\r\n  const {\r\n    isOpenDiscardModal,\r\n    loadingPost,\r\n    cancelTabChange,\r\n    confirmTabChange,\r\n    saveChanges,\r\n    message,\r\n    title,\r\n    confirmButtonText,\r\n  } = props;\r\n  return (\r\n    <Modal\r\n      isOpen={isOpenDiscardModal}\r\n      onClose={() => cancelTabChange()}\r\n      title={title ? title : \"Changes not saved!\"}\r\n    >\r\n      <p className=\"mb-5 h-20\">\r\n        {message\r\n          ? message\r\n          : \"Your changes haven’t been saved yet. Choose ‘Discard Changes‘ to revert or ‘Cancel‘ to go back.\"}\r\n      </p>\r\n      <div className=\"flex justify-end\">\r\n        {saveChanges ? (\r\n          <Button\r\n            onClick={() => {\r\n              saveChanges();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"green\",\r\n              border: \"1px solid green\",\r\n              background: \"#fff\",\r\n            }}\r\n            loading={loadingPost}\r\n          >\r\n            Save\r\n          </Button>\r\n        ) : null}\r\n        <Button\r\n          onClick={async () => {\r\n            try {\r\n              await confirmTabChange();\r\n            } catch (err) {\r\n              console.error(\"Error during confirmTabChange:\", err);\r\n            }\r\n          }}\r\n          className=\"\"\r\n          style={{\r\n            color: confirmButtonText ? \"#16a34a \" : \"#e95151\",\r\n            border: `1px solid ${confirmButtonText ? \"#16a34a \" : \"#e95151\"}`,\r\n            background: \"#fff\",\r\n          }}\r\n          loading={loadingPost}\r\n        >\r\n          {confirmButtonText ?? \"Discard Changes\"}\r\n        </Button>\r\n        <Button\r\n          onClick={() => {\r\n            cancelTabChange();\r\n          }}\r\n          style={{ background: \"#222\" }}\r\n          className=\"bg-gray-900 hover:bg-gray-950\"\r\n          loading={loadingPost}\r\n        >\r\n          Cancel\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default DiscardModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAaA,MAAM,eAAe,CAAC;IACpB,MAAM,EACJ,kBAAkB,EAClB,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,WAAW,EACX,OAAO,EACP,KAAK,EACL,iBAAiB,EAClB,GAAG;IACJ,qBACE,6LAAC,uHAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM;QACf,OAAO,QAAQ,QAAQ;;0BAEvB,6LAAC;gBAAE,WAAU;0BACV,UACG,UACA;;;;;;0BAEN,6LAAC;gBAAI,WAAU;;oBACZ,4BACC,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP;wBACF;wBACA,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,YAAY;wBACd;wBACA,SAAS;kCACV;;;;;+BAGC;kCACJ,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP,IAAI;gCACF,MAAM;4BACR,EAAE,OAAO,KAAK;gCACZ,QAAQ,KAAK,CAAC,kCAAkC;4BAClD;wBACF;wBACA,WAAU;wBACV,OAAO;4BACL,OAAO,oBAAoB,aAAa;4BACxC,QAAQ,CAAC,UAAU,EAAE,oBAAoB,aAAa,WAAW;4BACjE,YAAY;wBACd;wBACA,SAAS;kCAER,qBAAqB;;;;;;kCAExB,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP;wBACF;wBACA,OAAO;4BAAE,YAAY;wBAAO;wBAC5B,WAAU;wBACV,SAAS;kCACV;;;;;;;;;;;;;;;;;;AAMT;KAtEM;uCAwES"}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/updatelocalStoredVacancyTimeStamp.ts"], "sourcesContent": ["\"use client\";\r\nexport const updateLocalStoredVacancyTimeStamp = (\r\n  refreshVacancy_id: string,\r\n  mercuryPortal: boolean | undefined,\r\n  customTimestamp?: string // <-- optional third parameter\r\n) => {\r\n  if (!refreshVacancy_id) return;\r\n\r\n  const utcTimestamp = customTimestamp || new Date().toISOString(); // use custom timestamp if provided\r\n  const key = mercuryPortal\r\n    ? \"mercuryPortalVacancies\"\r\n    : \"selectedVacancyTimestamps\";\r\n\r\n  try {\r\n    const existing = localStorage.getItem(key);\r\n    const data: Record<string, string> = existing ? JSON.parse(existing) : {};\r\n\r\n    const normalizedVacancyId = refreshVacancy_id.toLowerCase(); // normalize here\r\n    data[normalizedVacancyId] = utcTimestamp;\r\n\r\n    localStorage.setItem(key, JSON.stringify(data));\r\n  } catch (err) {\r\n    console.error(\"Error updating vacancy timestamp in localStorage:\", err);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACO,MAAM,oCAAoC,CAC/C,mBACA,eACA,gBAAyB,+BAA+B;;IAExD,IAAI,CAAC,mBAAmB;IAExB,MAAM,eAAe,mBAAmB,IAAI,OAAO,WAAW,IAAI,mCAAmC;IACrG,MAAM,MAAM,gBACR,2BACA;IAEJ,IAAI;QACF,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,MAAM,OAA+B,WAAW,KAAK,KAAK,CAAC,YAAY,CAAC;QAExE,MAAM,sBAAsB,kBAAkB,WAAW,IAAI,iBAAiB;QAC9E,IAAI,CAAC,oBAAoB,GAAG;QAE5B,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;IAC3C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,qDAAqD;IACrE;AACF"}}, {"offset": {"line": 2680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/ThumbAction.tsx"], "sourcesContent": ["import { ThumbsDown, <PERSON>hum<PERSON>Up, Circle<PERSON>, <PERSON> } from \"lucide-react\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport { Candidate, RecruiterReview, Vacancy } from \"@/app/candidates/helper\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport DiscardModal from \"./DiscardModal\";\r\nimport { unFormattedDateWithBrowserTimezoneInDDMMYY } from \"@/utils/utils\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { initAppInsights } from \"@/library/appInsights\";\r\nimport { NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL } from \"@/api/config\";\r\nimport { updateLocalStoredVacancyTimeStamp } from \"@/utils/updatelocalStoredVacancyTimeStamp\";\r\n\r\ntype ReviewState = \"like\" | \"dislike\" | \"maybe\" | null;\r\n\r\nconst ThumbAction = ({\r\n  candidate,\r\n  candidates,\r\n  setCandidates,\r\n  vacancyId,\r\n  vacancyCandidates,\r\n  setVacancyCandidates,\r\n  vacancyRefNo,\r\n  selectedVacancy,\r\n  mercuryPortal,\r\n  session,\r\n  emailId,\r\n  isTableReadOnly,\r\n  setCurrentActionableCandidateId,\r\n}: {\r\n  candidate: Candidate;\r\n  candidates: Candidate[];\r\n  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  vacancyId: string;\r\n  vacancyCandidates: {\r\n    [key: string]: Candidate[];\r\n  };\r\n  setVacancyCandidates: React.Dispatch<\r\n    React.SetStateAction<{ [key: string]: Candidate[] } | null>\r\n  >;\r\n  vacancyRefNo: string;\r\n  selectedVacancy: Vacancy;\r\n  mercuryPortal: boolean;\r\n  session: any; // Adjust type as needed\r\n  emailId: string;\r\n  isTableReadOnly: boolean; // New prop to control table read-only state\r\n  setCurrentActionableCandidateId: React.Dispatch<\r\n    React.SetStateAction<number | null>\r\n  >;\r\n}) => {\r\n  const [reviewState, setReviewState] = useState<ReviewState>(null);\r\n  const [showCommentBox, setShowCommentBox] = useState(false);\r\n  const [viewState, setViewState] = useState(false);\r\n  const [originalComment, setOriginalComment] = useState(\"\");\r\n  const [comment, setComment] = useState(\"\");\r\n  const commentBoxRef = useRef<HTMLDivElement | null>(null);\r\n  const buttonRef = useRef<HTMLDivElement | null>(null);\r\n  const [openAbove, setOpenAbove] = useState(false);\r\n  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [feedbackDate, setFeedbackDate] = useState(\"\");\r\n  const abortRef = useRef<AbortController | null>(null);\r\n\r\n  const { showNotification } = useNotification();\r\n  useEffect(() => {\r\n    if (mercuryPortal) {\r\n      initAppInsights();\r\n\r\n      window.onerror = (msg, src, line, col, err) => {\r\n        getAppInsights()?.trackException({\r\n          error: err || new Error(String(msg)),\r\n        });\r\n      };\r\n    }\r\n  }, [mercuryPortal]);\r\n  useEffect(() => {\r\n    const decision = candidate?.candidate_data?.recruiter_review_decision;\r\n    if (decision && decision !== null) {\r\n      setReviewState(decision.vote as ReviewState);\r\n      setComment(decision.comment || \"\");\r\n      setOriginalComment(decision.comment || \"\");\r\n    }\r\n    let lastFeedbackDate =\r\n      candidate?.candidate_data?.recruiter_review_decision?.feedback_timestamp;\r\n    if (lastFeedbackDate) {\r\n      lastFeedbackDate = unFormattedDateWithBrowserTimezoneInDDMMYY(\r\n        lastFeedbackDate,\r\n        \"UTC_TO_LOCAL\"\r\n      );\r\n      setFeedbackDate(lastFeedbackDate);\r\n    }\r\n  }, [candidate]);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        commentBoxRef.current &&\r\n        !commentBoxRef.current.contains(event.target as Node)\r\n      ) {\r\n        closeCommentBox();\r\n      }\r\n    };\r\n    if (showCommentBox) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [showCommentBox, comment, candidate]);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && buttonRef.current) {\r\n      const buttonRect = buttonRef.current.getBoundingClientRect();\r\n      const windowHeight = window.innerHeight;\r\n      setOpenAbove(buttonRect.bottom + 220 > windowHeight);\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  const handleReviewClick = (state: ReviewState, isLike: boolean) => {\r\n    if (isTableReadOnly) return; // Prevent action if table is read-only\r\n    setViewState(false);\r\n    setReviewState((prev) => (prev === state ? null : state));\r\n    handleSend(reviewState === state ? null : state);\r\n    if (!isLike) {\r\n      setShowCommentBox(true);\r\n    } else {\r\n      setShowCommentBox(false);\r\n    }\r\n  };\r\n\r\n  const sendToastMessage = (\r\n    comment: string,\r\n    state: string | null,\r\n    type: \"success\" | \"error\"\r\n  ) => {\r\n    let message = \"\";\r\n    if (type === \"error\") {\r\n      message = \"Error submitting review. Please try again.\";\r\n    } else if (type === \"success\") {\r\n      if (comment !== originalComment) {\r\n        message = \"Review submitted successfully.\";\r\n      } else if (state === \"like\") {\r\n        message = \"You've marked this candidate as a good fit.\";\r\n      } else if (state === \"dislike\") {\r\n        message = \"You've marked this candidate as not a good fit.\";\r\n      } else if (state === null) {\r\n        message = \"Your review action removed successfully.\";\r\n      }\r\n    }\r\n    showNotification(message, type);\r\n  };\r\n  const refreshVacancy_id = selectedVacancy?.vacancy_id || (vacancyId ?? \"\");\r\n  const handleSend = async (\r\n    state: ReviewState,\r\n    isSubmitFromCommentButton = false\r\n  ) => {\r\n    if (!candidate?.candidate_contactid || !vacancyRefNo) {\r\n      showNotification(\"Missing candidate or vacancy reference\", \"error\");\r\n      return;\r\n    }\r\n    if (!abortRef.current) {\r\n      abortRef.current = new AbortController();\r\n    } else {\r\n      abortRef.current.abort();\r\n      abortRef.current = new AbortController();\r\n    }\r\n\r\n    setShowCommentBox(false);\r\n    setIsOpenDiscardModal(false);\r\n    const uName =\r\n      localStorage.getItem(\"userName\") || localStorage.getItem(\"emailId\");\r\n\r\n    setCurrentActionableCandidateId(candidate.id);\r\n\r\n    if (isSubmitFromCommentButton) {\r\n      setCurrentActionableCandidateId(null);\r\n    }\r\n\r\n    const requestObj: RecruiterReview = {\r\n      candidate_contact_id: candidate?.candidate_contactid,\r\n      vacancy_refno: vacancyRefNo,\r\n      vote: state,\r\n      comment: state === null ? \"\" : comment,\r\n      reviewer_email: uName || selectedVacancy?.locked_by,\r\n      portal_name: mercuryPortal ? \"Mercury\" : \"Recruiter\",\r\n      feedback_timestamp: new Date().toISOString(),\r\n    };\r\n    try {\r\n      setLoading(true);\r\n      const response = await trackedFetch(\"/api/vacancies\", {\r\n        method: \"POST\",\r\n        signal: abortRef.current.signal,\r\n        headers: {\r\n          Accept: \"application/json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(requestObj),\r\n      });\r\n      if (response.ok) {\r\n        const updatedCandidates = candidates.map((can: Candidate) => {\r\n          if (can?.candidate_contactid === candidate?.candidate_contactid) {\r\n            return {\r\n              ...can,\r\n              candidate_data: {\r\n                ...can.candidate_data,\r\n                recruiter_review_decision: requestObj,\r\n              },\r\n            };\r\n          }\r\n          return can;\r\n        });\r\n        setCandidates(updatedCandidates);\r\n        setVacancyCandidates({\r\n          ...vacancyCandidates,\r\n          [vacancyId]: updatedCandidates,\r\n        });\r\n        sendToastMessage(\r\n          requestObj.comment,\r\n          reviewState === state ? null : state,\r\n          \"success\"\r\n        );\r\n        setOriginalComment(requestObj.comment);\r\n        getAppInsights()?.trackEvent({\r\n          name: mercuryPortal\r\n            ? \"FE_MercuryPostVacanciesThumbAction\"\r\n            : \"FE_PostVacanciesThumbAction\",\r\n          properties: {\r\n            reviewerEmail: emailId || session?.user?.email || \"unknown\",\r\n            candidateId: candidate?.candidate_contactid,\r\n            embeddedFrom: document.referrer,\r\n            context: mercuryPortal\r\n              ? \"MercuryPostVacanciesThumbAction\"\r\n              : \"PostVacanciesThumbAction\",\r\n          },\r\n        });\r\n        updateLocalStoredVacancyTimeStamp(\r\n          refreshVacancy_id,\r\n          mercuryPortal ?? undefined\r\n        );\r\n      } else {\r\n        sendToastMessage(\r\n          requestObj.comment,\r\n          reviewState === state ? null : state,\r\n          \"error\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      showNotification(\"Error submitting review. Please try again.\", \"error\");\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"Mercury Post Vacancies ThumbAction api with error is \" + error\r\n            : \"Post Vacancies ThumbAction api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setComment(\"\");\r\n    }\r\n  };\r\n\r\n  const handleDiscardChange = () => {\r\n    setComment(\r\n      candidate?.candidate_data?.recruiter_review_decision?.comment || \"\"\r\n    );\r\n    setIsOpenDiscardModal(false);\r\n    setShowCommentBox(false);\r\n    setCurrentActionableCandidateId(null);\r\n  };\r\n\r\n  const handleCancelChange = () => {\r\n    setIsOpenDiscardModal(false);\r\n  };\r\n\r\n  const closeCommentBox = () => {\r\n    const originalComment =\r\n      candidate?.candidate_data?.recruiter_review_decision?.comment || \"\";\r\n    if (comment.trim() !== originalComment.trim()) {\r\n      setIsOpenDiscardModal(true);\r\n    } else {\r\n      setShowCommentBox(false);\r\n      setCurrentActionableCandidateId(null);\r\n    }\r\n  };\r\n\r\n  let isEditable = NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL === \"false\";\r\n\r\n  if (isTableReadOnly) {\r\n    isEditable = false;\r\n  }\r\n\r\n  if (mercuryPortal) {\r\n    isEditable = isTableReadOnly ? false : mercuryPortal;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"relative flex\" ref={buttonRef}>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <motion.div\r\n            onClick={() =>\r\n              isEditable\r\n                ? handleReviewClick(\"like\", reviewState === \"like\")\r\n                : null\r\n            }\r\n            animate={{ scale: reviewState === \"like\" ? 1.3 : 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n            whileHover={{\r\n              scale: reviewState === \"like\" ? 1.3 : !isEditable ? 1 : 1.1,\r\n            }}\r\n          >\r\n            <ThumbsUp\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                !isEditable\r\n                  ? \"text-green-950\"\r\n                  : reviewState === \"like\"\r\n                  ? \"text-green-700\"\r\n                  : \"text-green-600\"\r\n              }`}\r\n              fill={\r\n                !isEditable\r\n                  ? reviewState === \"like\"\r\n                    ? \"#14532d\"\r\n                    : \"white\"\r\n                  : reviewState === \"like\"\r\n                  ? \"#43bc6e\"\r\n                  : \"white\"\r\n              }\r\n            />\r\n          </motion.div>\r\n          <motion.div\r\n            onClick={() =>\r\n              isEditable\r\n                ? handleReviewClick(\"dislike\", reviewState === \"dislike\")\r\n                : null\r\n            }\r\n            animate={{ scale: reviewState === \"dislike\" ? 1.3 : 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n            whileHover={{\r\n              scale: reviewState === \"dislike\" ? 1.3 : !isEditable ? 1 : 1.1,\r\n            }}\r\n          >\r\n            <ThumbsDown\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                !isEditable\r\n                  ? \"text-red-900\"\r\n                  : reviewState === \"dislike\"\r\n                  ? \"text-red-700\"\r\n                  : \"text-red-600\"\r\n              }`}\r\n              fill={\r\n                !isEditable\r\n                  ? reviewState === \"dislike\"\r\n                    ? \"#7f1d1d\"\r\n                    : \"white\"\r\n                  : reviewState === \"dislike\"\r\n                  ? \"#f46161\"\r\n                  : \"white\"\r\n              }\r\n            />\r\n          </motion.div>\r\n          {(reviewState === \"like\" || reviewState === \"dislike\") &&\r\n            candidate?.candidate_data?.recruiter_review_decision?.comment && (\r\n              <motion.div\r\n                transition={{ type: \"spring\", stiffness: 300 }}\r\n                whileHover={{ scale: !showCommentBox ? 1.3 : 1.1 }}\r\n              >\r\n                <AppToolTip\r\n                  text=\"View Comments\"\r\n                  header={\r\n                    <Eye\r\n                      onClick={() => {\r\n                        setViewState(true);\r\n                        setShowCommentBox(true);\r\n                      }}\r\n                      size={20}\r\n                      className={`cursor-pointer text-blue-900`}\r\n                    />\r\n                  }\r\n                />\r\n              </motion.div>\r\n            )}\r\n        </div>\r\n        <AnimatePresence>\r\n          {showCommentBox && (\r\n            <motion.div\r\n              ref={commentBoxRef}\r\n              initial={{ opacity: 0, y: openAbove ? -10 : 10, x: 10 }}\r\n              animate={{ opacity: 1, y: 0, x: -180 }}\r\n              exit={{ opacity: 0, y: openAbove ? -10 : 10 }}\r\n              transition={{ duration: 0.3 }}\r\n              className={`absolute z-[1] ${\r\n                openAbove ? \"bottom-10\" : \"top-10\"\r\n              } w-[300px] h-[200px] bg-white shadow-lg rounded-lg p-4 border`}\r\n            >\r\n              <div className=\"flex justify-between items-center mb-1\">\r\n                <p className=\"text-sm font-semibold\">\r\n                  {reviewState === \"like\"\r\n                    ? \"Why did you like this?\"\r\n                    : reviewState === \"dislike\"\r\n                    ? \"Why did you dislike this?\"\r\n                    : \"What are you unsure about?\"}\r\n                </p>\r\n                <CircleX\r\n                  size={14}\r\n                  className=\"cursor-pointer\"\r\n                  onClick={closeCommentBox}\r\n                />\r\n              </div>\r\n              <textarea\r\n                className={`w-full ${\r\n                  viewState ? \"h-[140px]\" : \"h-[100px]\"\r\n                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                placeholder=\"Share your thoughts...\"\r\n                value={comment}\r\n                onChange={(e) => setComment(e.target.value)}\r\n                disabled={viewState || loading}\r\n              />\r\n              {viewState ? null : (\r\n                <div className=\"flex gap-2\">\r\n                  <Button\r\n                    onClick={() => {\r\n                      setComment(\r\n                        candidate?.candidate_data?.recruiter_review_decision\r\n                          ?.comment || \"\"\r\n                      );\r\n                      setShowCommentBox(false);\r\n                      setCurrentActionableCandidateId(null);\r\n                    }}\r\n                    disabled={comment.trim() === \"\" || loading}\r\n                    className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n                    size=\"icon\"\r\n                  >\r\n                    Discard\r\n                  </Button>\r\n\r\n                  <Button\r\n                    onClick={() => handleSend(reviewState, true)}\r\n                    disabled={comment.trim() === \"\" || loading}\r\n                    size=\"icon\"\r\n                    className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n                  >\r\n                    Save Comment\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n        <DiscardModal\r\n          saveChanges={() => handleSend(reviewState, true)}\r\n          isOpenDiscardModal={isOpenDiscardModal}\r\n          cancelTabChange={handleCancelChange}\r\n          confirmTabChange={handleDiscardChange}\r\n        />\r\n      </div>\r\n      <div className=\"!ml-0 text-[12px] mt-1\">{feedbackDate}</div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ThumbAction;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAXA;AAFA;AAAA;AAAA;AAEA;AAFA;;;;;;;;;;;;;;;;AAiBA,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,UAAU,EACV,aAAa,EACb,SAAS,EACT,iBAAiB,EACjB,oBAAoB,EACpB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,OAAO,EACP,OAAO,EACP,eAAe,EACf,+BAA+B,EAqBhC;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAEhD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,eAAe;gBACjB,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;gBAEd,OAAO,OAAO;6CAAG,CAAC,KAAK,KAAK,MAAM,KAAK;wBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;4BAC/B,OAAO,OAAO,IAAI,MAAM,OAAO;wBACjC;oBACF;;YACF;QACF;gCAAG;QAAC;KAAc;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,WAAW,gBAAgB;YAC5C,IAAI,YAAY,aAAa,MAAM;gBACjC,eAAe,SAAS,IAAI;gBAC5B,WAAW,SAAS,OAAO,IAAI;gBAC/B,mBAAmB,SAAS,OAAO,IAAI;YACzC;YACA,IAAI,mBACF,WAAW,gBAAgB,2BAA2B;YACxD,IAAI,kBAAkB;gBACpB,mBAAmB,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EAC1D,kBACA;gBAEF,gBAAgB;YAClB;QACF;gCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IACE,cAAc,OAAO,IACrB,CAAC,cAAc,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC5C;wBACA;oBACF;gBACF;;YACA,IAAI,gBAAgB;gBAClB,SAAS,gBAAgB,CAAC,aAAa;YACzC;YACA;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG;QAAC;QAAgB;QAAS;KAAU;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,UAAU,OAAO,EAAE;gBACvC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;gBAC1D,MAAM,eAAe,OAAO,WAAW;gBACvC,aAAa,WAAW,MAAM,GAAG,MAAM;YACzC;QACF;gCAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAC,OAAoB;QAC7C,IAAI,iBAAiB,QAAQ,uCAAuC;QACpE,aAAa;QACb,eAAe,CAAC,OAAU,SAAS,QAAQ,OAAO;QAClD,WAAW,gBAAgB,QAAQ,OAAO;QAC1C,IAAI,CAAC,QAAQ;YACX,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CACvB,SACA,OACA;QAEA,IAAI,UAAU;QACd,IAAI,SAAS,SAAS;YACpB,UAAU;QACZ,OAAO,IAAI,SAAS,WAAW;YAC7B,IAAI,YAAY,iBAAiB;gBAC/B,UAAU;YACZ,OAAO,IAAI,UAAU,QAAQ;gBAC3B,UAAU;YACZ,OAAO,IAAI,UAAU,WAAW;gBAC9B,UAAU;YACZ,OAAO,IAAI,UAAU,MAAM;gBACzB,UAAU;YACZ;QACF;QACA,iBAAiB,SAAS;IAC5B;IACA,MAAM,oBAAoB,iBAAiB,cAAc,CAAC,aAAa,EAAE;IACzE,MAAM,aAAa,OACjB,OACA,4BAA4B,KAAK;QAEjC,IAAI,CAAC,WAAW,uBAAuB,CAAC,cAAc;YACpD,iBAAiB,0CAA0C;YAC3D;QACF;QACA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,SAAS,OAAO,GAAG,IAAI;QACzB,OAAO;YACL,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,OAAO,GAAG,IAAI;QACzB;QAEA,kBAAkB;QAClB,sBAAsB;QACtB,MAAM,QACJ,aAAa,OAAO,CAAC,eAAe,aAAa,OAAO,CAAC;QAE3D,gCAAgC,UAAU,EAAE;QAE5C,IAAI,2BAA2B;YAC7B,gCAAgC;QAClC;QAEA,MAAM,aAA8B;YAClC,sBAAsB,WAAW;YACjC,eAAe;YACf,MAAM;YACN,SAAS,UAAU,OAAO,KAAK;YAC/B,gBAAgB,SAAS,iBAAiB;YAC1C,aAAa,gBAAgB,YAAY;YACzC,oBAAoB,IAAI,OAAO,WAAW;QAC5C;QACA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB;gBACpD,QAAQ;gBACR,QAAQ,SAAS,OAAO,CAAC,MAAM;gBAC/B,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC;oBACxC,IAAI,KAAK,wBAAwB,WAAW,qBAAqB;wBAC/D,OAAO;4BACL,GAAG,GAAG;4BACN,gBAAgB;gCACd,GAAG,IAAI,cAAc;gCACrB,2BAA2B;4BAC7B;wBACF;oBACF;oBACA,OAAO;gBACT;gBACA,cAAc;gBACd,qBAAqB;oBACnB,GAAG,iBAAiB;oBACpB,CAAC,UAAU,EAAE;gBACf;gBACA,iBACE,WAAW,OAAO,EAClB,gBAAgB,QAAQ,OAAO,OAC/B;gBAEF,mBAAmB,WAAW,OAAO;gBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM,gBACF,uCACA;oBACJ,YAAY;wBACV,eAAe,WAAW,SAAS,MAAM,SAAS;wBAClD,aAAa,WAAW;wBACxB,cAAc,SAAS,QAAQ;wBAC/B,SAAS,gBACL,oCACA;oBACN;gBACF;gBACA,CAAA,GAAA,6IAAA,CAAA,oCAAiC,AAAD,EAC9B,mBACA,iBAAiB;YAErB,OAAO;gBACL,iBACE,WAAW,OAAO,EAClB,gBAAgB,QAAQ,OAAO,OAC/B;YAEJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,iBAAiB,8CAA8C;YAC/D,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,0DAA0D,QAC1D,kDAAkD;gBAExD,eAAe;YACjB;QACF,SAAU;YACR,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,WACE,WAAW,gBAAgB,2BAA2B,WAAW;QAEnE,sBAAsB;QACtB,kBAAkB;QAClB,gCAAgC;IAClC;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,MAAM,kBACJ,WAAW,gBAAgB,2BAA2B,WAAW;QACnE,IAAI,QAAQ,IAAI,OAAO,gBAAgB,IAAI,IAAI;YAC7C,sBAAsB;QACxB,OAAO;YACL,kBAAkB;YAClB,gCAAgC;QAClC;IACF;IAEA,IAAI,aAAa,gHAAA,CAAA,+CAA4C,KAAK;IAElE,IAAI,iBAAiB;QACnB,aAAa;IACf;IAEA,IAAI,eAAe;QACjB,aAAa,kBAAkB,QAAQ;IACzC;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;gBAAgB,KAAK;;kCAClC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS,IACP,aACI,kBAAkB,QAAQ,gBAAgB,UAC1C;gCAEN,SAAS;oCAAE,OAAO,gBAAgB,SAAS,MAAM;gCAAE;gCACnD,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCACV,OAAO,gBAAgB,SAAS,MAAM,CAAC,aAAa,IAAI;gCAC1D;0CAEA,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCACP,MAAM;oCACN,WAAW,CAAC,eAAe,EACzB,CAAC,aACG,mBACA,gBAAgB,SAChB,mBACA,kBACJ;oCACF,MACE,CAAC,aACG,gBAAgB,SACd,YACA,UACF,gBAAgB,SAChB,YACA;;;;;;;;;;;0CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS,IACP,aACI,kBAAkB,WAAW,gBAAgB,aAC7C;gCAEN,SAAS;oCAAE,OAAO,gBAAgB,YAAY,MAAM;gCAAE;gCACtD,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCACV,OAAO,gBAAgB,YAAY,MAAM,CAAC,aAAa,IAAI;gCAC7D;0CAEA,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCACT,MAAM;oCACN,WAAW,CAAC,eAAe,EACzB,CAAC,aACG,iBACA,gBAAgB,YAChB,iBACA,gBACJ;oCACF,MACE,CAAC,aACG,gBAAgB,YACd,YACA,UACF,gBAAgB,YAChB,YACA;;;;;;;;;;;4BAIT,CAAC,gBAAgB,UAAU,gBAAgB,SAAS,KACnD,WAAW,gBAAgB,2BAA2B,yBACpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCAAE,OAAO,CAAC,iBAAiB,MAAM;gCAAI;0CAEjD,cAAA,6LAAC,4HAAA,CAAA,UAAU;oCACT,MAAK;oCACL,sBACE,6LAAC,mMAAA,CAAA,MAAG;wCACF,SAAS;4CACP,aAAa;4CACb,kBAAkB;wCACpB;wCACA,MAAM;wCACN,WAAW,CAAC,4BAA4B,CAAC;;;;;;;;;;;;;;;;;;;;;;kCAOvD,6LAAC,4LAAA,CAAA,kBAAe;kCACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,SAAS;gCAAE,SAAS;gCAAG,GAAG,YAAY,CAAC,KAAK;gCAAI,GAAG;4BAAG;4BACtD,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,GAAG,CAAC;4BAAI;4BACrC,MAAM;gCAAE,SAAS;gCAAG,GAAG,YAAY,CAAC,KAAK;4BAAG;4BAC5C,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAW,CAAC,eAAe,EACzB,YAAY,cAAc,SAC3B,6DAA6D,CAAC;;8CAE/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,gBAAgB,SACb,2BACA,gBAAgB,YAChB,8BACA;;;;;;sDAEN,6LAAC,+MAAA,CAAA,UAAO;4CACN,MAAM;4CACN,WAAU;4CACV,SAAS;;;;;;;;;;;;8CAGb,6LAAC;oCACC,WAAW,CAAC,OAAO,EACjB,YAAY,cAAc,YAC3B,mFAAmF,CAAC;oCACrF,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,UAAU,aAAa;;;;;;gCAExB,YAAY,qBACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS;gDACP,WACE,WAAW,gBAAgB,2BACvB,WAAW;gDAEjB,kBAAkB;gDAClB,gCAAgC;4CAClC;4CACA,UAAU,QAAQ,IAAI,OAAO,MAAM;4CACnC,WAAU;4CACV,MAAK;sDACN;;;;;;sDAID,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,IAAM,WAAW,aAAa;4CACvC,UAAU,QAAQ,IAAI,OAAO,MAAM;4CACnC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQX,6LAAC,4IAAA,CAAA,UAAY;wBACX,aAAa,IAAM,WAAW,aAAa;wBAC3C,oBAAoB;wBACpB,iBAAiB;wBACjB,kBAAkB;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;0BAA0B;;;;;;;;AAG/C;GAhcM;;QAgDyB,4HAAA,CAAA,kBAAe;;;KAhDxC;uCAkcS"}}, {"offset": {"line": 3183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/types/vacancy_status_api.ts"], "sourcesContent": ["export interface CatalystMatchStatus {\r\n  status: \"completed\" | \"pending\" | \"in_progress\" | \"queued\" | \"error\" | string;\r\n  initiated_at: string;\r\n  completed_at: string;\r\n  initiated_by: string;\r\n}\r\n\r\nexport interface UpdateTimestamps {\r\n  match_results_generated_at: string;\r\n  data_last_updated_at: string;\r\n  archived: boolean;\r\n}\r\n\r\nexport interface VacancyStatusData {\r\n  catalyst_match_status: CatalystMatchStatus;\r\n  update_timestamps: UpdateTimestamps;\r\n}\r\n\r\nexport enum UPDATE_TIMESTAMPS_STATUS {\r\n  COMPLETED = \"completed\",\r\n  IN_PROCESS = \"inprocess\",\r\n  QUEUED = \"queued\",\r\n  ERROR = \"error\",\r\n}\r\n"], "names": [], "mappings": ";;;AAkBO,IAAA,AAAK,kDAAA;;;;;WAAA"}}, {"offset": {"line": 3202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  ChevronDownIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from \"lucide-react\";\r\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\nimport { Button, buttonVariants } from \"@/components/ui/button\";\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  captionLayout = \"label\",\r\n  buttonVariant = \"ghost\",\r\n  formatters,\r\n  components,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker> & {\r\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"];\r\n}) {\r\n  const defaultClassNames = getDefaultClassNames();\r\n\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\r\n        \"bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\r\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\r\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\r\n        className\r\n      )}\r\n      captionLayout={captionLayout}\r\n      formatters={{\r\n        formatMonthDropdown: (date) =>\r\n          date.toLocaleString(\"default\", { month: \"short\" }),\r\n        ...formatters,\r\n      }}\r\n      classNames={{\r\n        root: cn(\"w-fit\", defaultClassNames.root),\r\n        months: cn(\r\n          \"relative flex flex-col gap-4 md:flex-row\",\r\n          defaultClassNames.months\r\n        ),\r\n        month: cn(\"flex w-full flex-col gap-4\", defaultClassNames.month),\r\n        nav: cn(\r\n          \"absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1\",\r\n          defaultClassNames.nav\r\n        ),\r\n        button_previous: cn(\r\n          buttonVariants({ variant: buttonVariant }),\r\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\r\n          defaultClassNames.button_previous\r\n        ),\r\n        button_next: cn(\r\n          buttonVariants({ variant: buttonVariant }),\r\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\r\n          defaultClassNames.button_next\r\n        ),\r\n        month_caption: cn(\r\n          \"flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]\",\r\n          defaultClassNames.month_caption\r\n        ),\r\n        dropdowns: cn(\r\n          \"flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium\",\r\n          defaultClassNames.dropdowns\r\n        ),\r\n        dropdown_root: cn(\r\n          \"has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border\",\r\n          defaultClassNames.dropdown_root\r\n        ),\r\n        dropdown: cn(\r\n          \"bg-popover absolute inset-0 opacity-0\",\r\n          defaultClassNames.dropdown\r\n        ),\r\n        caption_label: cn(\r\n          \"select-none font-medium\",\r\n          captionLayout === \"label\"\r\n            ? \"text-sm\"\r\n            : \"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5\",\r\n          defaultClassNames.caption_label\r\n        ),\r\n        table: \"w-full border-collapse\",\r\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\r\n        weekday: cn(\r\n          \"text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal\",\r\n          defaultClassNames.weekday\r\n        ),\r\n        week: cn(\"mt-2 flex w-full\", defaultClassNames.week),\r\n        week_number_header: cn(\r\n          \"w-[--cell-size] select-none\",\r\n          defaultClassNames.week_number_header\r\n        ),\r\n        week_number: cn(\r\n          \"text-muted-foreground select-none text-[0.8rem]\",\r\n          defaultClassNames.week_number\r\n        ),\r\n        day: cn(\r\n          \"group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md\",\r\n          defaultClassNames.day\r\n        ),\r\n        range_start: cn(\r\n          \"bg-accent rounded-l-md\",\r\n          defaultClassNames.range_start\r\n        ),\r\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\r\n        range_end: cn(\"bg-accent rounded-r-md\", defaultClassNames.range_end),\r\n        today: cn(\r\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\r\n          defaultClassNames.today\r\n        ),\r\n        outside: cn(\r\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\r\n          defaultClassNames.outside\r\n        ),\r\n        disabled: cn(\r\n          \"text-muted-foreground opacity-50\",\r\n          defaultClassNames.disabled\r\n        ),\r\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        Root: ({ className, rootRef, ...props }) => {\r\n          return (\r\n            <div\r\n              data-slot=\"calendar\"\r\n              ref={rootRef}\r\n              className={cn(className)}\r\n              {...props}\r\n            />\r\n          );\r\n        },\r\n        Chevron: ({ className, orientation, ...props }) => {\r\n          if (orientation === \"left\") {\r\n            return (\r\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\r\n            );\r\n          }\r\n\r\n          if (orientation === \"right\") {\r\n            return (\r\n              <ChevronRightIcon\r\n                className={cn(\"size-4\", className)}\r\n                {...props}\r\n              />\r\n            );\r\n          }\r\n\r\n          return (\r\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\r\n          );\r\n        },\r\n        DayButton: CalendarDayButton,\r\n        WeekNumber: ({ children, ...props }) => {\r\n          return (\r\n            <td {...props}>\r\n              <div className=\"flex size-[--cell-size] items-center justify-center text-center\">\r\n                {children}\r\n              </div>\r\n            </td>\r\n          );\r\n        },\r\n        ...components,\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CalendarDayButton({\r\n  className,\r\n  day,\r\n  modifiers,\r\n  ...props\r\n}: React.ComponentProps<typeof DayButton>) {\r\n  const defaultClassNames = getDefaultClassNames();\r\n\r\n  const ref = React.useRef<HTMLButtonElement>(null);\r\n  React.useEffect(() => {\r\n    if (modifiers.focused) ref.current?.focus();\r\n  }, [modifiers.focused]);\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      data-day={day.date.toLocaleDateString()}\r\n      data-selected-single={\r\n        modifiers.selected &&\r\n        !modifiers.range_start &&\r\n        !modifiers.range_end &&\r\n        !modifiers.range_middle\r\n      }\r\n      data-range-start={modifiers.range_start}\r\n      data-range-end={modifiers.range_end}\r\n      data-range-middle={modifiers.range_middle}\r\n      className={cn(\r\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70\",\r\n        defaultClassNames.day,\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Calendar, CalendarDayButton };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAQA;AACA;AAHA;AAAA;AALA;AAAA;AAAA;;;AAHA;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,6LAAC,qKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kJACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,4EACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,4EACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACT,yCACA,kBAAkB,QAAQ;YAE5B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,6LAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,6LAAC,2NAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,6LAAC,6NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,6LAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;KA/JS;AAiKT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;;IACvC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,8JAAM,MAAM,CAAoB;IAC5C,8JAAM,SAAS;uCAAC;YACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;QACtC;sCAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uwBACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf;GApCS;MAAA"}}, {"offset": {"line": 3381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/library/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent }"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,uKAAiB,MAAM;kBACtB,cAAA,6LAAC,uKAAiB,OAAO;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 3429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/vacancyFilterAvailabilityDateRange.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverTrigger,\r\n  PopoverContent,\r\n} from \"@/components/ui/popover\";\r\nimport { ChevronDownIcon } from \"lucide-react\";\r\n\r\ninterface AvailabilityDateRangeProps {\r\n  availabilityDateRange: { from: string; to: string };\r\n  setAvailabilityDateRange: React.Dispatch<\r\n    React.SetStateAction<{ from: string; to: string }>\r\n  >;\r\n  maxMinAvailabilityDateRange: { from: string; to: string };\r\n}\r\n\r\nexport function AvailabilityDateRange({\r\n  availabilityDateRange,\r\n  setAvailabilityDateRange,\r\n  maxMinAvailabilityDateRange,\r\n}: AvailabilityDateRangeProps) {\r\n  const warningMessage =\r\n    new Date(availabilityDateRange.from) > new Date(availabilityDateRange.to);\r\n\r\n  const [fromOpen, setFromOpen] = useState(false);\r\n  const [toOpen, setToOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (warningMessage) {\r\n      setAvailabilityDateRange((prev) => ({\r\n        ...prev,\r\n        to: \"\",\r\n      }));\r\n    }\r\n  }, [warningMessage, setAvailabilityDateRange]);\r\n\r\n  return (\r\n    <div className=\"p-2\">\r\n      <p>Availability Date Range :</p>\r\n      <div className=\"px-4 py-2\">\r\n        <div className=\"flex flex-col gap-2\">\r\n          {/* From Date Picker */}\r\n          <div className=\"flex gap-2 items-center\">\r\n            <span className=\"text-sm w-12\">From:</span>\r\n            <Popover open={fromOpen} onOpenChange={setFromOpen}>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-48 justify-between font-normal\"\r\n                >\r\n                  {availabilityDateRange?.from\r\n                    ? new Date(availabilityDateRange.from).toLocaleDateString()\r\n                    : \"Select From date\"}\r\n                  <ChevronDownIcon\r\n                    className={`ml-2 transition-transform ${\r\n                      fromOpen ? \"rotate-180\" : \"\"\r\n                    }`}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                <Calendar\r\n                  mode=\"single\"\r\n                  selected={\r\n                    availabilityDateRange?.from\r\n                      ? new Date(availabilityDateRange.from)\r\n                      : undefined\r\n                  }\r\n                  onSelect={(date: Date | undefined) => {\r\n                    if (date) {\r\n                      setAvailabilityDateRange({\r\n                        ...availabilityDateRange,\r\n                        from: new Date(date).toString(),\r\n                      });\r\n                      setFromOpen(false); // close popover after selection if desired\r\n                    } else {\r\n                      setAvailabilityDateRange({\r\n                        ...availabilityDateRange,\r\n                        from: \"\",\r\n                      });\r\n                    }\r\n                  }}\r\n                  defaultMonth={\r\n                    availabilityDateRange?.from\r\n                      ? new Date(availabilityDateRange.from)\r\n                      : undefined\r\n                  }\r\n                  captionLayout=\"dropdown\"\r\n                  fromYear={\r\n                    maxMinAvailabilityDateRange?.from\r\n                      ? new Date(maxMinAvailabilityDateRange.from).getFullYear()\r\n                      : 0\r\n                  }\r\n                  toYear={\r\n                    maxMinAvailabilityDateRange?.to\r\n                      ? new Date(maxMinAvailabilityDateRange.to).getFullYear()\r\n                      : 0\r\n                  }\r\n                  showOutsideDays={false}\r\n                  required={false}\r\n                  className=\"rounded-lg border\"\r\n                  disabled={{ after: new Date() }}\r\n                />\r\n              </PopoverContent>\r\n            </Popover>\r\n          </div>\r\n\r\n          {/* To Date Picker */}\r\n          <div className=\"flex gap-2 items-center\">\r\n            <span className=\"text-sm w-12\">To:</span>\r\n            <Popover open={toOpen} onOpenChange={setToOpen}>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-48 justify-between font-normal\"\r\n                >\r\n                  {availabilityDateRange?.to\r\n                    ? new Date(availabilityDateRange.to).toLocaleDateString()\r\n                    : \"Select To date\"}\r\n                  <ChevronDownIcon\r\n                    className={`ml-2 transition-transform ${\r\n                      toOpen ? \"rotate-180\" : \"\"\r\n                    }`}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                <Calendar\r\n                  mode=\"single\"\r\n                  selected={\r\n                    availabilityDateRange?.to\r\n                      ? new Date(availabilityDateRange.to)\r\n                      : undefined\r\n                  }\r\n                  onSelect={(date: Date | undefined) => {\r\n                    if (date) {\r\n                      setAvailabilityDateRange({\r\n                        ...availabilityDateRange,\r\n                        to: new Date(date).toString(),\r\n                      });\r\n                      setToOpen(false);\r\n                    } else {\r\n                      setAvailabilityDateRange({\r\n                        ...availabilityDateRange,\r\n                        to: \"\",\r\n                      });\r\n                    }\r\n                  }}\r\n                  captionLayout=\"dropdown\"\r\n                  showOutsideDays={false}\r\n                  defaultMonth={\r\n                    availabilityDateRange?.to\r\n                      ? new Date(availabilityDateRange.to)\r\n                      : undefined\r\n                  }\r\n                  required={false}\r\n                  className=\"rounded-lg border\"\r\n                  fromYear={\r\n                    maxMinAvailabilityDateRange?.from\r\n                      ? new Date(maxMinAvailabilityDateRange.from).getFullYear()\r\n                      : 0\r\n                  }\r\n                  toYear={\r\n                    maxMinAvailabilityDateRange?.to\r\n                      ? new Date(maxMinAvailabilityDateRange.to).getFullYear()\r\n                      : 0\r\n                  }\r\n                  disabled={{\r\n                    after: new Date(),\r\n                    before: new Date(availabilityDateRange.from),\r\n                  }}\r\n                />\r\n              </PopoverContent>\r\n            </Popover>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAKA;;;;;;;;AAUO,SAAS,sBAAsB,EACpC,qBAAqB,EACrB,wBAAwB,EACxB,2BAA2B,EACA;;IAC3B,MAAM,iBACJ,IAAI,KAAK,sBAAsB,IAAI,IAAI,IAAI,KAAK,sBAAsB,EAAE;IAE1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,gBAAgB;gBAClB;uDAAyB,CAAC,OAAS,CAAC;4BAClC,GAAG,IAAI;4BACP,IAAI;wBACN,CAAC;;YACH;QACF;0CAAG;QAAC;QAAgB;KAAyB;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,6LAAC,+HAAA,CAAA,UAAO;oCAAC,MAAM;oCAAU,cAAc;;sDACrC,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDAET,uBAAuB,OACpB,IAAI,KAAK,sBAAsB,IAAI,EAAE,kBAAkB,KACvD;kEACJ,6LAAC,2NAAA,CAAA,kBAAe;wDACd,WAAW,CAAC,0BAA0B,EACpC,WAAW,eAAe,IAC1B;;;;;;;;;;;;;;;;;sDAIR,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAa,OAAM;sDAC3C,cAAA,6LAAC,gIAAA,CAAA,WAAQ;gDACP,MAAK;gDACL,UACE,uBAAuB,OACnB,IAAI,KAAK,sBAAsB,IAAI,IACnC;gDAEN,UAAU,CAAC;oDACT,IAAI,MAAM;wDACR,yBAAyB;4DACvB,GAAG,qBAAqB;4DACxB,MAAM,IAAI,KAAK,MAAM,QAAQ;wDAC/B;wDACA,YAAY,QAAQ,2CAA2C;oDACjE,OAAO;wDACL,yBAAyB;4DACvB,GAAG,qBAAqB;4DACxB,MAAM;wDACR;oDACF;gDACF;gDACA,cACE,uBAAuB,OACnB,IAAI,KAAK,sBAAsB,IAAI,IACnC;gDAEN,eAAc;gDACd,UACE,6BAA6B,OACzB,IAAI,KAAK,4BAA4B,IAAI,EAAE,WAAW,KACtD;gDAEN,QACE,6BAA6B,KACzB,IAAI,KAAK,4BAA4B,EAAE,EAAE,WAAW,KACpD;gDAEN,iBAAiB;gDACjB,UAAU;gDACV,WAAU;gDACV,UAAU;oDAAE,OAAO,IAAI;gDAAO;;;;;;;;;;;;;;;;;;;;;;;sCAOtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,6LAAC,+HAAA,CAAA,UAAO;oCAAC,MAAM;oCAAQ,cAAc;;sDACnC,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDAET,uBAAuB,KACpB,IAAI,KAAK,sBAAsB,EAAE,EAAE,kBAAkB,KACrD;kEACJ,6LAAC,2NAAA,CAAA,kBAAe;wDACd,WAAW,CAAC,0BAA0B,EACpC,SAAS,eAAe,IACxB;;;;;;;;;;;;;;;;;sDAIR,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAa,OAAM;sDAC3C,cAAA,6LAAC,gIAAA,CAAA,WAAQ;gDACP,MAAK;gDACL,UACE,uBAAuB,KACnB,IAAI,KAAK,sBAAsB,EAAE,IACjC;gDAEN,UAAU,CAAC;oDACT,IAAI,MAAM;wDACR,yBAAyB;4DACvB,GAAG,qBAAqB;4DACxB,IAAI,IAAI,KAAK,MAAM,QAAQ;wDAC7B;wDACA,UAAU;oDACZ,OAAO;wDACL,yBAAyB;4DACvB,GAAG,qBAAqB;4DACxB,IAAI;wDACN;oDACF;gDACF;gDACA,eAAc;gDACd,iBAAiB;gDACjB,cACE,uBAAuB,KACnB,IAAI,KAAK,sBAAsB,EAAE,IACjC;gDAEN,UAAU;gDACV,WAAU;gDACV,UACE,6BAA6B,OACzB,IAAI,KAAK,4BAA4B,IAAI,EAAE,WAAW,KACtD;gDAEN,QACE,6BAA6B,KACzB,IAAI,KAAK,4BAA4B,EAAE,EAAE,WAAW,KACpD;gDAEN,UAAU;oDACR,OAAO,IAAI;oDACX,QAAQ,IAAI,KAAK,sBAAsB,IAAI;gDAC7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAnKgB;KAAA"}}, {"offset": {"line": 3696, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3702, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/utils/rangeSlider.tsx"], "sourcesContent": ["import React, { useRef } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\n\r\ninterface RangeSliderProps {\r\n  label: string;\r\n  min: number;\r\n  max: number;\r\n  step?: number;\r\n  value: [number, number];\r\n  onChange: (value: [number, number]) => void;\r\n  unit?: string;\r\n  decimal?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const RangeSlider: React.FC<RangeSliderProps> = ({\r\n  label,\r\n  min,\r\n  max,\r\n  step = 1,\r\n  value,\r\n  onChange,\r\n  unit = \"\",\r\n  decimal = false,\r\n  className = \"\",\r\n}) => {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  // always up to date value for drag\r\n  const valueRef = useRef<[number, number]>(value);\r\n  valueRef.current = value;\r\n\r\n  const percent = (val: number) => ((val - min) / (max - min)) * 100;\r\n\r\n  const pxToValue = (px: number) => {\r\n    const width = containerRef.current?.getBoundingClientRect().width || 1;\r\n    let percentX = Math.max(0, Math.min(1, px / width));\r\n    let newValue = min + (max - min) * percentX;\r\n    // For decimal, round to nearest step (0.01 etc), else integer step\r\n    if (decimal) {\r\n      newValue = Math.round(newValue / step) * step;\r\n      return Number(newValue.toFixed(2));\r\n    } else {\r\n      newValue = Math.round(newValue / step) * step;\r\n      return Math.round(newValue);\r\n    }\r\n  };\r\n\r\n  // Drag Handler\r\n  const handleDrag =\r\n    (handleIdx: 0 | 1) => (e: React.MouseEvent<HTMLDivElement>) => {\r\n      e.preventDefault();\r\n      const container = containerRef.current;\r\n      if (!container) return;\r\n\r\n      const handleMouseMove = (moveEvent: MouseEvent) => {\r\n        const rect = container.getBoundingClientRect();\r\n        const x = moveEvent.clientX - rect.left;\r\n        let newValue = pxToValue(x);\r\n\r\n        let [currentMin, currentMax] = valueRef.current;\r\n\r\n        if (handleIdx === 0) {\r\n          // dragging min\r\n          newValue = Math.min(newValue, currentMax - step); // Make sure min < max\r\n          newValue = Math.max(newValue, min);\r\n          onChange([newValue, currentMax]);\r\n        } else {\r\n          // dragging max\r\n          newValue = Math.max(newValue, currentMin + step); // Make sure max > min\r\n          newValue = Math.min(newValue, max);\r\n          onChange([currentMin, newValue]);\r\n        }\r\n      };\r\n\r\n      const handleMouseUp = () => {\r\n        document.removeEventListener(\"mousemove\", handleMouseMove);\r\n        document.removeEventListener(\"mouseup\", handleMouseUp);\r\n      };\r\n\r\n      document.addEventListener(\"mousemove\", handleMouseMove);\r\n      document.addEventListener(\"mouseup\", handleMouseUp);\r\n    };\r\n\r\n  return (\r\n    <div className={`p-2 w-[95%] ${className}`}>\r\n      <p>{label}</p>\r\n      <div className=\"px-4 py-2\">\r\n        <div className=\"flex gap-2 items-center mb-2\">\r\n          <span className=\"text-sm\">Min:</span>\r\n          <Input\r\n            type=\"number\"\r\n            min={min}\r\n            max={max}\r\n            step={step}\r\n            value={decimal ? value?.[0].toFixed(2) : value?.[0]}\r\n            onChange={(e) => {\r\n              let val = decimal\r\n                ? parseFloat(e.target.value)\r\n                : parseInt(e.target.value, 10);\r\n              if (isNaN(val)) val = min;\r\n              // Clamp so min <= max-step\r\n              val = Math.min(val, value?.[1] - step);\r\n              val = Math.max(min, val);\r\n              if (val !== value?.[0]) onChange([val, value?.[1]]);\r\n            }}\r\n            className=\"w-20 border border-gray-300 rounded px-2 py-1 text-sm\"\r\n          />\r\n          <span className=\"text-sm\">Max:</span>\r\n          <Input\r\n            type=\"number\"\r\n            min={min}\r\n            max={max}\r\n            step={step}\r\n            value={decimal ? value?.[1].toFixed(2) : value?.[1]}\r\n            onChange={(e) => {\r\n              let val = decimal\r\n                ? parseFloat(e.target.value)\r\n                : parseInt(e.target.value, 10);\r\n              if (isNaN(val)) val = max;\r\n              // Clamp so max >= min+step\r\n              val = Math.max(val, value?.[0] + step);\r\n              val = Math.min(max, val);\r\n              if (val !== value?.[1]) onChange([value?.[0], val]);\r\n            }}\r\n            className=\"w-20 border border-gray-300 rounded px-2 py-1 text-sm\"\r\n          />\r\n        </div>\r\n        {/* Slider UI */}\r\n        <div className=\"relative h-6 bg-gray-200 rounded-lg\" ref={containerRef}>\r\n          <div\r\n            className=\"absolute h-2 bg-blue-500 rounded-lg pointer-events-none\"\r\n            style={{\r\n              left: `${percent(value?.[0])}%`,\r\n              width: `${Math.max(\r\n                percent(value?.[1]) - percent(value?.[0]),\r\n                0\r\n              )}%`,\r\n              top: \"50%\",\r\n              transform: \"translateY(-50%)\",\r\n            }}\r\n          ></div>\r\n\r\n          {/* Min handle */}\r\n          <div\r\n            className=\"absolute w-4 h-4 bg-white border-2 border-blue-500 rounded-full cursor-pointer z-10\"\r\n            style={{\r\n              left: `calc(${percent(value?.[0])}% - 8px)`,\r\n              top: \"50%\",\r\n              transform: \"translateY(-50%)\",\r\n              // Always allow pointer events for min handle\r\n              pointerEvents: \"auto\",\r\n            }}\r\n            onMouseDown={handleDrag(0)}\r\n            tabIndex={0}\r\n            aria-label=\"Minimum value\"\r\n          ></div>\r\n          {/* Max handle */}\r\n          <div\r\n            className=\"absolute w-4 h-4 bg-white border-2 border-blue-500 rounded-full cursor-pointer z-10\"\r\n            style={{\r\n              left: `calc(${percent(value?.[1])}% - 8px)`,\r\n              top: \"50%\",\r\n              transform: \"translateY(-50%)\",\r\n              pointerEvents: value?.[1] <= value?.[0] + step ? \"none\" : \"auto\",\r\n            }}\r\n            onMouseDown={\r\n              value?.[1] > value?.[0] + step ? handleDrag(1) : undefined\r\n            }\r\n            tabIndex={0}\r\n            aria-label=\"Maximum value\"\r\n          ></div>\r\n        </div>\r\n        <div className=\"flex justify-between text-xs text-gray-500 mt-2\">\r\n          <span>\r\n            {decimal ? value?.[0].toFixed(2) : value?.[0]} {unit}\r\n          </span>\r\n          <span>\r\n            {decimal ? value?.[1].toFixed(2) : value?.[1]} {unit}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAcO,MAAM,cAA0C,CAAC,EACtD,KAAK,EACL,GAAG,EACH,GAAG,EACH,OAAO,CAAC,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EAAE,EACT,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,mCAAmC;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,SAAS,OAAO,GAAG;IAEnB,MAAM,UAAU,CAAC,MAAgB,AAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAK;IAE/D,MAAM,YAAY,CAAC;QACjB,MAAM,QAAQ,aAAa,OAAO,EAAE,wBAAwB,SAAS;QACrE,IAAI,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK;QAC5C,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,IAAI;QACnC,mEAAmE;QACnE,IAAI,SAAS;YACX,WAAW,KAAK,KAAK,CAAC,WAAW,QAAQ;YACzC,OAAO,OAAO,SAAS,OAAO,CAAC;QACjC,OAAO;YACL,WAAW,KAAK,KAAK,CAAC,WAAW,QAAQ;YACzC,OAAO,KAAK,KAAK,CAAC;QACpB;IACF;IAEA,eAAe;IACf,MAAM,aACJ,CAAC,YAAqB,CAAC;YACrB,EAAE,cAAc;YAChB,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,WAAW;YAEhB,MAAM,kBAAkB,CAAC;gBACvB,MAAM,OAAO,UAAU,qBAAqB;gBAC5C,MAAM,IAAI,UAAU,OAAO,GAAG,KAAK,IAAI;gBACvC,IAAI,WAAW,UAAU;gBAEzB,IAAI,CAAC,YAAY,WAAW,GAAG,SAAS,OAAO;gBAE/C,IAAI,cAAc,GAAG;oBACnB,eAAe;oBACf,WAAW,KAAK,GAAG,CAAC,UAAU,aAAa,OAAO,sBAAsB;oBACxE,WAAW,KAAK,GAAG,CAAC,UAAU;oBAC9B,SAAS;wBAAC;wBAAU;qBAAW;gBACjC,OAAO;oBACL,eAAe;oBACf,WAAW,KAAK,GAAG,CAAC,UAAU,aAAa,OAAO,sBAAsB;oBACxE,WAAW,KAAK,GAAG,CAAC,UAAU;oBAC9B,SAAS;wBAAC;wBAAY;qBAAS;gBACjC;YACF;YAEA,MAAM,gBAAgB;gBACpB,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,WAAW;YAC1C;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;QACvC;IAEF,qBACE,6LAAC;QAAI,WAAW,CAAC,YAAY,EAAE,WAAW;;0BACxC,6LAAC;0BAAG;;;;;;0BACJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;0CAC1B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,KAAK;gCACL,KAAK;gCACL,MAAM;gCACN,OAAO,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,OAAO,CAAC,EAAE;gCACnD,UAAU,CAAC;oCACT,IAAI,MAAM,UACN,WAAW,EAAE,MAAM,CAAC,KAAK,IACzB,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE;oCAC7B,IAAI,MAAM,MAAM,MAAM;oCACtB,2BAA2B;oCAC3B,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG;oCACjC,MAAM,KAAK,GAAG,CAAC,KAAK;oCACpB,IAAI,QAAQ,OAAO,CAAC,EAAE,EAAE,SAAS;wCAAC;wCAAK,OAAO,CAAC,EAAE;qCAAC;gCACpD;gCACA,WAAU;;;;;;0CAEZ,6LAAC;gCAAK,WAAU;0CAAU;;;;;;0CAC1B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,KAAK;gCACL,KAAK;gCACL,MAAM;gCACN,OAAO,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,OAAO,CAAC,EAAE;gCACnD,UAAU,CAAC;oCACT,IAAI,MAAM,UACN,WAAW,EAAE,MAAM,CAAC,KAAK,IACzB,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE;oCAC7B,IAAI,MAAM,MAAM,MAAM;oCACtB,2BAA2B;oCAC3B,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG;oCACjC,MAAM,KAAK,GAAG,CAAC,KAAK;oCACpB,IAAI,QAAQ,OAAO,CAAC,EAAE,EAAE,SAAS;wCAAC,OAAO,CAAC,EAAE;wCAAE;qCAAI;gCACpD;gCACA,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBAAI,WAAU;wBAAsC,KAAK;;0CACxD,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,QAAQ,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;oCAC/B,OAAO,GAAG,KAAK,GAAG,CAChB,QAAQ,OAAO,CAAC,EAAE,IAAI,QAAQ,OAAO,CAAC,EAAE,GACxC,GACA,CAAC,CAAC;oCACJ,KAAK;oCACL,WAAW;gCACb;;;;;;0CAIF,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,MAAM,CAAC,KAAK,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC;oCAC3C,KAAK;oCACL,WAAW;oCACX,6CAA6C;oCAC7C,eAAe;gCACjB;gCACA,aAAa,WAAW;gCACxB,UAAU;gCACV,cAAW;;;;;;0CAGb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,MAAM,CAAC,KAAK,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC;oCAC3C,KAAK;oCACL,WAAW;oCACX,eAAe,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,SAAS;gCAC5D;gCACA,aACE,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,WAAW,KAAK;gCAEnD,UAAU;gCACV,cAAW;;;;;;;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCACE,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,OAAO,CAAC,EAAE;oCAAC;oCAAE;;;;;;;0CAElD,6LAAC;;oCACE,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,OAAO,CAAC,EAAE;oCAAC;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAM5D;GAxKa;KAAA"}}, {"offset": {"line": 3958, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3964, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/filterMenu.tsx"], "sourcesContent": ["import {\r\n  VACANCY_FILTER_LABELS,\r\n  VACANCY_FILTER_OTHER_LABELS,\r\n} from \"@/library/utils\";\r\nimport {\r\n  CircleX,\r\n  FilterIcon,\r\n  RotateCcw,\r\n  ThumbsDown,\r\n  ThumbsUp,\r\n} from \"lucide-react\";\r\nimport { AvailabilityDateRange } from \"@/components/candidates/vacancyFilterAvailabilityDateRange\";\r\nimport { RangeSlider } from \"@/components/utils/rangeSlider\";\r\nimport { Input } from \"@/components/ui/input\";\r\n\r\nexport interface iFilterSlider {\r\n  filterEntitlement: boolean;\r\n  showFilter: boolean;\r\n  isFunnelFill: boolean;\r\n  showSearchFieldError: boolean;\r\n  searchFields: string[];\r\n  searchText: string;\r\n  uniqueReviewValues: string[];\r\n  reviewStatus: string[];\r\n  uniqueShortList: string[];\r\n  shortListed: string[];\r\n  uniqueLocation: any;\r\n  state: string[];\r\n  city: string[];\r\n  uniqueFreshnessIndex: string[];\r\n  freshnessIndex: string[];\r\n  uniqueAiAgentStatus: string[];\r\n  aiAgentStatus: string[];\r\n  totalScoreRange: [number, number];\r\n  availabilityDateRange: { from: string; to: string };\r\n  handleResetFilter: () => void;\r\n  handleFilterIcon: () => void;\r\n  toggleSearchField: (value: string) => void;\r\n  setSearchText: (value: string) => void;\r\n  handleCheckBoxSelection: (value: string, item: string) => void;\r\n  setAvailabilityDateRange: React.Dispatch<\r\n    React.SetStateAction<{ from: string; to: string }>\r\n  >;\r\n  setTotalScoreRange: (value: [number, number]) => void;\r\n  distanceRange: [number, number];\r\n  setDistanceRange: (value: [number, number]) => void;\r\n  distance: [number, number];\r\n  maxMinAvailabilityDateRange: { from: string; to: string };\r\n}\r\n\r\nexport const filterSlider = ({\r\n  filterEntitlement,\r\n  showFilter,\r\n  isFunnelFill,\r\n  showSearchFieldError,\r\n  searchFields,\r\n  searchText,\r\n  uniqueReviewValues,\r\n  reviewStatus,\r\n  uniqueShortList,\r\n  shortListed,\r\n  uniqueLocation,\r\n  state,\r\n  city,\r\n  uniqueFreshnessIndex,\r\n  freshnessIndex,\r\n  availabilityDateRange,\r\n  uniqueAiAgentStatus,\r\n  aiAgentStatus,\r\n  totalScoreRange,\r\n  setAvailabilityDateRange,\r\n  handleResetFilter,\r\n  handleFilterIcon,\r\n  toggleSearchField,\r\n  setSearchText,\r\n  handleCheckBoxSelection,\r\n  setTotalScoreRange,\r\n  distanceRange,\r\n  setDistanceRange,\r\n  distance,\r\n  maxMinAvailabilityDateRange,\r\n}: iFilterSlider) => {\r\n  const onCheckBoxSelection = (label: string, selectedValue: string) => {\r\n    // Call original handler first\r\n    handleCheckBoxSelection(label as any, selectedValue);\r\n\r\n    // If label is STATE, uncheck all related cities automatically\r\n    if (\r\n      label === VACANCY_FILTER_LABELS.STATE &&\r\n      uniqueLocation &&\r\n      uniqueLocation?.[selectedValue]\r\n    ) {\r\n      const citiesToUncheck = uniqueLocation?.[selectedValue];\r\n      citiesToUncheck.forEach((cityName: string) => {\r\n        if (city.includes(cityName)) {\r\n          handleCheckBoxSelection(VACANCY_FILTER_LABELS.CITY, cityName);\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Defensive: ensure availabilityDateRange is always an object with 'from' and 'to'\r\n  const safeAvailabilityDateRange =\r\n    availabilityDateRange &&\r\n    typeof availabilityDateRange === \"object\" &&\r\n    \"from\" in availabilityDateRange &&\r\n    \"to\" in availabilityDateRange\r\n      ? availabilityDateRange\r\n      : { from: \"\", to: \"\" };\r\n\r\n  return (\r\n    <>\r\n      {filterEntitlement && !showFilter && (\r\n        <div className=\"max-h-[75vh] filter-scroll overflow-y-auto rounded-lg mr-2 border border-gray-800\">\r\n          <div className={`flex justify-between items-center p-2 `}>\r\n            <div className=\"flex gap-2 items-center\">\r\n              <FilterIcon\r\n                size={15}\r\n                fill={isFunnelFill ? \"#2a70ea\" : \"white\"}\r\n                color={isFunnelFill ? \"#2a70ea\" : \"black\"}\r\n              />\r\n              <p>Filter</p>\r\n            </div>\r\n            <div\r\n              className=\"hover:cursor-pointer flex gap-2 items-center\"\r\n              onClick={handleResetFilter}\r\n            >\r\n              <RotateCcw size={15} />\r\n              <p>Reset</p>\r\n            </div>\r\n            <CircleX\r\n              className=\"hover:cursor-pointer\"\r\n              size={15}\r\n              onClick={handleFilterIcon}\r\n            />\r\n          </div>\r\n          <div className=\"p-2\">\r\n            {/* 1. Search with name and whyfit checkbox */}\r\n            <div className=\"p-2\">\r\n              <p className=\"mb-2 font-medium\">Search :</p>\r\n              <div\r\n                className={`px-4 py-2 border rounded ${\r\n                  showSearchFieldError && searchText?.length > 0\r\n                    ? \"border-red-500\"\r\n                    : \"border-gray-300\"\r\n                }`}\r\n              >\r\n                <div className=\"flex gap-2 mb-2\">\r\n                  <label className=\"text-sm flex items-center gap-1\">\r\n                    <Input\r\n                      type=\"checkbox\"\r\n                      className=\"size-3\"\r\n                      placeholder={\r\n                        Array.isArray(searchFields) && searchFields.length === 0\r\n                          ? \"Search...\"\r\n                          : `Searching by ${(searchFields || []).join(\r\n                              \" & \"\r\n                            )}...`\r\n                      }\r\n                      checked={(searchFields || []).includes(\r\n                        VACANCY_FILTER_OTHER_LABELS.NAME\r\n                      )}\r\n                      onChange={() =>\r\n                        toggleSearchField(VACANCY_FILTER_OTHER_LABELS.NAME)\r\n                      }\r\n                    />\r\n                    Name\r\n                  </label>\r\n                  <label className=\"text-sm flex items-center gap-1\">\r\n                    <Input\r\n                      type=\"checkbox\"\r\n                      className=\"size-3\"\r\n                      checked={searchFields?.includes(\r\n                        VACANCY_FILTER_OTHER_LABELS.WHYFIT\r\n                      )}\r\n                      onChange={() =>\r\n                        toggleSearchField(VACANCY_FILTER_OTHER_LABELS.WHYFIT)\r\n                      }\r\n                    />\r\n                    Why Fit\r\n                  </label>\r\n                </div>\r\n                <Input\r\n                  type=\"text\"\r\n                  placeholder=\"Search...\"\r\n                  value={searchText}\r\n                  onChange={(e) => setSearchText(e.target.value)}\r\n                  className=\"w-full border border-gray-300 rounded px-2 py-1 text-sm\"\r\n                />\r\n                {showSearchFieldError && searchText?.length > 0 && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">\r\n                    Please select at least one field to search\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {/* 2. Rating */}\r\n            <div className=\"p-2\">\r\n              <p className=\"mb-2 font-medium\">Rating :</p>\r\n              <div className=\"flex items-center gap-4 pl-2\">\r\n                {uniqueReviewValues &&\r\n                  uniqueReviewValues?.map((item, index) => {\r\n                    if (item === VACANCY_FILTER_OTHER_LABELS.THUMBSUP) {\r\n                      return (\r\n                        <div\r\n                          key={item + index}\r\n                          className={`cursor-pointer ${\r\n                            reviewStatus?.includes(item) ? \"scale-125\" : \"\"\r\n                          }`}\r\n                          onClick={() =>\r\n                            handleCheckBoxSelection(\r\n                              VACANCY_FILTER_LABELS.REVIEW_DECISION,\r\n                              item\r\n                            )\r\n                          }\r\n                          title={VACANCY_FILTER_OTHER_LABELS.THUMBSUP}\r\n                        >\r\n                          <ThumbsUp\r\n                            size={20}\r\n                            className={`${\r\n                              reviewStatus.includes(item)\r\n                                ? \"text-green-700\"\r\n                                : \"text-gray-500\"\r\n                            }`}\r\n                            fill={\r\n                              reviewStatus.includes(item)\r\n                                ? \"#166534\"\r\n                                : \"transparent\"\r\n                            }\r\n                          />\r\n                        </div>\r\n                      );\r\n                    } else if (\r\n                      item === VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN\r\n                    ) {\r\n                      return (\r\n                        <div\r\n                          key={item + index}\r\n                          className={`cursor-pointer ${\r\n                            reviewStatus?.includes(item) ? \"scale-125\" : \"\"\r\n                          }`}\r\n                          onClick={() =>\r\n                            handleCheckBoxSelection(\r\n                              VACANCY_FILTER_LABELS.REVIEW_DECISION,\r\n                              item\r\n                            )\r\n                          }\r\n                          title={VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN}\r\n                        >\r\n                          <ThumbsDown\r\n                            size={20}\r\n                            className={`${\r\n                              reviewStatus?.includes(item)\r\n                                ? \"text-red-700\"\r\n                                : \"text-gray-500\"\r\n                            }`}\r\n                            fill={\r\n                              reviewStatus?.includes(item)\r\n                                ? \"#7f1d1d\"\r\n                                : \"transparent\"\r\n                            }\r\n                          />\r\n                        </div>\r\n                      );\r\n                    } else {\r\n                      return (\r\n                        <label\r\n                          key={item + index}\r\n                          className=\"flex items-center gap-1\"\r\n                        >\r\n                          <Input\r\n                            type=\"checkbox\"\r\n                            className=\"size-3\"\r\n                            value={item}\r\n                            checked={reviewStatus?.includes(item)}\r\n                            onChange={() =>\r\n                              handleCheckBoxSelection(\r\n                                VACANCY_FILTER_LABELS.REVIEW_DECISION,\r\n                                item\r\n                              )\r\n                            }\r\n                          />\r\n                          <span className=\"text-sm\">No Rating</span>\r\n                        </label>\r\n                      );\r\n                    }\r\n                  })}\r\n              </div>\r\n            </div>\r\n            <div className=\"p-2\">\r\n              <p>Shortlisted :</p>\r\n              <div className=\"flex gap-1 items-center\">\r\n                {uniqueShortList &&\r\n                  uniqueShortList?.map((item, index) => (\r\n                    <div className=\"pl-4\" key={item + `${index}`}>\r\n                      <ul key={index}>\r\n                        <li>\r\n                          <label className=\"flex items-center gap-1\">\r\n                            <Input\r\n                              type=\"checkbox\"\r\n                              className=\"size-3\"\r\n                              value={item}\r\n                              checked={shortListed?.includes(item)}\r\n                              onChange={() =>\r\n                                handleCheckBoxSelection(\r\n                                  VACANCY_FILTER_LABELS.SHORT_LISTED,\r\n                                  item\r\n                                )\r\n                              }\r\n                            />\r\n                            <span className=\"pl-1\">{item}</span>\r\n                          </label>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          {/* Distance From Work Site Filter */}\r\n          <RangeSlider\r\n            label=\"Distance (in miles)\"\r\n            min={distance?.[0]}\r\n            max={distance?.[1]}\r\n            step={Math.max(1, Math.floor((distanceRange?.[1] ?? 100) / 100))}\r\n            value={[...distanceRange]}\r\n            onChange={(val: [number, number]) => setDistanceRange([...val])}\r\n            unit=\"mi\"\r\n          />\r\n          {/* State and City Filter */}\r\n          <div className=\"p-2\">\r\n            <p>\r\n              {VACANCY_FILTER_LABELS.STATE} & {VACANCY_FILTER_LABELS.CITY} :\r\n            </p>\r\n            {uniqueLocation &&\r\n              Object.keys(uniqueLocation)?.map((stateName, index) => (\r\n                <div key={stateName + index} className=\"pl-4\">\r\n                  <label className=\"flex items-center gap-1\">\r\n                    <Input\r\n                      type=\"checkbox\"\r\n                      className=\"size-3\"\r\n                      value={stateName}\r\n                      checked={state?.includes(stateName)}\r\n                      onChange={() =>\r\n                        onCheckBoxSelection(\r\n                          VACANCY_FILTER_LABELS.STATE,\r\n                          stateName\r\n                        )\r\n                      }\r\n                    />\r\n                    <span className=\"pl-1\">{stateName}</span>\r\n                  </label>\r\n                  {state?.includes(stateName) &&\r\n                    uniqueLocation?.[stateName]?.length > 0 && (\r\n                      <ul className=\"pl-4\">\r\n                        {uniqueLocation?.[stateName]?.map(\r\n                          (cityName: string, index: number) => (\r\n                            <li key={cityName + index}>\r\n                              <label className=\"flex items-center gap-1\">\r\n                                <Input\r\n                                  type=\"checkbox\"\r\n                                  className=\"size-3\"\r\n                                  value={cityName}\r\n                                  checked={city.includes(cityName)}\r\n                                  onChange={() =>\r\n                                    handleCheckBoxSelection(\r\n                                      VACANCY_FILTER_LABELS.CITY,\r\n                                      cityName\r\n                                    )\r\n                                  }\r\n                                />\r\n                                <span className=\"pl-1\">{cityName}</span>\r\n                              </label>\r\n                            </li>\r\n                          )\r\n                        )}\r\n                      </ul>\r\n                    )}\r\n                </div>\r\n              ))}\r\n          </div>\r\n          <div className=\"p-2\">\r\n            <p>Freshness Index :</p>\r\n            {uniqueFreshnessIndex &&\r\n              uniqueFreshnessIndex?.map((item, index) => (\r\n                <div className=\"pl-4\" key={item + `${index}`}>\r\n                  <ul key={index}>\r\n                    <li>\r\n                      <label className=\"flex items-center gap-1\">\r\n                        <Input\r\n                          type=\"checkbox\"\r\n                          className=\"size-3\"\r\n                          value={item}\r\n                          checked={freshnessIndex?.includes(item)}\r\n                          onChange={() =>\r\n                            handleCheckBoxSelection(\r\n                              VACANCY_FILTER_LABELS.FRESHNESS_INDEX,\r\n                              item\r\n                            )\r\n                          }\r\n                        />\r\n                        <span className=\"pl-1\">{item}</span>\r\n                      </label>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              ))}\r\n          </div>\r\n          {/* Availability Date Range Filter */}\r\n          <AvailabilityDateRange\r\n            availabilityDateRange={safeAvailabilityDateRange}\r\n            setAvailabilityDateRange={setAvailabilityDateRange}\r\n            maxMinAvailabilityDateRange={maxMinAvailabilityDateRange}\r\n          />\r\n          {/* AI Agent Status Filter */}\r\n          <div className=\"p-2\">\r\n            <p>AI Agent Status :</p>\r\n            {uniqueAiAgentStatus &&\r\n              uniqueAiAgentStatus?.map((item, index) => (\r\n                <div className=\"pl-4\" key={item + `${index}`}>\r\n                  <ul key={index}>\r\n                    <li>\r\n                      <label className=\"flex items-center gap-1\">\r\n                        <Input\r\n                          type=\"checkbox\"\r\n                          className=\"size-3\"\r\n                          value={item}\r\n                          checked={aiAgentStatus?.includes(item)}\r\n                          onChange={() =>\r\n                            handleCheckBoxSelection(\r\n                              VACANCY_FILTER_LABELS.AI_AGENT_STATUS,\r\n                              item\r\n                            )\r\n                          }\r\n                        />\r\n                        <span className=\"pl-1\">{item}</span>\r\n                      </label>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              ))}\r\n          </div>\r\n          {/* Total Score Range Filter */}\r\n          <RangeSlider\r\n            label=\"Total Score Range\"\r\n            min={0}\r\n            max={1}\r\n            step={0.01}\r\n            value={totalScoreRange}\r\n            onChange={setTotalScoreRange}\r\n            decimal\r\n          />\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAWA;AACA;AACA;AATA;AAAA;AAAA;AAAA;AAAA;;;;;;;AA8CO,MAAM,eAAe,CAAC,EAC3B,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,oBAAoB,EACpB,YAAY,EACZ,UAAU,EACV,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,KAAK,EACL,IAAI,EACJ,oBAAoB,EACpB,cAAc,EACd,qBAAqB,EACrB,mBAAmB,EACnB,aAAa,EACb,eAAe,EACf,wBAAwB,EACxB,iBAAiB,EACjB,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,uBAAuB,EACvB,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,2BAA2B,EACb;IACd,MAAM,sBAAsB,CAAC,OAAe;QAC1C,8BAA8B;QAC9B,wBAAwB,OAAc;QAEtC,8DAA8D;QAC9D,IACE,UAAU,mHAAA,CAAA,wBAAqB,CAAC,KAAK,IACrC,kBACA,gBAAgB,CAAC,cAAc,EAC/B;YACA,MAAM,kBAAkB,gBAAgB,CAAC,cAAc;YACvD,gBAAgB,OAAO,CAAC,CAAC;gBACvB,IAAI,KAAK,QAAQ,CAAC,WAAW;oBAC3B,wBAAwB,mHAAA,CAAA,wBAAqB,CAAC,IAAI,EAAE;gBACtD;YACF;QACF;IACF;IAEA,mFAAmF;IACnF,MAAM,4BACJ,yBACA,OAAO,0BAA0B,YACjC,UAAU,yBACV,QAAQ,wBACJ,wBACA;QAAE,MAAM;QAAI,IAAI;IAAG;IAEzB,qBACE;kBACG,qBAAqB,CAAC,4BACrB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAW,CAAC,sCAAsC,CAAC;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,aAAU;oCACT,MAAM;oCACN,MAAM,eAAe,YAAY;oCACjC,OAAO,eAAe,YAAY;;;;;;8CAEpC,6LAAC;8CAAE;;;;;;;;;;;;sCAEL,6LAAC;4BACC,WAAU;4BACV,SAAS;;8CAET,6LAAC,mNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;8CACjB,6LAAC;8CAAE;;;;;;;;;;;;sCAEL,6LAAC,+MAAA,CAAA,UAAO;4BACN,WAAU;4BACV,MAAM;4BACN,SAAS;;;;;;;;;;;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAmB;;;;;;8CAChC,6LAAC;oCACC,WAAW,CAAC,yBAAyB,EACnC,wBAAwB,YAAY,SAAS,IACzC,mBACA,mBACJ;;sDAEF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC,6HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,WAAU;4DACV,aACE,MAAM,OAAO,CAAC,iBAAiB,aAAa,MAAM,KAAK,IACnD,cACA,CAAC,aAAa,EAAE,CAAC,gBAAgB,EAAE,EAAE,IAAI,CACvC,OACA,GAAG,CAAC;4DAEZ,SAAS,CAAC,gBAAgB,EAAE,EAAE,QAAQ,CACpC,mHAAA,CAAA,8BAA2B,CAAC,IAAI;4DAElC,UAAU,IACR,kBAAkB,mHAAA,CAAA,8BAA2B,CAAC,IAAI;;;;;;wDAEpD;;;;;;;8DAGJ,6LAAC;oDAAM,WAAU;;sEACf,6LAAC,6HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,WAAU;4DACV,SAAS,cAAc,SACrB,mHAAA,CAAA,8BAA2B,CAAC,MAAM;4DAEpC,UAAU,IACR,kBAAkB,mHAAA,CAAA,8BAA2B,CAAC,MAAM;;;;;;wDAEtD;;;;;;;;;;;;;sDAIN,6LAAC,6HAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;wCAEX,wBAAwB,YAAY,SAAS,mBAC5C,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;sCAO/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAmB;;;;;;8CAChC,6LAAC;oCAAI,WAAU;8CACZ,sBACC,oBAAoB,IAAI,CAAC,MAAM;wCAC7B,IAAI,SAAS,mHAAA,CAAA,8BAA2B,CAAC,QAAQ,EAAE;4CACjD,qBACE,6LAAC;gDAEC,WAAW,CAAC,eAAe,EACzB,cAAc,SAAS,QAAQ,cAAc,IAC7C;gDACF,SAAS,IACP,wBACE,mHAAA,CAAA,wBAAqB,CAAC,eAAe,EACrC;gDAGJ,OAAO,mHAAA,CAAA,8BAA2B,CAAC,QAAQ;0DAE3C,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oDACP,MAAM;oDACN,WAAW,GACT,aAAa,QAAQ,CAAC,QAClB,mBACA,iBACJ;oDACF,MACE,aAAa,QAAQ,CAAC,QAClB,YACA;;;;;;+CAtBH,OAAO;;;;;wCA2BlB,OAAO,IACL,SAAS,mHAAA,CAAA,8BAA2B,CAAC,UAAU,EAC/C;4CACA,qBACE,6LAAC;gDAEC,WAAW,CAAC,eAAe,EACzB,cAAc,SAAS,QAAQ,cAAc,IAC7C;gDACF,SAAS,IACP,wBACE,mHAAA,CAAA,wBAAqB,CAAC,eAAe,EACrC;gDAGJ,OAAO,mHAAA,CAAA,8BAA2B,CAAC,UAAU;0DAE7C,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDACT,MAAM;oDACN,WAAW,GACT,cAAc,SAAS,QACnB,iBACA,iBACJ;oDACF,MACE,cAAc,SAAS,QACnB,YACA;;;;;;+CAtBH,OAAO;;;;;wCA2BlB,OAAO;4CACL,qBACE,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,WAAU;wDACV,OAAO;wDACP,SAAS,cAAc,SAAS;wDAChC,UAAU,IACR,wBACE,mHAAA,CAAA,wBAAqB,CAAC,eAAe,EACrC;;;;;;kEAIN,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;+CAfrB,OAAO;;;;;wCAkBlB;oCACF;;;;;;;;;;;;sCAGN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAI,WAAU;8CACZ,mBACC,iBAAiB,IAAI,CAAC,MAAM,sBAC1B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DACC,cAAA,6LAAC;8DACC,cAAA,6LAAC;wDAAM,WAAU;;0EACf,6LAAC,6HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,WAAU;gEACV,OAAO;gEACP,SAAS,aAAa,SAAS;gEAC/B,UAAU,IACR,wBACE,mHAAA,CAAA,wBAAqB,CAAC,YAAY,EAClC;;;;;;0EAIN,6LAAC;gEAAK,WAAU;0EAAQ;;;;;;;;;;;;;;;;;+CAfrB;;;;;2CADgB,OAAO,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;8BA0BtD,6LAAC,sIAAA,CAAA,cAAW;oBACV,OAAM;oBACN,KAAK,UAAU,CAAC,EAAE;oBAClB,KAAK,UAAU,CAAC,EAAE;oBAClB,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,IAAI,GAAG,IAAI;oBAC3D,OAAO;2BAAI;qBAAc;oBACzB,UAAU,CAAC,MAA0B,iBAAiB;+BAAI;yBAAI;oBAC9D,MAAK;;;;;;8BAGP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCACE,mHAAA,CAAA,wBAAqB,CAAC,KAAK;gCAAC;gCAAI,mHAAA,CAAA,wBAAqB,CAAC,IAAI;gCAAC;;;;;;;wBAE7D,kBACC,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,WAAW,sBAC3C,6LAAC;gCAA4B,WAAU;;kDACrC,6LAAC;wCAAM,WAAU;;0DACf,6LAAC,6HAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,WAAU;gDACV,OAAO;gDACP,SAAS,OAAO,SAAS;gDACzB,UAAU,IACR,oBACE,mHAAA,CAAA,wBAAqB,CAAC,KAAK,EAC3B;;;;;;0DAIN,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;;;;;;;oCAEzB,OAAO,SAAS,cACf,gBAAgB,CAAC,UAAU,EAAE,SAAS,mBACpC,6LAAC;wCAAG,WAAU;kDACX,gBAAgB,CAAC,UAAU,EAAE,IAC5B,CAAC,UAAkB,sBACjB,6LAAC;0DACC,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC,6HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,WAAU;4DACV,OAAO;4DACP,SAAS,KAAK,QAAQ,CAAC;4DACvB,UAAU,IACR,wBACE,mHAAA,CAAA,wBAAqB,CAAC,IAAI,EAC1B;;;;;;sEAIN,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;+CAdnB,WAAW;;;;;;;;;;;+BArBtB,YAAY;;;;;;;;;;;8BA6C5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;wBACF,wBACC,sBAAsB,IAAI,CAAC,MAAM,sBAC/B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CACC,cAAA,6LAAC;kDACC,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,6HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO;oDACP,SAAS,gBAAgB,SAAS;oDAClC,UAAU,IACR,wBACE,mHAAA,CAAA,wBAAqB,CAAC,eAAe,EACrC;;;;;;8DAIN,6LAAC;oDAAK,WAAU;8DAAQ;;;;;;;;;;;;;;;;;mCAfrB;;;;;+BADgB,OAAO,GAAG,OAAO;;;;;;;;;;;8BAwBlD,6LAAC,kKAAA,CAAA,wBAAqB;oBACpB,uBAAuB;oBACvB,0BAA0B;oBAC1B,6BAA6B;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;wBACF,uBACC,qBAAqB,IAAI,CAAC,MAAM,sBAC9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CACC,cAAA,6LAAC;kDACC,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,6HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO;oDACP,SAAS,eAAe,SAAS;oDACjC,UAAU,IACR,wBACE,mHAAA,CAAA,wBAAqB,CAAC,eAAe,EACrC;;;;;;8DAIN,6LAAC;oDAAK,WAAU;8DAAQ;;;;;;;;;;;;;;;;;mCAfrB;;;;;+BADgB,OAAO,GAAG,OAAO;;;;;;;;;;;8BAwBlD,6LAAC,sIAAA,CAAA,cAAW;oBACV,OAAM;oBACN,KAAK;oBACL,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,OAAO;;;;;;;;;;;;;AAMnB"}}, {"offset": {"line": 4616, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4622, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/CandidateTable/CandidateTable.tsx"], "sourcesContent": ["import {\r\n  <PERSON>H<PERSON>er,\r\n  TableBody,\r\n  TableRow,\r\n  TableHead,\r\n  TableCell,\r\n} from \"@/components/ui/table\";\r\nimport { ArrowUpDown, FileText, SquareArrowOutUpRight } from \"lucide-react\";\r\nimport Loading from \"@/components/Loading\";\r\nimport WhyFitAction from \"../candidates/WhyFitAction\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport ThumbAction from \"../candidates/ThumbAction\";\r\nimport { Candidate, Vacancy } from \"@/app/candidates/helper\";\r\nimport {\r\n  unFormattedDateWithBrowserTimezoneInDDMMYY,\r\n  VACANCY_FILTER_URL_REGEX,\r\n} from \"@/utils/utils\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Modal from \"../Modal\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport DiscardModal from \"../candidates/DiscardModal\";\r\nimport { useEntitlement } from \"@/context/EntitlementContext\";\r\nimport { initAppInsights } from \"@/library/appInsights\";\r\nimport { NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL } from \"@/api/config\";\r\nimport { updateLocalStoredVacancyTimeStamp } from \"@/utils/updatelocalStoredVacancyTimeStamp\";\r\nimport { UPDATE_TIMESTAMPS_STATUS } from \"@/types/vacancy_status_api\";\r\nimport { Input } from \"../ui/input\";\r\nimport { filterSlider, type iFilterSlider } from \"../candidates/filterMenu\";\r\nimport { VACANCY_FILTER_OTHER_LABELS } from \"@/library/utils\";\r\n\r\ninterface TableProps {\r\n  handleCandidateSort: (name: string) => void;\r\n  isResumeModalOpen: boolean;\r\n  loading?: boolean;\r\n  paginatedCandidates: Candidate[];\r\n  selectedVacancy: Vacancy;\r\n  candidates: Candidate[];\r\n  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  vacancyCandidates: {\r\n    [key: string]: Candidate[];\r\n  } | null;\r\n  setVacancyCandidates: React.Dispatch<\r\n    React.SetStateAction<{\r\n      [key: string]: Candidate[];\r\n    } | null>\r\n  >;\r\n  fetchResume: (name: Candidate) => void;\r\n  showSelectBox?: boolean;\r\n  handleCheckboxClick?: (id: number) => void;\r\n  selectedRows?: number[];\r\n  mercuryPortal: boolean;\r\n  fetchCandidatesById: (vacamcyId: string) => void;\r\n  emailId: string;\r\n  isTableReadOnly?: boolean; // New prop to control table read-only state\r\n  vacancyId: string | undefined;\r\n  catalystRegenerationData: any;\r\n  filterProps: iFilterSlider;\r\n  tableLoading: boolean;\r\n}\r\n\r\nconst CandidateTable = ({\r\n  handleCandidateSort,\r\n  isResumeModalOpen,\r\n  loading,\r\n  paginatedCandidates,\r\n  selectedVacancy,\r\n  candidates,\r\n  setCandidates,\r\n  vacancyCandidates,\r\n  setVacancyCandidates,\r\n  fetchResume,\r\n  showSelectBox,\r\n  handleCheckboxClick,\r\n  selectedRows,\r\n  mercuryPortal,\r\n  fetchCandidatesById,\r\n  emailId,\r\n  isTableReadOnly = false, // New prop to control table read-only state\r\n  vacancyId,\r\n  catalystRegenerationData,\r\n  filterProps,\r\n  tableLoading,\r\n}: TableProps) => {\r\n  const { entitlements } = useEntitlement();\r\n  const { data: session } = useSession();\r\n  const [existingEdit, setExistingEdit] = useState(false);\r\n  const [candidateEditId, setCandidateEditId] = useState<string>(\"\");\r\n  const [candidateEditId2, setCandidateEditId2] = useState<string>(\"\");\r\n  const [expandPopupOpen, setExpandPopupOpen] = useState(false);\r\n  const [hideDislikedCandidates, setHideDislikedCandidates] = useState(false);\r\n  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);\r\n  const [mercuryURL, setMercuryURL] = useState<string>(\"\");\r\n  const [currentActionableCandidateId, setCurrentActionableCandidateId] =\r\n    useState<null | number>(null);\r\n  const handleHideDislikedCandidates = () => {\r\n    setHideDislikedCandidates(!hideDislikedCandidates);\r\n  };\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { showNotification } = useNotification();\r\n  // Add at the top inside CandidateTable component\r\n  const [confirmShortlist, setConfirmShortlist] = useState<{\r\n    open: boolean;\r\n    contactId: string | null;\r\n  }>({ open: false, contactId: null });\r\n\r\n  const showShortlist = entitlements?.Shorting_Listing ?? false;\r\n\r\n  // Filter candidates based on hide disliked state\r\n  const filteredCandidates = Array.isArray(paginatedCandidates)\r\n    ? hideDislikedCandidates\r\n      ? paginatedCandidates.filter(\r\n          (c) =>\r\n            c?.candidate_data?.recruiter_review_decision?.vote !== \"dislike\" ||\r\n            (currentActionableCandidateId === c.id &&\r\n              c?.candidate_data?.recruiter_review_decision?.vote === \"dislike\")\r\n        )\r\n      : paginatedCandidates\r\n    : [];\r\n\r\n  useEffect(() => {\r\n    initAppInsights();\r\n  }, []);\r\n\r\n  let isShortlistDisabled =\r\n    NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL === \"true\";\r\n\r\n  if (isTableReadOnly) {\r\n    isShortlistDisabled = true;\r\n  }\r\n\r\n  if (mercuryPortal) {\r\n    isShortlistDisabled = isTableReadOnly ? true : !mercuryPortal;\r\n  }\r\n\r\n  function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n    const code = envCode.toLowerCase();\r\n    if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n      return \"sandbox\";\r\n    }\r\n    if (code === \"ua\") {\r\n      return \"uat\";\r\n    }\r\n    if (code === \"prod\") {\r\n      return \"prod\";\r\n    }\r\n    return \"sandbox\";\r\n  }\r\n\r\n  const getDomainClient = () => {\r\n    const url = window.location.href;\r\n    const match = url.match(VACANCY_FILTER_URL_REGEX);\r\n    const result = match && match[1] ? match[1] : \"prod\";\r\n\r\n    const envType = getEnvType(result);\r\n    const crmDomain =\r\n      envType === \"prod\"\r\n        ? \"tandymgroup.crm.dynamics.com\"\r\n        : `tandymgroup-${envType}.crm.dynamics.com`;\r\n\r\n    return crmDomain;\r\n  };\r\n\r\n  const handleOpenPopup = async (contactId: string, mercuryPortal: boolean) => {\r\n    const crmDomain = getDomainClient();\r\n    let tandymURL = \"\";\r\n    try {\r\n      tandymURL = window.top?.location?.href || \"\";\r\n    } catch (err) {\r\n      console.warn(\r\n        \"Unable to access top window location due to cross-origin:\",\r\n        err\r\n      );\r\n      tandymURL = window.location.href;\r\n    }\r\n    const url = `https://${crmDomain}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&forceUCI=1&pagetype=entityrecord&etn=contact&id=${contactId}`;\r\n    url && window.open(url, \"_blank\");\r\n  };\r\n  const refreshVacancy_id = selectedVacancy?.vacancy_id || (vacancyId ?? \"\");\r\n  const handleShortlist = async (contactId: string) => {\r\n    if (!contactId || !selectedVacancy?.vacancy_id) {\r\n      showNotification(\"Candidate Contact ID is missing\", \"error\");\r\n      return;\r\n    }\r\n\r\n    const payload = {\r\n      vacancy_id: selectedVacancy?.vacancy_id,\r\n      candidate_id: contactId,\r\n      reviewer_email: emailId || session?.user?.email,\r\n      portal_name: mercuryPortal ? \"Mercury\" : \"Recruiter\",\r\n    };\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/shortlisted`,\r\n        {\r\n          method: \"POST\",\r\n          body: JSON.stringify(payload),\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        },\r\n        { context: \"Post Shortlisted Vacancies\" }\r\n      );\r\n      const data = await response.json();\r\n\r\n      if (data?.data?.status_code === 200) {\r\n        // Success\r\n        showNotification(\r\n          data?.data?.message\r\n            ? `${data?.data?.message} (Code : 200)`\r\n            : data?.message\r\n            ? `${data?.message} (Code : 200)`\r\n            : \"Shortlisted the candidate successfully for this vacancy:(Code : 200)\",\r\n          \"success\"\r\n        );\r\n        getAppInsights()?.trackEvent({\r\n          name: mercuryPortal\r\n            ? \"FE_MercuryPostVacanciesShortlisted\"\r\n            : \"FE_PostVacanciesShortlisted\",\r\n          properties: {\r\n            reviewerEmail: emailId || session?.user?.email,\r\n            candidateId: contactId,\r\n            embeddedFrom: document.referrer,\r\n            context: mercuryPortal\r\n              ? \"MercuryPostVacanciesShortlisted\"\r\n              : \"PostVacanciesShortlisted\",\r\n          },\r\n        });\r\n        // Remove the shortlisted candidate from the table\r\n        setCandidates((prev) =>\r\n          prev.filter(\r\n            (candidate) => candidate?.candidate_data?.contactid !== contactId\r\n          )\r\n        );\r\n        updateLocalStoredVacancyTimeStamp(\r\n          refreshVacancy_id,\r\n          mercuryPortal ?? undefined\r\n        );\r\n        fetchCandidatesById(selectedVacancy?.vacancy_id);\r\n      } else if (\r\n        data?.status_code === 409 ||\r\n        (typeof data?.error === \"string\" &&\r\n          data.error.toLowerCase().includes(\"already shortlisted\"))\r\n      ) {\r\n        // Already shortlisted\r\n        showNotification(\r\n          `${data.error} (Code : 409)` ||\r\n            `${data.detail?.message} (Code : 409)` ||\r\n            \"Failed to shortlist the candidate for this vacancy- (Code : 409)\",\r\n          \"warning\"\r\n        );\r\n      } else {\r\n        showNotification(\r\n          `Failed to shortlist the candidate for this vacancy - (Code : ${data?.status_code})`,\r\n          \"error\"\r\n        );\r\n        // All other errors\r\n        throw new Error(\r\n          data?.error ||\r\n            data?.detail?.message ||\r\n            \"Failed to shortlist candidate\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error while shortlisting candidate:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"MercuryPost Vacancies Shortlisted api with error is \" + error\r\n            : \"Post Vacancies Shortlisted api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  const StatusCompletion = catalystRegenerationData?.status\r\n    ?.toLowerCase()\r\n    ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED);\r\n  const StatusQueued = catalystRegenerationData?.status\r\n    ?.toLowerCase()\r\n    ?.includes(UPDATE_TIMESTAMPS_STATUS.QUEUED);\r\n  const StatusError = catalystRegenerationData?.status\r\n    ?.toLowerCase()\r\n    ?.includes(UPDATE_TIMESTAMPS_STATUS.ERROR);\r\n  const StatusInProcess = catalystRegenerationData?.status\r\n    ?.toLowerCase()\r\n    ?.includes(UPDATE_TIMESTAMPS_STATUS.IN_PROCESS);\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-4 gap-1 w-full max-h-[80vh]\">\r\n      {!filterProps?.showFilter && (\r\n        <div className=\"w-full\">\r\n          {filterSlider({\r\n            ...filterProps,\r\n          })}\r\n        </div>\r\n      )}\r\n      <div\r\n        className={\r\n          filterProps?.showFilter\r\n            ? `grid col-span-4 w-full max-h-[80vh] rounded-lg ${\r\n                expandPopupOpen ? \"overflow-hidden\" : \"overflow-auto\"\r\n              } justify-items-start`\r\n            : `grid col-span-3 max-h-[80vh] rounded-lg ${\r\n                expandPopupOpen ? \"overflow-hidden\" : \"overflow-auto\"\r\n              }`\r\n        }\r\n        style={{ alignItems: \"flex-start\" }}\r\n      >\r\n        <table className=\"w-full caption-bottom text-sm border border-gray-800 rounded-lg\">\r\n          <TableHeader className=\"rounded-lg sticky top-0 z-[10]\">\r\n            <TableRow className=\"bg-gray-900 rounded-lg hover:bg-gray-900 text-white text-[12px]\">\r\n              {showSelectBox && (\r\n                <TableHead className=\"p-2 text-white w-[100px] rounded-tl-lg\">\r\n                  Select\r\n                </TableHead>\r\n              )}\r\n              <TableHead\r\n                className={`p-2 text-white w-[150px] cursor-pointer ${\r\n                  !showSelectBox ? \"rounded-tl-lg\" : \"\"\r\n                }`}\r\n              >\r\n                Name\r\n              </TableHead>\r\n              <TableHead\r\n                onClick={() => handleCandidateSort(\"city\")}\r\n                className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n              >\r\n                Location\r\n                <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n              </TableHead>\r\n              <TableHead\r\n                onClick={() => handleCandidateSort(\"distance_from_work_site\")}\r\n                className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n              >\r\n                Miles from Worksite\r\n                <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n              </TableHead>\r\n              <TableHead\r\n                onClick={() => handleCandidateSort(\"availability\")}\r\n                className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n              >\r\n                Availability Date\r\n                <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n              </TableHead>\r\n              <TableHead className=\"p-2 text-white w-[140px] cursor-pointer\">\r\n                AI Agent Status\r\n              </TableHead>\r\n              <TableHead\r\n                onClick={() => handleCandidateSort(\"freshness_index\")}\r\n                className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n              >\r\n                Freshness Index\r\n                <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"p-2 text-white w-[70px] cursor-pointer\"\r\n                onClick={() => handleCandidateSort(\"overallscore\")}\r\n              >\r\n                Total Score\r\n                <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n              </TableHead>\r\n              <TableHead className=\"p-2 text-white w-[80px]\">\r\n                Parsed Resume\r\n              </TableHead>\r\n              <TableHead className=\"p-2 text-white w-[85px]\">\r\n                <div className=\"flex flex-col items-center\">\r\n                  <span className=\"text-xs mb-1\">Rating</span>\r\n                  <button\r\n                    onClick={handleHideDislikedCandidates}\r\n                    className={`flex items-center gap-1 px-2 py-1 text-xs border border-white rounded transition-colors ${\r\n                      hideDislikedCandidates\r\n                        ? \"bg-white text-gray-900\"\r\n                        : \"bg-transparent hover:bg-white hover:text-gray-900\"\r\n                    }`}\r\n                  >\r\n                    <span>{hideDislikedCandidates ? \"View\" : \"Hide\"}</span>\r\n                    <svg className=\"w-3 h-3 fill-red-500\" viewBox=\"0 0 24 24\">\r\n                      <path d=\"M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z\" />\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n              </TableHead>\r\n\r\n              <TableHead className=\"p-2 text-white w-[180px] 2xl:w-[300px] text-left\">\r\n                Why Fit\r\n              </TableHead>\r\n              {(showShortlist || mercuryPortal) && (\r\n                <TableHead className=\"p-2 text-white w-[100px] text-left\">\r\n                  Shortlist\r\n                </TableHead>\r\n              )}\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {(tableLoading || loading) && !isResumeModalOpen ? (\r\n              <TableRow>\r\n                <TableCell colSpan={12} className=\"p-4 text-center\">\r\n                  <Loading height=\"h-[50vh]\" />\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : candidates.length === 0 ? (\r\n              !tableLoading &&\r\n              !loading &&\r\n              (StatusCompletion ||\r\n                StatusQueued ||\r\n                StatusError ||\r\n                StatusInProcess) ? (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={12}\r\n                    className=\"p-4 text-center text-gray-700\"\r\n                  >\r\n                    {StatusQueued\r\n                      ? \"Matching has been queued. Please wait for some time and check again.\"\r\n                      : StatusInProcess\r\n                      ? \"Matching is in progress. Please wait for some time and check again.\"\r\n                      : \"No candidates found.\"}\r\n                  </TableCell>\r\n                </TableRow>\r\n              ) : (\r\n                !tableLoading &&\r\n                !loading && (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={12}\r\n                      className=\"p-4 text-center text-gray-700\"\r\n                    >\r\n                      Please fix the vacancy template to initiate the Catalyst Match generation.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )\r\n              )\r\n            ) : (\r\n              filteredCandidates?.length > 0 &&\r\n              filteredCandidates?.map((candidate, ind) => {\r\n                return (\r\n                  <TableRow\r\n                    key={candidate?.candidate_data?.contactid + ind}\r\n                    className={ind % 2 === 1 ? \"bg-gray-100\" : \"bg-white\"}\r\n                  >\r\n                    {showSelectBox && selectedRows && handleCheckboxClick && (\r\n                      <TableCell className=\"p-2 text-[14px] w-[100px]\">\r\n                        <Input\r\n                          type=\"checkbox\"\r\n                          checked={selectedRows.includes(candidate?.id)}\r\n                          onChange={() => handleCheckboxClick(candidate?.id)}\r\n                          className=\"accent-gray-800 cursor-pointer\"\r\n                        />\r\n                      </TableCell>\r\n                    )}\r\n                    <TableCell\r\n                      className=\"p-2 text-[14px] w-[250px]\"\r\n                      onClick={() => {\r\n                        handleOpenPopup(\r\n                          candidate?.candidate_data?.contactid,\r\n                          mercuryPortal\r\n                        );\r\n                      }}\r\n                    >\r\n                      <AppToolTip\r\n                        text={\r\n                          <span className=\"flex items-center\">\r\n                            Open `{candidate?.candidate_data?.name}` in new tab{\" \"}\r\n                            <SquareArrowOutUpRight size={14} className=\"ml-1\" />\r\n                          </span>\r\n                        }\r\n                        header={\r\n                          <p className=\"truncate md:max-w-[250px] text-blue-500 hover:underline\">\r\n                            {candidate?.candidate_data?.name}\r\n                          </p>\r\n                        }\r\n                        direction=\"top\"\r\n                      />\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[14px] w-[90px]\">\r\n                      {(() => {\r\n                        const { city, state } = candidate?.candidate_data;\r\n                        const isMissing = (val: string | null | undefined) =>\r\n                          !val || val === VACANCY_FILTER_OTHER_LABELS.MISSING;\r\n\r\n                        if (isMissing(city) && isMissing(state)) return \"\";\r\n                        if (!isMissing(city) && !isMissing(state))\r\n                          return `${city}, ${state}`;\r\n                        return !isMissing(city) ? city : state;\r\n                      })()}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[13px] w-[70px]\">\r\n                      {candidate?.candidate_data?.[\"distance_from_work_site\"]\r\n                        ? candidate?.candidate_data?.[\"distance_from_work_site\"]\r\n                        : \"\"}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[14px] w-[90px]\">\r\n                      {(() => {\r\n                        const availability =\r\n                          candidate?.candidate_data?.availability;\r\n                        if (\r\n                          !availability ||\r\n                          availability === VACANCY_FILTER_OTHER_LABELS.MISSING\r\n                        )\r\n                          return \"\";\r\n                        try {\r\n                          return unFormattedDateWithBrowserTimezoneInDDMMYY(\r\n                            availability\r\n                          );\r\n                        } catch {\r\n                          return \"\";\r\n                        }\r\n                      })()}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[14px] w-[140px]\">\r\n                      {candidate?.candidate_data?.[\"info_bot_response\"] ?? \"\"}\r\n                      <br />\r\n                      {candidate?.candidate_data?.[\"info_bot_response_date\"] !=\r\n                      null\r\n                        ? unFormattedDateWithBrowserTimezoneInDDMMYY(\r\n                            candidate?.candidate_data?.[\r\n                              \"info_bot_response_date\"\r\n                            ]\r\n                          )\r\n                        : null}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[13px] w-[70px]\">\r\n                      {candidate?.candidate_data?.[\"freshness_index\"]}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 text-[14px] w-[70px]\">\r\n                      {typeof candidate?.candidate_data?.[\r\n                        \"classification score\"\r\n                      ]?.overallscore === \"number\"\r\n                        ? candidate?.candidate_data?.[\r\n                            \"classification score\"\r\n                          ].overallscore.toFixed(2)\r\n                        : \"N/A\"}\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 space-x-2 h-14 w-[50px] sticky right-[0px]\">\r\n                      <AppToolTip\r\n                        header={\r\n                          <FileText\r\n                            size={18}\r\n                            className=\"text-gray-700\"\r\n                            onClick={() => fetchResume(candidate)}\r\n                            // aria-disabled={!candidate.candidate_contactid}\r\n                          />\r\n                        }\r\n                        text=\"View Resume\"\r\n                      />\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 space-x-2 h-14 w-[85px]\">\r\n                      <ThumbAction\r\n                        candidate={candidate}\r\n                        setCandidates={setCandidates}\r\n                        candidates={candidates}\r\n                        vacancyId={selectedVacancy?.vacancy_id}\r\n                        vacancyRefNo={selectedVacancy?.refno}\r\n                        vacancyCandidates={vacancyCandidates || {}}\r\n                        setVacancyCandidates={setVacancyCandidates}\r\n                        selectedVacancy={selectedVacancy}\r\n                        mercuryPortal={mercuryPortal}\r\n                        session={session}\r\n                        emailId={emailId}\r\n                        isTableReadOnly={isTableReadOnly}\r\n                        setCurrentActionableCandidateId={\r\n                          setCurrentActionableCandidateId\r\n                        }\r\n                      />\r\n                    </TableCell>\r\n                    <TableCell className=\"p-2 pl-0 space-x-2 h-14 w-[180px] 2xl:w-[300px]\">\r\n                      <div className={`flex gap-[5px]`}>\r\n                        <WhyFitAction\r\n                          candidate={candidate}\r\n                          setCandidates={setCandidates}\r\n                          selectedVacancy={selectedVacancy}\r\n                          existingEdit={existingEdit}\r\n                          setExistingEdit={setExistingEdit}\r\n                          candidateEditId={candidateEditId}\r\n                          candidateEditId2={candidateEditId2}\r\n                          setCandidateEditId={setCandidateEditId}\r\n                          setCandidateEditId2={setCandidateEditId2}\r\n                          setExpandPopupOpen={setExpandPopupOpen}\r\n                          isTableReadOnly={isTableReadOnly}\r\n                        />\r\n                      </div>\r\n                    </TableCell>\r\n                    {(showShortlist || mercuryPortal) && (\r\n                      <TableCell className=\"p-2 space-x-2 h-14 w-[80px]\">\r\n                        <Button\r\n                          className=\"rounded w-24\"\r\n                          onClick={() =>\r\n                            setConfirmShortlist({\r\n                              open: true,\r\n                              contactId: candidate?.candidate_data?.contactid,\r\n                            })\r\n                          }\r\n                          disabled={\r\n                            isShortlistDisabled || isTableReadOnly\r\n                              ? true\r\n                              : candidate?.candidate_data?.shortlisted_details\r\n                                  ?.status ===\r\n                                VACANCY_FILTER_OTHER_LABELS.SUCCESS\r\n                          }\r\n                        >\r\n                          {candidate?.candidate_data?.shortlisted_details\r\n                            ?.status !== VACANCY_FILTER_OTHER_LABELS.SUCCESS\r\n                            ? \"Shortlist\"\r\n                            : \"Shortlisted\"}\r\n                        </Button>\r\n                      </TableCell>\r\n                    )}\r\n                  </TableRow>\r\n                );\r\n              })\r\n            )}\r\n          </TableBody>\r\n        </table>\r\n        {isModalOpen && mercuryPortal && mercuryURL && (\r\n          <Modal\r\n            isOpen={isModalOpen}\r\n            width=\"max-w-6xl\"\r\n            height=\"h-[90vh]\"\r\n            children={\r\n              <div className=\"relative w-full h-[80vh]\">\r\n                <iframe\r\n                  src={mercuryURL}\r\n                  width=\"100%\"\r\n                  height=\"100%\"\r\n                  allowFullScreen\r\n                />\r\n              </div>\r\n            }\r\n            onClose={() => setIsModalOpen(false)}\r\n          />\r\n        )}\r\n        {isLoading && (\r\n          <div className=\"fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50\">\r\n            <Loading height=\"h-[50px]\" />\r\n          </div>\r\n        )}\r\n        {confirmShortlist.open && (\r\n          <DiscardModal\r\n            isOpenDiscardModal={confirmShortlist?.open}\r\n            title=\"Confirm Shortlist\"\r\n            cancelTabChange={() =>\r\n              setConfirmShortlist({ open: false, contactId: null })\r\n            }\r\n            message=\"This action will shortlist the candidate for the selected vacancy. Do you want to continue?\"\r\n            confirmButtonText=\"Confirm\"\r\n            confirmTabChange={async () => {\r\n              setConfirmShortlist({ open: false, contactId: null });\r\n              if (confirmShortlist?.contactId) {\r\n                await handleShortlist(confirmShortlist?.contactId);\r\n              }\r\n            }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateTable;\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AACA;AACA;AAEA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAzBA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,MAAM,iBAAiB,CAAC,EACtB,mBAAmB,EACnB,iBAAiB,EACjB,OAAO,EACP,mBAAmB,EACnB,eAAe,EACf,UAAU,EACV,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,WAAW,EACX,aAAa,EACb,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,OAAO,EACP,kBAAkB,KAAK,EACvB,SAAS,EACT,wBAAwB,EACxB,WAAW,EACX,YAAY,EACD;;IACX,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1B,MAAM,+BAA+B;QACnC,0BAA0B,CAAC;IAC7B;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,iDAAiD;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGpD;QAAE,MAAM;QAAO,WAAW;IAAK;IAElC,MAAM,gBAAgB,cAAc,oBAAoB;IAExD,iDAAiD;IACjD,MAAM,qBAAqB,MAAM,OAAO,CAAC,uBACrC,yBACE,oBAAoB,MAAM,CACxB,CAAC,IACC,GAAG,gBAAgB,2BAA2B,SAAS,aACtD,iCAAiC,EAAE,EAAE,IACpC,GAAG,gBAAgB,2BAA2B,SAAS,aAE7D,sBACF,EAAE;IAEN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;QAChB;mCAAG,EAAE;IAEL,IAAI,sBACF,gHAAA,CAAA,+CAA4C,KAAK;IAEnD,IAAI,iBAAiB;QACnB,sBAAsB;IACxB;IAEA,IAAI,eAAe;QACjB,sBAAsB,kBAAkB,OAAO,CAAC;IAClD;IAEA,SAAS,WAAW,OAAe;QACjC,MAAM,OAAO,QAAQ,WAAW;QAChC,IAAI;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,OAAO;YACrC,OAAO;QACT;QACA,IAAI,SAAS,MAAM;YACjB,OAAO;QACT;QACA,IAAI,SAAS,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI;QAChC,MAAM,QAAQ,IAAI,KAAK,CAAC,iHAAA,CAAA,2BAAwB;QAChD,MAAM,SAAS,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;QAE9C,MAAM,UAAU,WAAW;QAC3B,MAAM,YACJ,YAAY,SACR,iCACA,CAAC,YAAY,EAAE,QAAQ,iBAAiB,CAAC;QAE/C,OAAO;IACT;IAEA,MAAM,kBAAkB,OAAO,WAAmB;QAChD,MAAM,YAAY;QAClB,IAAI,YAAY;QAChB,IAAI;YACF,YAAY,OAAO,GAAG,EAAE,UAAU,QAAQ;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,IAAI,CACV,6DACA;YAEF,YAAY,OAAO,QAAQ,CAAC,IAAI;QAClC;QACA,MAAM,MAAM,CAAC,QAAQ,EAAE,UAAU,sGAAsG,EAAE,WAAW;QACpJ,OAAO,OAAO,IAAI,CAAC,KAAK;IAC1B;IACA,MAAM,oBAAoB,iBAAiB,cAAc,CAAC,aAAa,EAAE;IACzE,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,aAAa,CAAC,iBAAiB,YAAY;YAC9C,iBAAiB,mCAAmC;YACpD;QACF;QAEA,MAAM,UAAU;YACd,YAAY,iBAAiB;YAC7B,cAAc;YACd,gBAAgB,WAAW,SAAS,MAAM;YAC1C,aAAa,gBAAgB,YAAY;QAC3C;QACA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,0BAA0B,CAAC,EAC5B;gBACE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;gBACrB,SAAS;oBACP,gBAAgB;gBAClB;YACF,GACA;gBAAE,SAAS;YAA6B;YAE1C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,MAAM,MAAM,gBAAgB,KAAK;gBACnC,UAAU;gBACV,iBACE,MAAM,MAAM,UACR,GAAG,MAAM,MAAM,QAAQ,aAAa,CAAC,GACrC,MAAM,UACN,GAAG,MAAM,QAAQ,aAAa,CAAC,GAC/B,wEACJ;gBAEF,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM,gBACF,uCACA;oBACJ,YAAY;wBACV,eAAe,WAAW,SAAS,MAAM;wBACzC,aAAa;wBACb,cAAc,SAAS,QAAQ;wBAC/B,SAAS,gBACL,oCACA;oBACN;gBACF;gBACA,kDAAkD;gBAClD,cAAc,CAAC,OACb,KAAK,MAAM,CACT,CAAC,YAAc,WAAW,gBAAgB,cAAc;gBAG5D,CAAA,GAAA,6IAAA,CAAA,oCAAiC,AAAD,EAC9B,mBACA,iBAAiB;gBAEnB,oBAAoB,iBAAiB;YACvC,OAAO,IACL,MAAM,gBAAgB,OACrB,OAAO,MAAM,UAAU,YACtB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,wBACpC;gBACA,sBAAsB;gBACtB,iBACE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,IAC1B,GAAG,KAAK,MAAM,EAAE,QAAQ,aAAa,CAAC,IACtC,oEACF;YAEJ,OAAO;gBACL,iBACE,CAAC,6DAA6D,EAAE,MAAM,YAAY,CAAC,CAAC,EACpF;gBAEF,mBAAmB;gBACnB,MAAM,IAAI,MACR,MAAM,SACJ,MAAM,QAAQ,WACd;YAEN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,yDAAyD,QACzD,kDAAkD;gBAExD,eAAe;YACjB;QACF,SAAU;YACR,aAAa;QACf;IACF;IACA,MAAM,mBAAmB,0BAA0B,QAC/C,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS;IAC/C,MAAM,eAAe,0BAA0B,QAC3C,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,MAAM;IAC5C,MAAM,cAAc,0BAA0B,QAC1C,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,KAAK;IAC3C,MAAM,kBAAkB,0BAA0B,QAC9C,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,UAAU;IAEhD,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,aAAa,4BACb,6LAAC;gBAAI,WAAU;0BACZ,CAAA,GAAA,0IAAA,CAAA,eAAY,AAAD,EAAE;oBACZ,GAAG,WAAW;gBAChB;;;;;;0BAGJ,6LAAC;gBACC,WACE,aAAa,aACT,CAAC,+CAA+C,EAC9C,kBAAkB,oBAAoB,gBACvC,oBAAoB,CAAC,GACtB,CAAC,wCAAwC,EACvC,kBAAkB,oBAAoB,iBACtC;gBAER,OAAO;oBAAE,YAAY;gBAAa;;kCAElC,6LAAC;wBAAM,WAAU;;0CACf,6LAAC,6HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;wCACjB,+BACC,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyC;;;;;;sDAIhE,6LAAC,6HAAA,CAAA,YAAS;4CACR,WAAW,CAAC,wCAAwC,EAClD,CAAC,gBAAgB,kBAAkB,IACnC;sDACH;;;;;;sDAGD,6LAAC,6HAAA,CAAA,YAAS;4CACR,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;gDACX;8DAEC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,6HAAA,CAAA,YAAS;4CACR,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;gDACX;8DAEC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,6HAAA,CAAA,YAAS;4CACR,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;gDACX;8DAEC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAA0C;;;;;;sDAG/D,6LAAC,6HAAA,CAAA,YAAS;4CACR,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;gDACX;8DAEC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,6HAAA,CAAA,YAAS;4CACR,WAAU;4CACV,SAAS,IAAM,oBAAoB;;gDACpC;8DAEC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAA0B;;;;;;sDAG/C,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAe;;;;;;kEAC/B,6LAAC;wDACC,SAAS;wDACT,WAAW,CAAC,wFAAwF,EAClG,yBACI,2BACA,qDACJ;;0EAEF,6LAAC;0EAAM,yBAAyB,SAAS;;;;;;0EACzC,6LAAC;gEAAI,WAAU;gEAAuB,SAAQ;0EAC5C,cAAA,6LAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMhB,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAmD;;;;;;wCAGvE,CAAC,iBAAiB,aAAa,mBAC9B,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqC;;;;;;;;;;;;;;;;;0CAMhE,6LAAC,6HAAA,CAAA,YAAS;0CACP,CAAC,gBAAgB,OAAO,KAAK,CAAC,kCAC7B,6LAAC,6HAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;wCAAC,SAAS;wCAAI,WAAU;kDAChC,cAAA,6LAAC,yHAAA,CAAA,UAAO;4CAAC,QAAO;;;;;;;;;;;;;;;2CAGlB,WAAW,MAAM,KAAK,IACxB,CAAC,gBACD,CAAC,WACD,CAAC,oBACC,gBACA,eACA,eAAe,kBACf,6LAAC,6HAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;wCACR,SAAS;wCACT,WAAU;kDAET,eACG,yEACA,kBACA,wEACA;;;;;;;;;;2CAIR,CAAC,gBACD,CAAC,yBACC,6LAAC,6HAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;2CAOP,oBAAoB,SAAS,KAC7B,oBAAoB,IAAI,CAAC,WAAW;oCAClC,qBACE,6LAAC,6HAAA,CAAA,WAAQ;wCAEP,WAAW,MAAM,MAAM,IAAI,gBAAgB;;4CAE1C,iBAAiB,gBAAgB,qCAChC,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,6LAAC,6HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,SAAS,aAAa,QAAQ,CAAC,WAAW;oDAC1C,UAAU,IAAM,oBAAoB,WAAW;oDAC/C,WAAU;;;;;;;;;;;0DAIhB,6LAAC,6HAAA,CAAA,YAAS;gDACR,WAAU;gDACV,SAAS;oDACP,gBACE,WAAW,gBAAgB,WAC3B;gDAEJ;0DAEA,cAAA,6LAAC,4HAAA,CAAA,UAAU;oDACT,oBACE,6LAAC;wDAAK,WAAU;;4DAAoB;4DAC3B,WAAW,gBAAgB;4DAAK;4DAAa;0EACpD,6LAAC,uPAAA,CAAA,wBAAqB;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;;oDAG/C,sBACE,6LAAC;wDAAE,WAAU;kEACV,WAAW,gBAAgB;;;;;;oDAGhC,WAAU;;;;;;;;;;;0DAGd,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,CAAC;oDACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,WAAW;oDACnC,MAAM,YAAY,CAAC,MACjB,CAAC,OAAO,QAAQ,mHAAA,CAAA,8BAA2B,CAAC,OAAO;oDAErD,IAAI,UAAU,SAAS,UAAU,QAAQ,OAAO;oDAChD,IAAI,CAAC,UAAU,SAAS,CAAC,UAAU,QACjC,OAAO,GAAG,KAAK,EAAE,EAAE,OAAO;oDAC5B,OAAO,CAAC,UAAU,QAAQ,OAAO;gDACnC,CAAC;;;;;;0DAEH,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,WAAW,gBAAgB,CAAC,0BAA0B,GACnD,WAAW,gBAAgB,CAAC,0BAA0B,GACtD;;;;;;0DAEN,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,CAAC;oDACA,MAAM,eACJ,WAAW,gBAAgB;oDAC7B,IACE,CAAC,gBACD,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,OAAO,EAEpD,OAAO;oDACT,IAAI;wDACF,OAAO,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EAC9C;oDAEJ,EAAE,OAAM;wDACN,OAAO;oDACT;gDACF,CAAC;;;;;;0DAEH,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;;oDAClB,WAAW,gBAAgB,CAAC,oBAAoB,IAAI;kEACrD,6LAAC;;;;;oDACA,WAAW,gBAAgB,CAAC,yBAAyB,IACtD,OACI,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EACvC,WAAW,gBAAgB,CACzB,yBACD,IAEH;;;;;;;0DAEN,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,WAAW,gBAAgB,CAAC,kBAAkB;;;;;;0DAEjD,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,OAAO,WAAW,gBAAgB,CACjC,uBACD,EAAE,iBAAiB,WAChB,WAAW,gBAAgB,CACzB,uBACD,CAAC,aAAa,QAAQ,KACvB;;;;;;0DAEN,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,6LAAC,4HAAA,CAAA,UAAU;oDACT,sBACE,6LAAC,iNAAA,CAAA,WAAQ;wDACP,MAAM;wDACN,WAAU;wDACV,SAAS,IAAM,YAAY;;;;;;oDAI/B,MAAK;;;;;;;;;;;0DAGT,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,6LAAC,2IAAA,CAAA,UAAW;oDACV,WAAW;oDACX,eAAe;oDACf,YAAY;oDACZ,WAAW,iBAAiB;oDAC5B,cAAc,iBAAiB;oDAC/B,mBAAmB,qBAAqB,CAAC;oDACzC,sBAAsB;oDACtB,iBAAiB;oDACjB,eAAe;oDACf,SAAS;oDACT,SAAS;oDACT,iBAAiB;oDACjB,iCACE;;;;;;;;;;;0DAIN,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,6LAAC;oDAAI,WAAW,CAAC,cAAc,CAAC;8DAC9B,cAAA,6LAAC,4IAAA,CAAA,UAAY;wDACX,WAAW;wDACX,eAAe;wDACf,iBAAiB;wDACjB,cAAc;wDACd,iBAAiB;wDACjB,iBAAiB;wDACjB,kBAAkB;wDAClB,oBAAoB;wDACpB,qBAAqB;wDACrB,oBAAoB;wDACpB,iBAAiB;;;;;;;;;;;;;;;;4CAItB,CAAC,iBAAiB,aAAa,mBAC9B,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,WAAU;oDACV,SAAS,IACP,oBAAoB;4DAClB,MAAM;4DACN,WAAW,WAAW,gBAAgB;wDACxC;oDAEF,UACE,uBAAuB,kBACnB,OACA,WAAW,gBAAgB,qBACvB,WACJ,mHAAA,CAAA,8BAA2B,CAAC,OAAO;8DAGxC,WAAW,gBAAgB,qBACxB,WAAW,mHAAA,CAAA,8BAA2B,CAAC,OAAO,GAC9C,cACA;;;;;;;;;;;;uCAtKL,WAAW,gBAAgB,YAAY;;;;;gCA4KlD;;;;;;;;;;;;oBAIL,eAAe,iBAAiB,4BAC/B,6LAAC,uHAAA,CAAA,UAAK;wBACJ,QAAQ;wBACR,OAAM;wBACN,QAAO;wBACP,wBACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,OAAM;gCACN,QAAO;gCACP,eAAe;;;;;;;;;;;wBAIrB,SAAS,IAAM,eAAe;;;;;;oBAGjC,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yHAAA,CAAA,UAAO;4BAAC,QAAO;;;;;;;;;;;oBAGnB,iBAAiB,IAAI,kBACpB,6LAAC,4IAAA,CAAA,UAAY;wBACX,oBAAoB,kBAAkB;wBACtC,OAAM;wBACN,iBAAiB,IACf,oBAAoB;gCAAE,MAAM;gCAAO,WAAW;4BAAK;wBAErD,SAAQ;wBACR,mBAAkB;wBAClB,kBAAkB;4BAChB,oBAAoB;gCAAE,MAAM;gCAAO,WAAW;4BAAK;4BACnD,IAAI,kBAAkB,WAAW;gCAC/B,MAAM,gBAAgB,kBAAkB;4BAC1C;wBACF;;;;;;;;;;;;;;;;;;AAMZ;GAxlBM;;QAuBqB,iIAAA,CAAA,iBAAc;QACb,iJAAA,CAAA,aAAU;QAcP,4HAAA,CAAA,kBAAe;;;KAtCxC;uCA0lBS"}}, {"offset": {"line": 5438, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5444, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/RegenerateButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport { unFormattedDateWithBrowserTimezoneWithTimezone } from \"@/utils/utils\";\r\nimport Modal from \"../Modal\";\r\nimport { Info } from \"lucide-react\";\r\nimport { CatalystMatchStatus } from \"@/types/vacancy_status_api\";\r\nimport { UPDATE_TIMESTAMPS_STATUS } from \"@/types/vacancy_status_api\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\n\r\ninterface RegenerateButtonProps {\r\n  vacancyId: string;\r\n  emailId: string;\r\n  canRegenerate: boolean;\r\n  onReloadTable: () => void;\r\n  setRegenerationReadOnly: (value: boolean) => void;\r\n  catalystRegenerationData: any;\r\n  setIsLoading?: (value: boolean) => void; // Optional prop to control loading state\r\n  isTableReadOnly?: boolean; // Optional prop to control table read-only state\r\n  setIsStatusInProcessByClickOfRegenerateButton: () => void;\r\n  mercuryPortal: boolean;\r\n  isClosed: boolean;\r\n}\r\n\r\nconst RegenerateButton: React.FC<RegenerateButtonProps> = ({\r\n  vacancyId,\r\n  emailId,\r\n  canRegenerate,\r\n  onReloadTable,\r\n  setRegenerationReadOnly,\r\n  catalystRegenerationData,\r\n  setIsLoading = () => {}, // Default to no-op if not provided\r\n  isTableReadOnly = false, // Default to false if not provided\r\n  setIsStatusInProcessByClickOfRegenerateButton,\r\n  mercuryPortal,\r\n  isClosed = false, // Optional prop to control if the button is closed\r\n}) => {\r\n  const [regenerationData, setRegenerationData] =\r\n    useState<CatalystMatchStatus | null>(null);\r\n  const [conflictModalOpen, setConflictModalOpen] = useState(false);\r\n  const [showInfoPopup, setShowInfoPopup] = useState(false);\r\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\r\n  const { showNotification } = useNotification();\r\n\r\n  // update regeneration data from catalyst match\r\n  useEffect(() => {\r\n    setRegenerationData(catalystRegenerationData);\r\n  }, [catalystRegenerationData]);\r\n\r\n  // Close popup when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = () => {\r\n      if (showInfoPopup) {\r\n        setShowInfoPopup(false);\r\n      }\r\n    };\r\n\r\n    if (showInfoPopup) {\r\n      document.addEventListener(\"click\", handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener(\"click\", handleClickOutside);\r\n    };\r\n  }, [showInfoPopup]);\r\n\r\n  // Helper function to check if 2 hours have passed since initiated_at\r\n  const isCooldownPeriodOver = (initiatedAt: string | null): boolean => {\r\n    if (!initiatedAt) return true;\r\n    return canRegenerateAfterCooldown(initiatedAt);\r\n  };\r\n\r\n  // Helper function to determine if button should be shown\r\n  const shouldShowButton = (): boolean => {\r\n    if (!regenerationData || !regenerationData.status) return true; // Show when status is \"\"\r\n\r\n    const { status, initiated_at } = regenerationData;\r\n\r\n    if (status === UPDATE_TIMESTAMPS_STATUS.ERROR) return true;\r\n    if (\r\n      regenerationData?.status\r\n        ?.toLowerCase()\r\n        ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED) &&\r\n      isCooldownPeriodOver(initiated_at)\r\n    )\r\n      return true;\r\n\r\n    return false; // Hide for queued, inprocess, in_progress, or completed within 2 hours\r\n  };\r\n\r\n  // Helper function to determine if button should be disabled\r\n  const shouldDisableButton = (): boolean => {\r\n    if (!canRegenerate) return true;\r\n    if (!regenerationData || !regenerationData.status) return false; // Enable when status is \"\"\r\n\r\n    const { status, initiated_at } = regenerationData;\r\n\r\n    if (\r\n      status === UPDATE_TIMESTAMPS_STATUS.QUEUED ||\r\n      status === UPDATE_TIMESTAMPS_STATUS.IN_PROCESS\r\n    )\r\n      return true;\r\n    if (\r\n      regenerationData?.status\r\n        ?.toLowerCase()\r\n        ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED) &&\r\n      !isCooldownPeriodOver(initiated_at)\r\n    )\r\n      return true;\r\n\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (regenerationData) {\r\n      const { status } = regenerationData;\r\n\r\n      if (\r\n        status === UPDATE_TIMESTAMPS_STATUS.IN_PROCESS ||\r\n        status === UPDATE_TIMESTAMPS_STATUS.QUEUED\r\n      ) {\r\n        setRegenerationReadOnly(true);\r\n      } else {\r\n        setRegenerationReadOnly(false);\r\n      }\r\n    }\r\n  }, [regenerationData, emailId, canRegenerate]);\r\n\r\n  // Utility function to check if 2 hours have passed since last regeneration\r\n  const canRegenerateAfterCooldown = (initiatedAt: string | null): boolean => {\r\n    return true;\r\n  };\r\n\r\n  // Reusable function to render info icon with popup\r\n  const renderInfoIcon = () => {\r\n    if (!regenerationData?.initiated_by) return null;\r\n\r\n    return (\r\n      <div className=\"relative\">\r\n        <Info\r\n          size={16}\r\n          className=\"text-blue-500 cursor-pointer hover:text-blue-700\"\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            setShowInfoPopup(!showInfoPopup);\r\n          }}\r\n        />\r\n        {showInfoPopup && (\r\n          <div className=\"absolute right-0 top-6 bg-white text-black px-2 py-1 rounded shadow-lg whitespace-nowrap z-40\">\r\n            Initiated by: {regenerationData.initiated_by}\r\n            <div className=\"absolute -top-1 right-2 w-2 h-2 bg-white transform rotate-45\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const handleRegenerateClick = () => {\r\n    if (!vacancyId || !canRegenerate) return;\r\n    setConfirmModalOpen(true);\r\n  };\r\n\r\n  const confirmRegeneration = async () => {\r\n    setConfirmModalOpen(false);\r\n    await regenerateCatalystMatch();\r\n  };\r\n\r\n  const regenerateCatalystMatch = async () => {\r\n    if (!vacancyId || !canRegenerate) return;\r\n\r\n    // Check cooldown period before making API call\r\n    if (\r\n      !regenerationData ||\r\n      (regenerationData &&\r\n        regenerationData?.status\r\n          ?.toLowerCase()\r\n          ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED))\r\n    ) {\r\n      const initiatedAt = regenerationData?.initiated_at;\r\n      const initiatedBy = regenerationData?.initiated_by;\r\n\r\n      // Check if regeneration was initiated less than 2 hours ago by another user\r\n      if (\r\n        initiatedAt &&\r\n        initiatedBy &&\r\n        initiatedBy !== emailId &&\r\n        !canRegenerateAfterCooldown(initiatedAt)\r\n      ) {\r\n        setConflictModalOpen(true);\r\n        setRegenerationReadOnly(true);\r\n        return;\r\n      }\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true); // Set loading state if provided\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${vacancyId}/regenerate-catalyst-match`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({\r\n            reviewer_email: emailId,\r\n            portal_name: mercuryPortal ? \"Mercury\" : \"Recruiter\",\r\n          }),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data: any = await response.json();\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_REGENERATE_CATALYST_MATCH\",\r\n          properties: {\r\n            reviewerEmail: emailId,\r\n            vacancyId: vacancyId,\r\n            regenerationStatus: data.status,\r\n            context: \"RegenerateCatalystMatch\",\r\n          },\r\n        });\r\n        // Handle different statuses from API response\r\n        setRegenerationData(data);\r\n        if (data.status === UPDATE_TIMESTAMPS_STATUS.ERROR) {\r\n          showNotification(\r\n            \"Failed to regenerate catalyst match. Please try again.\",\r\n            \"error\"\r\n          );\r\n          return;\r\n        } else if (\r\n          data?.status\r\n            ?.toLowerCase()\r\n            ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED)\r\n        ) {\r\n          showNotification(\r\n            \"Catalyst match regeneration completed successfully\",\r\n            \"success\"\r\n          );\r\n          onReloadTable();\r\n        } else if (\r\n          data.status === UPDATE_TIMESTAMPS_STATUS.IN_PROCESS ||\r\n          data.status === UPDATE_TIMESTAMPS_STATUS.QUEUED\r\n        ) {\r\n          setIsStatusInProcessByClickOfRegenerateButton();\r\n          setRegenerationReadOnly(true);\r\n          showNotification(\r\n            \"Catalyst match regeneration initiated successfully. Table is now read-only.\",\r\n            \"info\"\r\n          );\r\n        } else {\r\n          // Default success message for other statuses\r\n          showNotification(\r\n            \"Catalyst match regeneration initiated successfully\",\r\n            \"success\"\r\n          );\r\n        }\r\n      } else if (response.status == 409) {\r\n        await response.json();\r\n        setConflictModalOpen(true);\r\n        setRegenerationData(null);\r\n        setRegenerationReadOnly(true);\r\n        return;\r\n      } else {\r\n        setRegenerationData(null);\r\n        try {\r\n          const errorData = await response.json();\r\n          // Handle specific error cases\r\n          if (response.status === 409) {\r\n            showNotification(\r\n              \"Regeneration already in progress. Please wait for the current job to complete-(409).\",\r\n              \"error\"\r\n            );\r\n          } else if (response.status === 404) {\r\n            showNotification(\r\n              \"Vacancy not found. Please refresh the page and try again-(404).\",\r\n              \"error\"\r\n            );\r\n          } else {\r\n            showNotification(\r\n              `Error: ${errorData.error || response.statusText}`,\r\n              \"error\"\r\n            );\r\n          }\r\n        } catch (jsonError) {\r\n          console.error(\"Error parsing error response:\", jsonError);\r\n          showNotification(\r\n            `Request failed: ${response.status} ${response.statusText}`,\r\n            \"error\"\r\n          );\r\n        }\r\n      }\r\n    } catch (error) {\r\n      showNotification(\r\n        `Error regenerating catalyst match. Please try again - (500)`,\r\n        \"error\"\r\n      );\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          \"Error regenerating catalyst match: \" +\r\n            (error instanceof Error ? error.message : String(error))\r\n        ),\r\n        properties: {\r\n          reviewerEmail: emailId,\r\n          vacancyId: vacancyId,\r\n          context: \"RegenerateCatalystMatch\",\r\n        },\r\n      });\r\n      if (error instanceof Error) {\r\n        if (error?.message?.includes(\"409\")) {\r\n          setConflictModalOpen(true);\r\n          setRegenerationData(null);\r\n          setRegenerationReadOnly(true);\r\n        }\r\n      }\r\n    } finally {\r\n      setIsLoading(false); // Reset loading state\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex flex-col gap-2 mt-4 mb-2\">\r\n        <div className=\"flex gap-4 items-center justify-end\">\r\n          {/* Date label display based on status */}\r\n          {regenerationData &&\r\n            (regenerationData.completed_at || regenerationData.initiated_at) &&\r\n            regenerationData?.status\r\n              ?.toLowerCase()\r\n              ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED) && (\r\n              <span className=\"text-sm text-gray-600 bg-blue-200 p-2 flex gap-2 items-center\">\r\n                Generation completion:{\" \"}\r\n                {unFormattedDateWithBrowserTimezoneWithTimezone(\r\n                  regenerationData.completed_at! ||\r\n                    regenerationData.initiated_at!\r\n                )}\r\n                {renderInfoIcon()}\r\n              </span>\r\n            )}\r\n          {regenerationData &&\r\n            regenerationData.initiated_at &&\r\n            (regenerationData.status === UPDATE_TIMESTAMPS_STATUS.QUEUED ||\r\n              regenerationData.status ===\r\n                UPDATE_TIMESTAMPS_STATUS.IN_PROCESS) && (\r\n              <span className=\"text-sm text-gray-600 bg-blue-200 p-2 flex gap-2 items-center\">\r\n                Generation initiation:{\" \"}\r\n                {unFormattedDateWithBrowserTimezoneWithTimezone(\r\n                  regenerationData.initiated_at\r\n                )}\r\n                {renderInfoIcon()}\r\n              </span>\r\n            )}\r\n\r\n          {regenerationData &&\r\n            regenerationData.initiated_at &&\r\n            regenerationData.status === UPDATE_TIMESTAMPS_STATUS.ERROR && (\r\n              <span className=\"text-sm text-gray-600 bg-blue-200 p-2 flex gap-2 items-center\">\r\n                Generation initiation:{\" \"}\r\n                {unFormattedDateWithBrowserTimezoneWithTimezone(\r\n                  regenerationData.initiated_at\r\n                )}\r\n                {renderInfoIcon()}\r\n              </span>\r\n            )}\r\n\r\n          {/* Show button based on conditions */}\r\n          {\r\n            <Button\r\n              onClick={handleRegenerateClick}\r\n              disabled={shouldDisableButton() || isTableReadOnly || isClosed}\r\n              className={\r\n                shouldDisableButton()\r\n                  ? \"bg-gray-400 hover:bg-gray-400 cursor-not-allowed mr-1 rounded\"\r\n                  : \"mr-1 rounded\"\r\n              }\r\n            >\r\n              Regenerate\r\n            </Button>\r\n          }\r\n        </div>\r\n\r\n        {/* Regeneration comments/messages */}\r\n        <div className=\"text-sm text-gray-700 text-right relative\">\r\n          {/* Show message based on status and time conditions */}\r\n          {isClosed && (\r\n            <div className=\"flex items-center justify-end gap-1\">\r\n              <span>Cannot regenerate — the vacancy has been closed</span>\r\n            </div>\r\n          )}\r\n          {regenerationData &&\r\n            (regenerationData.status === UPDATE_TIMESTAMPS_STATUS.QUEUED ||\r\n              regenerationData.status ===\r\n                UPDATE_TIMESTAMPS_STATUS.IN_PROCESS) && (\r\n              <div className=\"flex items-center justify-end gap-1\">\r\n                <span>\r\n                  Regeneration in progress (Catalyst match is read-only until\r\n                  complete)\r\n                </span>\r\n              </div>\r\n            )}\r\n          {regenerationData &&\r\n            regenerationData?.status\r\n              ?.toLowerCase()\r\n              ?.includes(UPDATE_TIMESTAMPS_STATUS.COMPLETED) &&\r\n            !isCooldownPeriodOver(regenerationData.initiated_at) && (\r\n              <div className=\"flex items-center justify-end gap-1\">\r\n                <span>\r\n                  Regeneration complete. To regenerate, wait for 2 hours from\r\n                  last time\r\n                </span>\r\n              </div>\r\n            )}\r\n          {regenerationData &&\r\n            (regenerationData?.status === \"\" ||\r\n              regenerationData?.status === null) && (\r\n              <div className=\"flex items-center justify-end gap-1\">\r\n                <span>Catalyst match not initiated yet</span>\r\n              </div>\r\n            )}\r\n          {regenerationData &&\r\n            regenerationData.status === UPDATE_TIMESTAMPS_STATUS.ERROR && (\r\n              <div className=\"flex items-center justify-end gap-1\">\r\n                <span>\r\n                  Error during regeneration. Try again or if persists, contact\r\n                  support\r\n                </span>\r\n              </div>\r\n            )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Conflict Modal */}\r\n      <Modal\r\n        isOpen={conflictModalOpen}\r\n        onClose={() => setConflictModalOpen(false)}\r\n        title=\"Regeneration Already in Progress\"\r\n      >\r\n        <div className=\"mb-4\">\r\n          <p className=\"text-gray-700 mb-2\">\r\n            Regeneration is already initiated by other user.\r\n          </p>\r\n        </div>\r\n        <div className=\"flex justify-end\">\r\n          <Button\r\n            onClick={() => setConflictModalOpen(false)}\r\n            className=\"bg-blue-500 hover:bg-blue-600 text-white\"\r\n          >\r\n            OK\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* Confirmation Modal */}\r\n      <Modal\r\n        isOpen={confirmModalOpen}\r\n        onClose={() => setConfirmModalOpen(false)}\r\n        title=\"Confirm Regeneration\"\r\n      >\r\n        <div className=\"mb-4\">\r\n          <p className=\"text-gray-700\">Are you sure you want to regenerate?</p>\r\n        </div>\r\n        <div className=\"flex justify-end gap-2\">\r\n          <Button\r\n            onClick={() => setConfirmModalOpen(false)}\r\n            className=\"bg-gray-500 hover:bg-gray-600 text-white\"\r\n          >\r\n            No\r\n          </Button>\r\n          <Button\r\n            onClick={confirmRegeneration}\r\n            className=\"bg-blue-500 hover:bg-blue-600 text-white\"\r\n          >\r\n            Yes\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RegenerateButton;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAJA;;;AANA;;;;;;;;;;AA0BA,MAAM,mBAAoD,CAAC,EACzD,SAAS,EACT,OAAO,EACP,aAAa,EACb,aAAa,EACb,uBAAuB,EACvB,wBAAwB,EACxB,eAAe,KAAO,CAAC,EACvB,kBAAkB,KAAK,EACvB,6CAA6C,EAC7C,aAAa,EACb,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE3C,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,oBAAoB;QACtB;qCAAG;QAAC;KAAyB;IAE7B,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB;oBACzB,IAAI,eAAe;wBACjB,iBAAiB;oBACnB;gBACF;;YAEA,IAAI,eAAe;gBACjB,SAAS,gBAAgB,CAAC,SAAS;YACrC;YAEA;8CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;gBACxC;;QACF;qCAAG;QAAC;KAAc;IAElB,qEAAqE;IACrE,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,2BAA2B;IACpC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB;QACvB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,MAAM,EAAE,OAAO,MAAM,yBAAyB;QAEzF,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAEjC,IAAI,WAAW,8HAAA,CAAA,2BAAwB,CAAC,KAAK,EAAE,OAAO;QACtD,IACE,kBAAkB,QACd,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,KAC/C,qBAAqB,eAErB,OAAO;QAET,OAAO,OAAO,uEAAuE;IACvF;IAEA,4DAA4D;IAC5D,MAAM,sBAAsB;QAC1B,IAAI,CAAC,eAAe,OAAO;QAC3B,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,MAAM,EAAE,OAAO,OAAO,2BAA2B;QAE5F,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAEjC,IACE,WAAW,8HAAA,CAAA,2BAAwB,CAAC,MAAM,IAC1C,WAAW,8HAAA,CAAA,2BAAwB,CAAC,UAAU,EAE9C,OAAO;QACT,IACE,kBAAkB,QACd,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,KAC/C,CAAC,qBAAqB,eAEtB;;QAAW;QAEb,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,kBAAkB;gBACpB,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IACE,WAAW,8HAAA,CAAA,2BAAwB,CAAC,UAAU,IAC9C,WAAW,8HAAA,CAAA,2BAAwB,CAAC,MAAM,EAC1C;oBACA,wBAAwB;gBAC1B,OAAO;oBACL,wBAAwB;gBAC1B;YACF;QACF;qCAAG;QAAC;QAAkB;QAAS;KAAc;IAE7C,2EAA2E;IAC3E,MAAM,6BAA6B,CAAC;QAClC,OAAO;IACT;IAEA,mDAAmD;IACnD,MAAM,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,cAAc,OAAO;QAE5C,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qMAAA,CAAA,OAAI;oBACH,MAAM;oBACN,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,iBAAiB,CAAC;oBACpB;;;;;;gBAED,+BACC,6LAAC;oBAAI,WAAU;;wBAAgG;wBAC9F,iBAAiB,YAAY;sCAC5C,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,aAAa,CAAC,eAAe;QAClC,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,oBAAoB;QACpB,MAAM;IACR;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,aAAa,CAAC,eAAe;QAElC,+CAA+C;QAC/C,IACE,CAAC,oBACA,oBACC,kBAAkB,QACd,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,GACjD;YACA,MAAM,cAAc,kBAAkB;YACtC,MAAM,cAAc,kBAAkB;YAEtC,4EAA4E;YAC5E,IACE,eACA,eACA,gBAAgB,WAChB,CAAC,2BAA2B,cAC5B;;YAIF;QACF;QAEA,IAAI;YACF,aAAa,OAAO,gCAAgC;YACpD,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,UAAU,0BAA0B,CAAC,EACvD;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,aAAa,gBAAgB,YAAY;gBAC3C;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAY,MAAM,SAAS,IAAI;gBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,eAAe;wBACf,WAAW;wBACX,oBAAoB,KAAK,MAAM;wBAC/B,SAAS;oBACX;gBACF;gBACA,8CAA8C;gBAC9C,oBAAoB;gBACpB,IAAI,KAAK,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,KAAK,EAAE;oBAClD,iBACE,0DACA;oBAEF;gBACF,OAAO,IACL,MAAM,QACF,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,GAC/C;oBACA,iBACE,sDACA;oBAEF;gBACF,OAAO,IACL,KAAK,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,UAAU,IACnD,KAAK,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,MAAM,EAC/C;oBACA;oBACA,wBAAwB;oBACxB,iBACE,+EACA;gBAEJ,OAAO;oBACL,6CAA6C;oBAC7C,iBACE,sDACA;gBAEJ;YACF,OAAO,IAAI,SAAS,MAAM,IAAI,KAAK;gBACjC,MAAM,SAAS,IAAI;gBACnB,qBAAqB;gBACrB,oBAAoB;gBACpB,wBAAwB;gBACxB;YACF,OAAO;gBACL,oBAAoB;gBACpB,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,8BAA8B;oBAC9B,IAAI,SAAS,MAAM,KAAK,KAAK;wBAC3B,iBACE,wFACA;oBAEJ,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;wBAClC,iBACE,mEACA;oBAEJ,OAAO;wBACL,iBACE,CAAC,OAAO,EAAE,UAAU,KAAK,IAAI,SAAS,UAAU,EAAE,EAClD;oBAEJ;gBACF,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,iBACE,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC3D;gBAEJ;YACF;QACF,EAAE,OAAO,OAAO;YACd,iBACE,CAAC,2DAA2D,CAAC,EAC7D;YAEF,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,wCACE,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,MAAM;gBAE3D,YAAY;oBACV,eAAe;oBACf,WAAW;oBACX,SAAS;gBACX;YACF;YACA,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,OAAO,SAAS,SAAS,QAAQ;oBACnC,qBAAqB;oBACrB,oBAAoB;oBACpB,wBAAwB;gBAC1B;YACF;QACF,SAAU;YACR,aAAa,QAAQ,sBAAsB;QAC7C;IACF;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAEZ,oBACC,CAAC,iBAAiB,YAAY,IAAI,iBAAiB,YAAY,KAC/D,kBAAkB,QACd,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,mBAC7C,6LAAC;gCAAK,WAAU;;oCAAgE;oCACvD;oCACtB,CAAA,GAAA,iHAAA,CAAA,iDAA8C,AAAD,EAC5C,iBAAiB,YAAY,IAC3B,iBAAiB,YAAY;oCAEhC;;;;;;;4BAGN,oBACC,iBAAiB,YAAY,IAC7B,CAAC,iBAAiB,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,MAAM,IAC1D,iBAAiB,MAAM,KACrB,8HAAA,CAAA,2BAAwB,CAAC,UAAU,mBACrC,6LAAC;gCAAK,WAAU;;oCAAgE;oCACvD;oCACtB,CAAA,GAAA,iHAAA,CAAA,iDAA8C,AAAD,EAC5C,iBAAiB,YAAY;oCAE9B;;;;;;;4BAIN,oBACC,iBAAiB,YAAY,IAC7B,iBAAiB,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,KAAK,kBACxD,6LAAC;gCAAK,WAAU;;oCAAgE;oCACvD;oCACtB,CAAA,GAAA,iHAAA,CAAA,iDAA8C,AAAD,EAC5C,iBAAiB,YAAY;oCAE9B;;;;;;;0CAML,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,yBAAyB,mBAAmB;gCACtD,WACE,wBACI,kEACA;0CAEP;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;;4BAEZ,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;4BAGT,oBACC,CAAC,iBAAiB,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,MAAM,IAC1D,iBAAiB,MAAM,KACrB,8HAAA,CAAA,2BAAwB,CAAC,UAAU,mBACrC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;4BAMX,oBACC,kBAAkB,QACd,eACA,SAAS,8HAAA,CAAA,2BAAwB,CAAC,SAAS,KAC/C,CAAC,qBAAqB,iBAAiB,YAAY,mBACjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;4BAMX,oBACC,CAAC,kBAAkB,WAAW,MAC5B,kBAAkB,WAAW,IAAI,mBACjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;4BAGX,oBACC,iBAAiB,MAAM,KAAK,8HAAA,CAAA,2BAAwB,CAAC,KAAK,kBACxD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAUhB,6LAAC,uHAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,OAAM;;kCAEN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC,uHAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,OAAM;;kCAEN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CACX;;;;;;0CAGD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;AAOX;GArcM;;QAkByB,4HAAA,CAAA,kBAAe;;;KAlBxC;uCAucS"}}, {"offset": {"line": 5953, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5959, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 6019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6025, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAGA;AAFA;AACA;AAAA;AAAA;AAJA;;;;;;AAOA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,sKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,8JAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,sKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,8JAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,sKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,sKAAgB,MAAM;kBACrB,cAAA,6LAAC,sKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,sKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sKAAgB,aAAa;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6LAAC,sKAAgB,QAAQ;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,8JAAM,UAAU,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,sKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 6234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6240, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 6282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6288, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/notifications/NotificationList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport { X } from \"lucide-react\";\r\n\r\nexport default function NotificationList() {\r\n  const { notifications, removeNotification } = useNotification();\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-[9999] space-y-2 w-[300px]\">\r\n      {notifications?.map((notif) => (\r\n        <div\r\n          key={notif.id}\r\n          className={`flex items-start justify-between gap-2 px-4 py-3 rounded shadow-md text-white animate-slide-in-down\r\n            ${\r\n              notif.type === \"success\"\r\n                ? \"bg-green-600\"\r\n                : notif.type === \"error\"\r\n                ? \"bg-red-600\"\r\n                : notif.type === \"warning\"\r\n                ? \"bg-yellow-500\"\r\n                : \"bg-blue-600\"\r\n            }\r\n          `}\r\n        >\r\n          <div className=\"text-sm flex-1\">{notif.message}</div>\r\n          <button\r\n            onClick={() => removeNotification(notif.id)}\r\n            className=\"ml-2 mt-1\"\r\n          >\r\n            <X className=\"w-4 h-4 text-white\" />\r\n          </button>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAIe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE5D,qBACE,6LAAC;QAAI,WAAU;kBACZ,eAAe,IAAI,CAAC,sBACnB,6LAAC;gBAEC,WAAW,CAAC;YACV,EACE,MAAM,IAAI,KAAK,YACX,iBACA,MAAM,IAAI,KAAK,UACf,eACA,MAAM,IAAI,KAAK,YACf,kBACA,cACL;UACH,CAAC;;kCAED,6LAAC;wBAAI,WAAU;kCAAkB,MAAM,OAAO;;;;;;kCAC9C,6LAAC;wBACC,SAAS,IAAM,mBAAmB,MAAM,EAAE;wBAC1C,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;eAlBV,MAAM,EAAE;;;;;;;;;;AAwBvB;GA/BwB;;QACwB,4HAAA,CAAA,kBAAe;;;KADvC"}}, {"offset": {"line": 6355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/hooks/useVacancyStatus.ts"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport {\r\n  UPDATE_TIMESTAMPS_STATUS,\r\n  type VacancyStatusData,\r\n} from \"@/types/vacancy_status_api\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\n\r\ntype TimerMap = { [key: string]: ReturnType<typeof setInterval> };\r\n\r\nexport const useVacancyStatus = (\r\n  statusApiCall: boolean,\r\n  vacancy_Id: string,\r\n  localTime: string,\r\n  dependency: any[] = [] // Added dependency array to avoid ESLint warning\r\n) => {\r\n  const timersRef = useRef<TimerMap>({});\r\n  const { data: session } = useSession();\r\n  const { showNotification } = useNotification();\r\n  const [statusData, setStatusData] = useState<VacancyStatusData | null>(null);\r\n  const [statusError, setStatusError] = useState<string | null>(null);\r\n  const [isStatusInProcess, setIsStatusInProcess] = useState<boolean | null>(\r\n    null\r\n  );\r\n  const [statusCode, setStatusCode] = useState<number | null>(null); // ✅ Track response status\r\n  const [isVacancyApiCalled, setIsVacancyApiCalled] = useState(false);\r\n  const [error, setError] = useState(false);\r\n\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n  const fetchGetVacancyStatusPoll = async (id: string) => {\r\n    if (!id) return;\r\n    // Abort previous fetch\r\n    abortControllerRef.current?.abort();\r\n    abortControllerRef.current = new AbortController();\r\n    try {\r\n      const response = await fetch(\r\n        `/api/vacancies/catelist-match-status?id=${id}`,\r\n        { signal: abortControllerRef.current.signal }\r\n      );\r\n      setStatusCode(response.status);\r\n      const data: VacancyStatusData = await response.json();\r\n      if (!response.ok) {\r\n        setError(true);\r\n        throw new Error(\r\n          JSON.stringify({\r\n            error: true,\r\n            status: response.status,\r\n            message: (data as any)?.message || \"Failed to fetch vacancy status\",\r\n          })\r\n        );\r\n      }\r\n      setStatusData(data);\r\n      setIsVacancyApiCalled(true);\r\n      const status = data?.catalyst_match_status?.status ?? null;\r\n\r\n      setIsStatusInProcess(\r\n        status === UPDATE_TIMESTAMPS_STATUS.QUEUED ||\r\n          status === UPDATE_TIMESTAMPS_STATUS.IN_PROCESS\r\n      );\r\n      getAppInsights()?.trackEvent({\r\n        name: \"FE_GET_UPDATE_CATALYST_MATCH\",\r\n        properties: {\r\n          reviewerEmail: session?.user?.email,\r\n          // candidateId: contactId,\r\n          context: \"GetUpdateCatalystMatch\",\r\n        },\r\n      });\r\n      //  }\r\n    } catch (error: any) {\r\n      if (error.name === \"AbortError\") return; // Fetch was cancelled,\r\n      let errorObj = {\r\n        error: true,\r\n        status: 500,\r\n        message: \"Failed to fetch vacancy status\",\r\n      };\r\n      if (typeof error === \"string\") {\r\n        try {\r\n          errorObj = JSON.parse(error);\r\n        } catch {\r\n          errorObj.message = error;\r\n        }\r\n      } else if (error instanceof Error && error.message) {\r\n        try {\r\n          errorObj = JSON.parse(error.message);\r\n        } catch {\r\n          errorObj.message = error.message;\r\n        }\r\n      }\r\n      showNotification(errorObj.message + \" : \" + errorObj.status, \"error\");\r\n      setStatusError(errorObj.message);\r\n      setStatusCode(errorObj.status);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!vacancy_Id && !statusApiCall) return;\r\n    if (\r\n      !timersRef.current[vacancy_Id] &&\r\n      statusApiCall &&\r\n      statusData === null\r\n    ) {\r\n      fetchGetVacancyStatusPoll(vacancy_Id);\r\n      timersRef.current[vacancy_Id] = setInterval(() => {\r\n        fetchGetVacancyStatusPoll(vacancy_Id);\r\n      }, 30000);\r\n    } else if (\r\n      !timersRef.current[vacancy_Id] &&\r\n      statusApiCall &&\r\n      statusData !== null\r\n    ) {\r\n      timersRef.current[vacancy_Id] = setInterval(() => {\r\n        fetchGetVacancyStatusPoll(vacancy_Id);\r\n      }, 30000);\r\n    }\r\n    return () => {\r\n      Object.values(timersRef.current).forEach(clearInterval);\r\n      timersRef.current = {};\r\n      abortControllerRef.current?.abort();\r\n    };\r\n  }, [statusApiCall, vacancy_Id, ...dependency]);\r\n\r\n  if (!statusData)\r\n    return {\r\n      isVacancyApiCalled,\r\n      catalyst_match_status: undefined,\r\n      update_timestamps: undefined,\r\n      isStatusInProcess,\r\n      statusError,\r\n      statusCode,\r\n      setStatusData,\r\n      setIsStatusInProcess,\r\n      setIsVacancyApiCalled,\r\n    };\r\n  const { catalyst_match_status, update_timestamps } = statusData;\r\n\r\n  return {\r\n    isVacancyApiCalled,\r\n    isStatusInProcess,\r\n    catalyst_match_status,\r\n    update_timestamps,\r\n    statusError,\r\n    statusCode,\r\n    error,\r\n    setStatusData,\r\n    setIsStatusInProcess,\r\n    setIsVacancyApiCalled,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAIA;AACA;;AARA;;;;;;AAYO,MAAM,mBAAmB,CAC9B,eACA,YACA,WACA,aAAoB,EAAE,CAAC,iDAAiD;AAAlD;;IAEtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAY,CAAC;IACpC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,0BAA0B;IAC7F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,4BAA4B,OAAO;QACvC,IAAI,CAAC,IAAI;QACT,uBAAuB;QACvB,mBAAmB,OAAO,EAAE;QAC5B,mBAAmB,OAAO,GAAG,IAAI;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,wCAAwC,EAAE,IAAI,EAC/C;gBAAE,QAAQ,mBAAmB,OAAO,CAAC,MAAM;YAAC;YAE9C,cAAc,SAAS,MAAM;YAC7B,MAAM,OAA0B,MAAM,SAAS,IAAI;YACnD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS;gBACT,MAAM,IAAI,MACR,KAAK,SAAS,CAAC;oBACb,OAAO;oBACP,QAAQ,SAAS,MAAM;oBACvB,SAAS,AAAC,MAAc,WAAW;gBACrC;YAEJ;YACA,cAAc;YACd,sBAAsB;YACtB,MAAM,SAAS,MAAM,uBAAuB,UAAU;YAEtD,qBACE,WAAW,8HAAA,CAAA,2BAAwB,CAAC,MAAM,IACxC,WAAW,8HAAA,CAAA,2BAAwB,CAAC,UAAU;YAElD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;gBAC3B,MAAM;gBACN,YAAY;oBACV,eAAe,SAAS,MAAM;oBAC9B,0BAA0B;oBAC1B,SAAS;gBACX;YACF;QACA,KAAK;QACP,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,IAAI,KAAK,cAAc,QAAQ,uBAAuB;YAChE,IAAI,WAAW;gBACb,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;YACA,IAAI,OAAO,UAAU,UAAU;gBAC7B,IAAI;oBACF,WAAW,KAAK,KAAK,CAAC;gBACxB,EAAE,OAAM;oBACN,SAAS,OAAO,GAAG;gBACrB;YACF,OAAO,IAAI,iBAAiB,SAAS,MAAM,OAAO,EAAE;gBAClD,IAAI;oBACF,WAAW,KAAK,KAAK,CAAC,MAAM,OAAO;gBACrC,EAAE,OAAM;oBACN,SAAS,OAAO,GAAG,MAAM,OAAO;gBAClC;YACF;YACA,iBAAiB,SAAS,OAAO,GAAG,QAAQ,SAAS,MAAM,EAAE;YAC7D,eAAe,SAAS,OAAO;YAC/B,cAAc,SAAS,MAAM;QAC/B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,cAAc,CAAC,eAAe;YACnC,IACE,CAAC,UAAU,OAAO,CAAC,WAAW,IAC9B,iBACA,eAAe,MACf;gBACA,0BAA0B;gBAC1B,UAAU,OAAO,CAAC,WAAW,GAAG;kDAAY;wBAC1C,0BAA0B;oBAC5B;iDAAG;YACL,OAAO,IACL,CAAC,UAAU,OAAO,CAAC,WAAW,IAC9B,iBACA,eAAe,MACf;gBACA,UAAU,OAAO,CAAC,WAAW,GAAG;kDAAY;wBAC1C,0BAA0B;oBAC5B;iDAAG;YACL;YACA;8CAAO;oBACL,OAAO,MAAM,CAAC,UAAU,OAAO,EAAE,OAAO,CAAC;oBACzC,UAAU,OAAO,GAAG,CAAC;oBACrB,mBAAmB,OAAO,EAAE;gBAC9B;;QACF;qCAAG;QAAC;QAAe;WAAe;KAAW;IAE7C,IAAI,CAAC,YACH,OAAO;QACL;QACA,uBAAuB;QACvB,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QACA;IACF;IACF,MAAM,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,GAAG;IAErD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzIa;;QAOe,iJAAA,CAAA,aAAU;QACP,4HAAA,CAAA,kBAAe"}}, {"offset": {"line": 6510, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6516, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\nexport function useDebounce<T>(value: T, delay: number = 300): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    // Cleanup old timer if value changes before delay\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS,YAAe,KAAQ,EAAE,QAAgB,GAAG;;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;gBACpB;gDAAG;YAEH,kDAAkD;YAClD;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;GAfgB"}}, {"offset": {"line": 6549, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/store/vacancyFilterStore.ts"], "sourcesContent": ["\"use client\";\r\nimport { VACANCY_FILTER_LABELS } from \"@/library/utils\";\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\nexport type VacancyFilter = {\r\n  [VACANCY_FILTER_LABELS.STATE]: string[];\r\n  [VACANCY_FILTER_LABELS.CITY]: string[];\r\n  [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: string[];\r\n  [VACANCY_FILTER_LABELS.SHORT_LISTED]: string[];\r\n  [VACANCY_FILTER_LABELS.REVIEW_DECISION]: string[];\r\n  [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: string[];\r\n  [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: string[];\r\n  [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [number, number];\r\n  [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: { from: string; to: string };\r\n  [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: string[];\r\n  [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [number, number];\r\n};\r\n\r\nexport type ArrayFilterKeys =\r\n  | VACANCY_FILTER_LABELS.STATE\r\n  | VACANCY_FILTER_LABELS.CITY\r\n  | VACANCY_FILTER_LABELS.FRESHNESS_INDEX\r\n  | VACANCY_FILTER_LABELS.SHORT_LISTED\r\n  | VACANCY_FILTER_LABELS.REVIEW_DECISION\r\n  | VACANCY_FILTER_LABELS.AI_AGENT_STATUS\r\n  | VACANCY_FILTER_LABELS.SEARCH_BY_NAME;\r\n\r\ntype VacancyFilterMap = {\r\n  [vacancyId: string]: VacancyFilter;\r\n};\r\n\r\ntype FilterStore = {\r\n  ShowFilter: boolean;\r\n  filters: VacancyFilter;\r\n  setFilterField: <K extends keyof VacancyFilter>(\r\n    field: K,\r\n    value: VacancyFilter[K]\r\n  ) => void;\r\n  resetFilter: (maxDistance: number, maxTotalScore: number) => void;\r\n  toggleSearchField: (field: string) => void;\r\n  handleCheckBoxSelection: (label: ArrayFilterKeys, value: string) => void;\r\n  perVacancyFilters: VacancyFilterMap;\r\n  saveFiltersForVacancy: (vacancyId: string, filters: VacancyFilter) => void;\r\n  loadFiltersForVacancy: (vacancyId: string) => VacancyFilter | null;\r\n  saveCurrentFiltersForVacancy: (vacancyId: string) => void;\r\n  clearFiltersForVacancy: (vacancyId: string) => void;\r\n  setDistanceRange: (range: [number, number]) => void;\r\n};\r\n\r\nexport const defaultFilter: VacancyFilter = {\r\n  [VACANCY_FILTER_LABELS.STATE]: [],\r\n  [VACANCY_FILTER_LABELS.CITY]: [],\r\n  [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: [],\r\n  [VACANCY_FILTER_LABELS.SHORT_LISTED]: [],\r\n  [VACANCY_FILTER_LABELS.REVIEW_DECISION]: [],\r\n  [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: [],\r\n  [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: [],\r\n  [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [0, 0],\r\n  [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: { from: \"\", to: \"\" },\r\n  [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: [],\r\n  [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [0, 0], // Default distance range\r\n};\r\n\r\nconst createFilterStore = (storageKey: string) =>\r\n  create<FilterStore>()(\r\n    persist(\r\n      (set, get) => ({\r\n        ShowFilter: true,\r\n        mercuryPortal: storageKey.includes(\"mercury\"),\r\n        filters: { ...defaultFilter },\r\n        perVacancyFilters: {},\r\n\r\n        setFilterField: (field, value) =>\r\n          set((state) => ({\r\n            filters: {\r\n              ...state.filters,\r\n              [field]: value,\r\n            },\r\n          })),\r\n\r\n        resetFilter: (maxDistance: number, maxTotalScore: number) =>\r\n          set({\r\n            filters: {\r\n              ...defaultFilter,\r\n              [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [0, maxTotalScore],\r\n              [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [0, maxDistance],\r\n            },\r\n          }),\r\n\r\n        toggleSearchField: (field) =>\r\n          set((state) => {\r\n            const searchFields = state?.filters?.[\r\n              VACANCY_FILTER_LABELS.SEARCH_FIELDS\r\n            ].includes(field)\r\n              ? state?.filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS].filter(\r\n                  (f) => f !== field\r\n                )\r\n              : [\r\n                  ...state?.filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS],\r\n                  field,\r\n                ];\r\n            return {\r\n              filters: {\r\n                ...state?.filters,\r\n                [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: searchFields,\r\n              },\r\n            };\r\n          }),\r\n\r\n        saveFiltersForVacancy: (vacancyId, filters) =>\r\n          set((state) => ({\r\n            perVacancyFilters: {\r\n              ...state.perVacancyFilters,\r\n              [vacancyId.toLowerCase()]: filters,\r\n            },\r\n          })),\r\n\r\n        loadFiltersForVacancy: (vacancyId) => {\r\n          const stored = get()?.perVacancyFilters[vacancyId.toLowerCase()];\r\n          return stored ?? null;\r\n        },\r\n\r\n        saveCurrentFiltersForVacancy: (vacancyId: string) => {\r\n          const filters = get().filters;\r\n          get().saveFiltersForVacancy(vacancyId, filters);\r\n        },\r\n\r\n        setDistanceRange: (range: [number, number]) => {\r\n          set((state) => ({\r\n            filters: {\r\n              ...state?.filters,\r\n              [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: range,\r\n            },\r\n          }));\r\n        },\r\n\r\n        clearFiltersForVacancy: (vacancyId: string) => {\r\n          set((state) => {\r\n            const key = vacancyId.toLowerCase();\r\n            const newPerVacancyFilters = { ...state.perVacancyFilters };\r\n            delete newPerVacancyFilters[key];\r\n            return { perVacancyFilters: newPerVacancyFilters };\r\n          });\r\n        },\r\n\r\n        handleCheckBoxSelection: (label, selectedValue) =>\r\n          set((state) => {\r\n            const validLabels: ArrayFilterKeys[] = [\r\n              VACANCY_FILTER_LABELS.STATE,\r\n              VACANCY_FILTER_LABELS.CITY,\r\n              VACANCY_FILTER_LABELS.FRESHNESS_INDEX,\r\n              VACANCY_FILTER_LABELS.SHORT_LISTED,\r\n              VACANCY_FILTER_LABELS.REVIEW_DECISION,\r\n              VACANCY_FILTER_LABELS.AI_AGENT_STATUS,\r\n              VACANCY_FILTER_LABELS.SEARCH_BY_NAME,\r\n            ];\r\n\r\n            if (!validLabels.includes(label)) {\r\n              console.warn(`Invalid filter label: ${label}`);\r\n              return {};\r\n            }\r\n\r\n            const currentValues = state?.filters[label] as string[];\r\n            const newValues = currentValues.includes(selectedValue)\r\n              ? currentValues?.filter((v) => v !== selectedValue)\r\n              : [...currentValues, selectedValue];\r\n\r\n            return {\r\n              filters: {\r\n                ...state.filters,\r\n                [label]: newValues,\r\n              },\r\n            };\r\n          }),\r\n      }),\r\n      {\r\n        name: storageKey,\r\n        partialize: (state) => ({\r\n          filters: state?.filters,\r\n          perVacancyFilters: state?.perVacancyFilters,\r\n        }),\r\n      }\r\n    )\r\n  );\r\n\r\n// Export both stores — one for mercuryPortal, one for default\r\nexport const useMercuryFilterStore = createFilterStore(\"mercury-filter-store\");\r\nexport const useDefaultFilterStore = createFilterStore(\r\n  \"recruiter-filter-store\"\r\n);\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAHA;;;;AAkDO,MAAM,gBAA+B;IAC1C,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC,EAAE,EAAE;IACjC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE;IAChC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,EAAE;IAC3C,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,EAAE,EAAE;IACxC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,EAAE;IAC3C,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,EAAE;IAC3C,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,EAAE;IAC1C,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE;QAAC;QAAG;KAAE;IACjD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE;QAAE,MAAM;QAAI,IAAI;IAAG;IACpE,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,EAAE;IACzC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE;QAAC;QAAG;KAAE;AAChD;AAEA,MAAM,oBAAoB,CAAC,aACzB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACH,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;YACb,YAAY;YACZ,eAAe,WAAW,QAAQ,CAAC;YACnC,SAAS;gBAAE,GAAG,aAAa;YAAC;YAC5B,mBAAmB,CAAC;YAEpB,gBAAgB,CAAC,OAAO,QACtB,IAAI,CAAC,QAAU,CAAC;wBACd,SAAS;4BACP,GAAG,MAAM,OAAO;4BAChB,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YAEH,aAAa,CAAC,aAAqB,gBACjC,IAAI;oBACF,SAAS;wBACP,GAAG,aAAa;wBAChB,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE;4BAAC;4BAAG;yBAAc;wBAC7D,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE;4BAAC;4BAAG;yBAAY;oBAC1D;gBACF;YAEF,mBAAmB,CAAC,QAClB,IAAI,CAAC;oBACH,MAAM,eAAe,OAAO,SAAS,CACnC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CACpC,CAAC,SAAS,SACP,OAAO,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,CAAC,OACpD,CAAC,IAAM,MAAM,SAEf;2BACK,OAAO,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC;wBACxD;qBACD;oBACL,OAAO;wBACL,SAAS;4BACP,GAAG,OAAO,OAAO;4BACjB,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE;wBACzC;oBACF;gBACF;YAEF,uBAAuB,CAAC,WAAW,UACjC,IAAI,CAAC,QAAU,CAAC;wBACd,mBAAmB;4BACjB,GAAG,MAAM,iBAAiB;4BAC1B,CAAC,UAAU,WAAW,GAAG,EAAE;wBAC7B;oBACF,CAAC;YAEH,uBAAuB,CAAC;gBACtB,MAAM,SAAS,OAAO,iBAAiB,CAAC,UAAU,WAAW,GAAG;gBAChE,OAAO,UAAU;YACnB;YAEA,8BAA8B,CAAC;gBAC7B,MAAM,UAAU,MAAM,OAAO;gBAC7B,MAAM,qBAAqB,CAAC,WAAW;YACzC;YAEA,kBAAkB,CAAC;gBACjB,IAAI,CAAC,QAAU,CAAC;wBACd,SAAS;4BACP,GAAG,OAAO,OAAO;4BACjB,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE;wBAC1C;oBACF,CAAC;YACH;YAEA,wBAAwB,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,MAAM,UAAU,WAAW;oBACjC,MAAM,uBAAuB;wBAAE,GAAG,MAAM,iBAAiB;oBAAC;oBAC1D,OAAO,oBAAoB,CAAC,IAAI;oBAChC,OAAO;wBAAE,mBAAmB;oBAAqB;gBACnD;YACF;YAEA,yBAAyB,CAAC,OAAO,gBAC/B,IAAI,CAAC;oBACH,MAAM,cAAiC;wBACrC,mHAAA,CAAA,wBAAqB,CAAC,KAAK;wBAC3B,mHAAA,CAAA,wBAAqB,CAAC,IAAI;wBAC1B,mHAAA,CAAA,wBAAqB,CAAC,eAAe;wBACrC,mHAAA,CAAA,wBAAqB,CAAC,YAAY;wBAClC,mHAAA,CAAA,wBAAqB,CAAC,eAAe;wBACrC,mHAAA,CAAA,wBAAqB,CAAC,eAAe;wBACrC,mHAAA,CAAA,wBAAqB,CAAC,cAAc;qBACrC;oBAED,IAAI,CAAC,YAAY,QAAQ,CAAC,QAAQ;wBAChC,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,OAAO;wBAC7C,OAAO,CAAC;oBACV;oBAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,MAAM;oBAC3C,MAAM,YAAY,cAAc,QAAQ,CAAC,iBACrC,eAAe,OAAO,CAAC,IAAM,MAAM,iBACnC;2BAAI;wBAAe;qBAAc;oBAErC,OAAO;wBACL,SAAS;4BACP,GAAG,MAAM,OAAO;4BAChB,CAAC,MAAM,EAAE;wBACX;oBACF;gBACF;QACJ,CAAC,GACD;QACE,MAAM;QACN,YAAY,CAAC,QAAU,CAAC;gBACtB,SAAS,OAAO;gBAChB,mBAAmB,OAAO;YAC5B,CAAC;IACH;AAKC,MAAM,wBAAwB,kBAAkB;AAChD,MAAM,wBAAwB,kBACnC"}}, {"offset": {"line": 6699, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/helper.ts"], "sourcesContent": ["import type { Candidate } from \"@/app/candidates/helper\";\r\nimport { VACANCY_FILTER_OTHER_LABELS } from \"@/library/utils\";\r\nimport { capitalizeEachWord } from \"@/utils/utils\";\r\n\r\n/**\r\n * Extracts unique states and their corresponding unique cities from candidates.\r\n * Returns an object mapping each state to an array of its cities.\r\n * Ignores empty, null, undefined, and 'MISSING' values.\r\n */\r\nexport const extractLocations = (\r\n  candidates: Candidate[]\r\n): Record<string, string[]> => {\r\n  // Map of state to Set of unique cities\r\n  const stateToCitiesMap: Record<string, Set<string>> = {};\r\n\r\n  const isValid = (val: string | null | undefined) =>\r\n    val && val !== VACANCY_FILTER_OTHER_LABELS.MISSING && val.trim() !== \"\";\r\n\r\n  candidates &&\r\n    candidates?.forEach((candidate) => {\r\n      const cityRaw = candidate?.candidate_data?.city;\r\n      const stateRaw = candidate?.candidate_data?.state;\r\n\r\n      if (!isValid(stateRaw)) return; // Ignore if state invalid\r\n\r\n      const state = capitalizeEachWord(stateRaw.toLowerCase());\r\n\r\n      if (!stateToCitiesMap[state]) {\r\n        stateToCitiesMap[state] = new Set<string>();\r\n      }\r\n\r\n      if (isValid(cityRaw)) {\r\n        const city = capitalizeEachWord(cityRaw.toLowerCase());\r\n        // Add city under this state\r\n        stateToCitiesMap[state].add(city);\r\n      }\r\n    });\r\n\r\n  // Convert Sets to arrays for output\r\n  const uniqueLocation: Record<string, string[]> = {};\r\n  Object.keys(stateToCitiesMap)?.forEach((state) => {\r\n    uniqueLocation[state] = Array.from(stateToCitiesMap[state]).sort((a, b) =>\r\n      a.localeCompare(b)\r\n    );\r\n  });\r\n\r\n  return uniqueLocation;\r\n};\r\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAOO,MAAM,mBAAmB,CAC9B;IAEA,uCAAuC;IACvC,MAAM,mBAAgD,CAAC;IAEvD,MAAM,UAAU,CAAC,MACf,OAAO,QAAQ,mHAAA,CAAA,8BAA2B,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO;IAEvE,cACE,YAAY,QAAQ,CAAC;QACnB,MAAM,UAAU,WAAW,gBAAgB;QAC3C,MAAM,WAAW,WAAW,gBAAgB;QAE5C,IAAI,CAAC,QAAQ,WAAW,QAAQ,0BAA0B;QAE1D,MAAM,QAAQ,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,WAAW;QAErD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAC5B,gBAAgB,CAAC,MAAM,GAAG,IAAI;QAChC;QAEA,IAAI,QAAQ,UAAU;YACpB,MAAM,OAAO,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,WAAW;YACnD,4BAA4B;YAC5B,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B;IACF;IAEF,oCAAoC;IACpC,MAAM,iBAA2C,CAAC;IAClD,OAAO,IAAI,CAAC,mBAAmB,QAAQ,CAAC;QACtC,cAAc,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IACnE,EAAE,aAAa,CAAC;IAEpB;IAEA,OAAO;AACT"}}, {"offset": {"line": 6740, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6746, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/CatalystMatchForm.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Send, RotateCcw, CheckCircle, XCircle, Download } from \"lucide-react\";\r\nimport { submitCatalystMatchForm, getVacancyTemplate, saveVacancyTemplate } from \"@/api/serverActions\";\r\n\r\ninterface FormData {\r\n  requiredSkills: string[];\r\n  preferredSkills: string[];\r\n  recencyMustHaveSkills: string;\r\n  additionalJobTitles: string[];\r\n  city: string;\r\n  state: string;\r\n  miles: string;\r\n  yearsExperience: string;\r\n  degrees: string[];\r\n  certificationsLicenses: string[];\r\n  industry: string;\r\n  confidential: string;\r\n  enableCatalystMatch: boolean;\r\n}\r\n\r\nconst CatalystMatchForm: React.FC = () => {\r\n  // Sample city data by state\r\n  const citiesByState: { [key: string]: string[] } = {\r\n    \"CA\": [\"Los Angeles\", \"San Francisco\", \"San Diego\", \"San Jose\", \"Sacramento\", \"Fresno\", \"Oakland\", \"Long Beach\", \"Bakersfield\", \"Anaheim\"],\r\n    \"NY\": [\"New York City\", \"Buffalo\", \"Rochester\", \"Yonkers\", \"Syracuse\", \"Albany\", \"New Rochelle\", \"Mount Vernon\", \"Schenectady\", \"Utica\"],\r\n    \"TX\": [\"Houston\", \"San Antonio\", \"Dallas\", \"Austin\", \"Fort Worth\", \"El Paso\", \"Arlington\", \"Corpus Christi\", \"Plano\", \"Lubbock\"],\r\n    \"FL\": [\"Jacksonville\", \"Miami\", \"Tampa\", \"Orlando\", \"St. Petersburg\", \"Hialeah\", \"Tallahassee\", \"Fort Lauderdale\", \"Port St. Lucie\", \"Cape Coral\"],\r\n    \"IL\": [\"Chicago\", \"Aurora\", \"Rockford\", \"Joliet\", \"Naperville\", \"Springfield\", \"Peoria\", \"Elgin\", \"Waukegan\", \"Champaign\"],\r\n    \"PA\": [\"Philadelphia\", \"Pittsburgh\", \"Allentown\", \"Erie\", \"Reading\", \"Scranton\", \"Bethlehem\", \"Lancaster\", \"Harrisburg\", \"Altoona\"],\r\n    \"OH\": [\"Columbus\", \"Cleveland\", \"Cincinnati\", \"Toledo\", \"Akron\", \"Dayton\", \"Parma\", \"Canton\", \"Youngstown\", \"Lorain\"],\r\n    \"GA\": [\"Atlanta\", \"Augusta\", \"Columbus\", \"Macon\", \"Savannah\", \"Athens\", \"Sandy Springs\", \"Roswell\", \"Albany\", \"Johns Creek\"],\r\n    \"NC\": [\"Charlotte\", \"Raleigh\", \"Greensboro\", \"Durham\", \"Winston-Salem\", \"Fayetteville\", \"Cary\", \"Wilmington\", \"High Point\", \"Greenville\"],\r\n    \"MI\": [\"Detroit\", \"Grand Rapids\", \"Warren\", \"Sterling Heights\", \"Lansing\", \"Ann Arbor\", \"Flint\", \"Dearborn\", \"Livonia\", \"Westland\"]\r\n  };\r\n\r\n  const [formData, setFormData] = useState<FormData>({\r\n    requiredSkills: [],\r\n    preferredSkills: [],\r\n    recencyMustHaveSkills: \"Current +1\",\r\n    additionalJobTitles: [],\r\n    city: \"\",\r\n    state: \"\",\r\n    miles: \"50\",\r\n    yearsExperience: \"\",\r\n    degrees: [],\r\n    certificationsLicenses: [],\r\n    industry: \"No\",\r\n    confidential: \"No\",\r\n    enableCatalystMatch: false,\r\n  });\r\n\r\n  const [vacancyId, setVacancyId] = useState(\"\");\r\n  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);\r\n  const [templateMessage, setTemplateMessage] = useState(\"\");\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitMessage, setSubmitMessage] = useState(\"\");\r\n  const [saveWithGenerate, setSaveWithGenerate] = useState(false);\r\n  \r\n  const [currentInputs, setCurrentInputs] = useState({\r\n    requiredSkills: \"\",\r\n    preferredSkills: \"\",\r\n    additionalJobTitles: \"\",\r\n    degrees: \"\",\r\n    certificationsLicenses: \"\",\r\n  });\r\n\r\n  const handleInputChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>\r\n  ) => {\r\n    const { name, value, type } = e.target;\r\n    \r\n    // Clear city when state changes\r\n    if (name === \"state\") {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: value,\r\n        city: \"\", // Clear city when state changes\r\n      }));\r\n    } else {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: type === \"checkbox\" ? (e.target as HTMLInputElement).checked : value,\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleTagInput = (field: keyof FormData, value: string) => {\r\n    setCurrentInputs(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const removeTag = (field: keyof FormData, index: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: (prev[field] as string[]).filter((_, i) => i !== index)\r\n    }));\r\n  };\r\n\r\n  const normalizeTemplate = (raw: any) => {\r\n    // deep clone to strip out any Proxies/non-serializables if they sneak in\r\n    const safe = JSON.parse(JSON.stringify(raw ?? {}));\r\n\r\n    // fix small value mismatches so the UI always has a valid option\r\n    const recency = safe.recencyMustHaveSkills;\r\n    const normalizedRecency =\r\n      recency === \"Current + 1\" ? \"Current +1\"\r\n      : recency === \"Current +1\" ? \"Current +1\"\r\n      : recency === \"Current\" ? \"Current\"\r\n      : \"Current +1\";\r\n\r\n    const industry =\r\n      typeof safe.industry === \"string\" && safe.industry.toLowerCase() === \"yes\"\r\n        ? \"Yes\"\r\n        : \"No\";\r\n\r\n    return {\r\n      requiredSkills: Array.isArray(safe.requiredSkills) ? safe.requiredSkills : [],\r\n      preferredSkills: Array.isArray(safe.preferredSkills) ? safe.preferredSkills : [],\r\n      recencyMustHaveSkills: normalizedRecency,\r\n      additionalJobTitles: Array.isArray(safe.additionalJobTitles) ? safe.additionalJobTitles : [],\r\n      city: typeof safe.city === \"string\" ? safe.city : \"\",\r\n      state: typeof safe.state === \"string\" ? safe.state : \"\",\r\n      miles: typeof safe.miles === \"string\" || typeof safe.miles === \"number\" ? String(safe.miles) : \"50\",\r\n      yearsExperience: typeof safe.yearsExperience === \"string\" || typeof safe.yearsExperience === \"number\" ? String(safe.yearsExperience) : \"\",\r\n      degrees: Array.isArray(safe.degrees) ? safe.degrees : [],\r\n      certificationsLicenses: Array.isArray(safe.certificationsLicenses) ? safe.certificationsLicenses : [],\r\n      industry,\r\n      confidential: safe.confidential === \"Yes\" ? \"Yes\" : \"No\",\r\n      enableCatalystMatch: !!safe.enableCatalystMatch,\r\n    } as FormData;\r\n  };\r\n\r\n  const handleLoadTemplate = async () => {\r\n    if (!vacancyId.trim()) {\r\n      setTemplateMessage(\"Error: Please enter a vacancy ID\");\r\n      return;\r\n    }\r\n\r\n    setIsLoadingTemplate(true);\r\n    setTemplateMessage(\"\");\r\n\r\n    try {\r\n      const result = await getVacancyTemplate({ vacancy_id: vacancyId.trim() });\r\n\r\n      const success = !!result?.data?.success;\r\n      const msg = typeof result?.data?.message === \"string\" ? result.data.message : \"\";\r\n\r\n      if (success && 'data' in result.data && result.data.data) {\r\n        setFormData(normalizeTemplate(result.data.data));\r\n        setCurrentInputs({ requiredSkills: \"\", preferredSkills: \"\", additionalJobTitles: \"\", degrees: \"\", certificationsLicenses: \"\" });\r\n        setTemplateMessage(\"Template loaded successfully!\");\r\n      } else {\r\n        setTemplateMessage(`Error: ${msg || \"Failed to load template\"}`);\r\n      }\r\n    } catch (error) {\r\n      setTemplateMessage(\"Error: Failed to load template\");\r\n      console.error(\"Load template error:\", error);\r\n    } finally {\r\n      setIsLoadingTemplate(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setSubmitMessage(\"\");\r\n\r\n    // Validation for required fields\r\n    if (!formData.city.trim()) {\r\n      setSubmitMessage(\"Error: City is required\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!formData.state.trim()) {\r\n      setSubmitMessage(\"Error: State is required\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!formData.yearsExperience.trim()) {\r\n      setSubmitMessage(\"Error: Years of Experience is required\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Save template if vacancy ID is provided\r\n      if (vacancyId.trim()) {\r\n        const templateData = {\r\n          vacancy_id: vacancyId.trim(),\r\n          requiredSkills: formData.requiredSkills,\r\n          preferredSkills: formData.preferredSkills,\r\n          recencyMustHaveSkills: formData.recencyMustHaveSkills,\r\n          additionalJobTitles: formData.additionalJobTitles,\r\n          city: formData.city,\r\n          state: formData.state,\r\n          miles: formData.miles,\r\n          yearsExperience: formData.yearsExperience,\r\n          degrees: formData.degrees,\r\n          certificationsLicenses: formData.certificationsLicenses,\r\n          industry: formData.industry,\r\n          confidential: formData.confidential,\r\n          enableCatalystMatch: formData.enableCatalystMatch,\r\n        };\r\n\r\n        console.log(\"=== SAVE TEMPLATE DATA ===\");\r\n        console.log(\"Template Data:\", JSON.stringify(templateData, null, 2));\r\n        console.log(\"Save with Generate:\", saveWithGenerate);\r\n        console.log(\"==========================\");\r\n\r\n        const saveResult = await saveVacancyTemplate(templateData, saveWithGenerate);\r\n        console.log(\"Save Result:\", saveResult);\r\n        \r\n        const saveOk = !!saveResult?.data?.success;\r\n        const saveMsg = typeof saveResult?.data?.message === \"string\" ? saveResult.data.message : \"\";\r\n\r\n        if (!saveOk) {\r\n          setSubmitMessage(`Error saving template: ${saveMsg || \"Failed to save template\"}`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const formPayload = {\r\n        required_skills: formData.requiredSkills.join(', '),\r\n        preferred_skills: formData.preferredSkills.join(', '),\r\n        recency_must_have_skills: formData.recencyMustHaveSkills,\r\n        additional_job_titles: formData.additionalJobTitles.join(', '),\r\n        city: formData.city,\r\n        state: formData.state,\r\n        miles: formData.miles,\r\n        years_experience: formData.yearsExperience,\r\n        degrees: formData.degrees.join(', '),\r\n        certifications_licenses: formData.certificationsLicenses.join(', '),\r\n        industry: formData.industry,\r\n        confidential: formData.confidential,\r\n        enable_catalyst_match: formData.enableCatalystMatch,\r\n      };\r\n\r\n      const result = await submitCatalystMatchForm(formPayload);\r\n      const ok = !!result?.data?.success;\r\n      const msg = typeof result?.data?.message === \"string\" ? result.data.message : \"\";\r\n\r\n      const successMessage = vacancyId.trim() \r\n        ? `Template saved and form submitted successfully!`\r\n        : \"Form submitted successfully!\";\r\n      \r\n      setSubmitMessage(ok ? successMessage : `Error: ${msg || \"Failed to submit form\"}`);\r\n    } catch (error) {\r\n      setSubmitMessage(\"Error: Failed to submit form\");\r\n      console.error(\"Submit error:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleClear = () => {\r\n    setFormData({\r\n      requiredSkills: [],\r\n      preferredSkills: [],\r\n      recencyMustHaveSkills: \"Current +1\",\r\n      additionalJobTitles: [],\r\n      city: \"\",\r\n      state: \"\",\r\n      miles: \"50\",\r\n      yearsExperience: \"\",\r\n      degrees: [],\r\n      certificationsLicenses: [],\r\n      industry: \"No\",\r\n      confidential: \"No\",\r\n      enableCatalystMatch: false,\r\n    });\r\n    setCurrentInputs({\r\n      requiredSkills: \"\",\r\n      preferredSkills: \"\",\r\n      additionalJobTitles: \"\",\r\n      degrees: \"\",\r\n      certificationsLicenses: \"\",\r\n    });\r\n    setVacancyId(\"\");\r\n    setTemplateMessage(\"\");\r\n    setSubmitMessage(\"\");\r\n  };\r\n\r\n  const inputClasses = \"w-full px-1 py-0.5 text-xs border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors\";\r\n  const labelClasses = \"block text-xs font-medium text-gray-700 mb-0.5\";\r\n\r\n  return (\r\n    <div className=\"w-full max-w-4xl mx-auto\">\r\n      {/* Job Template Section */}\r\n      <div className=\"bg-white border border-gray-200 rounded-md p-2 sm:p-3\">\r\n        <div className=\"mb-1.5\">\r\n          <h3 className=\"text-sm font-medium text-gray-900 mb-0.5\">Vacancy Template</h3>\r\n        </div>\r\n        \r\n        <form onSubmit={handleSubmit} className=\"space-y-1.5\">\r\n          {/* Load Template Section */}\r\n          <div className=\"border-b border-gray-200 pb-2 mb-2\">\r\n            <div className=\"flex items-end gap-2\">\r\n              <div className=\"flex-1\">\r\n                <label htmlFor=\"vacancyId\" className={labelClasses}>\r\n                  Load Template from Vacancy ID:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"vacancyId\"\r\n                  value={vacancyId}\r\n                  onChange={(e) => setVacancyId(e.target.value)}\r\n                  className={inputClasses}\r\n                  placeholder=\"Enter vacancy ID (e.g., CR-123456)\"\r\n                />\r\n              </div>\r\n              <Button\r\n                type=\"button\"\r\n                onClick={handleLoadTemplate}\r\n                disabled={isLoadingTemplate || !vacancyId.trim()}\r\n                className=\"px-3 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {isLoadingTemplate ? (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"></div>\r\n                    Loading...\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Download className=\"h-3 w-3\" />\r\n                    Load Template\r\n                  </div>\r\n                )}\r\n              </Button>\r\n\r\n            </div>\r\n            {templateMessage && (\r\n              <div className={`mt-1 text-xs ${templateMessage.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>\r\n                {templateMessage}\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n\r\n          {/* Required Skills */}\r\n          <div>\r\n            <label htmlFor=\"requiredSkills\" className={labelClasses}>\r\n              Required Skills - (Required; optional for App, Nursing and Therapy, Clinical, Corporate Finance & Corporate Services):\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"requiredSkills\"\r\n              name=\"requiredSkills\"\r\n              value={currentInputs.requiredSkills}\r\n              onChange={(e) => handleTagInput('requiredSkills', e.target.value)}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter' && e.currentTarget.value.trim()) {\r\n                  const value = e.currentTarget.value.trim();\r\n                  setFormData(prev => ({\r\n                    ...prev,\r\n                    requiredSkills: prev.requiredSkills.includes(value)\r\n                      ? prev.requiredSkills\r\n                      : [...prev.requiredSkills, value]\r\n                  }));\r\n                  setCurrentInputs(prev => ({ ...prev, requiredSkills: \"\" }));\r\n                  e.preventDefault();\r\n                }\r\n              }}\r\n              className={inputClasses}\r\n              placeholder=\"e.g. Java, Phlebotomy (press Enter or comma to add)\"\r\n              maxLength={128}\r\n            />\r\n            {formData.requiredSkills.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                {formData.requiredSkills.map((skill, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md border border-blue-200\"\r\n                  >\r\n                    {skill}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => removeTag('requiredSkills', index)}\r\n                      className=\"text-blue-600 hover:text-blue-800 font-bold\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Preferred Skills */}\r\n          <div>\r\n            <label htmlFor=\"preferredSkills\" className={labelClasses}>\r\n              Preferred Skills (Optional):\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"preferredSkills\"\r\n              name=\"preferredSkills\"\r\n              value={currentInputs.preferredSkills}\r\n              onChange={(e) => handleTagInput('preferredSkills', e.target.value)}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter' && e.currentTarget.value.trim()) {\r\n                  const value = e.currentTarget.value.trim();\r\n                  setFormData(prev => ({\r\n                    ...prev,\r\n                    preferredSkills: prev.preferredSkills.includes(value)\r\n                      ? prev.preferredSkills\r\n                      : [...prev.preferredSkills, value]\r\n                  }));\r\n                  setCurrentInputs(prev => ({ ...prev, preferredSkills: \"\" }));\r\n                  e.preventDefault();\r\n                }\r\n              }}\r\n              className={inputClasses}\r\n              placeholder=\"e.g. JavaScript, speech therapy (press Enter or comma to add)\"\r\n              maxLength={128}\r\n            />\r\n            {formData.preferredSkills.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                {formData.preferredSkills.map((skill, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-md border border-green-200\"\r\n                  >\r\n                    {skill}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => removeTag('preferredSkills', index)}\r\n                      className=\"text-green-600 hover:text-green-800 font-bold\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Recency of Must-Have Skills */}\r\n          <div>\r\n            <label htmlFor=\"recencyMustHaveSkills\" className={labelClasses}>\r\n              Recency of Must-Have Skills:\r\n            </label>\r\n            <select\r\n              id=\"recencyMustHaveSkills\"\r\n              name=\"recencyMustHaveSkills\"\r\n              value={formData.recencyMustHaveSkills}\r\n              onChange={handleInputChange}\r\n              className={inputClasses}\r\n            >\r\n              <option value=\"Current +1\">Current +1</option>\r\n              <option value=\"Current\">Current</option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* Additional Job Titles */}\r\n          <div>\r\n            <label htmlFor=\"additionalJobTitles\" className={labelClasses}>\r\n              Additional Job Titles:\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"additionalJobTitles\"\r\n              name=\"additionalJobTitles\"\r\n              value={currentInputs.additionalJobTitles}\r\n              onChange={(e) => handleTagInput('additionalJobTitles', e.target.value)}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter' && e.currentTarget.value.trim()) {\r\n                  const value = e.currentTarget.value.trim();\r\n                  setFormData(prev => ({\r\n                    ...prev,\r\n                    additionalJobTitles: prev.additionalJobTitles.includes(value)\r\n                      ? prev.additionalJobTitles\r\n                      : [...prev.additionalJobTitles, value]\r\n                  }));\r\n                  setCurrentInputs(prev => ({ ...prev, additionalJobTitles: \"\" }));\r\n                  e.preventDefault();\r\n                }\r\n              }}\r\n              className={inputClasses}\r\n              placeholder=\"Business analysts, product specialist, product analyst, etc. (press Enter or comma to add)\"\r\n              maxLength={128}\r\n            />\r\n            {formData.additionalJobTitles.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                {formData.additionalJobTitles.map((title, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center gap-1 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-md border border-purple-200\"\r\n                  >\r\n                    {title}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => removeTag('additionalJobTitles', index)}\r\n                      className=\"text-purple-600 hover:text-purple-800 font-bold\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Location Fields */}\r\n          <div className=\"grid grid-cols-3 gap-1.5 sm:gap-2\">\r\n            {/* City */}\r\n            <div>\r\n              <label htmlFor=\"city\" className={labelClasses}>\r\n                City (Required):\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"city\"\r\n                name=\"city\"\r\n                value={formData.city}\r\n                onChange={handleInputChange}\r\n                className={inputClasses}\r\n                placeholder=\"Enter city...\"\r\n                list=\"cityOptions\"\r\n                required\r\n              />\r\n              <datalist id=\"cityOptions\">\r\n                {formData.state && citiesByState[formData.state] ? (\r\n                  citiesByState[formData.state].map((city) => (\r\n                    <option key={city} value={city} />\r\n                  ))\r\n                ) : null}\r\n              </datalist>\r\n            </div>\r\n\r\n            {/* State */}\r\n            <div>\r\n              <label htmlFor=\"state\" className={labelClasses}>\r\n                State (Required):\r\n              </label>\r\n              <select\r\n                id=\"state\"\r\n                name=\"state\"\r\n                value={formData.state}\r\n                onChange={handleInputChange}\r\n                className={inputClasses}\r\n                required\r\n              >\r\n                <option value=\"\">Select State</option>\r\n                <option value=\"AL\">Alabama</option>\r\n                <option value=\"AK\">Alaska</option>\r\n                <option value=\"AZ\">Arizona</option>\r\n                <option value=\"AR\">Arkansas</option>\r\n                <option value=\"CA\">California</option>\r\n                <option value=\"CO\">Colorado</option>\r\n                <option value=\"CT\">Connecticut</option>\r\n                <option value=\"DE\">Delaware</option>\r\n                <option value=\"FL\">Florida</option>\r\n                <option value=\"GA\">Georgia</option>\r\n                <option value=\"HI\">Hawaii</option>\r\n                <option value=\"ID\">Idaho</option>\r\n                <option value=\"IL\">Illinois</option>\r\n                <option value=\"IN\">Indiana</option>\r\n                <option value=\"IA\">Iowa</option>\r\n                <option value=\"KS\">Kansas</option>\r\n                <option value=\"KY\">Kentucky</option>\r\n                <option value=\"LA\">Louisiana</option>\r\n                <option value=\"ME\">Maine</option>\r\n                <option value=\"MD\">Maryland</option>\r\n                <option value=\"MA\">Massachusetts</option>\r\n                <option value=\"MI\">Michigan</option>\r\n                <option value=\"MN\">Minnesota</option>\r\n                <option value=\"MS\">Mississippi</option>\r\n                <option value=\"MO\">Missouri</option>\r\n                <option value=\"MT\">Montana</option>\r\n                <option value=\"NE\">Nebraska</option>\r\n                <option value=\"NV\">Nevada</option>\r\n                <option value=\"NH\">New Hampshire</option>\r\n                <option value=\"NJ\">New Jersey</option>\r\n                <option value=\"NM\">New Mexico</option>\r\n                <option value=\"NY\">New York</option>\r\n                <option value=\"NC\">North Carolina</option>\r\n                <option value=\"ND\">North Dakota</option>\r\n                <option value=\"OH\">Ohio</option>\r\n                <option value=\"OK\">Oklahoma</option>\r\n                <option value=\"OR\">Oregon</option>\r\n                <option value=\"PA\">Pennsylvania</option>\r\n                <option value=\"RI\">Rhode Island</option>\r\n                <option value=\"SC\">South Carolina</option>\r\n                <option value=\"SD\">South Dakota</option>\r\n                <option value=\"TN\">Tennessee</option>\r\n                <option value=\"TX\">Texas</option>\r\n                <option value=\"UT\">Utah</option>\r\n                <option value=\"VT\">Vermont</option>\r\n                <option value=\"VA\">Virginia</option>\r\n                <option value=\"WA\">Washington</option>\r\n                <option value=\"WV\">West Virginia</option>\r\n                <option value=\"WI\">Wisconsin</option>\r\n                <option value=\"WY\">Wyoming</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Miles */}\r\n            <div>\r\n              <label htmlFor=\"miles\" className={labelClasses}>\r\n                Miles:\r\n              </label>\r\n              <input\r\n                type=\"number\"\r\n                id=\"miles\"\r\n                name=\"miles\"\r\n                value={formData.miles}\r\n                onChange={handleInputChange}\r\n                className={inputClasses}\r\n                min=\"1\"\r\n                max=\"500\"\r\n                placeholder=\"50\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Years of Experience */}\r\n          <div>\r\n            <label htmlFor=\"yearsExperience\" className={labelClasses}>\r\n              Years of Experience (Required):\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"yearsExperience\"\r\n              name=\"yearsExperience\"\r\n              value={formData.yearsExperience}\r\n              onChange={handleInputChange}\r\n              className={inputClasses}\r\n              placeholder=\"Enter years of experience...\"\r\n              list=\"yearsExperienceOptions\"\r\n              required\r\n            />\r\n            <datalist id=\"yearsExperienceOptions\">\r\n              {Array.from({ length: 31 }, (_, i) => (\r\n                <option key={i} value={i} />\r\n              ))}\r\n            </datalist>\r\n          </div>\r\n\r\n          {/* Degrees */}\r\n          <div>\r\n            <label htmlFor=\"degrees\" className={labelClasses}>\r\n              Degrees (Required for App & Corporate Finance):\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"degrees\"\r\n              name=\"degrees\"\r\n              value={currentInputs.degrees}\r\n              onChange={(e) => handleTagInput('degrees', e.target.value)}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter' && e.currentTarget.value.trim()) {\r\n                  const value = e.currentTarget.value.trim();\r\n                  setFormData(prev => ({\r\n                    ...prev,\r\n                    degrees: prev.degrees.includes(value)\r\n                      ? prev.degrees\r\n                      : [...prev.degrees, value]\r\n                  }));\r\n                  setCurrentInputs(prev => ({ ...prev, degrees: \"\" }));\r\n                  e.preventDefault();\r\n                }\r\n              }}\r\n              className={inputClasses}\r\n              placeholder=\"List necessary degrees (press Enter or comma to add)\"\r\n              maxLength={128}\r\n            />\r\n            {formData.degrees.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                {formData.degrees.map((degree, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center gap-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-md border border-orange-200\"\r\n                  >\r\n                    {degree}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => removeTag('degrees', index)}\r\n                      className=\"text-orange-600 hover:text-orange-800 font-bold\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Certifications/Licenses */}\r\n          <div>\r\n            <label htmlFor=\"certificationsLicenses\" className={labelClasses}>\r\n              Certifications/Licenses (Optional):\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"certificationsLicenses\"\r\n              name=\"certificationsLicenses\"\r\n              value={currentInputs.certificationsLicenses}\r\n              onChange={(e) => handleTagInput('certificationsLicenses', e.target.value)}\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter' && e.currentTarget.value.trim()) {\r\n                  const value = e.currentTarget.value.trim();\r\n                  setFormData(prev => ({\r\n                    ...prev,\r\n                    certificationsLicenses: prev.certificationsLicenses.includes(value)\r\n                      ? prev.certificationsLicenses\r\n                      : [...prev.certificationsLicenses, value]\r\n                  }));\r\n                  setCurrentInputs(prev => ({ ...prev, certificationsLicenses: \"\" }));\r\n                  e.preventDefault();\r\n                }\r\n              }}\r\n              className={inputClasses}\r\n              placeholder=\"List necessary certifications (press Enter or comma to add)\"\r\n              maxLength={128}\r\n            />\r\n            {formData.certificationsLicenses.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                {formData.certificationsLicenses.map((cert, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center gap-1 px-2 py-1 text-xs bg-teal-100 text-teal-800 rounded-md border border-teal-200\"\r\n                  >\r\n                    {cert}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => removeTag('certificationsLicenses', index)}\r\n                      className=\"text-teal-600 hover:text-teal-800 font-bold\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Industry and Confidential */}\r\n          <div className=\"grid grid-cols-2 gap-1.5 sm:gap-2\">\r\n            {/* Industry */}\r\n            <div>\r\n              <label className={labelClasses}>\r\n                Industry:\r\n              </label>\r\n              <div className=\"flex items-center space-x-4 mt-1\">\r\n                <label className=\"flex items-center space-x-1\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"industry\"\r\n                    value=\"No\"\r\n                    checked={formData.industry === \"No\"}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500\"\r\n                  />\r\n                  <span className=\"text-xs text-gray-700\">No</span>\r\n                </label>\r\n                <label className=\"flex items-center space-x-1\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"industry\"\r\n                    value=\"Yes\"\r\n                    checked={formData.industry === \"Yes\"}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500\"\r\n                  />\r\n                  <span className=\"text-xs text-gray-700\">Yes</span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Confidential */}\r\n            <div>\r\n              <label className={labelClasses}>\r\n                Confidential:\r\n              </label>\r\n              <div className=\"flex items-center space-x-4 mt-1\">\r\n                <label className=\"flex items-center space-x-1\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"confidential\"\r\n                    value=\"No\"\r\n                    checked={formData.confidential === \"No\"}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500\"\r\n                  />\r\n                  <span className=\"text-xs text-gray-700\">No</span>\r\n                </label>\r\n                <label className=\"flex items-center space-x-1\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"confidential\"\r\n                    value=\"Yes\"\r\n                    checked={formData.confidential === \"Yes\"}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500\"\r\n                  />\r\n                  <span className=\"text-xs text-gray-700\">Yes</span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Enable Catalyst Match Toggle */}\r\n          <div className=\"border border-gray-200 bg-gray-50 rounded-sm p-1.5\">\r\n            <div className=\"flex items-center space-x-1.5\">\r\n              <input\r\n                type=\"checkbox\"\r\n                id=\"enableCatalystMatch\"\r\n                name=\"enableCatalystMatch\"\r\n                checked={formData.enableCatalystMatch}\r\n                onChange={handleInputChange}\r\n                className=\"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\r\n              />\r\n              <label htmlFor=\"enableCatalystMatch\" className=\"text-xs font-medium text-gray-700\">\r\n                Enable catalyst match\r\n              </label>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Submit Message */}\r\n          {submitMessage && (\r\n            <div className={`border rounded-sm p-1.5 ${submitMessage.startsWith(\"Error\") ? \"border-red-200 bg-red-50\" : \"border-green-200 bg-green-50\"}`}>\r\n              <div className=\"flex items-start space-x-1.5\">\r\n                <div className=\"flex-shrink-0 mt-0.5\">\r\n                  {submitMessage.startsWith(\"Error\") ? (\r\n                    <XCircle className=\"h-3 w-3 text-red-600\" />\r\n                  ) : (\r\n                    <CheckCircle className=\"h-3 w-3 text-green-600\" />\r\n                  )}\r\n                </div>\r\n                <div className={`text-xs ${submitMessage.startsWith(\"Error\") ? \"text-red-800\" : \"text-green-800\"} break-words`}>\r\n                  {submitMessage}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-1.5 pt-1.5\">\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              size=\"sm\"\r\n              className=\"flex items-center justify-center gap-0.5 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 w-full sm:w-auto text-xs py-1 px-2\"\r\n              onClick={() => setSaveWithGenerate(true)}\r\n            >\r\n              <Send className=\"h-2.5 w-2.5\" />\r\n              {isSubmitting ? \"Submitting...\" : \"Save with Generate\"}\r\n            </Button>\r\n            <Button\r\n              type=\"button\"\r\n              onClick={async () => {\r\n                setSaveWithGenerate(false);\r\n                const formEvent = new Event('submit', { bubbles: true, cancelable: true }) as any;\r\n                await handleSubmit(formEvent);\r\n              }}\r\n              disabled={isSubmitting}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"flex items-center justify-center gap-0.5 border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto text-xs py-1 px-2\"\r\n            >\r\n              <RotateCcw className=\"h-2.5 w-2.5\" />\r\n              {isSubmitting ? \"Saving...\" : \"Save without Generate\"}\r\n            </Button>\r\n            <Button\r\n              type=\"button\"\r\n              onClick={handleClear}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"flex items-center justify-center gap-0.5 border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto text-xs py-1 px-2\"\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CatalystMatchForm; "], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;AAsBA,MAAM,oBAA8B;;IAClC,4BAA4B;IAC5B,MAAM,gBAA6C;QACjD,MAAM;YAAC;YAAe;YAAiB;YAAa;YAAY;YAAc;YAAU;YAAW;YAAc;YAAe;SAAU;QAC1I,MAAM;YAAC;YAAiB;YAAW;YAAa;YAAW;YAAY;YAAU;YAAgB;YAAgB;YAAe;SAAQ;QACxI,MAAM;YAAC;YAAW;YAAe;YAAU;YAAU;YAAc;YAAW;YAAa;YAAkB;YAAS;SAAU;QAChI,MAAM;YAAC;YAAgB;YAAS;YAAS;YAAW;YAAkB;YAAW;YAAe;YAAmB;YAAkB;SAAa;QAClJ,MAAM;YAAC;YAAW;YAAU;YAAY;YAAU;YAAc;YAAe;YAAU;YAAS;YAAY;SAAY;QAC1H,MAAM;YAAC;YAAgB;YAAc;YAAa;YAAQ;YAAW;YAAY;YAAa;YAAa;YAAc;SAAU;QACnI,MAAM;YAAC;YAAY;YAAa;YAAc;YAAU;YAAS;YAAU;YAAS;YAAU;YAAc;SAAS;QACrH,MAAM;YAAC;YAAW;YAAW;YAAY;YAAS;YAAY;YAAU;YAAiB;YAAW;YAAU;SAAc;QAC5H,MAAM;YAAC;YAAa;YAAW;YAAc;YAAU;YAAiB;YAAgB;YAAQ;YAAc;YAAc;SAAa;QACzI,MAAM;YAAC;YAAW;YAAgB;YAAU;YAAoB;YAAW;YAAa;YAAS;YAAY;YAAW;SAAW;IACrI;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,gBAAgB,EAAE;QAClB,iBAAiB,EAAE;QACnB,uBAAuB;QACvB,qBAAqB,EAAE;QACvB,MAAM;QACN,OAAO;QACP,OAAO;QACP,iBAAiB;QACjB,SAAS,EAAE;QACX,wBAAwB,EAAE;QAC1B,UAAU;QACV,cAAc;QACd,qBAAqB;IACvB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB;QACrB,SAAS;QACT,wBAAwB;IAC1B;IAEA,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAEtC,gCAAgC;QAChC,IAAI,SAAS,SAAS;YACpB,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;oBACR,MAAM;gBACR,CAAC;QACH,OAAO;YACL,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;gBACzE,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAuB;QAC7C,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,YAAY,CAAC,OAAuB;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,AAAC,IAAI,CAAC,MAAM,CAAc,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC5D,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,yEAAyE;QACzE,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,OAAO,CAAC;QAE/C,iEAAiE;QACjE,MAAM,UAAU,KAAK,qBAAqB;QAC1C,MAAM,oBACJ,YAAY,gBAAgB,eAC1B,YAAY,eAAe,eAC3B,YAAY,YAAY,YACxB;QAEJ,MAAM,WACJ,OAAO,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,CAAC,WAAW,OAAO,QACjE,QACA;QAEN,OAAO;YACL,gBAAgB,MAAM,OAAO,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,GAAG,EAAE;YAC7E,iBAAiB,MAAM,OAAO,CAAC,KAAK,eAAe,IAAI,KAAK,eAAe,GAAG,EAAE;YAChF,uBAAuB;YACvB,qBAAqB,MAAM,OAAO,CAAC,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,GAAG,EAAE;YAC5F,MAAM,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG;YAClD,OAAO,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;YACrD,OAAO,OAAO,KAAK,KAAK,KAAK,YAAY,OAAO,KAAK,KAAK,KAAK,WAAW,OAAO,KAAK,KAAK,IAAI;YAC/F,iBAAiB,OAAO,KAAK,eAAe,KAAK,YAAY,OAAO,KAAK,eAAe,KAAK,WAAW,OAAO,KAAK,eAAe,IAAI;YACvI,SAAS,MAAM,OAAO,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,EAAE;YACxD,wBAAwB,MAAM,OAAO,CAAC,KAAK,sBAAsB,IAAI,KAAK,sBAAsB,GAAG,EAAE;YACrG;YACA,cAAc,KAAK,YAAY,KAAK,QAAQ,QAAQ;YACpD,qBAAqB,CAAC,CAAC,KAAK,mBAAmB;QACjD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,mBAAmB;YACnB;QACF;QAEA,qBAAqB;QACrB,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE,YAAY,UAAU,IAAI;YAAG;YAEvE,MAAM,UAAU,CAAC,CAAC,QAAQ,MAAM;YAChC,MAAM,MAAM,OAAO,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,CAAC,OAAO,GAAG;YAE9E,IAAI,WAAW,UAAU,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;gBACxD,YAAY,kBAAkB,OAAO,IAAI,CAAC,IAAI;gBAC9C,iBAAiB;oBAAE,gBAAgB;oBAAI,iBAAiB;oBAAI,qBAAqB;oBAAI,SAAS;oBAAI,wBAAwB;gBAAG;gBAC7H,mBAAmB;YACrB,OAAO;gBACL,mBAAmB,CAAC,OAAO,EAAE,OAAO,2BAA2B;YACjE;QACF,EAAE,OAAO,OAAO;YACd,mBAAmB;YACnB,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,qBAAqB;QACvB;IACF;IAIA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,iBAAiB;QAEjB,iCAAiC;QACjC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,iBAAiB;YACjB,gBAAgB;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,iBAAiB;YACjB,gBAAgB;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;YACpC,iBAAiB;YACjB,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,0CAA0C;YAC1C,IAAI,UAAU,IAAI,IAAI;gBACpB,MAAM,eAAe;oBACnB,YAAY,UAAU,IAAI;oBAC1B,gBAAgB,SAAS,cAAc;oBACvC,iBAAiB,SAAS,eAAe;oBACzC,uBAAuB,SAAS,qBAAqB;oBACrD,qBAAqB,SAAS,mBAAmB;oBACjD,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,iBAAiB,SAAS,eAAe;oBACzC,SAAS,SAAS,OAAO;oBACzB,wBAAwB,SAAS,sBAAsB;oBACvD,UAAU,SAAS,QAAQ;oBAC3B,cAAc,SAAS,YAAY;oBACnC,qBAAqB,SAAS,mBAAmB;gBACnD;gBAEA,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,cAAc,MAAM;gBACjE,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,QAAQ,GAAG,CAAC;gBAEZ,MAAM,aAAa,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;gBAC3D,QAAQ,GAAG,CAAC,gBAAgB;gBAE5B,MAAM,SAAS,CAAC,CAAC,YAAY,MAAM;gBACnC,MAAM,UAAU,OAAO,YAAY,MAAM,YAAY,WAAW,WAAW,IAAI,CAAC,OAAO,GAAG;gBAE1F,IAAI,CAAC,QAAQ;oBACX,iBAAiB,CAAC,uBAAuB,EAAE,WAAW,2BAA2B;oBACjF;gBACF;YACF;YAEA,MAAM,cAAc;gBAClB,iBAAiB,SAAS,cAAc,CAAC,IAAI,CAAC;gBAC9C,kBAAkB,SAAS,eAAe,CAAC,IAAI,CAAC;gBAChD,0BAA0B,SAAS,qBAAqB;gBACxD,uBAAuB,SAAS,mBAAmB,CAAC,IAAI,CAAC;gBACzD,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,kBAAkB,SAAS,eAAe;gBAC1C,SAAS,SAAS,OAAO,CAAC,IAAI,CAAC;gBAC/B,yBAAyB,SAAS,sBAAsB,CAAC,IAAI,CAAC;gBAC9D,UAAU,SAAS,QAAQ;gBAC3B,cAAc,SAAS,YAAY;gBACnC,uBAAuB,SAAS,mBAAmB;YACrD;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,MAAM,KAAK,CAAC,CAAC,QAAQ,MAAM;YAC3B,MAAM,MAAM,OAAO,QAAQ,MAAM,YAAY,WAAW,OAAO,IAAI,CAAC,OAAO,GAAG;YAE9E,MAAM,iBAAiB,UAAU,IAAI,KACjC,CAAC,+CAA+C,CAAC,GACjD;YAEJ,iBAAiB,KAAK,iBAAiB,CAAC,OAAO,EAAE,OAAO,yBAAyB;QACnF,EAAE,OAAO,OAAO;YACd,iBAAiB;YACjB,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YACV,gBAAgB,EAAE;YAClB,iBAAiB,EAAE;YACnB,uBAAuB;YACvB,qBAAqB,EAAE;YACvB,MAAM;YACN,OAAO;YACP,OAAO;YACP,iBAAiB;YACjB,SAAS,EAAE;YACX,wBAAwB,EAAE;YAC1B,UAAU;YACV,cAAc;YACd,qBAAqB;QACvB;QACA,iBAAiB;YACf,gBAAgB;YAChB,iBAAiB;YACjB,qBAAqB;YACrB,SAAS;YACT,wBAAwB;QAC1B;QACA,aAAa;QACb,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,eAAe;IACrB,MAAM,eAAe;IAErB,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;;;;;;8BAG3D,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAY,WAAW;8DAAc;;;;;;8DAGpD,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,WAAW;oDACX,aAAY;;;;;;;;;;;;sDAGhB,6LAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,UAAU,qBAAqB,CAAC,UAAU,IAAI;4CAC9C,WAAU;sDAET,kCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDAAkE;;;;;;qEAInF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;gCAOvC,iCACC,6LAAC;oCAAI,WAAW,CAAC,aAAa,EAAE,gBAAgB,QAAQ,CAAC,WAAW,iBAAiB,kBAAkB;8CACpG;;;;;;;;;;;;sCAOP,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAiB,WAAW;8CAAc;;;;;;8CAGzD,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,cAAc,cAAc;oCACnC,UAAU,CAAC,IAAM,eAAe,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCAChE,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;4CACrD,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;4CACxC,YAAY,CAAA,OAAQ,CAAC;oDACnB,GAAG,IAAI;oDACP,gBAAgB,KAAK,cAAc,CAAC,QAAQ,CAAC,SACzC,KAAK,cAAc,GACnB;2DAAI,KAAK,cAAc;wDAAE;qDAAM;gDACrC,CAAC;4CACD,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB;gDAAG,CAAC;4CACzD,EAAE,cAAc;wCAClB;oCACF;oCACA,WAAW;oCACX,aAAY;oCACZ,WAAW;;;;;;gCAEZ,SAAS,cAAc,CAAC,MAAM,GAAG,mBAChC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU,kBAAkB;oDAC3C,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAkB,WAAW;8CAAc;;;;;;8CAG1D,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,cAAc,eAAe;oCACpC,UAAU,CAAC,IAAM,eAAe,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;4CACrD,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;4CACxC,YAAY,CAAA,OAAQ,CAAC;oDACnB,GAAG,IAAI;oDACP,iBAAiB,KAAK,eAAe,CAAC,QAAQ,CAAC,SAC3C,KAAK,eAAe,GACpB;2DAAI,KAAK,eAAe;wDAAE;qDAAM;gDACtC,CAAC;4CACD,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,iBAAiB;gDAAG,CAAC;4CAC1D,EAAE,cAAc;wCAClB;oCACF;oCACA,WAAW;oCACX,aAAY;oCACZ,WAAW;;;;;;gCAEZ,SAAS,eAAe,CAAC,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,sBACpC,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU,mBAAmB;oDAC5C,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAwB,WAAW;8CAAc;;;;;;8CAGhE,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,qBAAqB;oCACrC,UAAU;oCACV,WAAW;;sDAEX,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAsB,WAAW;8CAAc;;;;;;8CAG9D,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,cAAc,mBAAmB;oCACxC,UAAU,CAAC,IAAM,eAAe,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACrE,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;4CACrD,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;4CACxC,YAAY,CAAA,OAAQ,CAAC;oDACnB,GAAG,IAAI;oDACP,qBAAqB,KAAK,mBAAmB,CAAC,QAAQ,CAAC,SACnD,KAAK,mBAAmB,GACxB;2DAAI,KAAK,mBAAmB;wDAAE;qDAAM;gDAC1C,CAAC;4CACD,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,qBAAqB;gDAAG,CAAC;4CAC9D,EAAE,cAAc;wCAClB;oCACF;oCACA,WAAW;oCACX,aAAY;oCACZ,WAAW;;;;;;gCAEZ,SAAS,mBAAmB,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxC,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU,uBAAuB;oDAChD,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAW;sDAAc;;;;;;sDAG/C,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAW;4CACX,aAAY;4CACZ,MAAK;4CACL,QAAQ;;;;;;sDAEV,6LAAC;4CAAS,IAAG;sDACV,SAAS,KAAK,IAAI,aAAa,CAAC,SAAS,KAAK,CAAC,GAC9C,aAAa,CAAC,SAAS,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,qBACjC,6LAAC;oDAAkB,OAAO;mDAAb;;;;4DAEb;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAW;sDAAc;;;;;;sDAGhD,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW;4CACX,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;8CAKvB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAW;sDAAc;;;;;;sDAGhD,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW;4CACX,KAAI;4CACJ,KAAI;4CACJ,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAkB,WAAW;8CAAc;;;;;;8CAG1D,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU;oCACV,WAAW;oCACX,aAAY;oCACZ,MAAK;oCACL,QAAQ;;;;;;8CAEV,6LAAC;oCAAS,IAAG;8CACV,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC;4CAAe,OAAO;2CAAV;;;;;;;;;;;;;;;;sCAMnB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAU,WAAW;8CAAc;;;;;;8CAGlD,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,cAAc,OAAO;oCAC5B,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;4CACrD,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;4CACxC,YAAY,CAAA,OAAQ,CAAC;oDACnB,GAAG,IAAI;oDACP,SAAS,KAAK,OAAO,CAAC,QAAQ,CAAC,SAC3B,KAAK,OAAO,GACZ;2DAAI,KAAK,OAAO;wDAAE;qDAAM;gDAC9B,CAAC;4CACD,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS;gDAAG,CAAC;4CAClD,EAAE,cAAc;wCAClB;oCACF;oCACA,WAAW;oCACX,aAAY;oCACZ,WAAW;;;;;;gCAEZ,SAAS,OAAO,CAAC,MAAM,GAAG,mBACzB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU,WAAW;oDACpC,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAyB,WAAW;8CAAc;;;;;;8CAGjE,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,cAAc,sBAAsB;oCAC3C,UAAU,CAAC,IAAM,eAAe,0BAA0B,EAAE,MAAM,CAAC,KAAK;oCACxE,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;4CACrD,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;4CACxC,YAAY,CAAA,OAAQ,CAAC;oDACnB,GAAG,IAAI;oDACP,wBAAwB,KAAK,sBAAsB,CAAC,QAAQ,CAAC,SACzD,KAAK,sBAAsB,GAC3B;2DAAI,KAAK,sBAAsB;wDAAE;qDAAM;gDAC7C,CAAC;4CACD,iBAAiB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,wBAAwB;gDAAG,CAAC;4CACjE,EAAE,cAAc;wCAClB;oCACF;oCACA,WAAW;oCACX,aAAY;oCACZ,WAAW;;;;;;gCAEZ,SAAS,sBAAsB,CAAC,MAAM,GAAG,mBACxC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,sBAAsB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1C,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU,0BAA0B;oDACnD,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAW;sDAAc;;;;;;sDAGhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,QAAQ,KAAK;4DAC/B,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,QAAQ,KAAK;4DAC/B,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM9C,6LAAC;;sDACC,6LAAC;4CAAM,WAAW;sDAAc;;;;;;sDAGhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,YAAY,KAAK;4DACnC,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,YAAY,KAAK;4DACnC,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,SAAS,SAAS,mBAAmB;wCACrC,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAsB,WAAU;kDAAoC;;;;;;;;;;;;;;;;;wBAOtF,+BACC,6LAAC;4BAAI,WAAW,CAAC,wBAAwB,EAAE,cAAc,UAAU,CAAC,WAAW,6BAA6B,gCAAgC;sCAC1I,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,cAAc,UAAU,CAAC,yBACxB,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAG3B,6LAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,cAAc,UAAU,CAAC,WAAW,iBAAiB,iBAAiB,YAAY,CAAC;kDAC3G;;;;;;;;;;;;;;;;;sCAOT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;;sDAEnC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,eAAe,kBAAkB;;;;;;;8CAEpC,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,oBAAoB;wCACpB,MAAM,YAAY,IAAI,MAAM,UAAU;4CAAE,SAAS;4CAAM,YAAY;wCAAK;wCACxE,MAAM,aAAa;oCACrB;oCACA,UAAU;oCACV,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB,eAAe,cAAc;;;;;;;8CAEhC,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAh2BM;KAAA;uCAk2BS"}}, {"offset": {"line": 8581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8587, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/Candidates.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useMemo, useRef, useState } from \"react\";\r\nimport {\r\n  ArrowUpDown,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  FilterIcon,\r\n  LockKeyhole,\r\n  RefreshCcw,\r\n} from \"lucide-react\";\r\nimport { Candidate, ResumeData, Vacancy } from \"../../app/candidates/helper\";\r\nimport Loading from \"@/components/Loading\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport VacancyItem from \"@/components/candidates/VacancyItem\";\r\nimport CandidateResume from \"@/components/candidates/CandidateResume\";\r\nimport CandidateTable from \"../CandidateTable/CandidateTable\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { Button } from \"../ui/button\";\r\nimport RegenerateButton from \"./RegenerateButton\";\r\nimport { Tabs, TabsList, TabsTrigger, TabsContent } from \"@/components/ui/tabs\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport VacancyDetails from \"./VacancyDetails\";\r\nimport { Badge } from \"../ui/badge\";\r\nimport { fetchEntitlements } from \"@/api/serverActions\";\r\nimport { NotificationProvider } from \"@/hooks/useNotification\";\r\nimport NotificationList from \"../notifications/NotificationList\";\r\nimport { initAppInsights } from \"@/library/appInsights\";\r\nimport { useEntitlement } from \"@/context/EntitlementContext\";\r\nimport { useVacancyStatus } from \"@/hooks/useVacancyStatus\";\r\nimport { CatalystMatchStatus } from \"@/types/vacancy_status_api\";\r\nimport { updateLocalStoredVacancyTimeStamp } from \"@/utils/updatelocalStoredVacancyTimeStamp\";\r\nimport { UPDATE_TIMESTAMPS_STATUS } from \"@/types/vacancy_status_api\";\r\nimport {\r\n  AI_AGENTS_RESPONSE_STATUS,\r\n  VACANCY_FILTER_LABELS,\r\n  VACANCY_FILTER_OTHER_LABELS,\r\n} from \"@/library/utils\";\r\nimport { useDebounce } from \"@/hooks/useDebounce\";\r\nimport type { iFilterSlider } from \"./filterMenu\";\r\nimport {\r\n  defaultFilter,\r\n  useDefaultFilterStore,\r\n  useMercuryFilterStore,\r\n  type ArrayFilterKeys,\r\n  type VacancyFilter,\r\n} from \"@/store/vacancyFilterStore\";\r\nimport { capitalizeEachWord } from \"@/utils/utils\";\r\nimport { extractLocations } from \"./helper\";\r\nimport CatalystMatchForm from \"../CatalystMatchForm\";\r\n\r\nconst DEFAULT_PER_PAGE = 150;\r\nconst SHOW_SEARCH_BY_EMAIL = false;\r\n\r\nconst Candidates = ({\r\n  vacancyid,\r\n  mercuryPortal,\r\n  emailId,\r\n  entitlementData,\r\n}: {\r\n  vacancyid?: string;\r\n  mercuryPortal?: boolean;\r\n  emailId?: string;\r\n  entitlementData?: { [key: string]: boolean };\r\n}) => {\r\n  const isFetchCandidatesRunningRef = useRef(false);\r\n  const prevVacancyRef = useRef<string | null>(null);\r\n\r\n  const storeRef = useMemo(\r\n    () => (mercuryPortal ? useMercuryFilterStore : useDefaultFilterStore),\r\n    [mercuryPortal]\r\n  );\r\n\r\n  const useFilterStore = mercuryPortal\r\n    ? useMercuryFilterStore\r\n    : useDefaultFilterStore;\r\n\r\n  const pathname = usePathname();\r\n  const pageNotFound =\r\n    vacancyid === undefined &&\r\n    !pathname.includes(`/CandidateTuning/For_Mercury_Portal?vacancyid=`);\r\n  const session = useSession();\r\n  const name = session?.data?.user?.name;\r\n  // Entitlement hook\r\n  const { entitlements } = useEntitlement();\r\n\r\n  // vacancy table filter entitlement\r\n  const filterEntitlement =\r\n    (entitlements?.Filter_Feature || entitlementData?.Filter_Feature) ?? false;\r\n  // mercury disable\r\n  const isMercuryReadOnly = false;\r\n\r\n  const uniqueShortList = [\"Yes\", \"No\"];\r\n  const uniqueReviewValues = [\"ThumbsUp\", \"ThumbsDown\", \"NoReview\"];\r\n  const uniqueAiAgentStatus = [\r\n    \"Responded\",\r\n    \"Contacted\",\r\n    \"Not Contacted (Blank)\",\r\n    \"Not interested\",\r\n  ]?.sort((a, b) => a.localeCompare(b));\r\n  const responded = \"responded\";\r\n  const contacted = \"contacted\";\r\n  const notInterested = \"not interested\";\r\n\r\n  const [vacancies, setVacancies] = useState<Vacancy[]>([]);\r\n  const [vacancyCandidates, setVacancyCandidates] = useState<Record<\r\n    string,\r\n    Candidate[]\r\n  > | null>({});\r\n  const [selectedResume, setSelectedResume] = useState<ResumeData | null>(null);\r\n  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);\r\n  const [selectedVacancy, setSelectedVacancy] = useState<Vacancy | null>(null);\r\n  const [candidates, setCandidates] = useState<Candidate[]>([]);\r\n  const [filteredCandidatesList, setFilteredCandidatesList] = useState<\r\n    Candidate[]\r\n  >([]);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [tableLoader, setTableLoader] = useState<boolean>(\r\n    mercuryPortal ?? false\r\n  );\r\n  const [page, setPage] = useState<number>(1);\r\n  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE);\r\n  const [search, setSearch] = useState<string>(\"\");\r\n  const [vacancySearch, setVacancySearch] = useState<string>(\"\");\r\n  const [activeVacancy, setActiveVacancy] = useState<Vacancy | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const [preventInitiallApiCall, setPreventInitiallApiCall] =\r\n    useState<boolean>(false);\r\n  const [isParsedVacancyPopupOpen, setIsParsedVacancyPopupOpen] =\r\n    useState<boolean>(false);\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string | null;\r\n    direction: \"asc\" | \"desc\" | null;\r\n  }>({\r\n    key: null,\r\n    direction: null,\r\n  });\r\n  const [statusVacancyId, setStatusVacancyId] = useState<string>(\"\");\r\n  const [updatedStatusCompleted, setUpdatedStatusCompleted] =\r\n    useState<boolean>(false);\r\n  const [canRegenerate, setCanRegenerate] = useState(true);\r\n  // set data from catalyst match status api\r\n  const [catalystRegenerationData, setCatalystRegenerationData] = useState<\r\n    CatalystMatchStatus | undefined\r\n  >();\r\n  const [showUpdate, setShowUpdate] = useState(false);\r\n  const [isRefreshButtonClick, setIsRefreshButtonClick] = useState(false);\r\n  const [sortConfigCan, setSortConfigCan] = useState<{\r\n    key: string | null;\r\n    direction: \"asc\" | \"desc\" | null;\r\n  }>({\r\n    key: null,\r\n    direction: null,\r\n  });\r\n  const [showVacancyList, setShowVacancyList] = useState<boolean>(true);\r\n  const [searchText, setSearchText] = useState<string>(\"\");\r\n  const [showFilter, setShowFilter] = useState(true);\r\n  const [activeTab, setActiveTab] = useState<string>(\"vacancy-template\");\r\n  // Distance filter state is required for filtering candidates by distance Range bar default values\r\n  const [distance, setDistance] = useState<[number, number]>([0, 0]);\r\n  // Availability date range state for calendar years dropdown\r\n  const [maxMinAvailabilityDateRange, setMaxMinAvailabilityDateRange] =\r\n    useState<{\r\n      from: string;\r\n      to: string;\r\n    }>({ from: \"\", to: \"\" });\r\n\r\n  // zustand store values\r\n  const filters = useFilterStore((state) => state?.filters);\r\n  const setFilterField = useFilterStore((state) => state?.setFilterField);\r\n  const toggleSearchField = useFilterStore((state) => state?.toggleSearchField);\r\n  const handleCheckBoxSelection = useFilterStore(\r\n    (state) => state?.handleCheckBoxSelection\r\n  );\r\n  const resetFilter = useFilterStore((state) => state?.resetFilter);\r\n  const clearFiltersForVacancyList = useFilterStore(\r\n    (state) => state?.clearFiltersForVacancy\r\n  );\r\n  const distanceRange = useFilterStore(\r\n    (state) => state.filters[VACANCY_FILTER_LABELS.DISTANCE_RANGE]\r\n  );\r\n  const setDistanceRange = useFilterStore((state) => state.setDistanceRange);\r\n\r\n  // Debounce for filter input search filed\r\n  const debouncedValue = useDebounce(searchText, 300);\r\n\r\n  const showMercuryFilter =\r\n    filterEntitlement && showFilter && candidates?.length > 0;\r\n\r\n  const {\r\n    [VACANCY_FILTER_LABELS.STATE]: state,\r\n    [VACANCY_FILTER_LABELS.CITY]: city,\r\n    [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: freshnessIndex,\r\n    [VACANCY_FILTER_LABELS.SHORT_LISTED]: shortListed,\r\n    [VACANCY_FILTER_LABELS.REVIEW_DECISION]: reviewStatus,\r\n    [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: aiAgentStatus,\r\n    [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: searchByName,\r\n    [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: totalScoreRange,\r\n    [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: availabilityDateRange,\r\n    [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: searchFields,\r\n  } = filters;\r\n\r\n  // Set initial distance and availability year ranges\r\n  useEffect(() => {\r\n    const initialDistances =\r\n      candidates\r\n        ?.map((c) => c?.candidate_data?.distance_from_work_site)\r\n        .filter((d): d is number => d !== null && d !== undefined) || [];\r\n\r\n    const minMaxYears = candidates?.map((c) => {\r\n      const year = new Date(c?.candidate_data?.availability).getFullYear();\r\n      return year >= 2020 && year <= 2030 ? year : null;\r\n    });\r\n\r\n    const minYear = Math.min(\r\n      ...(minMaxYears.filter((y): y is number => y !== null) || [])\r\n    );\r\n    const maxYear = Math.max(\r\n      ...(minMaxYears.filter((y): y is number => y !== null) || [])\r\n    );\r\n\r\n    if (minYear && maxYear) {\r\n      setMaxMinAvailabilityDateRange({\r\n        from: String(minYear),\r\n        to: String(maxYear),\r\n      });\r\n    }\r\n\r\n    const minDistance =\r\n      initialDistances?.length > 0 ? Math.min(...initialDistances) : 0;\r\n    const maxDistance =\r\n      initialDistances?.length > 0 ? Math.max(...initialDistances) : 0;\r\n\r\n    if (initialDistances?.length > 0) {\r\n      setDistance([minDistance, maxDistance]);\r\n      setDistanceRange([minDistance, maxDistance]);\r\n    }\r\n  }, [candidates, setDistanceRange]);\r\n\r\n  const isFunnelFill =\r\n    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.STATE]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.CITY]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.length > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[0] > 0 ||\r\n    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[1] < 1 ||\r\n    (filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length > 0 &&\r\n      filters?.[VACANCY_FILTER_LABELS.SEARCH_BY_NAME]?.includes(\r\n        searchText.trim()\r\n      )) || // adapt if searchText replaced\r\n    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from.trim() !==\r\n      \"\" ||\r\n    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to.trim() !==\r\n      \"\" ||\r\n    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[0] > distance?.[0] ||\r\n    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[1] < distance?.[1] ||\r\n    false;\r\n\r\n  const filterIcon = () => (\r\n    <FilterIcon\r\n      fill={isFunnelFill ? \"#2a70ea\" : \"white\"}\r\n      color={isFunnelFill ? \"#2a70ea\" : \"black\"}\r\n      className=\"hover:cursor-pointer\"\r\n      onClick={handleFilterIcon}\r\n    />\r\n  );\r\n\r\n  let showSearchFieldError =\r\n    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS]?.length === 0;\r\n\r\n  useEffect(() => {\r\n    initAppInsights();\r\n  }, []);\r\n\r\n  const setRegenerationReadOnly = (value: boolean) => {\r\n    setCanRegenerate(!value);\r\n  };\r\n\r\n  const handleReloadTable = () => {\r\n    const vacancy_id = vacancyid ?? selectedVacancy?.vacancy_id;\r\n    if (vacancy_id) {\r\n      fetchCandidatesById(vacancy_id);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (emailId) {\r\n      try {\r\n        const setEntitlement = async () => {\r\n          await fetchEntitlements(emailId);\r\n        };\r\n        setEntitlement();\r\n      } catch (error) {\r\n        console.error(\"Error fetching entitlements::\", error);\r\n      }\r\n      localStorage.setItem(\"emailId\", emailId);\r\n    }\r\n  }, [emailId]);\r\n\r\n  const openParsedVacancyPopup = () => {\r\n    setActiveVacancy(selectedVacancy);\r\n    setIsParsedVacancyPopupOpen(true);\r\n  };\r\n\r\n  const fetchVacancies = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await trackedFetch(\r\n        \"/api/vacancies\",\r\n        {},\r\n        { context: \"GetVacancies\" }\r\n      );\r\n      if (response.ok) {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        const data: { vacancies: Vacancy[] } = await response.json();\r\n        if (data?.vacancies) {\r\n          setVacancies(Array.isArray(data?.vacancies) ? data.vacancies : []);\r\n        }\r\n        getAppInsights()?.trackEvent({\r\n          name: mercuryPortal\r\n            ? \"FE_MercuryVacanciesFetched\"\r\n            : \"FE_VacanciesFetched\",\r\n          properties: {\r\n            count: data?.vacancies?.length ?? 0,\r\n            context: mercuryPortal ? \"MercuryGetVacancies\" : \"GetVacancies\",\r\n          },\r\n        });\r\n        return data?.vacancies;\r\n      } else {\r\n        console.error(\"Failed to fetch attributes:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"Mercury vacancies api with error is \" + error\r\n            : \"vacancies api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchCandidatesById = async (vacancyid: string) => {\r\n    if (isFetchCandidatesRunningRef.current) {\r\n      // Skip if a fetch call is already running\r\n      return;\r\n    }\r\n    isFetchCandidatesRunningRef.current = true;\r\n    setTableLoader(true);\r\n    try {\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${vacancyid?.split(\"/\").join(\"-\")}`,\r\n        {},\r\n        {\r\n          context: \"GetCandidatesById\",\r\n        }\r\n      );\r\n      if (response.ok) {\r\n        const data: { candidates: Candidate[] } = await response.json();\r\n        setCandidates(Array.isArray(data?.candidates) ? data?.candidates : []);\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_CandidatesFetchedById\",\r\n          properties: {\r\n            vacancyid,\r\n            context: mercuryPortal\r\n              ? \"MercuryGetCandidatesByVacancyId\"\r\n              : \"GetCandidatesByVacancyId\",\r\n          },\r\n        });\r\n        if (isRefreshButtonClick) {\r\n          updateLocalStoredVacancyTimeStamp(\r\n            (vacancyid ?? \"\") || (selectedVacancy?.vacancy_id ?? \"\"),\r\n            mercuryPortal ?? false\r\n          );\r\n        }\r\n      } else {\r\n        console.error(\"Failed to fetch attributes:\", response?.statusText);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching skills:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"Mercury CandidatesById api with error is \" + error\r\n            : \"CandidatesById api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setTableLoader(false);\r\n      setLoading(false);\r\n      setUpdatedStatusCompleted(false);\r\n      isFetchCandidatesRunningRef.current = false; // reset flag on finish\r\n    }\r\n  };\r\n\r\n  const isEmptyObject = (\r\n    obj: Record<string, unknown> | null | undefined\r\n  ): boolean =>\r\n    !!obj && Object.keys(obj)?.length === 0 && obj?.constructor === Object;\r\n\r\n  const showEntitlementCatalystMatchStatus =\r\n    !entitlements || isEmptyObject(entitlements)\r\n      ? entitlementData\r\n      : entitlements;\r\n\r\n  const statusApiCall =\r\n    showEntitlementCatalystMatchStatus?.Update_Availability ?? false;\r\n  const dependencyValue = mercuryPortal\r\n    ? vacancyid ?? \"\"\r\n    : selectedVacancy?.vacancy_id;\r\n\r\n  const {\r\n    isVacancyApiCalled,\r\n    catalyst_match_status,\r\n    update_timestamps,\r\n    isStatusInProcess,\r\n    statusCode,\r\n    statusError,\r\n    setStatusData,\r\n    setIsStatusInProcess,\r\n    setIsVacancyApiCalled,\r\n  } = useVacancyStatus(\r\n    statusApiCall ?? false,\r\n    statusVacancyId,\r\n    new Date().toISOString(),\r\n    [dependencyValue]\r\n  );\r\n\r\n  let isReadOnly = isVacancyApiCalled && isStatusInProcess;\r\n\r\n  const fetchSingleVacancyByVacancyId = async (vacancyid: string) => {\r\n    try {\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${vacancyid}/single-vacancy`,\r\n        {},\r\n        { context: \"GetSingleVacancyById\" }\r\n      );\r\n      if (response.ok) {\r\n        const data: { vacancy: Vacancy | null } = await response.json();\r\n        setSelectedVacancy(data?.vacancy || null);\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_SingleVacancyFetched\",\r\n          properties: {\r\n            vacancyid,\r\n            context: mercuryPortal\r\n              ? \"MercuryGetSingleVacancyById\"\r\n              : \"GetSingleVacancyById\",\r\n          },\r\n        });\r\n        if (data?.vacancy === null) {\r\n          setTableLoader(false);\r\n        }\r\n      } else {\r\n        console.error(\"Failed to fetch vacancy:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching single vacancy:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"Mercury SingleVacancy api with error is \" + error\r\n            : \"SingleVacancy api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    setCatalystRegenerationData(catalyst_match_status);\r\n  }, [catalyst_match_status]);\r\n\r\n  useEffect(() => {\r\n    if (mercuryPortal && vacancyid && selectedVacancy === null) {\r\n      // Call fetch single Vacany only if selectedVacancy is  null\r\n      fetchSingleVacancyByVacancyId(vacancyid);\r\n    } else if (!mercuryPortal) {\r\n      vacancies?.length === 0 &&\r\n        fetchVacancies().then((vacancies) => {\r\n          if (vacancies && vacancies.length > 0) {\r\n            vacancies?.map((vacancy: any) => {\r\n              if (\r\n                vacancy?.vacancy_id?.toLowerCase() === vacancyid?.toLowerCase()\r\n              ) {\r\n                setSelectedVacancy(vacancy);\r\n              }\r\n            });\r\n          }\r\n        });\r\n    }\r\n    if (\r\n      (isVacancyApiCalled || !statusApiCall) &&\r\n      selectedVacancy !== null &&\r\n      isStatusInProcess !== null &&\r\n      statusApiCall\r\n    ) {\r\n      fetchCandidatesById(selectedVacancy.vacancy_id);\r\n      if (\r\n        !isStatusInProcess &&\r\n        isStatusInProcess !== null &&\r\n        preventInitiallApiCall\r\n      ) {\r\n        fetchSingleVacancyByVacancyId(selectedVacancy.vacancy_id);\r\n        setPreventInitiallApiCall(true); // By making as true next time onwards it will call the API\r\n      }\r\n    } else if (selectedVacancy?.vacancy_id?.length && !mercuryPortal) {\r\n      setStatusVacancyId(selectedVacancy.vacancy_id);\r\n    } else {\r\n      setStatusVacancyId(vacancyid ?? \"\");\r\n    }\r\n  }, [\r\n    vacancyid,\r\n    isVacancyApiCalled,\r\n    selectedVacancy?.vacancy_id,\r\n    statusApiCall,\r\n    mercuryPortal,\r\n  ]);\r\n  // Call fetchCandidatesById when there's a statusError or unsuccessful status code (not 200)\r\n  useEffect(() => {\r\n    if (\r\n      ((statusError && statusError.length > 0) ||\r\n        (statusCode !== 200 && statusCode !== null)) &&\r\n      selectedVacancy !== null\r\n    ) {\r\n      fetchCandidatesById(selectedVacancy.vacancy_id);\r\n      if (mercuryPortal && vacancyid && selectedVacancy === null) {\r\n        fetchSingleVacancyByVacancyId(vacancyid);\r\n      }\r\n    }\r\n  }, [statusError, statusCode, selectedVacancy]);\r\n\r\n  useEffect(() => {\r\n    // Note: This will run only if entitlement is disabled or not available\r\n    if (!statusApiCall && selectedVacancy && !isVacancyApiCalled) {\r\n      fetchCandidatesById(selectedVacancy?.vacancy_id);\r\n    }\r\n  }, [selectedVacancy]);\r\n\r\n  useEffect(() => {\r\n    const handleUnload = () => {\r\n      const currentVacancyId =\r\n        selectedVacancy?.vacancy_id?.toLowerCase() ?? vacancyid?.toLowerCase();\r\n      if (currentVacancyId) {\r\n        storeRef.getState().saveCurrentFiltersForVacancy(currentVacancyId);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"beforeunload\", handleUnload);\r\n    return () => {\r\n      window.removeEventListener(\"beforeunload\", handleUnload);\r\n    };\r\n  }, [selectedVacancy?.vacancy_id, vacancyid, storeRef]);\r\n\r\n  useEffect(() => {\r\n    const currentVacancyId =\r\n      selectedVacancy?.vacancy_id?.toLowerCase() ?? vacancyid?.toLowerCase();\r\n\r\n    if (!currentVacancyId) return;\r\n\r\n    const {\r\n      saveCurrentFiltersForVacancy,\r\n      loadFiltersForVacancy,\r\n      setFilterField,\r\n      resetFilter,\r\n    } = storeRef.getState(); // Save existing filters before changing to the new one\r\n\r\n    if (\r\n      prevVacancyRef?.current &&\r\n      prevVacancyRef?.current !== currentVacancyId\r\n    ) {\r\n      saveCurrentFiltersForVacancy(prevVacancyRef?.current);\r\n    } // Load filters for the new vacancy if available\r\n\r\n    const loaded = loadFiltersForVacancy(currentVacancyId);\r\n    if (loaded) {\r\n      const keys = Object.keys(defaultFilter) as (keyof VacancyFilter)[];\r\n      keys.length !== 0 &&\r\n        keys?.forEach((key) => {\r\n          if (loaded[key] !== undefined) {\r\n            setFilterField(key, loaded[key]);\r\n          }\r\n        });\r\n    } else {\r\n      resetFilter(distanceRange?.[1], 1); // Start fresh\r\n    }\r\n\r\n    prevVacancyRef.current = currentVacancyId;\r\n  }, [selectedVacancy?.vacancy_id, vacancyid, storeRef]);\r\n\r\n  const handleSort = (key: string = \"refno\") => {\r\n    let direction: \"asc\" | \"desc\" | null = \"asc\";\r\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\r\n      direction = \"desc\";\r\n    }\r\n\r\n    const sortedData = [...vacancies]?.sort((a, b) => {\r\n      if (a?.refno < b?.refno) return direction === \"asc\" ? -1 : 1;\r\n      if (a?.refno > b?.refno) return direction === \"asc\" ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    setSortConfig({ key, direction });\r\n    setVacancies(sortedData);\r\n  };\r\n  const toNumber = (value: unknown, fallback = null): number | null => {\r\n    if (\r\n      value === null ||\r\n      value === undefined ||\r\n      value === VACANCY_FILTER_OTHER_LABELS.MISSING ||\r\n      value === \"\"\r\n    )\r\n      return null;\r\n\r\n    const parsed = parseFloat(value as string);\r\n    return isNaN(parsed) ? fallback : parsed;\r\n  };\r\n\r\n  const handleCandidateSort = (key: string) => {\r\n    let direction: \"asc\" | \"desc\" = \"asc\";\r\n    if (sortConfigCan?.key === key && sortConfigCan?.direction === \"asc\") {\r\n      direction = \"desc\";\r\n    }\r\n\r\n    const getValue = (candidate: Candidate): string | number | Date | null => {\r\n      const data = candidate?.candidate_data;\r\n\r\n      const classificationScore = data[\"classification score\"] ?? {};\r\n      switch (key) {\r\n        case VACANCY_FILTER_OTHER_LABELS.NAME:\r\n          return data.name?.toLowerCase() || \"\";\r\n        case \"overallscore\":\r\n          return classificationScore?.overallscore ?? 0;\r\n        case \"jobtitlescore\":\r\n          return classificationScore?.jobtitlescore ?? 0;\r\n        case \"softskillsscore\":\r\n          return classificationScore?.softskillsscore ?? 0;\r\n        case \"technicalskills\":\r\n          return classificationScore?.[\"technical skills\"] ?? 0;\r\n        case \"toolsplatformsscore\":\r\n          return classificationScore?.toolsplatformsscore ?? 0;\r\n        case \"degreesandcertifications\":\r\n          return classificationScore?.[\"degrees and certifications\"] ?? 0;\r\n        case \"industryexperiencescore\":\r\n          return classificationScore?.industryexperiencescore ?? 0;\r\n        case \"relevantexperiencescore\":\r\n          return classificationScore?.relevantexperiencescore ?? 0;\r\n        case \"jobtitle_recency_score\":\r\n          return classificationScore?.jobtitle_recency_score ?? 0;\r\n        case \"availability\": {\r\n          const availability = data?.availability;\r\n          // Treat null, empty, or \"Missing\" as missing value\r\n          if (\r\n            !availability ||\r\n            availability === VACANCY_FILTER_OTHER_LABELS.MISSING\r\n          )\r\n            return null;\r\n          const date = new Date(availability);\r\n          // Check for invalid date\r\n          if (isNaN(date.getTime())) return null;\r\n\r\n          return data?.availability;\r\n        }\r\n        case VACANCY_FILTER_OTHER_LABELS.CITY:\r\n          const { city, state } = data;\r\n          const isValid = (v: string | null | undefined) =>\r\n            v && v !== VACANCY_FILTER_OTHER_LABELS.MISSING;\r\n          if (isValid(city) && isValid(state))\r\n            return `${city}, ${state}`.toLowerCase();\r\n          if (isValid(city)) return city.toLowerCase();\r\n          if (isValid(state)) return state.toLowerCase();\r\n          return null;\r\n        case \"freshness\":\r\n          return classificationScore?.jobtitle_recency_score ?? 0;\r\n        case \"freshness_index\":\r\n          return data[\"freshness_index\"];\r\n        case \"distance_from_work_site\":\r\n          return toNumber(data[\"distance_from_work_site\"], null);\r\n        default:\r\n          return \"\";\r\n      }\r\n    };\r\n\r\n    const sortedCandidates = [...filteredCandidatesList]?.sort((a, b) => {\r\n      const aValue = getValue(a);\r\n      const bValue = getValue(b);\r\n\r\n      const isANull = aValue === null || aValue === undefined;\r\n      const isBNull = bValue === null || bValue === undefined;\r\n\r\n      if (isANull && isBNull) return 0;\r\n      if (isANull) return 1; // nulls always last\r\n      if (isBNull) return -1;\r\n\r\n      // Strings\r\n      if (typeof aValue === \"string\" && typeof bValue === \"string\") {\r\n        return direction === \"asc\"\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      // Dates\r\n      if (aValue instanceof Date && bValue instanceof Date) {\r\n        return direction === \"asc\"\r\n          ? aValue.getTime() - bValue.getTime()\r\n          : bValue.getTime() - aValue.getTime();\r\n      }\r\n\r\n      // Numbers (including parsed distance)\r\n      if (typeof aValue === \"number\" && typeof bValue === \"number\") {\r\n        return direction === \"asc\" ? aValue - bValue : bValue - aValue;\r\n      }\r\n\r\n      return 0;\r\n    });\r\n\r\n    setSortConfigCan({ key, direction });\r\n    setFilteredCandidatesList(sortedCandidates);\r\n  };\r\n\r\n  const handleVacancyClick = (vacancy: Vacancy) => {\r\n    setPreventInitiallApiCall(false);\r\n    setStatusData && setStatusData(null);\r\n    setIsVacancyApiCalled && setIsVacancyApiCalled(false);\r\n    setSelectedVacancy(vacancy ?? null);\r\n    setPage(1);\r\n    setSortConfigCan({\r\n      key: null,\r\n      direction: null,\r\n    });\r\n  };\r\n\r\n  const filteredCandidates = useMemo(() => {\r\n    return Array.isArray(filteredCandidatesList)\r\n      ? filteredCandidatesList.filter((c) =>\r\n          c.candidate_data.email?.toLowerCase().includes(search.toLowerCase())\r\n        )\r\n      : [];\r\n  }, [filteredCandidatesList, search]);\r\n\r\n  // Paginate candidates\r\n  const paginatedCandidates = useMemo(() => {\r\n    const safeLimit = limit > 0 ? limit : 10;\r\n    const start = (page - 1) * safeLimit;\r\n    return filteredCandidates.slice(start, start + safeLimit);\r\n  }, [filteredCandidates, page, limit]);\r\n\r\n  const filteredVacancy = useMemo(() => {\r\n    if (!vacancySearch) return vacancies;\r\n    return vacancies.filter((vacancy) =>\r\n      (vacancy.refno ?? \"\").toLowerCase().includes(vacancySearch.toLowerCase())\r\n    );\r\n  }, [vacancies, vacancySearch]);\r\n\r\n  const fetchResume = async (candidate: Candidate) => {\r\n    if (!candidate?.candidate_contactid) {\r\n      console.error(\"Candidate contact ID is missing\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setIsResumeModalOpen(true);\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/resume/${candidate.candidate_contactid}`,\r\n        {},\r\n        { context: \"GetResume\" }\r\n      );\r\n      if (response.ok) {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        const data: any = await response.json();\r\n        setSelectedResume(data);\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_ResumeFetched\",\r\n          properties: {\r\n            candidateId: candidate?.candidate_contactid,\r\n            context: mercuryPortal ? \"MercuryGetResume\" : \"GetResume\",\r\n          },\r\n        });\r\n      } else {\r\n        console.error(\"Failed to fetch resume:\", response?.statusText);\r\n      }\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching resume:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          mercuryPortal\r\n            ? \"MercuryGetResume api with error is \" + error\r\n            : \"GetResume api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRefreshVacancies = () => {\r\n    const refreshVacancy_id = selectedVacancy?.vacancy_id || (vacancyid ?? \"\");\r\n    if (statusApiCall) {\r\n      if (selectedVacancy) {\r\n        setIsVacancyApiCalled?.(false);\r\n        setStatusVacancyId(selectedVacancy?.vacancy_id);\r\n        setStatusData?.(null);\r\n        setIsStatusInProcess?.(false);\r\n        setIsRefreshButtonClick(true);\r\n        setShowUpdate(false);\r\n      } else if (vacancyid?.length && mercuryPortal) {\r\n        setIsVacancyApiCalled?.(false);\r\n        setStatusVacancyId(vacancyid);\r\n        setStatusData?.(null);\r\n        setIsStatusInProcess?.(false);\r\n        setIsRefreshButtonClick(true);\r\n        setShowUpdate(false);\r\n      }\r\n    }\r\n    fetchCandidatesById(refreshVacancy_id);\r\n    fetchSingleVacancyByVacancyId(refreshVacancy_id);\r\n  };\r\n\r\n  const isTableReadOnly = useMemo(() => {\r\n    return isStatusInProcess || tableLoader;\r\n  }, [isStatusInProcess, tableLoader]);\r\n\r\n  const setIsStatusInProcessByClickOfRegenerateButton = () => {\r\n    setIsStatusInProcess?.(true);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const utcTimestamp = new Date().toISOString();\r\n\r\n    if (mercuryPortal && vacancyid) {\r\n      const mercuryKey = \"mercuryPortalVacancies\";\r\n      try {\r\n        const existing = localStorage.getItem(mercuryKey);\r\n        const data: Record<string, string> = existing\r\n          ? JSON.parse(existing)\r\n          : {};\r\n        const normalizedVacancyId = vacancyid.toLowerCase(); // ✅ normalize\r\n        data[normalizedVacancyId] = utcTimestamp;\r\n        localStorage.setItem(mercuryKey, JSON.stringify(data));\r\n      } catch (err) {\r\n        console.error(\"Error updating mercuryPortalVacancies:\", err);\r\n      }\r\n    }\r\n\r\n    if (selectedVacancy?.vacancy_id) {\r\n      const generalKey = \"selectedVacancyTimestamps\";\r\n      try {\r\n        const existing = localStorage?.getItem(generalKey);\r\n        const data: Record<string, string> = existing\r\n          ? JSON.parse(existing)\r\n          : {};\r\n\r\n        const normalizedSelectedId = selectedVacancy?.vacancy_id?.toLowerCase(); // ✅ normalize\r\n        data[normalizedSelectedId] = utcTimestamp;\r\n\r\n        localStorage?.setItem(generalKey, JSON.stringify(data));\r\n      } catch (err) {\r\n        console.error(\"Error updating selectedVacancyTimestamps:\", err);\r\n      }\r\n    }\r\n  }, [\r\n    mercuryPortal,\r\n    vacancyid,\r\n    selectedVacancy?.vacancy_id,\r\n    isRefreshButtonClick,\r\n  ]);\r\n\r\n  useEffect(() => {\r\n    if (!selectedVacancy?.vacancy_id || !update_timestamps) return;\r\n\r\n    const key = mercuryPortal\r\n      ? \"mercuryPortalVacancies\"\r\n      : \"selectedVacancyTimestamps\";\r\n\r\n    try {\r\n      const stored = localStorage?.getItem(key);\r\n      if (!stored) return;\r\n\r\n      const parsed = JSON.parse(stored) as Record<string, string>;\r\n\r\n      const lookupKey = (\r\n        selectedVacancy?.vacancy_id ||\r\n        vacancyid ||\r\n        \"\"\r\n      ).toLowerCase();\r\n      const localTimestamp = parsed[lookupKey];\r\n\r\n      if (!localTimestamp) return;\r\n\r\n      const backendTimestamp = update_timestamps?.data_last_updated_at ?? null;\r\n      const completedStatus =\r\n        catalyst_match_status?.completed_at ??\r\n        catalyst_match_status?.initiated_at ??\r\n        null;\r\n\r\n      let isStatusCompleted =\r\n        catalyst_match_status?.status === UPDATE_TIMESTAMPS_STATUS.COMPLETED;\r\n\r\n      const shouldShowUpdate =\r\n        new Date(localTimestamp).getTime() <=\r\n        new Date(backendTimestamp).getTime();\r\n\r\n      if (\r\n        new Date(localTimestamp).getTime() <\r\n          new Date(completedStatus).getTime() &&\r\n        isStatusCompleted\r\n      ) {\r\n        const refreshVacancy_id =\r\n          selectedVacancy?.vacancy_id || (vacancyid ?? \"\");\r\n        isStatusCompleted = false;\r\n\r\n        updateLocalStoredVacancyTimeStamp(\r\n          lookupKey, // use normalized key\r\n          mercuryPortal ?? false,\r\n          completedStatus\r\n        );\r\n\r\n        fetchCandidatesById(refreshVacancy_id);\r\n        fetchSingleVacancyByVacancyId(refreshVacancy_id);\r\n      }\r\n\r\n      setShowUpdate(shouldShowUpdate);\r\n    } catch (error) {\r\n      console.error(\"Error comparing timestamps for showUpdate:\", error);\r\n    }\r\n  }, [\r\n    selectedVacancy?.vacancy_id,\r\n    update_timestamps,\r\n    mercuryPortal,\r\n    updatedStatusCompleted,\r\n    catalyst_match_status,\r\n  ]);\r\n\r\n  const handleFilterIcon = () => {\r\n    setShowFilter(!showFilter);\r\n    setShowVacancyList(!showVacancyList);\r\n  };\r\n\r\n  const handleResetFilter = () => {\r\n    resetFilter(distanceRange?.[1], 1);\r\n    resetFilter(distance?.[1], 1);\r\n    setSearchText(\"\");\r\n  };\r\n\r\n  const uniqueLocation = useMemo(\r\n    () => extractLocations(candidates),\r\n    [candidates]\r\n  );\r\n\r\n  const uniqueFreshnessIndex = useMemo(\r\n    () => [\r\n      ...new Set(\r\n        candidates\r\n          ?.map((c) => c?.candidate_data?.freshness_index)\r\n          ?.filter(\r\n            (freshness_index) =>\r\n              freshness_index !== null &&\r\n              freshness_index !== undefined &&\r\n              freshness_index !== \"\"\r\n          )\r\n      ),\r\n    ],\r\n    [candidates]\r\n  );\r\n\r\n  const toggleSearchFieldLocal = (field: string) => {\r\n    toggleSearchField(field);\r\n  };\r\n\r\n  const handleCheckBoxSelectionLocal = (\r\n    label: string,\r\n    selectedValue: string\r\n  ) => {\r\n    handleCheckBoxSelection(label as ArrayFilterKeys, selectedValue);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (filterEntitlement && candidates?.length > 0) {\r\n      const filtered =\r\n        candidates &&\r\n        candidates?.filter((candidate) => {\r\n          const candidateShortListed =\r\n            typeof candidate?.candidate_data?.shortlisted_details?.status ===\r\n              \"string\" &&\r\n            (candidate?.candidate_data?.shortlisted_details?.status.toLowerCase() ===\r\n              VACANCY_FILTER_OTHER_LABELS.SUCCESS ||\r\n              candidate?.candidate_data?.shortlisted_details?.status.toLowerCase() !==\r\n                \"failed\")\r\n              ? \"Yes\"\r\n              : \"No\";\r\n          const candidateState = capitalizeEachWord(\r\n            candidate?.candidate_data?.state?.toLowerCase() ?? \"\".trim()\r\n          );\r\n          const candidateCity = capitalizeEachWord(\r\n            candidate?.candidate_data?.city?.toLowerCase() ?? \"\"\r\n          );\r\n          const candidateFreshnessIndex =\r\n            candidate?.candidate_data?.freshness_index ?? \"\";\r\n          const candidateInfoBotResponse =\r\n            candidate?.candidate_data?.info_bot_response ?? \"\";\r\n          const candidateReview =\r\n            candidate?.candidate_data?.recruiter_review_decision?.vote;\r\n\r\n          // Map candidate review vote to filter values\r\n          let reviewDecision: string;\r\n          if (candidateReview === null || candidateReview === undefined) {\r\n            reviewDecision = VACANCY_FILTER_OTHER_LABELS.NOREVIEW;\r\n          } else if (candidateReview === VACANCY_FILTER_OTHER_LABELS.LIKE) {\r\n            reviewDecision = VACANCY_FILTER_OTHER_LABELS.THUMBSUP;\r\n          } else if (candidateReview === VACANCY_FILTER_OTHER_LABELS.DISLIKE) {\r\n            reviewDecision = VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN;\r\n          } else {\r\n            reviewDecision = VACANCY_FILTER_OTHER_LABELS.NOREVIEW;\r\n          }\r\n\r\n          // Map info_bot_response to AI Agent Status:\r\n          let aiAgentStatusValue = \"\";\r\n          if (!candidateInfoBotResponse || candidateInfoBotResponse === \"\") {\r\n            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.NOT_CONTACTED_BLANK;\r\n          } else if (\r\n            candidateInfoBotResponse.toLowerCase()?.includes(responded)\r\n          ) {\r\n            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.RESPONDED;\r\n          } else if (\r\n            candidateInfoBotResponse.toLowerCase()?.includes(contacted)\r\n          ) {\r\n            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.CONTACTED;\r\n          } else if (\r\n            candidateInfoBotResponse.toLowerCase()?.includes(notInterested)\r\n          ) {\r\n            aiAgentStatusValue = AI_AGENTS_RESPONSE_STATUS.NOT_INTERESTED;\r\n          } else {\r\n            aiAgentStatusValue = candidateInfoBotResponse;\r\n          }\r\n\r\n          const candidateName =\r\n            candidate?.candidate_data?.name?.toLowerCase() ?? \"\";\r\n          const candidateWhyFit =\r\n            candidate?.candidate_data?.current_fitness_reason?.reason?.toLowerCase() ??\r\n            \"\";\r\n          const candidateTotalScore =\r\n            candidate?.candidate_data?.[\r\n              VACANCY_FILTER_OTHER_LABELS.CLASSIFICATIONSCORE\r\n            ]?.overallscore?.toFixed(2) ?? 0;\r\n\r\n          const candidateAvailability =\r\n            candidate?.candidate_data?.availability ?? \"\";\r\n\r\n          // Match filters using store values\r\n          const matchesReviewStatus =\r\n            filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION]?.includes(\r\n              reviewDecision\r\n            );\r\n\r\n          const matchesState =\r\n            filters?.[VACANCY_FILTER_LABELS.STATE]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.STATE]?.includes(candidateState);\r\n\r\n          const matchesCity =\r\n            filters?.[VACANCY_FILTER_LABELS.CITY]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.CITY]?.includes(candidateCity);\r\n\r\n          const matchesFreshnessIndex =\r\n            filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX]?.includes(\r\n              candidateFreshnessIndex\r\n            );\r\n\r\n          const matchesShortList =\r\n            filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED]?.includes(\r\n              candidateShortListed\r\n            );\r\n\r\n          const matchesAiAgentStatus =\r\n            filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.length === 0 ||\r\n            filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS]?.includes(\r\n              aiAgentStatusValue\r\n            );\r\n\r\n          const matchesTotalScore =\r\n            candidateTotalScore <=\r\n              filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[1]?.toFixed(\r\n                2\r\n              ) &&\r\n            candidateTotalScore >=\r\n              filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]?.[0]?.toFixed(\r\n                2\r\n              );\r\n\r\n          const candidateDistance =\r\n            candidate?.candidate_data?.distance_from_work_site;\r\n          const minDistance =\r\n            filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[0] ?? 0;\r\n          const maxDistance =\r\n            filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE]?.[1] ??\r\n            distanceRange?.[1];\r\n\r\n          const matchesDistance =\r\n            candidateDistance === null || candidateDistance === undefined\r\n              ? minDistance === 0 && maxDistance === distanceRange?.[1]\r\n              : candidateDistance >= distanceRange?.[0] &&\r\n                candidateDistance <= distanceRange?.[1];\r\n\r\n          const matchesSearch = (() => {\r\n            const searchText = debouncedValue.trim();\r\n            const searchFields =\r\n              filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS] ?? [];\r\n\r\n            if (searchText === \"\") {\r\n              // No search text entered\r\n              if (searchFields?.length === 0) {\r\n                showSearchFieldError = true; // show warning for missing field selection\r\n              }\r\n              // Don't filter in this case, so return true to include all candidates\r\n              return true;\r\n            } else {\r\n              // Search text is entered\r\n              if (searchFields?.length === 0) {\r\n                showSearchFieldError = true; // show warning for missing field selection\r\n                return true;\r\n              }\r\n              // Normal filtering when searchFields selected with non-empty search text\r\n              const searchLower = searchText.toLowerCase();\r\n              const byName =\r\n                searchFields?.includes(VACANCY_FILTER_OTHER_LABELS.NAME) &&\r\n                candidateName?.includes(searchLower);\r\n              const byWhyFit =\r\n                searchFields?.includes(VACANCY_FILTER_OTHER_LABELS.WHYFIT) &&\r\n                candidateWhyFit?.includes(searchLower);\r\n\r\n              return byName || byWhyFit;\r\n            }\r\n          })();\r\n\r\n          const matchesAvailabilityDate = (() => {\r\n            const from =\r\n              filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from;\r\n            const to =\r\n              filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to;\r\n\r\n            if (!from && !to) return true;\r\n            if (!candidateAvailability) return false;\r\n\r\n            function normalize(date: Date) {\r\n              return new Date(\r\n                date.getFullYear(),\r\n                date.getMonth(),\r\n                date.getDate()\r\n              );\r\n            }\r\n\r\n            const candidateDate = normalize(new Date(candidateAvailability));\r\n            const fromDate = from ? normalize(new Date(from)) : null;\r\n            const toDate = to ? normalize(new Date(to)) : null;\r\n\r\n            if (fromDate && toDate && fromDate > toDate) {\r\n              return true;\r\n            }\r\n\r\n            // Inclusive range: candidateDate >= fromDate && candidateDate <= toDate\r\n            if (fromDate && candidateDate < fromDate) return false;\r\n            if (toDate && candidateDate > toDate) return false;\r\n\r\n            return true;\r\n          })();\r\n\r\n          return (\r\n            matchesState &&\r\n            matchesCity &&\r\n            matchesFreshnessIndex &&\r\n            matchesShortList &&\r\n            matchesReviewStatus &&\r\n            matchesAiAgentStatus &&\r\n            matchesTotalScore &&\r\n            matchesSearch &&\r\n            matchesAvailabilityDate &&\r\n            matchesDistance\r\n          );\r\n        });\r\n      setFilteredCandidatesList(filtered);\r\n    } else {\r\n      if (!filterEntitlement && selectedVacancy?.vacancy_id) {\r\n        clearFiltersForVacancyList(selectedVacancy?.vacancy_id);\r\n      }\r\n      setFilteredCandidatesList(candidates);\r\n    }\r\n  }, [\r\n    filterEntitlement,\r\n    candidates,\r\n    filters?.[VACANCY_FILTER_LABELS.STATE],\r\n    filters?.[VACANCY_FILTER_LABELS.CITY],\r\n    filters?.[VACANCY_FILTER_LABELS.FRESHNESS_INDEX],\r\n    filters?.[VACANCY_FILTER_LABELS.SHORT_LISTED],\r\n    filters?.[VACANCY_FILTER_LABELS.REVIEW_DECISION],\r\n    filters?.[VACANCY_FILTER_LABELS.AI_AGENT_STATUS],\r\n    filters?.[VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE],\r\n    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.from,\r\n    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]?.to,\r\n    filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS],\r\n    filters?.[VACANCY_FILTER_LABELS.SEARCH_BY_NAME],\r\n    debouncedValue,\r\n    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE],\r\n    filters?.[VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE],\r\n    filters?.[VACANCY_FILTER_LABELS.DISTANCE_RANGE],\r\n  ]);\r\n\r\n  const filterProps: iFilterSlider = {\r\n    filterEntitlement,\r\n    showFilter,\r\n    isFunnelFill,\r\n    showSearchFieldError,\r\n    searchFields,\r\n    searchText,\r\n    uniqueReviewValues,\r\n    reviewStatus,\r\n    uniqueShortList,\r\n    shortListed,\r\n    uniqueLocation,\r\n    state,\r\n    city,\r\n    uniqueFreshnessIndex,\r\n    freshnessIndex,\r\n    availabilityDateRange,\r\n    uniqueAiAgentStatus,\r\n    aiAgentStatus,\r\n    totalScoreRange,\r\n    handleResetFilter,\r\n    handleFilterIcon,\r\n    toggleSearchField: toggleSearchFieldLocal,\r\n    setSearchText,\r\n    handleCheckBoxSelection: handleCheckBoxSelectionLocal,\r\n    setTotalScoreRange: (val) =>\r\n      setFilterField(VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE, val),\r\n    setAvailabilityDateRange: (\r\n      value: React.SetStateAction<{ from: string; to: string }>\r\n    ): void => {\r\n      const resolvedValue =\r\n        typeof value === \"function\" ? value(availabilityDateRange) : value;\r\n      setFilterField(\r\n        VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE,\r\n        resolvedValue\r\n      );\r\n    },\r\n    distanceRange,\r\n    setDistanceRange: (val) =>\r\n      setFilterField(VACANCY_FILTER_LABELS.DISTANCE_RANGE, val),\r\n    distance,\r\n    maxMinAvailabilityDateRange,\r\n  };\r\n\r\n  return (\r\n    <NotificationProvider>\r\n      <NotificationList />\r\n      <div className=\"p-4\">\r\n        {isParsedVacancyPopupOpen && (\r\n          <VacancyDetails\r\n            vacancy={selectedVacancy}\r\n            setActiveVacancy={() => {\r\n              setActiveVacancy(null);\r\n              setIsParsedVacancyPopupOpen(false);\r\n            }}\r\n            mercuryPortal={mercuryPortal ?? false}\r\n          />\r\n        )}\r\n        {!vacancyid && !mercuryPortal ? (\r\n          <h2 className=\"text-2xl font-bold text-center py-2\">\r\n            Catalyst Match\r\n          </h2>\r\n        ) : (\r\n          selectedVacancy && (\r\n            <>\r\n              <div className=\"flex justify-center items-baseline\">\r\n                <h2 className=\"text-2xl font-bold text-center\">\r\n                  Catalyst Match for the Vacancy\r\n                </h2>\r\n                <span className=\"ml-2\">\r\n                  <a\r\n                    href=\"#\"\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      openParsedVacancyPopup();\r\n                    }}\r\n                    className=\"text-blue-600 underline hover:text-blue-800 cursor-pointer\"\r\n                  >\r\n                    [Parsed Vacancy]\r\n                  </a>\r\n                </span>\r\n              </div>\r\n            </>\r\n          )\r\n        )}\r\n\r\n        <div className=\"flex flex-col lg:flex-row gap-4 justify-center\">\r\n          {/* Vacancy Panel */}\r\n          {showVacancyList && !vacancyid && !mercuryPortal && (\r\n            <div className=\"w-full lg:w-[350px] p-2 lg:border-r border-b lg:border-b-0 z-30 lg:order-2 order-2\">\r\n              <h3\r\n                className=\"text-lg font-semibold mb-2 cursor-pointer\"\r\n                onClick={() => handleSort()}\r\n              >\r\n                Vacancy List{\" \"}\r\n                <ArrowUpDown className=\"text-black inline ml-1 h-4 w-4\" />\r\n              </h3>\r\n              <p>\r\n                <Input\r\n                  type=\"search\"\r\n                  placeholder=\"Search by vacancy code\"\r\n                  value={vacancySearch}\r\n                  onChange={(e) => setVacancySearch(e.target.value)}\r\n                  className=\"w-full border border-gray-300 rounded-lg p-2 my-2\"\r\n                />\r\n              </p>\r\n              {loading && !filteredVacancy.length ? (\r\n                <Loading height=\"h-[50vh]\" />\r\n              ) : (\r\n                <ul className=\"max-h-[66vh] overflow-y-auto\">\r\n                  {filteredVacancy?.map((vacancy) => (\r\n                    <VacancyItem\r\n                      vacancy={vacancy}\r\n                      handleVacancyClick={handleVacancyClick}\r\n                      selectedVacancy={selectedVacancy}\r\n                      setSearch={setSearch}\r\n                      key={vacancy.refno}\r\n                      activeVacancy={activeVacancy}\r\n                      setActiveVacancy={setActiveVacancy}\r\n                    />\r\n                  ))}\r\n                </ul>\r\n              )}\r\n            </div>\r\n          )}\r\n          {/* Candidates Table */}\r\n          <div className=\"flex-1 lg:order-3\">\r\n            {loading && !selectedVacancy ? (\r\n              <div className=\"flex items-center justify-center h-[50vh]\">\r\n                <Loading height=\"h-[50vh]\" />\r\n              </div>\r\n            ) : selectedVacancy && !vacancyid && filteredCandidatesList ? (\r\n              <>\r\n                <div className=\"flex justify-end mb-2 mr-1\">\r\n                  <Button\r\n                    className=\"rounded\"\r\n                    onClick={handleRefreshVacancies}\r\n                    disabled={isTableReadOnly!}\r\n                  >\r\n                    {showUpdate && <p>Updates Available</p>}\r\n                    <RefreshCcw className=\"w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform\" />\r\n                  </Button>\r\n                </div>\r\n                <div\r\n                  className={`flex items-center w-[100%] ${\r\n                    showFilter ? \"justify-between\" : \" justify-end\"\r\n                  }`}\r\n                >\r\n                  {selectedVacancy && showFilter && (\r\n                    <h3 className=\"text-lg font-semibold mb-2\">\r\n                      &quot;Candidates for {selectedVacancy?.refno}&quot;\r\n                    </h3>\r\n                  )}\r\n\r\n                  {isReadOnly && (\r\n                    <div className=\"bg-blue-200 px-2 py-1 mb-1 rounded-md flex items-center gap-2 mr-2\">\r\n                      <LockKeyhole size={15} />\r\n                      <h6 className=\"text-sm\">The below table is read-only</h6>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <div className={`flex items-center w-[100%] justify-between`}>\r\n                  <div className=\"flex items-center\">\r\n                    <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                      <TabsList className=\"grid w-fit grid-cols-2\">\r\n                        <TabsTrigger value=\"vacancy-template\">Vacancy Template</TabsTrigger>\r\n                        <TabsTrigger value=\"match-results\">Match Results</TabsTrigger>\r\n                      </TabsList>\r\n                    </Tabs>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {!showFilter\r\n                      ? selectedVacancy && (\r\n                          <h3 className=\"text-lg font-semibold whitespace-nowrap\">\r\n                            &quot;Candidates for {selectedVacancy?.refno}&quot;\r\n                          </h3>\r\n                        )\r\n                      : filterEntitlement &&\r\n                        showFilter &&\r\n                        candidates?.length > 0 &&\r\n                        filterIcon()}\r\n                    <div\r\n                      className=\"flex justify-end\"\r\n                      id=\"regenerate-button-container-1\"\r\n                    >\r\n                      {/* regenerate Recruitment*/}\r\n                      {entitlements?.Regenerate &&\r\n                        !loading &&\r\n                        selectedVacancy &&\r\n                        selectedVacancy?.vacancy_id && (\r\n                          <RegenerateButton\r\n                            vacancyId={selectedVacancy.vacancy_id}\r\n                            emailId={emailId ?? session?.data?.user?.email ?? \"\"}\r\n                            canRegenerate={canRegenerate}\r\n                            onReloadTable={handleReloadTable}\r\n                            setRegenerationReadOnly={setRegenerationReadOnly}\r\n                            setIsStatusInProcessByClickOfRegenerateButton={\r\n                              setIsStatusInProcessByClickOfRegenerateButton\r\n                            }\r\n                            catalystRegenerationData={catalystRegenerationData}\r\n                            setIsLoading={setIsLoading}\r\n                            isTableReadOnly={isTableReadOnly!}\r\n                            mercuryPortal={mercuryPortal ?? false}\r\n                            isClosed={selectedVacancy.closed}\r\n                          />\r\n                        )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                  <TabsContent value=\"vacancy-template\" className=\"mt-4\">\r\n                    <CatalystMatchForm/>\r\n                  </TabsContent>\r\n\r\n                  <TabsContent value=\"match-results\" className=\"mt-4\">\r\n                    {selectedVacancy && (\r\n                      <CandidateTable\r\n                        handleCandidateSort={handleCandidateSort}\r\n                        isResumeModalOpen={isResumeModalOpen}\r\n                        loading={loading}\r\n                        tableLoading={tableLoader}\r\n                        paginatedCandidates={paginatedCandidates}\r\n                        selectedVacancy={selectedVacancy}\r\n                        candidates={filteredCandidatesList}\r\n                        setCandidates={setFilteredCandidatesList}\r\n                        vacancyCandidates={vacancyCandidates}\r\n                        setVacancyCandidates={setVacancyCandidates}\r\n                        fetchResume={fetchResume}\r\n                        mercuryPortal={mercuryPortal ?? false}\r\n                        fetchCandidatesById={fetchCandidatesById}\r\n                        emailId={emailId ?? \"\"}\r\n                        isTableReadOnly={isTableReadOnly!}\r\n                        vacancyId={vacancyid}\r\n                        catalystRegenerationData={catalystRegenerationData}\r\n                        filterProps={filterProps}\r\n                      />\r\n                    )}\r\n                    {/* Pagination */}\r\n                    <div className=\"flex justify-between items-center mt-4\">\r\n                      <Select\r\n                        value={limit as unknown as string}\r\n                        onValueChange={(val: string) => {\r\n                          setLimit(Number(val));\r\n                          setPage(1);\r\n                        }}\r\n                      >\r\n                        <SelectTrigger className=\"w-32\">\r\n                          <SelectValue placeholder=\"Select per page-\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectGroup>\r\n                            {[50, 100, 150, 200, 500].map((size) => (\r\n                              <SelectItem\r\n                                key={size}\r\n                                value={size as unknown as string}\r\n                              >\r\n                                {size} per page\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectGroup>\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <div>\r\n                        <Button\r\n                          variant={\"outline\"}\r\n                          onClick={() => setPage((p) => Math.max(p - 1, 1))}\r\n                          disabled={page === 1}\r\n                          className=\"px-2 py-0.5 mr-1\"\r\n                        >\r\n                          <ChevronLeft />\r\n                        </Button>\r\n                        <Button\r\n                          variant={\"outline\"}\r\n                          onClick={() =>\r\n                            setPage((p) =>\r\n                              p * limit < filteredCandidates?.length ? p + 1 : p\r\n                            )\r\n                          }\r\n                          className=\"px-2 py-0.5\"\r\n                          disabled={\r\n                            Math.ceil(filteredCandidates?.length / limit) === page\r\n                          }\r\n                        >\r\n                          <ChevronRight />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n                </Tabs>\r\n              </>\r\n            ) : selectedVacancy && vacancyid ? (\r\n              <div className=\"\">\r\n                {SHOW_SEARCH_BY_EMAIL && (\r\n                  <Input\r\n                    type=\"search\"\r\n                    placeholder=\"Search by email\"\r\n                    value={search}\r\n                    onChange={(e) => setSearch(e.target.value)}\r\n                    className=\"w-1/3 border border-gray-300 rounded-lg p-2 my-2\"\r\n                  />\r\n                )}\r\n                <div className=\"flex justify-end mb-2 mr-1\">\r\n                  <Button\r\n                    className=\"rounded\"\r\n                    onClick={handleRefreshVacancies}\r\n                    disabled={isTableReadOnly!}\r\n                  >\r\n                    {showUpdate && <p>Updates Available</p>}\r\n                    <RefreshCcw className=\"w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform\" />\r\n                  </Button>\r\n                </div>\r\n                {canRegenerate && isMercuryReadOnly && (\r\n                  <div className=\"bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <LockKeyhole size={15} />\r\n                      <h6 className=\"text-sm\">The below table is read-only</h6>\r\n                    </div>\r\n                    <Badge variant=\"outline\" className=\"bg-blue-200\">\r\n                      Read only\r\n                    </Badge>\r\n                  </div>\r\n                )}\r\n                {/* regenerate mercury */}\r\n                <div\r\n                  className={`${\"justify-end\"}`}\r\n                  id=\"regenerate-button-container-2\"\r\n                >\r\n                  <div className=\"flex items-center justify-between w-full\">\r\n                    <div className=\"flex items-center\">\r\n                      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                        <TabsList className=\"grid w-fit grid-cols-2\">\r\n                          <TabsTrigger value=\"vacancy-template\">Vacancy Template</TabsTrigger>\r\n                          <TabsTrigger value=\"match-results\">Match Results</TabsTrigger>\r\n                        </TabsList>\r\n                      </Tabs>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {showMercuryFilter && (\r\n                        <div className=\"mb-2\">{filterIcon()}</div>\r\n                      )}\r\n                      {/* regenerate */}\r\n                      {(entitlementData?.Regenerate ||\r\n                        entitlements?.Regenerate) &&\r\n                        !loading &&\r\n                        selectedVacancy &&\r\n                        selectedVacancy.vacancy_id && (\r\n                          <RegenerateButton\r\n                            vacancyId={selectedVacancy?.vacancy_id}\r\n                            emailId={emailId ?? session?.data?.user?.email ?? \"\"}\r\n                            canRegenerate={canRegenerate}\r\n                            onReloadTable={handleReloadTable}\r\n                            setRegenerationReadOnly={setRegenerationReadOnly}\r\n                            setIsStatusInProcessByClickOfRegenerateButton={\r\n                              setIsStatusInProcessByClickOfRegenerateButton\r\n                            }\r\n                            catalystRegenerationData={catalystRegenerationData}\r\n                            setIsLoading={setIsLoading}\r\n                            isTableReadOnly={isTableReadOnly!}\r\n                            mercuryPortal={mercuryPortal ?? false}\r\n                            isClosed={selectedVacancy.closed}\r\n                          />\r\n                        )}\r\n                    </div>\r\n                  </div>\r\n                  <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                    <TabsContent value=\"vacancy-template\" className=\"mt-4\">\r\n                      <CatalystMatchForm/>\r\n                    </TabsContent>\r\n\r\n                    <TabsContent value=\"match-results\" className=\"mt-4\">\r\n                      {selectedVacancy && (\r\n                        <CandidateTable\r\n                          handleCandidateSort={handleCandidateSort}\r\n                          isResumeModalOpen={isResumeModalOpen}\r\n                          loading={loading}\r\n                          tableLoading={tableLoader}\r\n                          paginatedCandidates={paginatedCandidates}\r\n                          selectedVacancy={selectedVacancy}\r\n                          candidates={candidates}\r\n                          setCandidates={setCandidates}\r\n                          vacancyCandidates={vacancyCandidates}\r\n                          setVacancyCandidates={setVacancyCandidates}\r\n                          fetchResume={fetchResume}\r\n                          mercuryPortal={mercuryPortal ?? false}\r\n                          fetchCandidatesById={fetchCandidatesById}\r\n                          emailId={emailId ?? \"\"}\r\n                          isTableReadOnly={isTableReadOnly!}\r\n                          vacancyId={vacancyid}\r\n                          catalystRegenerationData={catalystRegenerationData}\r\n                          filterProps={filterProps}\r\n                        />\r\n                      )}\r\n                      {/* Pagination */}\r\n                      <div className=\"flex justify-between items-center mt-4\">\r\n                        <Select\r\n                          value={limit as unknown as string}\r\n                          onValueChange={(val: string) => {\r\n                            setLimit(Number(val));\r\n                            setPage(1);\r\n                          }}\r\n                        >\r\n                          <SelectTrigger className=\"w-32\">\r\n                            <SelectValue placeholder=\"Select per page-\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            <SelectGroup>\r\n                              {[50, 100, 150, 200, 500].map((size) => (\r\n                                <SelectItem\r\n                                  key={size}\r\n                                  value={size as unknown as string}\r\n                                >\r\n                                  {size} per page\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectGroup>\r\n                          </SelectContent>\r\n                        </Select>\r\n                        <div>\r\n                          <Button\r\n                            variant={\"outline\"}\r\n                            onClick={() => setPage((p) => Math.max(p - 1, 1))}\r\n                            disabled={page === 1}\r\n                            className=\"px-2 py-0.5 mr-1\"\r\n                          >\r\n                            <ChevronLeft />\r\n                          </Button>\r\n                          <Button\r\n                            variant={\"outline\"}\r\n                            onClick={() =>\r\n                              setPage((p) =>\r\n                                p * limit < filteredCandidates.length ? p + 1 : p\r\n                              )\r\n                            }\r\n                            className=\"px-2 py-0.5\"\r\n                            disabled={\r\n                              Math.ceil(filteredCandidates.length / limit) === page\r\n                            }\r\n                          >\r\n                            <ChevronRight />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    </TabsContent>\r\n                  </Tabs>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              (filteredCandidatesList?.length === 0 || statusCode !== 200) && (\r\n                <div className=\"\">\r\n                  {SHOW_SEARCH_BY_EMAIL && (\r\n                    <Input\r\n                      type=\"search\"\r\n                      placeholder=\"Search by email\"\r\n                      value={search}\r\n                      onChange={(e) => setSearch(e.target.value)}\r\n                      className=\"w-1/3 border border-gray-300 rounded-lg p-2 my-2\"\r\n                    />\r\n                  )}\r\n                  <div className=\"flex justify-end mb-2 mr-1\">\r\n                    <Button\r\n                      className=\"rounded\"\r\n                      onClick={handleRefreshVacancies}\r\n                      disabled={isTableReadOnly!}\r\n                    >\r\n                      {showUpdate && <p>Updates Available</p>}\r\n                      <RefreshCcw className=\"w-5 h-5 text-white-700 cursor-pointer hover:rotate-90 transition-transform\" />\r\n                    </Button>\r\n                  </div>\r\n                  {canRegenerate && isMercuryReadOnly && (\r\n                    <div className=\"bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <LockKeyhole size={15} />\r\n                        <h6 className=\"text-sm\">\r\n                          The below table is read-only\r\n                        </h6>\r\n                      </div>\r\n                      <Badge variant=\"outline\" className=\"bg-blue-200\">\r\n                        Read only\r\n                      </Badge>\r\n                    </div>\r\n                  )}\r\n                  {/* regenerate and tabs */}\r\n                  <div className=\"flex items-center justify-between w-full mb-4\">\r\n                    <div className=\"flex items-center\">\r\n                      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                        <TabsList className=\"grid w-fit grid-cols-2\">\r\n                          <TabsTrigger value=\"vacancy-template\">Vacancy Template</TabsTrigger>\r\n                          <TabsTrigger value=\"match-results\">Match Results</TabsTrigger>\r\n                        </TabsList>\r\n                      </Tabs>\r\n                    </div>\r\n                    <div className=\"flex justify-end\" id=\"regenerate-button-container-3\">\r\n                      {(entitlementData?.Regenerate ||\r\n                        entitlements?.Regenerate) &&\r\n                        !loading &&\r\n                        vacancyid && (\r\n                          <RegenerateButton\r\n                            vacancyId={selectedVacancy?.vacancy_id ?? vacancyid}\r\n                            emailId={emailId ?? session?.data?.user?.email ?? \"\"}\r\n                            canRegenerate={canRegenerate}\r\n                            onReloadTable={handleReloadTable}\r\n                            setRegenerationReadOnly={setRegenerationReadOnly}\r\n                            setIsStatusInProcessByClickOfRegenerateButton={\r\n                              setIsStatusInProcessByClickOfRegenerateButton\r\n                            }\r\n                            catalystRegenerationData={catalystRegenerationData}\r\n                            setIsLoading={setIsLoading}\r\n                            isTableReadOnly={isTableReadOnly!}\r\n                            mercuryPortal={mercuryPortal ?? false}\r\n                            isClosed={selectedVacancy?.closed!}\r\n                          />\r\n                        )}\r\n                    </div>\r\n                  </div>\r\n                  <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n                    <TabsContent value=\"vacancy-template\" className=\"mt-4\">\r\n                      <CatalystMatchForm/>\r\n                    </TabsContent>\r\n\r\n                    <TabsContent value=\"match-results\" className=\"mt-4\">\r\n                      <CandidateTable\r\n                        handleCandidateSort={handleCandidateSort}\r\n                        isResumeModalOpen={isResumeModalOpen}\r\n                        loading={loading}\r\n                        paginatedCandidates={paginatedCandidates}\r\n                        selectedVacancy={selectedVacancy!}\r\n                        candidates={filteredCandidatesList}\r\n                        setCandidates={setFilteredCandidatesList}\r\n                        vacancyCandidates={vacancyCandidates}\r\n                        setVacancyCandidates={setVacancyCandidates}\r\n                        fetchResume={fetchResume}\r\n                        mercuryPortal={mercuryPortal ?? false}\r\n                        fetchCandidatesById={fetchCandidatesById}\r\n                        emailId={emailId ?? \"\"}\r\n                        isTableReadOnly={isTableReadOnly!}\r\n                        vacancyId={vacancyid}\r\n                        catalystRegenerationData={catalystRegenerationData}\r\n                        filterProps={filterProps}\r\n                        tableLoading={tableLoader}\r\n                      />\r\n                      {/* Pagination */}\r\n                      <div className=\"flex justify-between items-center mt-4\">\r\n                        <Select\r\n                          value={limit as unknown as string}\r\n                          onValueChange={(val: string) => {\r\n                            setLimit(Number(val));\r\n                            setPage(1);\r\n                          }}\r\n                        >\r\n                          <SelectTrigger className=\"w-32\">\r\n                            <SelectValue placeholder=\"Select per page-\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            <SelectGroup>\r\n                              {[50, 100, 150, 200, 500]?.map((size) => (\r\n                                <SelectItem\r\n                                  key={size}\r\n                                  value={size as unknown as string}\r\n                                >\r\n                                  {size} per page\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectGroup>\r\n                          </SelectContent>\r\n                        </Select>\r\n                        <div>\r\n                          <Button\r\n                            variant={\"outline\"}\r\n                            onClick={() => setPage((p) => Math.max(p - 1, 1))}\r\n                            disabled={page === 1}\r\n                            className=\"px-2 py-0.5 mr-1\"\r\n                          >\r\n                            <ChevronLeft />\r\n                          </Button>\r\n                          <Button\r\n                            variant={\"outline\"}\r\n                            onClick={() =>\r\n                              setPage((p) =>\r\n                                p * limit < filteredCandidates?.length ? p + 1 : p\r\n                              )\r\n                            }\r\n                            className=\"px-2 py-0.5\"\r\n                            disabled={\r\n                              Math.ceil(filteredCandidates?.length / limit) === page\r\n                            }\r\n                          >\r\n                            <ChevronRight />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    </TabsContent>\r\n                  </Tabs>\r\n                </div>\r\n              )\r\n            )}\r\n          </div>\r\n        </div>\r\n        <CandidateResume\r\n          vacancy={selectedVacancy}\r\n          selectedResume={selectedResume}\r\n          setSelectedResume={setSelectedResume}\r\n          isResumeModalOpen={isResumeModalOpen}\r\n          setIsResumeModalOpen={setIsResumeModalOpen}\r\n          isLoading={loading}\r\n        />\r\n        {vacancyid === undefined &&\r\n          !loading &&\r\n          pageNotFound &&\r\n          mercuryPortal && (\r\n            <div className=\"bg-white border-t p-4 h-screen flex items-center justify-center\">\r\n              <p>\r\n                <span className=\"text-lg mr-1\">404</span>Page not found\r\n              </p>\r\n            </div>\r\n          )}\r\n        {isLoading && (\r\n          <div className=\"fixed inset-0 z-[2000] flex items-center justify-center bg-black bg-opacity-30\">\r\n            <div className=\"bg-transparent rounded flex items-center justify-center\">\r\n              <Loading />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </NotificationProvider>\r\n  );\r\n};\r\n\r\nexport default Candidates;\r\n"], "names": [], "mappings": ";;;;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAKA;AAEA;AAOA;AACA;AACA;AAxDA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,MAAM,mBAAmB;AACzB,MAAM,uBAAuB;AAE7B,MAAM,aAAa,CAAC,EAClB,SAAS,EACT,aAAa,EACb,OAAO,EACP,eAAe,EAMhB;;IACC,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAE7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCACrB,IAAO,gBAAgB,8HAAA,CAAA,wBAAqB,GAAG,8HAAA,CAAA,wBAAqB;uCACpE;QAAC;KAAc;IAGjB,MAAM,iBAAiB,gBACnB,8HAAA,CAAA,wBAAqB,GACrB,8HAAA,CAAA,wBAAqB;IAEzB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eACJ,cAAc,aACd,CAAC,SAAS,QAAQ,CAAC,CAAC,8CAA8C,CAAC;IACrE,MAAM,UAAU,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACzB,MAAM,OAAO,SAAS,MAAM,MAAM;IAClC,mBAAmB;IACnB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEtC,mCAAmC;IACnC,MAAM,oBACJ,CAAC,cAAc,kBAAkB,iBAAiB,cAAc,KAAK;IACvE,kBAAkB;IAClB,MAAM,oBAAoB;IAE1B,MAAM,kBAAkB;QAAC;QAAO;KAAK;IACrC,MAAM,qBAAqB;QAAC;QAAY;QAAc;KAAW;IACjE,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;KACD,EAAE,KAAK,CAAC,GAAG,IAAM,EAAE,aAAa,CAAC;IAClC,MAAM,YAAY;IAClB,MAAM,YAAY;IAClB,MAAM,gBAAgB;IAEtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG/C,CAAC;IACX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEjE,EAAE;IACJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3C,iBAAiB;IAEnB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,wBAAwB,0BAA0B,GACvD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxC;QACD,KAAK;QACL,WAAW;IACb;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GACvD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,0CAA0C;IAC1C,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAGvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG9C;QACD,KAAK;QACL,WAAW;IACb;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,kGAAkG;IAClG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAE;IACjE,4DAA4D;IAC5D,MAAM,CAAC,6BAA6B,+BAA+B,GACjE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGJ;QAAE,MAAM;QAAI,IAAI;IAAG;IAExB,uBAAuB;IACvB,MAAM,UAAU;8CAAe,CAAC,QAAU,OAAO;;IACjD,MAAM,iBAAiB;qDAAe,CAAC,QAAU,OAAO;;IACxD,MAAM,oBAAoB;wDAAe,CAAC,QAAU,OAAO;;IAC3D,MAAM,0BAA0B;8DAC9B,CAAC,QAAU,OAAO;;IAEpB,MAAM,cAAc;kDAAe,CAAC,QAAU,OAAO;;IACrD,MAAM,6BAA6B;iEACjC,CAAC,QAAU,OAAO;;IAEpB,MAAM,gBAAgB;oDACpB,CAAC,QAAU,MAAM,OAAO,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;;IAEhE,MAAM,mBAAmB;uDAAe,CAAC,QAAU,MAAM,gBAAgB;;IAEzE,yCAAyC;IACzC,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,YAAY;IAE/C,MAAM,oBACJ,qBAAqB,cAAc,YAAY,SAAS;IAE1D,MAAM,EACJ,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC,EAAE,KAAK,EACpC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,IAAI,EAClC,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,cAAc,EACvD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,EAAE,WAAW,EACjD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,YAAY,EACrD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,aAAa,EACtD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,YAAY,EACpD,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE,eAAe,EAC1D,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE,qBAAqB,EACtE,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,YAAY,EACpD,GAAG;IAEJ,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,mBACJ,YACI;wCAAI,CAAC,IAAM,GAAG,gBAAgB;uCAC/B;wCAAO,CAAC,IAAmB,MAAM,QAAQ,MAAM;0CAAc,EAAE;YAEpE,MAAM,cAAc,YAAY;wCAAI,CAAC;oBACnC,MAAM,OAAO,IAAI,KAAK,GAAG,gBAAgB,cAAc,WAAW;oBAClE,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO;gBAC/C;;YAEA,MAAM,UAAU,KAAK,GAAG,IAClB,YAAY,MAAM;gDAAC,CAAC,IAAmB,MAAM;kDAAS,EAAE;YAE9D,MAAM,UAAU,KAAK,GAAG,IAClB,YAAY,MAAM;gDAAC,CAAC,IAAmB,MAAM;kDAAS,EAAE;YAG9D,IAAI,WAAW,SAAS;gBACtB,+BAA+B;oBAC7B,MAAM,OAAO;oBACb,IAAI,OAAO;gBACb;YACF;YAEA,MAAM,cACJ,kBAAkB,SAAS,IAAI,KAAK,GAAG,IAAI,oBAAoB;YACjE,MAAM,cACJ,kBAAkB,SAAS,IAAI,KAAK,GAAG,IAAI,oBAAoB;YAEjE,IAAI,kBAAkB,SAAS,GAAG;gBAChC,YAAY;oBAAC;oBAAa;iBAAY;gBACtC,iBAAiB;oBAAC;oBAAa;iBAAY;YAC7C;QACF;+BAAG;QAAC;QAAY;KAAiB;IAEjC,MAAM,eACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,SAAS,KACzD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC,EAAE,SAAS,KACjD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,SAAS,KAChD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAAS,KAC3D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,EAAE,SAAS,KACxD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAAS,KAC3D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAAS,KAC3D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,KAC1D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,KACzD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,SAAS,KACxD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,SAC/C,WAAW,IAAI,OACX,+BAA+B;IACvC,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE,KAAK,WAC7D,MACF,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE,GAAG,WAC3D,MACF,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IACpE,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IACpE;IAEF,MAAM,aAAa,kBACjB,6LAAC,6MAAA,CAAA,aAAU;YACT,MAAM,eAAe,YAAY;YACjC,OAAO,eAAe,YAAY;YAClC,WAAU;YACV,SAAS;;;;;;IAIb,IAAI,uBACF,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,WAAW;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;QAChB;+BAAG,EAAE;IAEL,MAAM,0BAA0B,CAAC;QAC/B,iBAAiB,CAAC;IACpB;IAEA,MAAM,oBAAoB;QACxB,MAAM,aAAa,aAAa,iBAAiB;QACjD,IAAI,YAAY;YACd,oBAAoB;QACtB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS;gBACX,IAAI;oBACF,MAAM;+DAAiB;4BACrB,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;wBAC1B;;oBACA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;gBACA,aAAa,OAAO,CAAC,WAAW;YAClC;QACF;+BAAG;QAAC;KAAQ;IAEZ,MAAM,yBAAyB;QAC7B,iBAAiB;QACjB,4BAA4B;IAC9B;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,kBACA,CAAC,GACD;gBAAE,SAAS;YAAe;YAE5B,IAAI,SAAS,EAAE,EAAE;gBACf,8DAA8D;gBAC9D,MAAM,OAAiC,MAAM,SAAS,IAAI;gBAC1D,IAAI,MAAM,WAAW;oBACnB,aAAa,MAAM,OAAO,CAAC,MAAM,aAAa,KAAK,SAAS,GAAG,EAAE;gBACnE;gBACA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM,gBACF,+BACA;oBACJ,YAAY;wBACV,OAAO,MAAM,WAAW,UAAU;wBAClC,SAAS,gBAAgB,wBAAwB;oBACnD;gBACF;gBACA,OAAO,MAAM;YACf,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B,SAAS,UAAU;YAClE;QACF,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,yCAAyC,QACzC,iCAAiC;gBAEvC,eAAe;YACjB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,4BAA4B,OAAO,EAAE;YACvC,0CAA0C;YAC1C;QACF;QACA,4BAA4B,OAAO,GAAG;QACtC,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,WAAW,MAAM,KAAK,KAAK,MAAM,EACnD,CAAC,GACD;gBACE,SAAS;YACX;YAEF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAoC,MAAM,SAAS,IAAI;gBAC7D,cAAc,MAAM,OAAO,CAAC,MAAM,cAAc,MAAM,aAAa,EAAE;gBACrE,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV;wBACA,SAAS,gBACL,oCACA;oBACN;gBACF;gBACA,IAAI,sBAAsB;oBACxB,CAAA,GAAA,6IAAA,CAAA,oCAAiC,AAAD,EAC9B,CAAC,aAAa,EAAE,KAAK,CAAC,iBAAiB,cAAc,EAAE,GACvD,iBAAiB;gBAErB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B,UAAU;YACzD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,8CAA8C,QAC9C,sCAAsC;gBAE5C,eAAe;YACjB;QACF,SAAU;YACR,eAAe;YACf,WAAW;YACX,0BAA0B;YAC1B,4BAA4B,OAAO,GAAG,OAAO,uBAAuB;QACtE;IACF;IAEA,MAAM,gBAAgB,CACpB,MAEA,CAAC,CAAC,OAAO,OAAO,IAAI,CAAC,MAAM,WAAW,KAAK,KAAK,gBAAgB;IAElE,MAAM,qCACJ,CAAC,gBAAgB,cAAc,gBAC3B,kBACA;IAEN,MAAM,gBACJ,oCAAoC,uBAAuB;IAC7D,MAAM,kBAAkB,gBACpB,aAAa,KACb,iBAAiB;IAErB,MAAM,EACJ,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,iBAAiB,EACjB,UAAU,EACV,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,qBAAqB,EACtB,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EACjB,iBAAiB,OACjB,iBACA,IAAI,OAAO,WAAW,IACtB;QAAC;KAAgB;IAGnB,IAAI,aAAa,sBAAsB;IAEvC,MAAM,gCAAgC,OAAO;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,UAAU,eAAe,CAAC,EAC5C,CAAC,GACD;gBAAE,SAAS;YAAuB;YAEpC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAoC,MAAM,SAAS,IAAI;gBAC7D,mBAAmB,MAAM,WAAW;gBACpC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV;wBACA,SAAS,gBACL,gCACA;oBACN;gBACF;gBACA,IAAI,MAAM,YAAY,MAAM;oBAC1B,eAAe;gBACjB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,4BAA4B,SAAS,UAAU;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,6CAA6C,QAC7C,qCAAqC;gBAE3C,eAAe;YACjB;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,4BAA4B;QAC9B;+BAAG;QAAC;KAAsB;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,iBAAiB,aAAa,oBAAoB,MAAM;gBAC1D,4DAA4D;gBAC5D,8BAA8B;YAChC,OAAO,IAAI,CAAC,eAAe;gBACzB,WAAW,WAAW,KACpB,iBAAiB,IAAI;4CAAC,CAAC;wBACrB,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;4BACrC,WAAW;wDAAI,CAAC;oCACd,IACE,SAAS,YAAY,kBAAkB,WAAW,eAClD;wCACA,mBAAmB;oCACrB;gCACF;;wBACF;oBACF;;YACJ;YACA,IACE,CAAC,sBAAsB,CAAC,aAAa,KACrC,oBAAoB,QACpB,sBAAsB,QACtB,eACA;gBACA,oBAAoB,gBAAgB,UAAU;gBAC9C,IACE,CAAC,qBACD,sBAAsB,QACtB,wBACA;oBACA,8BAA8B,gBAAgB,UAAU;oBACxD,0BAA0B,OAAO,2DAA2D;gBAC9F;YACF,OAAO,IAAI,iBAAiB,YAAY,UAAU,CAAC,eAAe;gBAChE,mBAAmB,gBAAgB,UAAU;YAC/C,OAAO;gBACL,mBAAmB,aAAa;YAClC;QACF;+BAAG;QACD;QACA;QACA,iBAAiB;QACjB;QACA;KACD;IACD,4FAA4F;IAC5F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IACE,CAAC,AAAC,eAAe,YAAY,MAAM,GAAG,KACnC,eAAe,OAAO,eAAe,IAAK,KAC7C,oBAAoB,MACpB;gBACA,oBAAoB,gBAAgB,UAAU;gBAC9C,IAAI,iBAAiB,aAAa,oBAAoB,MAAM;oBAC1D,8BAA8B;gBAChC;YACF;QACF;+BAAG;QAAC;QAAa;QAAY;KAAgB;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,uEAAuE;YACvE,IAAI,CAAC,iBAAiB,mBAAmB,CAAC,oBAAoB;gBAC5D,oBAAoB,iBAAiB;YACvC;QACF;+BAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,MAAM,mBACJ,iBAAiB,YAAY,iBAAiB,WAAW;oBAC3D,IAAI,kBAAkB;wBACpB,SAAS,QAAQ,GAAG,4BAA4B,CAAC;oBACnD;gBACF;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YACxC;wCAAO;oBACL,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;+BAAG;QAAC,iBAAiB;QAAY;QAAW;KAAS;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,mBACJ,iBAAiB,YAAY,iBAAiB,WAAW;YAE3D,IAAI,CAAC,kBAAkB;YAEvB,MAAM,EACJ,4BAA4B,EAC5B,qBAAqB,EACrB,cAAc,EACd,WAAW,EACZ,GAAG,SAAS,QAAQ,IAAI,uDAAuD;YAEhF,IACE,gBAAgB,WAChB,gBAAgB,YAAY,kBAC5B;gBACA,6BAA6B,gBAAgB;YAC/C,EAAE,gDAAgD;YAElD,MAAM,SAAS,sBAAsB;YACrC,IAAI,QAAQ;gBACV,MAAM,OAAO,OAAO,IAAI,CAAC,8HAAA,CAAA,gBAAa;gBACtC,KAAK,MAAM,KAAK,KACd,MAAM;4CAAQ,CAAC;wBACb,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;4BAC7B,eAAe,KAAK,MAAM,CAAC,IAAI;wBACjC;oBACF;;YACJ,OAAO;gBACL,YAAY,eAAe,CAAC,EAAE,EAAE,IAAI,cAAc;YACpD;YAEA,eAAe,OAAO,GAAG;QAC3B;+BAAG;QAAC,iBAAiB;QAAY;QAAW;KAAS;IAErD,MAAM,aAAa,CAAC,MAAc,OAAO;QACvC,IAAI,YAAmC;QACvC,IAAI,WAAW,GAAG,KAAK,OAAO,WAAW,SAAS,KAAK,OAAO;YAC5D,YAAY;QACd;QAEA,MAAM,aAAa;eAAI;SAAU,EAAE,KAAK,CAAC,GAAG;YAC1C,IAAI,GAAG,QAAQ,GAAG,OAAO,OAAO,cAAc,QAAQ,CAAC,IAAI;YAC3D,IAAI,GAAG,QAAQ,GAAG,OAAO,OAAO,cAAc,QAAQ,IAAI,CAAC;YAC3D,OAAO;QACT;QAEA,cAAc;YAAE;YAAK;QAAU;QAC/B,aAAa;IACf;IACA,MAAM,WAAW,CAAC,OAAgB,WAAW,IAAI;QAC/C,IACE,UAAU,QACV,UAAU,aACV,UAAU,mHAAA,CAAA,8BAA2B,CAAC,OAAO,IAC7C,UAAU,IAEV,OAAO;QAET,MAAM,SAAS,WAAW;QAC1B,OAAO,MAAM,UAAU,WAAW;IACpC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,YAA4B;QAChC,IAAI,eAAe,QAAQ,OAAO,eAAe,cAAc,OAAO;YACpE,YAAY;QACd;QAEA,MAAM,WAAW,CAAC;YAChB,MAAM,OAAO,WAAW;YAExB,MAAM,sBAAsB,IAAI,CAAC,uBAAuB,IAAI,CAAC;YAC7D,OAAQ;gBACN,KAAK,mHAAA,CAAA,8BAA2B,CAAC,IAAI;oBACnC,OAAO,KAAK,IAAI,EAAE,iBAAiB;gBACrC,KAAK;oBACH,OAAO,qBAAqB,gBAAgB;gBAC9C,KAAK;oBACH,OAAO,qBAAqB,iBAAiB;gBAC/C,KAAK;oBACH,OAAO,qBAAqB,mBAAmB;gBACjD,KAAK;oBACH,OAAO,qBAAqB,CAAC,mBAAmB,IAAI;gBACtD,KAAK;oBACH,OAAO,qBAAqB,uBAAuB;gBACrD,KAAK;oBACH,OAAO,qBAAqB,CAAC,6BAA6B,IAAI;gBAChE,KAAK;oBACH,OAAO,qBAAqB,2BAA2B;gBACzD,KAAK;oBACH,OAAO,qBAAqB,2BAA2B;gBACzD,KAAK;oBACH,OAAO,qBAAqB,0BAA0B;gBACxD,KAAK;oBAAgB;wBACnB,MAAM,eAAe,MAAM;wBAC3B,mDAAmD;wBACnD,IACE,CAAC,gBACD,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,OAAO,EAEpD,OAAO;wBACT,MAAM,OAAO,IAAI,KAAK;wBACtB,yBAAyB;wBACzB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;wBAElC,OAAO,MAAM;oBACf;gBACA,KAAK,mHAAA,CAAA,8BAA2B,CAAC,IAAI;oBACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;oBACxB,MAAM,UAAU,CAAC,IACf,KAAK,MAAM,mHAAA,CAAA,8BAA2B,CAAC,OAAO;oBAChD,IAAI,QAAQ,SAAS,QAAQ,QAC3B,OAAO,GAAG,KAAK,EAAE,EAAE,OAAO,CAAC,WAAW;oBACxC,IAAI,QAAQ,OAAO,OAAO,KAAK,WAAW;oBAC1C,IAAI,QAAQ,QAAQ,OAAO,MAAM,WAAW;oBAC5C,OAAO;gBACT,KAAK;oBACH,OAAO,qBAAqB,0BAA0B;gBACxD,KAAK;oBACH,OAAO,IAAI,CAAC,kBAAkB;gBAChC,KAAK;oBACH,OAAO,SAAS,IAAI,CAAC,0BAA0B,EAAE;gBACnD;oBACE,OAAO;YACX;QACF;QAEA,MAAM,mBAAmB;eAAI;SAAuB,EAAE,KAAK,CAAC,GAAG;YAC7D,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS,SAAS;YAExB,MAAM,UAAU,WAAW,QAAQ,WAAW;YAC9C,MAAM,UAAU,WAAW,QAAQ,WAAW;YAE9C,IAAI,WAAW,SAAS,OAAO;YAC/B,IAAI,SAAS,OAAO,GAAG,oBAAoB;YAC3C,IAAI,SAAS,OAAO,CAAC;YAErB,UAAU;YACV,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gBAC5D,OAAO,cAAc,QACjB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;YAC3B;YAEA,QAAQ;YACR,IAAI,kBAAkB,QAAQ,kBAAkB,MAAM;gBACpD,OAAO,cAAc,QACjB,OAAO,OAAO,KAAK,OAAO,OAAO,KACjC,OAAO,OAAO,KAAK,OAAO,OAAO;YACvC;YAEA,sCAAsC;YACtC,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gBAC5D,OAAO,cAAc,QAAQ,SAAS,SAAS,SAAS;YAC1D;YAEA,OAAO;QACT;QAEA,iBAAiB;YAAE;YAAK;QAAU;QAClC,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,0BAA0B;QAC1B,iBAAiB,cAAc;QAC/B,yBAAyB,sBAAsB;QAC/C,mBAAmB,WAAW;QAC9B,QAAQ;QACR,iBAAiB;YACf,KAAK;YACL,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACjC,OAAO,MAAM,OAAO,CAAC,0BACjB,uBAAuB,MAAM;0DAAC,CAAC,IAC7B,EAAE,cAAc,CAAC,KAAK,EAAE,cAAc,SAAS,OAAO,WAAW;2DAEnE,EAAE;QACR;iDAAG;QAAC;QAAwB;KAAO;IAEnC,sBAAsB;IACtB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAClC,MAAM,YAAY,QAAQ,IAAI,QAAQ;YACtC,MAAM,QAAQ,CAAC,OAAO,CAAC,IAAI;YAC3B,OAAO,mBAAmB,KAAK,CAAC,OAAO,QAAQ;QACjD;kDAAG;QAAC;QAAoB;QAAM;KAAM;IAEpC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC9B,IAAI,CAAC,eAAe,OAAO;YAC3B,OAAO,UAAU,MAAM;uDAAC,CAAC,UACvB,CAAC,QAAQ,KAAK,IAAI,EAAE,EAAE,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW;;QAE1E;8CAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,WAAW,qBAAqB;YACnC,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI;YACF,WAAW;YACX,qBAAqB;YACrB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,sBAAsB,EAAE,UAAU,mBAAmB,EAAE,EACxD,CAAC,GACD;gBAAE,SAAS;YAAY;YAEzB,IAAI,SAAS,EAAE,EAAE;gBACf,8DAA8D;gBAC9D,MAAM,OAAY,MAAM,SAAS,IAAI;gBACrC,kBAAkB;gBAClB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,aAAa,WAAW;wBACxB,SAAS,gBAAgB,qBAAqB;oBAChD;gBACF;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,UAAU;YACrD;QACA,8DAA8D;QAChE,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,gBACI,wCAAwC,QACxC,iCAAiC;gBAEvC,eAAe;YACjB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,oBAAoB,iBAAiB,cAAc,CAAC,aAAa,EAAE;QACzE,IAAI,eAAe;YACjB,IAAI,iBAAiB;gBACnB,wBAAwB;gBACxB,mBAAmB,iBAAiB;gBACpC,gBAAgB;gBAChB,uBAAuB;gBACvB,wBAAwB;gBACxB,cAAc;YAChB,OAAO,IAAI,WAAW,UAAU,eAAe;gBAC7C,wBAAwB;gBACxB,mBAAmB;gBACnB,gBAAgB;gBAChB,uBAAuB;gBACvB,wBAAwB;gBACxB,cAAc;YAChB;QACF;QACA,oBAAoB;QACpB,8BAA8B;IAChC;IAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC9B,OAAO,qBAAqB;QAC9B;8CAAG;QAAC;QAAmB;KAAY;IAEnC,MAAM,gDAAgD;QACpD,uBAAuB;IACzB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,eAAe,IAAI,OAAO,WAAW;YAE3C,IAAI,iBAAiB,WAAW;gBAC9B,MAAM,aAAa;gBACnB,IAAI;oBACF,MAAM,WAAW,aAAa,OAAO,CAAC;oBACtC,MAAM,OAA+B,WACjC,KAAK,KAAK,CAAC,YACX,CAAC;oBACL,MAAM,sBAAsB,UAAU,WAAW,IAAI,cAAc;oBACnE,IAAI,CAAC,oBAAoB,GAAG;oBAC5B,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAClD,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D;YACF;YAEA,IAAI,iBAAiB,YAAY;gBAC/B,MAAM,aAAa;gBACnB,IAAI;oBACF,MAAM,WAAW,cAAc,QAAQ;oBACvC,MAAM,OAA+B,WACjC,KAAK,KAAK,CAAC,YACX,CAAC;oBAEL,MAAM,uBAAuB,iBAAiB,YAAY,eAAe,cAAc;oBACvF,IAAI,CAAC,qBAAqB,GAAG;oBAE7B,cAAc,QAAQ,YAAY,KAAK,SAAS,CAAC;gBACnD,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,6CAA6C;gBAC7D;YACF;QACF;+BAAG;QACD;QACA;QACA,iBAAiB;QACjB;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,iBAAiB,cAAc,CAAC,mBAAmB;YAExD,MAAM,MAAM,gBACR,2BACA;YAEJ,IAAI;gBACF,MAAM,SAAS,cAAc,QAAQ;gBACrC,IAAI,CAAC,QAAQ;gBAEb,MAAM,SAAS,KAAK,KAAK,CAAC;gBAE1B,MAAM,YAAY,CAChB,iBAAiB,cACjB,aACA,EACF,EAAE,WAAW;gBACb,MAAM,iBAAiB,MAAM,CAAC,UAAU;gBAExC,IAAI,CAAC,gBAAgB;gBAErB,MAAM,mBAAmB,mBAAmB,wBAAwB;gBACpE,MAAM,kBACJ,uBAAuB,gBACvB,uBAAuB,gBACvB;gBAEF,IAAI,oBACF,uBAAuB,WAAW,8HAAA,CAAA,2BAAwB,CAAC,SAAS;gBAEtE,MAAM,mBACJ,IAAI,KAAK,gBAAgB,OAAO,MAChC,IAAI,KAAK,kBAAkB,OAAO;gBAEpC,IACE,IAAI,KAAK,gBAAgB,OAAO,KAC9B,IAAI,KAAK,iBAAiB,OAAO,MACnC,mBACA;oBACA,MAAM,oBACJ,iBAAiB,cAAc,CAAC,aAAa,EAAE;oBACjD,oBAAoB;oBAEpB,CAAA,GAAA,6IAAA,CAAA,oCAAiC,AAAD,EAC9B,WACA,iBAAiB,OACjB;oBAGF,oBAAoB;oBACpB,8BAA8B;gBAChC;gBAEA,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8CAA8C;YAC9D;QACF;+BAAG;QACD,iBAAiB;QACjB;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB;QACvB,cAAc,CAAC;QACf,mBAAmB,CAAC;IACtB;IAEA,MAAM,oBAAoB;QACxB,YAAY,eAAe,CAAC,EAAE,EAAE;QAChC,YAAY,UAAU,CAAC,EAAE,EAAE;QAC3B,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAC3B,IAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE;6CACvB;QAAC;KAAW;IAGd,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDACjC,IAAM;mBACD,IAAI,IACL,YACI;gEAAI,CAAC,IAAM,GAAG,gBAAgB;gEAC9B;gEACA,CAAC,kBACC,oBAAoB,QACpB,oBAAoB,aACpB,oBAAoB;;aAG7B;mDACD;QAAC;KAAW;IAGd,MAAM,yBAAyB,CAAC;QAC9B,kBAAkB;IACpB;IAEA,MAAM,+BAA+B,CACnC,OACA;QAEA,wBAAwB,OAA0B;IACpD;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,qBAAqB,YAAY,SAAS,GAAG;gBAC/C,MAAM,WACJ,cACA,YAAY;4CAAO,CAAC;wBAClB,MAAM,uBACJ,OAAO,WAAW,gBAAgB,qBAAqB,WACrD,YACF,CAAC,WAAW,gBAAgB,qBAAqB,OAAO,kBACtD,mHAAA,CAAA,8BAA2B,CAAC,OAAO,IACnC,WAAW,gBAAgB,qBAAqB,OAAO,kBACrD,QAAQ,IACR,QACA;wBACN,MAAM,iBAAiB,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EACtC,WAAW,gBAAgB,OAAO,iBAAiB,GAAG,IAAI;wBAE5D,MAAM,gBAAgB,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EACrC,WAAW,gBAAgB,MAAM,iBAAiB;wBAEpD,MAAM,0BACJ,WAAW,gBAAgB,mBAAmB;wBAChD,MAAM,2BACJ,WAAW,gBAAgB,qBAAqB;wBAClD,MAAM,kBACJ,WAAW,gBAAgB,2BAA2B;wBAExD,6CAA6C;wBAC7C,IAAI;wBACJ,IAAI,oBAAoB,QAAQ,oBAAoB,WAAW;4BAC7D,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,QAAQ;wBACvD,OAAO,IAAI,oBAAoB,mHAAA,CAAA,8BAA2B,CAAC,IAAI,EAAE;4BAC/D,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,QAAQ;wBACvD,OAAO,IAAI,oBAAoB,mHAAA,CAAA,8BAA2B,CAAC,OAAO,EAAE;4BAClE,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,UAAU;wBACzD,OAAO;4BACL,iBAAiB,mHAAA,CAAA,8BAA2B,CAAC,QAAQ;wBACvD;wBAEA,4CAA4C;wBAC5C,IAAI,qBAAqB;wBACzB,IAAI,CAAC,4BAA4B,6BAA6B,IAAI;4BAChE,qBAAqB,mHAAA,CAAA,4BAAyB,CAAC,mBAAmB;wBACpE,OAAO,IACL,yBAAyB,WAAW,IAAI,SAAS,YACjD;4BACA,qBAAqB,mHAAA,CAAA,4BAAyB,CAAC,SAAS;wBAC1D,OAAO,IACL,yBAAyB,WAAW,IAAI,SAAS,YACjD;4BACA,qBAAqB,mHAAA,CAAA,4BAAyB,CAAC,SAAS;wBAC1D,OAAO,IACL,yBAAyB,WAAW,IAAI,SAAS,gBACjD;4BACA,qBAAqB,mHAAA,CAAA,4BAAyB,CAAC,cAAc;wBAC/D,OAAO;4BACL,qBAAqB;wBACvB;wBAEA,MAAM,gBACJ,WAAW,gBAAgB,MAAM,iBAAiB;wBACpD,MAAM,kBACJ,WAAW,gBAAgB,wBAAwB,QAAQ,iBAC3D;wBACF,MAAM,sBACJ,WAAW,gBAAgB,CACzB,mHAAA,CAAA,8BAA2B,CAAC,mBAAmB,CAChD,EAAE,cAAc,QAAQ,MAAM;wBAEjC,MAAM,wBACJ,WAAW,gBAAgB,gBAAgB;wBAE7C,mCAAmC;wBACnC,MAAM,sBACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,WAAW,KAC7D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAChD;wBAGJ,MAAM,eACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC,EAAE,WAAW,KACnD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC,EAAE,SAAS;wBAEnD,MAAM,cACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,WAAW,KAClD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,SAAS;wBAElD,MAAM,wBACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,WAAW,KAC7D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAChD;wBAGJ,MAAM,mBACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,EAAE,WAAW,KAC1D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,EAAE,SAC7C;wBAGJ,MAAM,uBACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,WAAW,KAC7D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,EAAE,SAChD;wBAGJ,MAAM,oBACJ,uBACE,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,QACvD,MAEJ,uBACE,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,QACvD;wBAGN,MAAM,oBACJ,WAAW,gBAAgB;wBAC7B,MAAM,cACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,IAAI;wBAC1D,MAAM,cACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,IACpD,eAAe,CAAC,EAAE;wBAEpB,MAAM,kBACJ,sBAAsB,QAAQ,sBAAsB,YAChD,gBAAgB,KAAK,gBAAgB,eAAe,CAAC,EAAE,GACvD,qBAAqB,eAAe,CAAC,EAAE,IACvC,qBAAqB,eAAe,CAAC,EAAE;wBAE7C,MAAM,gBAAgB;kEAAC;gCACrB,MAAM,aAAa,eAAe,IAAI;gCACtC,MAAM,eACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,IAAI,EAAE;gCAEtD,IAAI,eAAe,IAAI;oCACrB,yBAAyB;oCACzB,IAAI,cAAc,WAAW,GAAG;wCAC9B,uBAAuB,MAAM,2CAA2C;oCAC1E;oCACA,sEAAsE;oCACtE,OAAO;gCACT,OAAO;oCACL,yBAAyB;oCACzB,IAAI,cAAc,WAAW,GAAG;wCAC9B,uBAAuB,MAAM,2CAA2C;wCACxE,OAAO;oCACT;oCACA,yEAAyE;oCACzE,MAAM,cAAc,WAAW,WAAW;oCAC1C,MAAM,SACJ,cAAc,SAAS,mHAAA,CAAA,8BAA2B,CAAC,IAAI,KACvD,eAAe,SAAS;oCAC1B,MAAM,WACJ,cAAc,SAAS,mHAAA,CAAA,8BAA2B,CAAC,MAAM,KACzD,iBAAiB,SAAS;oCAE5B,OAAO,UAAU;gCACnB;4BACF;yBAAC;wBAED,MAAM,0BAA0B;4EAAC;gCAC/B,MAAM,OACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE;gCAC5D,MAAM,KACJ,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE;gCAE5D,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO;gCACzB,IAAI,CAAC,uBAAuB,OAAO;gCAEnC,SAAS,UAAU,IAAU;oCAC3B,OAAO,IAAI,KACT,KAAK,WAAW,IAChB,KAAK,QAAQ,IACb,KAAK,OAAO;gCAEhB;gCAEA,MAAM,gBAAgB,UAAU,IAAI,KAAK;gCACzC,MAAM,WAAW,OAAO,UAAU,IAAI,KAAK,SAAS;gCACpD,MAAM,SAAS,KAAK,UAAU,IAAI,KAAK,OAAO;gCAE9C,IAAI,YAAY,UAAU,WAAW,QAAQ;oCAC3C,OAAO;gCACT;gCAEA,wEAAwE;gCACxE,IAAI,YAAY,gBAAgB,UAAU,OAAO;gCACjD,IAAI,UAAU,gBAAgB,QAAQ,OAAO;gCAE7C,OAAO;4BACT;yBAAC;wBAED,OACE,gBACA,eACA,yBACA,oBACA,uBACA,wBACA,qBACA,iBACA,2BACA;oBAEJ;;gBACF,0BAA0B;YAC5B,OAAO;gBACL,IAAI,CAAC,qBAAqB,iBAAiB,YAAY;oBACrD,2BAA2B,iBAAiB;gBAC9C;gBACA,0BAA0B;YAC5B;QACF;+BAAG;QACD;QACA;QACA,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,KAAK,CAAC;QACtC,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC;QACrC,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;QAChD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC;QAC7C,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;QAChD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;QAChD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC;QAClD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE;QAC1D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC,EAAE;QAC1D,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC;QAC9C,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;QAC/C;QACA,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;QAC/C,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,CAAC;QACxD,SAAS,CAAC,mHAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;KAChD;IAED,MAAM,cAA6B;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,mBAAmB;QACnB;QACA,yBAAyB;QACzB,oBAAoB,CAAC,MACnB,eAAe,mHAAA,CAAA,wBAAqB,CAAC,iBAAiB,EAAE;QAC1D,0BAA0B,CACxB;YAEA,MAAM,gBACJ,OAAO,UAAU,aAAa,MAAM,yBAAyB;YAC/D,eACE,mHAAA,CAAA,wBAAqB,CAAC,uBAAuB,EAC7C;QAEJ;QACA;QACA,kBAAkB,CAAC,MACjB,eAAe,mHAAA,CAAA,wBAAqB,CAAC,cAAc,EAAE;QACvD;QACA;IACF;IAEA,qBACE,6LAAC,4HAAA,CAAA,uBAAoB;;0BACnB,6LAAC,mJAAA,CAAA,UAAgB;;;;;0BACjB,6LAAC;gBAAI,WAAU;;oBACZ,0CACC,6LAAC,8IAAA,CAAA,UAAc;wBACb,SAAS;wBACT,kBAAkB;4BAChB,iBAAiB;4BACjB,4BAA4B;wBAC9B;wBACA,eAAe,iBAAiB;;;;;;oBAGnC,CAAC,aAAa,CAAC,8BACd,6LAAC;wBAAG,WAAU;kCAAsC;;;;;+BAIpD,iCACE;kCACE,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAG/C,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB;wCACF;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCASX,6LAAC;wBAAI,WAAU;;4BAEZ,mBAAmB,CAAC,aAAa,CAAC,+BACjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM;;4CAChB;4CACc;0DACb,6LAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC;kDACC,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;;;;;;;;;;;oCAGb,WAAW,CAAC,gBAAgB,MAAM,iBACjC,6LAAC,yHAAA,CAAA,UAAO;wCAAC,QAAO;;;;;6DAEhB,6LAAC;wCAAG,WAAU;kDACX,iBAAiB,IAAI,CAAC,wBACrB,6LAAC,2IAAA,CAAA,UAAW;gDACV,SAAS;gDACT,oBAAoB;gDACpB,iBAAiB;gDACjB,WAAW;gDAEX,eAAe;gDACf,kBAAkB;+CAFb,QAAQ,KAAK;;;;;;;;;;;;;;;;0CAU9B,6LAAC;gCAAI,WAAU;0CACZ,WAAW,CAAC,gCACX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yHAAA,CAAA,UAAO;wCAAC,QAAO;;;;;;;;;;2CAEhB,mBAAmB,CAAC,aAAa,uCACnC;;sDACE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;;oDAET,4BAAc,6LAAC;kEAAE;;;;;;kEAClB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,6LAAC;4CACC,WAAW,CAAC,2BAA2B,EACrC,aAAa,oBAAoB,gBACjC;;gDAED,mBAAmB,4BAClB,6LAAC;oDAAG,WAAU;;wDAA6B;wDACnB,iBAAiB;wDAAM;;;;;;;gDAIhD,4BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,6LAAC;4DAAG,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAW,CAAC,0CAA0C,CAAC;;8DAC1D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;wDAAC,OAAO;wDAAW,eAAe;wDAAc,WAAU;kEAC7D,cAAA,6LAAC,4HAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,6LAAC,4HAAA,CAAA,cAAW;oEAAC,OAAM;8EAAmB;;;;;;8EACtC,6LAAC,4HAAA,CAAA,cAAW;oEAAC,OAAM;8EAAgB;;;;;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;wDACZ,CAAC,aACE,iCACE,6LAAC;4DAAG,WAAU;;gEAA0C;gEAChC,iBAAiB;gEAAM;;;;;;mEAGjD,qBACA,cACA,YAAY,SAAS,KACrB;sEACJ,6LAAC;4DACC,WAAU;4DACV,IAAG;sEAGF,cAAc,cACb,CAAC,WACD,mBACA,iBAAiB,4BACf,6LAAC,gJAAA,CAAA,UAAgB;gEACf,WAAW,gBAAgB,UAAU;gEACrC,SAAS,WAAW,SAAS,MAAM,MAAM,SAAS;gEAClD,eAAe;gEACf,eAAe;gEACf,yBAAyB;gEACzB,+CACE;gEAEF,0BAA0B;gEAC1B,cAAc;gEACd,iBAAiB;gEACjB,eAAe,iBAAiB;gEAChC,UAAU,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sDAM5C,6LAAC,4HAAA,CAAA,OAAI;4CAAC,OAAO;4CAAW,eAAe;4CAAc,WAAU;;8DAC7D,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAmB,WAAU;8DAC9C,cAAA,6LAAC,mIAAA,CAAA,UAAiB;;;;;;;;;;8DAGpB,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAgB,WAAU;;wDAC1C,iCACC,6LAAC,kJAAA,CAAA,UAAc;4DACb,qBAAqB;4DACrB,mBAAmB;4DACnB,SAAS;4DACT,cAAc;4DACd,qBAAqB;4DACrB,iBAAiB;4DACjB,YAAY;4DACZ,eAAe;4DACf,mBAAmB;4DACnB,sBAAsB;4DACtB,aAAa;4DACb,eAAe,iBAAiB;4DAChC,qBAAqB;4DACrB,SAAS,WAAW;4DACpB,iBAAiB;4DACjB,WAAW;4DACX,0BAA0B;4DAC1B,aAAa;;;;;;sEAIjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;oEACP,eAAe,CAAC;wEACd,SAAS,OAAO;wEAChB,QAAQ;oEACV;;sFAEA,6LAAC,8HAAA,CAAA,gBAAa;4EAAC,WAAU;sFACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,6LAAC,8HAAA,CAAA,gBAAa;sFACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;0FACT;oFAAC;oFAAI;oFAAK;oFAAK;oFAAK;iFAAI,CAAC,GAAG,CAAC,CAAC,qBAC7B,6LAAC,8HAAA,CAAA,aAAU;wFAET,OAAO;;4FAEN;4FAAK;;uFAHD;;;;;;;;;;;;;;;;;;;;;8EASf,6LAAC;;sFACC,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAS;4EACT,SAAS,IAAM,QAAQ,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;4EAC9C,UAAU,SAAS;4EACnB,WAAU;sFAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;sFAEd,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAS;4EACT,SAAS,IACP,QAAQ,CAAC,IACP,IAAI,QAAQ,oBAAoB,SAAS,IAAI,IAAI;4EAGrD,WAAU;4EACV,UACE,KAAK,IAAI,CAAC,oBAAoB,SAAS,WAAW;sFAGpD,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAOvB,mBAAmB,0BACrB,6LAAC;oCAAI,WAAU;;wCACZ,sCACC,6LAAC,6HAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;sDAGd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;;oDAET,4BAAc,6LAAC;kEAAE;;;;;;kEAClB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAGzB,iBAAiB,mCAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,6LAAC;4DAAG,WAAU;sEAAU;;;;;;;;;;;;8DAE1B,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAc;;;;;;;;;;;;sDAMrD,6LAAC;4CACC,WAAW,GAAG,eAAe;4CAC7B,IAAG;;8DAEH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;gEAAC,OAAO;gEAAW,eAAe;gEAAc,WAAU;0EAC7D,cAAA,6LAAC,4HAAA,CAAA,WAAQ;oEAAC,WAAU;;sFAClB,6LAAC,4HAAA,CAAA,cAAW;4EAAC,OAAM;sFAAmB;;;;;;sFACtC,6LAAC,4HAAA,CAAA,cAAW;4EAAC,OAAM;sFAAgB;;;;;;;;;;;;;;;;;;;;;;sEAIzC,6LAAC;4DAAI,WAAU;;gEACZ,mCACC,6LAAC;oEAAI,WAAU;8EAAQ;;;;;;gEAGxB,CAAC,iBAAiB,cACjB,cAAc,UAAU,KACxB,CAAC,WACD,mBACA,gBAAgB,UAAU,kBACxB,6LAAC,gJAAA,CAAA,UAAgB;oEACf,WAAW,iBAAiB;oEAC5B,SAAS,WAAW,SAAS,MAAM,MAAM,SAAS;oEAClD,eAAe;oEACf,eAAe;oEACf,yBAAyB;oEACzB,+CACE;oEAEF,0BAA0B;oEAC1B,cAAc;oEACd,iBAAiB;oEACjB,eAAe,iBAAiB;oEAChC,UAAU,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;8DAK1C,6LAAC,4HAAA,CAAA,OAAI;oDAAC,OAAO;oDAAW,eAAe;oDAAc,WAAU;;sEAC7D,6LAAC,4HAAA,CAAA,cAAW;4DAAC,OAAM;4DAAmB,WAAU;sEAC9C,cAAA,6LAAC,mIAAA,CAAA,UAAiB;;;;;;;;;;sEAGpB,6LAAC,4HAAA,CAAA,cAAW;4DAAC,OAAM;4DAAgB,WAAU;;gEAC1C,iCACC,6LAAC,kJAAA,CAAA,UAAc;oEACb,qBAAqB;oEACrB,mBAAmB;oEACnB,SAAS;oEACT,cAAc;oEACd,qBAAqB;oEACrB,iBAAiB;oEACjB,YAAY;oEACZ,eAAe;oEACf,mBAAmB;oEACnB,sBAAsB;oEACtB,aAAa;oEACb,eAAe,iBAAiB;oEAChC,qBAAqB;oEACrB,SAAS,WAAW;oEACpB,iBAAiB;oEACjB,WAAW;oEACX,0BAA0B;oEAC1B,aAAa;;;;;;8EAIjB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,8HAAA,CAAA,SAAM;4EACL,OAAO;4EACP,eAAe,CAAC;gFACd,SAAS,OAAO;gFAChB,QAAQ;4EACV;;8FAEA,6LAAC,8HAAA,CAAA,gBAAa;oFAAC,WAAU;8FACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,6LAAC,8HAAA,CAAA,gBAAa;8FACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;kGACT;4FAAC;4FAAI;4FAAK;4FAAK;4FAAK;yFAAI,CAAC,GAAG,CAAC,CAAC,qBAC7B,6LAAC,8HAAA,CAAA,aAAU;gGAET,OAAO;;oGAEN;oGAAK;;+FAHD;;;;;;;;;;;;;;;;;;;;;sFASf,6LAAC;;8FACC,6LAAC,8HAAA,CAAA,SAAM;oFACL,SAAS;oFACT,SAAS,IAAM,QAAQ,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;oFAC9C,UAAU,SAAS;oFACnB,WAAU;8FAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;8FAEd,6LAAC,8HAAA,CAAA,SAAM;oFACL,SAAS;oFACT,SAAS,IACP,QAAQ,CAAC,IACP,IAAI,QAAQ,mBAAmB,MAAM,GAAG,IAAI,IAAI;oFAGpD,WAAU;oFACV,UACE,KAAK,IAAI,CAAC,mBAAmB,MAAM,GAAG,WAAW;8FAGnD,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAS3B,CAAC,wBAAwB,WAAW,KAAK,eAAe,GAAG,mBACzD,6LAAC;oCAAI,WAAU;;wCACZ,sCACC,6LAAC,6HAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;sDAGd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;;oDAET,4BAAc,6LAAC;kEAAE;;;;;;kEAClB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAGzB,iBAAiB,mCAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,6LAAC;4DAAG,WAAU;sEAAU;;;;;;;;;;;;8DAI1B,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAc;;;;;;;;;;;;sDAMrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;wDAAC,OAAO;wDAAW,eAAe;wDAAc,WAAU;kEAC7D,cAAA,6LAAC,4HAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,6LAAC,4HAAA,CAAA,cAAW;oEAAC,OAAM;8EAAmB;;;;;;8EACtC,6LAAC,4HAAA,CAAA,cAAW;oEAAC,OAAM;8EAAgB;;;;;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;oDAAmB,IAAG;8DAClC,CAAC,iBAAiB,cACjB,cAAc,UAAU,KACxB,CAAC,WACD,2BACE,6LAAC,gJAAA,CAAA,UAAgB;wDACf,WAAW,iBAAiB,cAAc;wDAC1C,SAAS,WAAW,SAAS,MAAM,MAAM,SAAS;wDAClD,eAAe;wDACf,eAAe;wDACf,yBAAyB;wDACzB,+CACE;wDAEF,0BAA0B;wDAC1B,cAAc;wDACd,iBAAiB;wDACjB,eAAe,iBAAiB;wDAChC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;sDAKrC,6LAAC,4HAAA,CAAA,OAAI;4CAAC,OAAO;4CAAW,eAAe;4CAAc,WAAU;;8DAC7D,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAmB,WAAU;8DAC9C,cAAA,6LAAC,mIAAA,CAAA,UAAiB;;;;;;;;;;8DAGpB,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAgB,WAAU;;sEAC3C,6LAAC,kJAAA,CAAA,UAAc;4DACb,qBAAqB;4DACrB,mBAAmB;4DACnB,SAAS;4DACT,qBAAqB;4DACrB,iBAAiB;4DACjB,YAAY;4DACZ,eAAe;4DACf,mBAAmB;4DACnB,sBAAsB;4DACtB,aAAa;4DACb,eAAe,iBAAiB;4DAChC,qBAAqB;4DACrB,SAAS,WAAW;4DACpB,iBAAiB;4DACjB,WAAW;4DACX,0BAA0B;4DAC1B,aAAa;4DACb,cAAc;;;;;;sEAGhB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;oEACP,eAAe,CAAC;wEACd,SAAS,OAAO;wEAChB,QAAQ;oEACV;;sFAEA,6LAAC,8HAAA,CAAA,gBAAa;4EAAC,WAAU;sFACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,6LAAC,8HAAA,CAAA,gBAAa;sFACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;0FACT;oFAAC;oFAAI;oFAAK;oFAAK;oFAAK;iFAAI,EAAE,IAAI,CAAC,qBAC9B,6LAAC,8HAAA,CAAA,aAAU;wFAET,OAAO;;4FAEN;4FAAK;;uFAHD;;;;;;;;;;;;;;;;;;;;;8EASf,6LAAC;;sFACC,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAS;4EACT,SAAS,IAAM,QAAQ,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;4EAC9C,UAAU,SAAS;4EACnB,WAAU;sFAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;sFAEd,6LAAC,8HAAA,CAAA,SAAM;4EACL,SAAS;4EACT,SAAS,IACP,QAAQ,CAAC,IACP,IAAI,QAAQ,oBAAoB,SAAS,IAAI,IAAI;4EAGrD,WAAU;4EACV,UACE,KAAK,IAAI,CAAC,oBAAoB,SAAS,WAAW;sFAGpD,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWjC,6LAAC,+IAAA,CAAA,UAAe;wBACd,SAAS;wBACT,gBAAgB;wBAChB,mBAAmB;wBACnB,mBAAmB;wBACnB,sBAAsB;wBACtB,WAAW;;;;;;oBAEZ,cAAc,aACb,CAAC,WACD,gBACA,+BACE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;8CAAe;;;;;;gCAAU;;;;;;;;;;;;oBAIhD,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yHAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GA5vDM;;QAuBa,qIAAA,CAAA,cAAW;QAIZ,iJAAA,CAAA,aAAU;QAGD,iIAAA,CAAA,iBAAc;QAoGhB,uHAAA,CAAA,cAAW;QAoP9B,4HAAA,CAAA,mBAAgB;;;KAtXhB;uCA8vDS"}}, {"offset": {"line": 10847, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10853, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/catalyst-match/IframeProtectedCandidate.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport IframeAuthGate from \"@/components/catalyst-match/IframeAuthGate\";\r\nimport VacancyCandidates from \"@/components/candidates/Candidates\";\r\n\r\nexport default function IframeProtectedCandidate({\r\n  vacancyid,\r\n  emailId,\r\n  entitlementResponse,\r\n}: {\r\n  vacancyid?: string;\r\n  mercuryPortal?: boolean;\r\n  emailId?: string;\r\n  entitlementResponse?: { [key: string]: boolean };\r\n}) {\r\n  const [mercuryPortal, setMercuryPortal] = useState<boolean | null>(null);\r\n  return (\r\n    <IframeAuthGate onAuthComplete={setMercuryPortal}>\r\n      {mercuryPortal === true && (\r\n        <VacancyCandidates\r\n          vacancyid={vacancyid}\r\n          mercuryPortal={mercuryPortal}\r\n          emailId={emailId}\r\n          entitlementData={entitlementResponse}\r\n        />\r\n      )}\r\n    </IframeAuthGate>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,yBAAyB,EAC/C,SAAS,EACT,OAAO,EACP,mBAAmB,EAMpB;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,qBACE,6LAAC,qJAAA,CAAA,UAAc;QAAC,gBAAgB;kBAC7B,kBAAkB,sBACjB,6LAAC,0IAAA,CAAA,UAAiB;YAChB,WAAW;YACX,eAAe;YACf,SAAS;YACT,iBAAiB;;;;;;;;;;;AAK3B;GAvBwB;KAAA"}}, {"offset": {"line": 10894, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}