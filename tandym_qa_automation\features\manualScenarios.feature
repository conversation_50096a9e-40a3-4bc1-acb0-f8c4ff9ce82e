Feature: Manual Scenarios

################################################ Recruiter Home Page Scenarios ################################################

@tc:4402
@manual @recruiterPortal
Scenario: Validate that user cannot login with invalid credentials if user is not authenticated
  Given user has the URL of recruiter Portal
  When user hits the recruiter portal URL and is not authenticated
  Then user should be redirected to the microsoft login page
  When user enters an invalid tandym email id
  And clicks on Next button
  Then user should see an error message for invalid email or account

@tc:4403
@manual @recruiterPortal
Scenario: Validate that user cannot login with invalid password if user is not authenticated
    Given user has the URL of recruiter Portal
    When user hits the recruiter portal URL and is not authenticated
    Then user should be redirected to the microsoft login page
    When user enters a valid tandym email id
    And clicks on Next button
    And user enters an invalid password
    And clicks on the Sign in button
    Then user should see an error message for invalid password

@tc:4404
@manual @recruiterPortal
Scenario: Validate that user cannot login with invalid OTP in Microsoft Authenticator
  Given user has the URL of recruiter Portal
  When user hits the recruiter portal URL and is not authenticated
  Then user should be redirected to the microsoft login page
  When user enters a valid tandym email id
  And clicks on Next button
  And user enters a valid password
  And clicks on the Sign in button
  Then user should be redirected to the tandym OTP page
  When user enters an invalid OTP in the mobile microsoft authenticator app
  Then user should see an error message for invalid OTP and remain on the OTP page

@tc:4405
@manual @recruiterPortal
Scenario: Validate that user can login with valid credentials if user is not authenticated
    Given user has the URL of recruiter Portal
    When user hits the recruiter portal URL and is not authenticated
    Then user should be redirected to the microsoft login page
    When user enters a valid tandym email id
    And clicks on Next button
    And user enters a valid password
    And clicks on the Sign in button
    Then user should be redirected to the tandym OTP page
    When user enters the OTP that we are seeing on tandym OTP page to the mobile microsoft authenticator app
    Then user should be landed on the recruiter portal home page

@tc:4406
@manual @recruiterPortal
Scenario: Validate that user directly lands on the recruiter portal home page if user is already authenticated
  Given user has the URL of recruiter Portal
  When user hits the recruiter portal URL and is already authenticated
  Then user should be landed on the recruiter portal home page

@tc:4407
@manual @recruiterPortal
Scenario: Verify welcome message and recruiter name is displayed on the home page
  Given the recruiter is logged in
  When the home page is displayed
  Then the page should show the welcome message "Welcome"
  And the tagline "Find the Right Fit, Fast." should be visible

##################################################### Vancancy Scenarios #####################################


@tc:4408
@manual @recruiterPortal
Scenario: Validate that user can view the Vacancy page
  Given the recruiter is on the home page
  When the recruiter clicks on the "Vacancy" link in the top navigation bar
  Then the user should be navigated to the Vacancy page


@tc:4409
@manual @recruiterPortal
Scenario: Display vacancy list on the Vacancy page
  Given the recruiter is on the "Vacancy" page
  Then the "Vacancy List" should be visible on the left panel
  And each vacancy should display the vacancy code and assigned email


@tc:4410
@manual @recruiterPortal
Scenario: Search for a vacancy by vacancy code
  Given the recruiter is on the "Vacancy" page
  When the recruiter enters "CR/505497" in the search bar
  Then only vacancies matching the code "CR/505497" should be displayed in the list


@tc:4411
@manual @recruiterPortal
Scenario: View candidates assigned to a specific vacancy
  Given the recruiter is on the "Vacancy" page
  When the recruiter selects the vacancy "CR/505144"
  Then the system should display a list of candidates for "CR/505144"
  And each candidate row should show:
    | Name                  |
    | Location              |
    | Miles from worksite   |
    | Availability Date     |
    | AI Agent status       |
    | Freshness Index       |
    | Total Score           |
    | Parsed Resume         |
    | Rating [hide]          |
    | Why Fit               |
    | Shortlist             |


@tc:4412
@manual @recruiterPortal
Scenario: Validate default pagination and max option
  Given the recruiter is viewing the candidate list
  Then the pagination should default to 150
  And pagination options should include values up to 500

@tc:4413
@manual @recruiterPortal
Scenario: Sort candidate list by availability date
  Given candidates are displayed for a vacancy
  When the recruiter clicks the header of the "Availability Date" column
  Then the candidates should be sorted by availability date

@tc:4414
@manual @recruiterPortal
Scenario: Sort candidate list by location
  Given candidates are displayed for a vacancy
  When the recruiter clicks the header of the "Location" column
  Then the candidates should be sorted alphabetically by location


@tc:4415
@manual @recruiterPortal
Scenario: Sort candidate list by miles from worksite
  Given candidates are displayed for a vacancy
  When the recruiter clicks the header of the "Miles from Worksite" column
  Then the candidates should be sorted by distance in ascending order


@tc:4416
@manual @recruiterPortal
Scenario: Sort candidate list by freshness index
  Given candidates are displayed for a vacancy
  When the recruiter clicks the header of the "Freshness Index" column
  Then the candidates should be sorted by freshness index in ascending order


@tc:4417
@manual @recruiterPortal
Scenario: Sort candidate list by total score
  Given candidates are displayed for a vacancy
  When the recruiter clicks the header of the "Total Score" column
  Then the candidates should be sorted by total score in descending order


@tc:4418
@manual @recruiterPortal
Scenario: Verify other columns are not sortable
  Given candidates are displayed for a vacancy
 When the recruiter hovers or clicks on other column headers
      | Name       |
      | AI Agent status |
      | Parsed Resume    |
      | rating           |
      | Why - fit        |
      | Shortlist         |
  Then no sort icons or actions should be available for above columns


@tc:4419
@manual @recruiterPortal
Scenario: Read-only Why Fit for all vacancies
  Given the recruiter is on the "Vacancy" page
  When a recruiter selects any vacancy
  Then the "Why Fit" section should be non-editable
  And an expand icon should allow viewing full content


@tc:4420
@manual @recruiterPortal
Scenario: Copy Why Fit comment to clipboard
  Given the recruiter expands the "Why Fit" section
  Then a "Copy to Clipboard" icon should be visible
  When the recruiter clicks it
  Then the comment should be copied to clipboard


@tc:4421
@manual @recruiterPortal
Scenario: Attempt to copy Why Fit comment when section is empty
  Given the recruiter expands the "Why Fit" section for a candidate with no comment
  Then a "Copy to Clipboard" icon should be visible
  When the recruiter clicks it
  Then user should get error message saying "Nothing to copy."


@tc:4422
@manual @recruiterPortal
Scenario: Hide Thumbs Down candidates
  Given the recruiter is on the candidate list
  When the recruiter enables "Hide Thumbs Down" toggle
  Then only candidates with Thumbs Up or no rating should be shown


@tc:4423
@manual @recruiterPortal
Scenario: View Thumbs Down candidates
  Given the recruiter is on the candidate list with "Hide Thumbs Down" enabled
  When the recruiter disables the toggle
  Then all candidates including Thumbs Down should be visible

@tc:4424
@manual @recruiterPortal
Scenario: View resume for any candidate
  Given the recruiter clicks the "View Resume" icon for a candidate
  Then a resume popup should open
  And the parsed resume should highlight:
    | High Matching Skills   |
    | Medium Matching Skills |
    | Normal Matching Skills |
  And full work experience should be visible
  And the header should include a "Resume Link"
  And there should be no download button
  When the recruiter closes the popup
  Then the recruiter should return to the vacancy page


@tc:4425
@manual @recruiterPortal
Scenario: Display last updated date below rating
  Given the recruiter is viewing the candidate list
  Then below each rating (Like/Dislike), the "Last Updated Date" should be shown


@tc:4426
@manual @recruiterPortal
Scenario: Validate column alignment
  Given the recruiter is viewing the candidate list
  Then all column headers and content should be vertically and horizontally aligned


@tc:4427
@manual @recruiterPortal
Scenario: Display comment, eye icon, and toast message after like or dislike action
  Given candidates are displayed for a vacancy
  And the recruiter likes or dislikes a candidate
  And adds a comment while performing the like or dislike action
  When the action is saved
  Then a toast message saying "Review submitted successfully" should be displayed
  And an eye icon should be displayed next to the thumbs up or thumbs down icon
  And when the recruiter clicks on the eye icon
  Then the comment should be displayed


@tc:4428
@manual @recruiterPortal
Scenario: Validate success toast message when user clicks  on thum up icon
When user clicks on thums up icon
Then validate success toast message should be display as "You've marked this candidate as a good fit."


@tc:4429
@manual @recruiterPortal
Scenario: Validate toast message when user clicks  on thum down icon
When user clicks on thums down icon
Then validate success toast message should be display as "You've marked this candidate as not a good fit."

########################################## GENERAL API Handling Scenarios ############################

@tc:4430
@manual @recruiterPortal
Scenario: Display Vacancy Table When Catalyst Match API Status is Completed or error
Given the user is on the Vacancy page
When the "/vacancies/{vacancy_id}/catalystmatchstatus" API returns status as "completed" or "error"
Then the vacancy table should be enabled for all actions (shortlisting, thumbs up/down, comments, etc.)


@tc:4431
@manual @recruiterPortal
Scenario: Disable Vacancy (Including Refresh button) Table When Catalyst Match API Status is In Process or Queued
Given the user is on the Vacancy page
When the "/vacancies/{vacancy_id}/catalystmatchstatus" API returns status as "inprocess" or "queued"
Then the vacancy table should be read-only with existing data displayed 
And actions should be enabled only for Parsed resume, eye icon, why fit popup and candidate link

######################################## REFRESH / UPDATE BUTTON Scenarios #########################

@tc:4432
@manual @recruiterPortal
Scenario: Always Display the Refresh Button
Given the user is on the Vacancy page
Then the Refresh button should always be visible, regardless of Catalyst Match status or timestamps


@tc:4433
@manual @recruiterPortal
Scenario: Display Updates Available LABEL When New Data is Present
Given the user is on the Vacancy page
When the Catalyst Match last updated timestamp is greater than the last data table load time
Then display the "Updates Available" label to the left of the Refresh button

#################################### REGENERATION FLOW Scenarios ################################


@tc:4434
@manual @recruiterPortal
Scenario: Initiate Regeneration When Regeneration Button is Clicked
  Given the user is on the Vacancy page
  When the user clicks the Regeneration button
  Then the system should call the "/vacancies/{vacancy_id}/regenerate-catalyst-match" API


@tc:4435
@manual @recruiterPortal
Scenario: Disable All Vacancy Table Functionalities After Regeneration Starts
Given the user has initiated Regeneration and status is "queued" or "inprocess"
Then disable actions on the vacancy table including Shortlisting, Thumbs Up/Down and Refresh button
And enable for thums up/down, why fit comment, eye icon and candidate link


 ###################################### REGENERATION TIMESTAMP AND INFO ICON ##########################

@tc:4436
@manual @recruiterPortal
Scenario: Display Last Regeneration Timestamp when completed
  Given the regeneration status is "completed"
  Then display a label "Generated at: mm/dd/yy hh:mm AM EST"
  And show an info icon with tooltip showing the initiator (user or system)

@tc:4437
@manual @recruiterPortal
Scenario: Display Last Regeneration Timestamp when inprocess or queued
  Given the regeneration status is "inprocess" or "queued"
  Then display a label "Generation started at: mm/dd/yy hh:mm AM EST"
  And show an info icon with tooltip showing the initiator (user or system)


################################# REGENERATION RELATED MESSAGES BELOW BUTTONS / TABLE ###################

@tc:4438
@manual @recruiterPortal
Scenario: validate when status is Candidate_Successful_but_no_matches_found
Then display ---> Generated at: mm/dd/yy hh:mm AM EST

@tc:4439
@manual @recruiterPortal
Scenario: Regeneration In Progress or queued Message
Given the status is "queued" or "inprocess"
Then display message: "Regeneration in progress. (Catalyst match is read-only until complete)"

@tc:4440
@manual @recruiterPortal
Scenario: If there is no candidate in any vacancy
Given we have vacancy but there is empty candidates
Then API status should be null
And dipslay the mesage: Catalyst match not initiated yet


@tc:4441
@manual @recruiterPortal
Scenario: Error During Regeneration
Given the status is "error"
Then display message: "Error during regeneration. Try again or if persists, contact support."


############################## CANDIDATE TABLE SCENARIOS ##################################

@tc:4442
@manual @recruiterPortal
Scenario: Show message when Catalyst Match is completed but no candidates are found
  Given the "/vacancies/{vacancy_id}/catalystmatchstatus" API returns status as "completed"
  And the Candidates API returns no results
  Then display the message below the table: "Matching successful, but no candidates found (Code: 100)"


@tc:4443
@manual @recruiterPortal
Scenario: Show Candidates When Matching is Successful
Given the Catalyst Match API status is "completed"
And the Candidates API returns candidate data
Then display the candidate list without any additional messages


@tc:4444
@manual @recruiterPortal
Scenario: Matching Not Yet Started
Given the Catalyst Match API returns status as ""
And Candidates API returns no results
Then below the table display message: "Matching yet to be done. Regenerate or contact support (Code: 102)"

 ######################################### Entitlement Scenarios ##################################

@tc:4445
@manual @recruiterPortal
Scenario: Validate Entitlement List Mode
  Given the Entitlement sheet is open
  When User 1 is granted only "Update Availability" in the entitlement list
  Then User 1 should be able to see only the "Update Availability" functionality
  And should not see the "Regenerate" functionality and other features
  When User 2 is granted only "Regenerate" in the entitlement list
  Then User 2 should be able to see only the "Regenerate" functionality
  And should not see the "Update Availability" functionality and other features
  When User 3 is granted both "Update Availability" and "Regenerate" in the entitlement list
  Then User 3 should be able to see both "Update Availability" and "Regenerate" functionalities
When User 4 is granted all (update, Regenerate, others) in the entitlement list
Then User 4 should be able to see all functionalities

############################################ Polling Service Time #####################################

@tc:4446
@manual @recruiterPortal
Scenario: Validate 30-second polling timer in Catalyst Match Status API
  Given the user is on the Vacancy page
  And Catalyst Match regeneration is in progress with status as "queued" or "inprocess"
  When the system starts polling the "/vacancies/{vacancy_id}/catalystmatchstatus" API
  Then the API should be called every 30 seconds
  And polling should continue until the status becomes "completed" or "error"
  And once the status changes to "completed" or "error", stop the polling

 ########################################## Multiple CRM Tab Instances (Same or Different Vacancies) ###############


@tc:4447
@manual @recruiterPortal
Scenario: Open same vacancy in multiple tabs - validate updates independently
  Given the user has Tab A and Tab B open with the same vacancy
  And Catalyst Match is regenerated in Tab A
  When 30 seconds have passed or the user triggers a manual check in Tab B
  Then the "Updates Available" label should be displayed if the completed_at timestamp is newer than Tab B load time
  And the Refresh button should remain available


@tc:4448
@manual @recruiterPortal
Scenario: Open different vacancies in multiple tabs - validate isolation
  Given the user has Tab A with Vacancy A and Tab B with Vacancy B
  When Catalyst Match status for Vacancy A is updated
  Then only Tab A should show "Updates Available"
  And Tab B should remain unchanged unless Vacancy B also gets an update

################################ Update Availiable- One tab, One vacnacy and Multiple Tab, Multiple vacancy ################

@tc:4449
@manual @recruiterPortal
Scenario: Display "Updates Available" in the same tab when Catalyst Match data is updated
  Given the user is on Vacancy A
  And the table was loaded at time T1
  When the Catalyst Match status API returns a last updated timestamp greater than T1
  Then display the "Updates Available" label next to the Refresh button


@tc:4450
@manual @recruiterPortal
Scenario: Hide "Updates Available" label after Refresh button is clicked
  Given the "Updates Available" label is shown for Vacancy A
  When the user clicks the Refresh button
  Then the table is reloaded
  And the "Updates Available" label is hidden


@tc:4451
@manual @recruiterPortal
Scenario: Display "Updates Available" in another tab for the same vacancy
  Given the user has Tab A and Tab B open with the same Vacancy A
  And Vacancy A's Catalyst Match data is updated from Tab A or backend
  When Tab B polling detects a newer timestamp
  Then Tab B should show the "Updates Available" label without manual action


@tc:4452
@manual @recruiterPortal
Scenario: Show "Updates Available" label correctly when switching between vacancies
  Given the user opens Vacancy A and Vacancy B in different tabs
  And Catalyst Match status for Vacancy A is updated
  When the user switches to Tab B (Vacancy A)
  Then the "Updates Available" label should be displayed if timestamp > last table load


@tc:4453
@manual @recruiterPortal
Scenario: Do not show "Updates Available" label for a different vacancy
  Given the user has Tab A on Vacancy A and Tab B on Vacancy B
  And Catalyst Match data is updated for Vacancy A
  When the user checks Tab B (Vacancy B)
  Then the "Updates Available" label should not appear


@tc:4454
@manual @recruiterPortal
Scenario: Manually update last_updated_at in database and verify "Updates Available" behavior
  Given the user is on Vacancy A and table is loaded at time T1
  And the Catalyst Match API is polling every 30 seconds
  When a developer or tester manually updates the `last_updated_at` value in the database for Vacancy A to time T2 (where T2 > T1)
  Then on the next poll, the Catalyst Match Status API returns `last_updated_at = T2`
  And the UI should display the "Updates Available" label next to the Refresh button

 ################################# Completion of Regeneration #################################

@tc:4455
@manual @recruiterPortal
Scenario: Reload table when Catalyst Match regeneration is completed
    # DB Simulation:
    # In table catalyst_match_job_processing_queue:
    # UPDATE search_match_status = 'completed'
    # UPDATE search_match_completed_at = CURRENT_DATETIME
    Given the Catalyst Match status for the vacancy is "completed"
    And the regeneration completed timestamp is the current datetime
    When the status changes to "completed"
    Then the vacancy table should reload only once
    And the "Regenerate" button should be enabled
    And the "Refresh" button should be enabled

######################################### Regeneration Queued #########################################

@tc:4456
@manual @recruiterPortal
Scenario: Do not reload table when Catalyst Match status is queued
    # DB Simulation:
    # In table catalyst_match_job_processing_queue:
    # UPDATE search_match_status = 'queued'
    Given the Catalyst Match status for the vacancy is "queued"
    When the status remains "queued"
    Then the vacancy table should not reload
    And the "Regenerate" button should remain disabled
    And the "Refresh" button should remain disabled

######################################## Catalyst Match Status = 404 (Vacancy Missing) ############################

@tc:4457
@manual @recruiterPortal
  Scenario: Display error when vacancy is missing and Catalyst Match returns 404
    # DB Simulation:
    # Remove vacancy_id from either vacancy_shortlist_processed OR catalyst_match_job_processing_queue
    Given the vacancy ID is not present in the required tables
    When the Catalyst Match API call is made
    Then the API should return status code 404
    And the system should display the message:
      """
      Please ensure Catalyst Match is enabled. If it is already enabled, then please wait a few minutes.
      """

################################# No Candidates Found #####################################

@tc:4458
@manual @recruiterPortal
Scenario: Show empty candidate list when no candidates exist
    # DB Simulation:
    # Remove all records of the vacancy from candidate_application_shortlists
    Given the vacancy exists without any candidate records
    When the page loads the candidate list
    Then the system should display an empty state
    And show a message indicating that no candidates are available

############################################ UpdateAvailable #############################################
@tc:4459
@manual @recruiterPortal
Scenario: Display "Update Available" text when new data is detected
    # DB Simulation:
    # In table vacancy_shortlist_processed:
    # UPDATE search_result_data_last_updated_at = CURRENT_DATETIME + INTERVAL '1 HOUR'
    Given the system detects a newer shortlist data timestamp than the current one
    When the page loads the vacancy data
    Then the system should display the text "Update Available"
    And the user should be able to refresh to view updated data

###################################### Shortlist Scenarios ##########################################

@tc:4460
@manual @recruiterPortal
Scenario: Show shortlist button when status is empty ("")
Given the user is viewing the candidate list
And the candidate's shortlisting status is empty
When the data is loaded or refreshed
Then the shortlist button should be visible and enabled


@tc:4461
@manual @recruiterPortal
Scenario: Show shortlisted message and disable button when status is success or failure with error 409
Given the user is viewing the candidate list
And the candidate's shortlisting status is "success" or "failure" with status code 409
When the data is loaded or refreshed
Then the shortlist button should be disabled
And the UI should display the message "Shortlisted"


@tc:4462
@manual @recruiterPortal
Scenario: Shortlist a candidate successfully
Given the candidate is eligible for shortlisting
When the user clicks on the shortlist button
And confirms in the confirmation modal
Then a request should be sent with valid vacancy_id, candidate_id and reviewer_email
And the response should have status 200
And the UI should show a success message
And the shortlist button should be disabled and labeled "Shortlisted"


@tc:4463
@manual @recruiterPortal
Scenario: Try to shortlist an already shortlisted candidate
Given the candidate is already shortlisted
When the user tries to shortlist again
Then the API should respond with status 409
And the response body should contain the message "Candidate is already shortlisted for this vacancy"
And a warning notification should be displayed


@tc:4464
@manual @recruiterPortal
Scenario: Handle server error during shortlisting
Given the user tries to shortlist a candidate
When the server is down or returns a 500 error
Then the API should respond with status 500
And the response body should contain the message "Failed to shortlist candidate"
And an error notification should be displayed


@tc:4465
@manual @recruiterPortal
Scenario: Hide shortlist button when shortlisting from entitlement is not allowed
Given shortlisting from entitlement is set to false
When the user views the candidate list
Then the shortlist button should not be visible


@tc:4466
@manual @recruiterPortal
Scenario: Disable all shortlist buttons when IS_LOCK_FEATURE_DISABLED is true
Given the feature flag IS_LOCK_FEATURE_DISABLED is true
When the user views the candidate list
Then all shortlist buttons should be disabled regardless of candidate status

#################################### Sub-config Library Scenarios ##########################################

 @tc:4638
 @manual @recruiterPortal
  Scenario: UI loads correctly on Subcategory Library Editor page
    Given the user navigates to the Subcategory Library Editor page
    Then the UI should load correctly

 @tc:4639
 @manual @recruiterPortal
  Scenario: Sticky columns remain visible on horizontal scroll
    Given the Subcategory Library Editor page is open
    When the user scrolls horizontally
    Then the sticky columns should remain visible

@tc:4640
@manual @recruiterPortal
Scenario: Verify search functionality with valid subcategory name
    Given the Subcategory Library Editor page is open
    When the user enters a valid subcategory name in the search box
    Then the matching subcategory should be displayed

@tc:4641
@manual @recruiterPortal
  Scenario: Verify search returns no match for invalid subcategory name
    Given the Subcategory Library Editor page is open
    When the user enters a non-existing subcategory name
    Then no results should be displayed

@tc:4642
@manual @recruiterPortal
  Scenario: Verify sorting by Category
    Given the Subcategory Library Editor page is open
    When the user clicks on the 'Category' header
    Then the subcategories should be sorted by Category in ascending order

@tc:4643
@manual @recruiterPortal
  Scenario: Verify sorting toggle on Category column
    Given the Category column is already sorted
    When the user clicks the 'Category' header again
    Then the sorting order should toggle to descending

@tc:4644
@manual @recruiterPortal
  Scenario: Verify sorting by Created Date
    Given the Subcategory Library Editor page is open
    When the user clicks on the 'Created Date' column header
    Then the subcategories should be sorted by Created Date

@tc:4645
@manual @recruiterPortal
  Scenario: Enable Save button on field modification
    Given the Subcategory Library Editor page is open
    When the user modifies a field value
    Then the Save button should become enabled

@tc:4646
@manual @recruiterPortal
  Scenario: Disable Save button when no changes are made
    Given the Subcategory Library Editor page is open
    When the user does not modify any row
    Then the Save button should remain disabled

@tc:4647
@manual @recruiterPortal
  Scenario: Enable Save All button on row modification
    Given the Subcategory Library Editor page is open
    When the user modifies any row
    Then the Save All button should become enabled

@tc:4648
@manual @recruiterPortal
  Scenario: Save All functionality
    Given the user has modified one or more rows
    When the user clicks the 'Save All' button
    Then all modified rows should be saved successfully

@tc:4649
@manual @recruiterPortal
  Scenario: Verify checkbox selection
    Given the Subcategory Library Editor page is open
    When the user clicks the checkbox in a row
    Then the checkbox should be selected

@tc:4650
@manual @recruiterPortal
  Scenario: Verify checkbox deselection
    Given a row checkbox is selected
    When the user clicks the checkbox again
    Then the checkbox should be deselected

@tc:4651
@manual @recruiterPortal
  Scenario: Enable Delete button when at least one row is selected
    Given one or more row checkboxes are selected
    Then the Delete button should become enabled

@tc:4652
@manual @recruiterPortal
  Scenario: Disable Delete button when no rows are selected
    Given no row checkboxes are selected
    Then the Delete button should remain disabled

@tc:4653
@manual @recruiterPortal
  Scenario: Perform delete action
    Given one or more row checkboxes are selected
    When the user clicks the Delete button
    Then the selected rows should be deleted

@tc:4654
@manual @recruiterPortal
  Scenario: Verify save notification after editing
    Given the user modifies a row
    When the user clicks Save
    Then a confirmation notification should appear

@tc:4655
@manual @recruiterPortal
  Scenario: Verify vertical scroll behavior
    Given the Subcategory Library Editor page contains many rows
    When the user scrolls vertically
    Then the scroll should function smoothly and correctly

@tc:4656
@manual @recruiterPortal
  Scenario: Handle empty table state
    Given there are no subcategories available
    Then the table should display an appropriate empty state message

@tc:4657
@manual @recruiterPortal
  Scenario: Verify multiple save actions
    Given multiple rows have been modified
    When the user saves them individually
    Then each save action should succeed without affecting others

@tc:4658
@manual @recruiterPortal
  Scenario: Check lazy loading in stats section
    Given the user opens the Subcategory Library Editor page
    Then the stats section should load lazily after initial page load

@tc:4659
@manual @recruiterPortal
  Scenario: Verify stats data is displayed
    Given the stats section has loaded
    Then correct stats data should be shown

@tc:4660
@manual @recruiterPortal
  Scenario: Update validation with no changes
    Given the user has not modified any field
    When the user attempts to save
    Then no save should occur and no update call should be triggered

@tc:4661
@manual @recruiterPortal
  Scenario: Verify button style consistency
    Given the Subcategory Library Editor page is open
    Then all buttons should follow consistent style and appearance guidelines

########################################## Catalyst Match Scenarios ##########################################

@tc:4662
@manual @recruiterPortal
  Scenario: Verify user has access to Catalyst Match page in CRM
    Given the user is logged into the CRM with appropriate permissions
    When the user clicks on the Catalyst Match page link
    Then the Catalyst Match page should be accessible

@tc:4663
@manual @recruiterPortal
  Scenario: Verify user does not have access to Catalyst Match page in CRM
    Given the user is logged into the CRM without proper permissions
    When the user clicks on the Catalyst Match page link
    Then the user should see an access denied or unauthorized message

@tc:4664
@manual @recruiterPortal
  Scenario: Verify Catalyst Match page matches Vacancy page in Recruiter portal
    Given the user has access to both CRM and Recruiter portal
    When the user opens the Catalyst Match page in CRM
    Then the content and layout should match the Vacancy page in the Recruiter portal

@tc:4665
@manual @recruiterPortal
  Scenario: Verify user can make changes even when Vacancy is locked in Recruiter portal
    Given the corresponding vacancy is locked in the Recruiter portal
    When the user opens the Catalyst Match page in CRM
    And clicks on thumbs up/down, adds a comment, and clicks on shortlist
    Then all changes should be saved successfully without restriction

@tc:4666
@manual @recruiterPortal
  Scenario: Verify user is redirected to the Candidate profile
    Given the Catalyst Match page is open
    When the user clicks on a candidate name in the table
    Then the user should be redirected to the candidate’s profile page

@tc:4667
@manual @recruiterPortal
  Scenario: Verify presence and functionality of Refresh icon
    Given the Catalyst Match page is open
    When the user clicks on the Refresh icon above the table
    Then the table data should reload successfully

@tc:4668
@manual @recruiterPortal
  Scenario: Verify unauthorized user cannot access Catalyst Match page via direct URL
    Given the user is not authorized to access the Catalyst Match page
    When the user attempts to open the page using a direct URL
    Then access should be denied and the user should see an error or redirect to login/access denied page

@tc:4669
@manual @recruiterPortal
  Scenario: Verify user can update vacancy data even when locked in Recruiter portal
    Given the vacancy is locked in the Recruiter portal
    When the user opens the Catalyst Match page in CRM
    And updates the rating, adds a comment, and shortlists a candidate
    Then the actions should be saved and reflected correctly



