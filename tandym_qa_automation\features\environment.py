import logging
import os
import sys
import tempfile
import uuid
from pathlib import Path
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from dotenv import load_dotenv
from cryptography.fernet import Fernet
from selenium import webdriver
from webdriver_manager.chrome import ChromeDriverManager
import subprocess
from time import sleep
from selenium.common.exceptions import WebDriverException
from datetime import datetime, timedelta, timezone
import re
import io
from dp_portal.config.azure_config_fetcher import fetch_config_from_azure_for_dp_portal
from dp_portal.db.postgres_connector import PostgresConnector
from dp_portal.appLogger import AppLogger

# Resolve path to root project (tandym-dataprocessing)
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))

# Confirm for debugging
print("🔍 sys.path[0]:", sys.path[0])
print("🔍 Looking for dp_portal in:", os.listdir(project_root))

# Load .env
dotenv_path = Path(__file__).resolve().parent / ".env"
load_dotenv(dotenv_path=dotenv_path, override=True)
print("✅ Loaded .env from:", dotenv_path)
print("✅ AppConfigurationEndpoint:", os.getenv("AppConfigurationEndpoint"))

capture_all = os.getenv("CAPTURE_ALL_SCREENSHOTS", "false").lower() == "true"

def before_all(context):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def before_all(context):

    # 🔐 Fernet setup

    # 🌐 Azure Config
    os.environ["APP_ENV"] = os.getenv("APP_ENV", "dev")
    fetch_config_from_azure_for_dp_portal()
    print("✅ Azure configuration values fetched with label TandymDP.")
    
    # print("Fernet Secret: ", os.environ["FERNET_SECRET"])
    context.fernet = Fernet(os.environ["FERNET_SECRET"])
    print("🔐 Fernet setup done in environment.py")

    # 📝 Logger setup
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    context.logger = AppLogger(logger_config)

    # 🗄️ DB Connection
    try:
        db_config = {
            "host": os.getenv("DB_URL"),
            "port": int(os.getenv("DB_PORT", 5432)),
            "user": os.getenv("DB_USERNAME"),
            "password": os.getenv("DB_PASSWORD"),
            "dbname": os.getenv("DB_NAME"),
        }
        connector = PostgresConnector(config=db_config, logger=context.logger)
        connection = connector.connect()
        if not connection:
            raise Exception("Database connection failed.")

        context.db_connector = connector
        context.db_connection = connection
        context.db_cursor = connection.cursor()
        context.logger.info(f"✅ DB connection established for environment: {os.getenv('APP_ENV')}")

    except Exception as e:
        context.logger.error(f"❌ DB setup failed: {e}")
        raise

    # ✅ Browser setup with better CI handling
    try:
        chrome_options = Options()
        print("T1: Chrome Launch: ", datetime.now())
        if os.getenv("CI") == "true":
            # ✅ Running in Azure Pipeline
            print("⚙️ Detected CI environment, setting up Chrome in headless mode.")
            chrome_options.binary_location = "/usr/bin/google-chrome"
            chrome_options.add_argument("--headless")  # Use new headless mode
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")


            # Diagnostic: Confirm chrome installation
            try:
                print("****************  Checking Chrome version on CI agent: *****************")
                subprocess.run(["google-chrome", "--version"], check=True)
            except Exception as chrome_check_error:
                print(f"*************** Chrome not found or failed to launch: {chrome_check_error} **************")
                raise

        else:
            # ✅ Local (Windows/macOS/Linux)
            print("🖥️ Local environment detected.")
            unique_profile = tempfile.mkdtemp(prefix=f"profile-{uuid.uuid4()}")
            chrome_options.add_argument(f"--user-data-dir={unique_profile}")
            chrome_options.add_experimental_option("detach", True)

        # ✅ Launch Chrome
        context.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=chrome_options
        )
        print("T2: Chrome Launch complete: ", datetime.now())
        print("****************Browser launched in before_all() *****************")

        # ✅ Give time to fully load in CI
        sleep(3)

    except WebDriverException as e:
        print(f" WebDriverException: {e}")
        context.logger.error(f" Failed to launch browser: {e}")
        raise
    except Exception as e:
        print(f"************  Unexpected error during browser launch: {e}")
        context.logger.error(f"************* Failed to launch browser: {e}")
        raise

def after_all(context):
    # 🔌 Close DB connection
    try:
        if hasattr(context, 'db_cursor'):
            context.db_cursor.close()
        if hasattr(context, 'db_connector'):
            context.db_connector.close()
            context.logger.info("************* DB connection closed. **************")
    except Exception as e:
        context.logger.error(f"************ DB cleanup failed: {e} **************")
        raise

    #  Close browser
    # try:
    #     if hasattr(context, 'driver'):
    #         context.driver.quit()
    #         print(" Browser closed after all tests")
    # except Exception as e:
    #     print(f"Error closing browser: {e}")

def sanitize_filename(name):
    safe_name = re.sub(r'[^A-Za-z0-9_\-]', '_', name)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{safe_name[:80]}_{timestamp}.png"

def take_screenshot(context, name):
    screenshots_dir = os.path.join(os.getcwd(), "Screenshots")
    os.makedirs(screenshots_dir, exist_ok=True)
    file_path = os.path.join(screenshots_dir, name)
    success = context.driver.save_screenshot(file_path)
    if success:
        print(f" Screenshot captured: {file_path}")
    else:
        print(f" Failed to capture screenshot: {file_path}")


def after_step(context, step):
    if step.status == 'failed' or capture_all:
        step_name = sanitize_filename(step.name)
        take_screenshot(context, step_name)
