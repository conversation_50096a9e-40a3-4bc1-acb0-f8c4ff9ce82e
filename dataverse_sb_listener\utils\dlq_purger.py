#!/usr/bin/env python3
"""
Dead Letter Queue Purger for Azure Service Bus

This utility purges all dead letter messages from a specified Azure Service Bus queue.
It can be used to free up space when the DLQ is causing issues with message processing.

Usage:
    python dlq_purger.py --queue-name <queue_name> --connection-string <connection_string>
    python dlq_purger.py --queue-name <queue_name> --connection-string-env <env_var_name>
"""

import argparse
import os
import sys
from typing import Optional

# Add the project root to the Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

try:
    from azure.servicebus import ServiceBusClient
    from azure.servicebus.exceptions import ServiceBusError
    from azure.core.exceptions import AzureError
    from common.appLogger import AppLogger, LoggerFactory
except ImportError as e:
    print(f"Error: Required dependencies not found: {e}")
    print("Run: pip install azure-servicebus")
    sys.exit(1)


class DLQPurger:
    """Utility class to purge dead letter messages from Azure Service Bus queues."""
    
    def __init__(self, connection_string: str, logger: Optional[AppLogger] = None, logger_name: str = "dlq_purger"):
        self.connection_string = connection_string
        self.client = ServiceBusClient.from_connection_string(connection_string)
        
        if logger:
            self.logger = logger
        else:
            # Use LoggerFactory to create a logger with custom name
            logger_config = {
                "name": logger_name,
                "log_level": "INFO",
                "log_to_stdout": True,
                "log_mode": "append",
                "log_file_path": f"{logger_name}.log",
                "json_log": True
            }
            self.logger = LoggerFactory.get_logger(logger_name, logger_config)
    
    def purge_dead_letter_messages(self, queue_name: str, batch_size: int = 100) -> int:
        """
        Purge all dead letter messages from the specified queue.
        This method uses the force approach as it's more reliable.
        
        Args:
            queue_name: Name of the queue to purge DLQ messages from
            batch_size: Number of messages to process in each batch
            
        Returns:
            Total number of messages purged
        """
        # Use the force method as it's more reliable
        return self.purge_dead_letter_messages_force(queue_name, batch_size)
    
    def purge_dead_letter_messages_force(self, queue_name: str, batch_size: int = 100) -> int:
        """
        Force purge all dead letter messages from the specified queue (original method).
        This method continues until no more messages are found.
        
        Args:
            queue_name: Name of the queue to purge DLQ messages from
            batch_size: Number of messages to process in each batch
            
        Returns:
            Total number of messages purged
        """
        purged_count = 0
        
        try:
            # Create a receiver for the dead letter queue
            # Azure Service Bus uses sub_queue=1 to access the dead letter queue
            # This is equivalent to queue_name + "/$DeadLetterQueue"
            with self.client.get_queue_receiver(
                queue_name=queue_name,
                sub_queue="deadletter",  # "deadletter" represents the dead letter queue
                max_wait_time=1  # 1 second timeout
            ) as receiver:
                
                self.logger.info(f"Starting force purge of dead letter messages from queue: {queue_name}")
                
                while True:
                    # Receive messages in batches
                    messages = receiver.receive_messages(max_message_count=batch_size, max_wait_time=5)
                    
                    if not messages:
                        self.logger.info("No more dead letter messages found")
                        break
                    
                    # Complete (remove) all messages in the batch
                    for message in messages:
                        try:
                            receiver.complete_message(message)
                            purged_count += 1
                            
                            if purged_count % 100 == 0:
                                self.logger.info(f"Purged {purged_count} messages so far...")
                                
                        except ServiceBusError as e:
                            self.logger.error(f"Error completing message: {e}")
                            continue
                    
                    self.logger.info(f"Purged batch of {len(messages)} messages")
                
                self.logger.info(f"Successfully purged {purged_count} dead letter messages from queue: {queue_name}")
                return purged_count
                
        except ServiceBusError as e:
            self.logger.error(f"Service Bus error while purging DLQ messages: {e}")
            raise
        except AzureError as e:
            self.logger.error(f"Azure error while purging DLQ messages: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error while purging DLQ messages: {e}")
            raise
    
    def get_dlq_message_count(self, queue_name: str) -> int:
        """
        Get the approximate count of dead letter messages in the queue.
        
        Args:
            queue_name: Name of the queue to check
            
        Returns:
            Approximate count of dead letter messages
        """
        try:
            # Create a receiver for the dead letter queue
            # Azure Service Bus uses sub_queue=1 to access the dead letter queue
            # This is equivalent to queue_name + "/$DeadLetterQueue"
            with self.client.get_queue_receiver(
                queue_name=queue_name,
                sub_queue="deadletter",  # "deadletter" represents the dead letter queue
                max_wait_time=1
            ) as receiver:
                
                # Get queue properties to check message count
                # Note: This is an approximation as the count can change rapidly
                count = 0
                while True:
                    messages = receiver.receive_messages(max_message_count=100, max_wait_time=1)
                    if not messages:
                        break
                    count += len(messages)
                    # Abandon messages to put them back in DLQ for counting
                    for message in messages:
                        receiver.abandon_message(message)
                
                return count
                
        except Exception as e:
            self.logger.error(f"Error getting DLQ message count: {e}")
            return -1


def get_connection_string(connection_string: Optional[str] = None, 
                         env_var_name: Optional[str] = None,
                         logger: Optional[AppLogger] = None) -> str:
    """
    Get the connection string from command line argument or environment variable.
    
    Args:
        connection_string: Connection string from command line
        env_var_name: Environment variable name containing connection string
        logger: Optional AppLogger instance for logging
        
    Returns:
        Azure Service Bus connection string
        
    Raises:
        ValueError: If connection string cannot be found
    """
    if connection_string:
        return connection_string
    
    if env_var_name:
        conn_str = os.getenv(env_var_name)
        if conn_str:
            return conn_str
        else:
            raise ValueError(f"Environment variable '{env_var_name}' not found")
    
    # Try common environment variable names
    common_env_vars = [
        'AZURE_SERVICE_BUS_CONNECTION_STRING',
        'SERVICE_BUS_CONNECTION_STRING',
        'SB_CONNECTION_STRING'
    ]
    
    for env_var in common_env_vars:
        conn_str = os.getenv(env_var)
        if conn_str:
            if logger:
                logger.info(f"Using connection string from environment variable: {env_var}")
            return conn_str
    
    raise ValueError("No connection string provided. Use --connection-string or --connection-string-env")


def main():
    """Main entry point for the DLQ purger tool."""
    parser = argparse.ArgumentParser(
        description="Purge dead letter messages from Azure Service Bus queue",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Using connection string directly
  python dlq_purger.py --queue-name my-queue --connection-string "Endpoint=sb://..."

  # Using environment variable
  python dlq_purger.py --queue-name my-queue --connection-string-env AZURE_SB_CONN_STR

  # Check message count before purging
  python dlq_purger.py --queue-name my-queue --connection-string-env AZURE_SB_CONN_STR --check-only
        """
    )
    
    parser.add_argument(
        "--queue-name",
        required=True,
        help="Name of the queue to purge dead letter messages from"
    )
    
    parser.add_argument(
        "--connection-string",
        help="Azure Service Bus connection string"
    )
    
    parser.add_argument(
        "--connection-string-env",
        help="Environment variable name containing the connection string"
    )
    
    parser.add_argument('--batch-size', type=int, default=100, 
                       help='Number of messages to process in each batch (default: 100)')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check the number of dead letter messages without purging')
    parser.add_argument('--verbose', action='store_true', 
                       help='Enable verbose logging (DEBUG level)')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be purged without actually purging')
    parser.add_argument('--force', action='store_true', 
                       help='Use force purging method (now same as default)')
    parser.add_argument('--logger-name', type=str, default='dlq_purger',
                       help='Custom name for the logger (default: dlq_purger)')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = "DEBUG" if args.verbose else "INFO"
    logger_config = {
        "name": args.logger_name,
        "log_level": log_level,
        "log_to_stdout": True,
        "log_mode": "append",
        "log_file_path": f"{args.logger_name}.log",
        "json_log": True
    }
    logger = LoggerFactory.get_logger(args.logger_name, logger_config)
    
    try:
        # Get connection string
        connection_string = get_connection_string(
            connection_string=args.connection_string,
            env_var_name=args.connection_string_env,
            logger=logger
        )
        
        # Create DLQ purger instance
        purger = DLQPurger(connection_string, logger=logger, logger_name=args.logger_name)
        
        if args.check_only:
            # Only check message count
            count = purger.get_dlq_message_count(args.queue_name)
            if count >= 0:
                print(f"Approximate dead letter message count in queue '{args.queue_name}': {count}")
            else:
                print(f"Could not determine dead letter message count for queue '{args.queue_name}'")
        elif args.dry_run:
            # Show what would be done
            count = purger.get_dlq_message_count(args.queue_name)
            if count >= 0:
                print(f"DRY RUN: Would purge approximately {count} dead letter messages from queue '{args.queue_name}'")
            else:
                print(f"DRY RUN: Could not determine dead letter message count for queue '{args.queue_name}'")
        else:
            # Purge dead letter messages
            print(f"Starting to purge dead letter messages from queue: {args.queue_name}")
            
            # Use the reliable force method (both regular and force now use the same approach)
            purged_count = purger.purge_dead_letter_messages(
                queue_name=args.queue_name,
                batch_size=args.batch_size
            )
            
            print(f"Successfully purged {purged_count} dead letter messages from queue: {args.queue_name}")
            
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except ServiceBusError as e:
        logger.error(f"Service Bus error: {e}")
        sys.exit(1)
    except AzureError as e:
        logger.error(f"Azure error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 