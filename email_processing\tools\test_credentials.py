#!/usr/bin/env python3
"""
Test Azure Credentials

One-time utility to test if Azure credentials are properly loaded.
Run this separately when you need to verify credential setup.
"""

import os
import sys

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from config import setup_logger_and_secrets, get_azure_credentials
from azure_client import AzureEmailFetcher


def test_credentials():
    """Test function to verify credentials are loaded correctly"""
    print("🧪 TESTING AZURE CREDENTIALS")
    print("=" * 50)
    
    # Load secrets first
    logger = setup_logger_and_secrets()
    
    credentials = get_azure_credentials()
    credential_names = {
        "client_id": "MAILBOX_RESUMES_CLIENT_ID",
        "client_secret": "MAILBOX_RESUMES_CLIENT_SECRET",
        "tenant_id": "MAILBOX_RESUMES_TENANT_ID"
    }
    
    print("\n1. Checking Environment Variables:")
    for key, env_name in credential_names.items():
        value = credentials[key]
        if value:
            # Mask the secret values for security
            if "secret" in key.lower():
                display_value = f"[SET - {len(value)} chars]"
            else:
                display_value = value
            print(f"   ✅ {env_name}: {display_value}")
        else:
            print(f"   ❌ {env_name}: NOT SET")
    
    missing = [env_name for key, env_name in credential_names.items() if not credentials[key]]
    if missing:
        print(f"\n❌ Missing credentials: {', '.join(missing)}")
        print("\nPlease ensure these credentials are set in your GPG secrets.")
        return False
    
    print("\n2. Testing Azure API Connection:")
    try:
        # Try to create email fetcher and get access token
        email_fetcher = AzureEmailFetcher(logger=logger)
        access_token = email_fetcher.get_access_token()
        
        if access_token:
            print(f"   ✅ Successfully obtained access token ({len(access_token)} chars)")
            print("   ✅ Azure API connection working")
        else:
            print("   ❌ Failed to obtain access token")
            return False
            
    except Exception as e:
        print(f"   ❌ Azure API connection failed: {e}")
        return False
    
    print("\n✅ ALL TESTS PASSED!")
    print("Your Azure credentials are working correctly.")
    return True


def main():
    """Main function"""
    print("Azure Email Fetcher - Credential Test Tool")
    print("This is a one-time utility for testing credential setup.\n")
    
    success = test_credentials()
    
    if success:
        print("\nYou can now run the main email fetcher:")
        print("  python3 ../main.py")
        print("  python3 ../run.py")
    else:
        print("\nPlease fix the credential issues before using the main email fetcher.")
        sys.exit(1)


if __name__ == "__main__":
    main() 