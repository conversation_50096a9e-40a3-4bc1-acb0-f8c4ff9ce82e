"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Send, RotateCcw, CheckCircle, XCircle, Download, Check, ChevronsUpDown, X } from "lucide-react";
import { submitCatalystMatchForm, getVacancyTemplate, saveVacancyTemplate } from "@/api/serverActions";
import { cn } from "@/library/utils";

interface FormData {
  requiredSkills: string[];
  preferredSkills: string[];
  recencyMustHaveSkills: string;
  additionalJobTitles: string[];
  city: string;
  state: string;
  miles: string;
  yearsExperience: string;
  degrees: string[];
  certificationsLicenses: string[];
  industry: string;
  confidential: string;
}

const CatalystMatchForm: React.FC = () => {
  // Sample city data by state
  const citiesByState: { [key: string]: string[] } = {
    "CA": ["Los Angeles", "San Francisco", "San Diego", "San Jose", "Sacramento", "Fresno", "Oakland", "Long Beach", "Bakersfield", "Anaheim"],
    "NY": ["New York City", "Buffalo", "Rochester", "Yonkers", "Syracuse", "Albany", "New Rochelle", "Mount Vernon", "Schenectady", "Utica"],
    "TX": ["Houston", "San Antonio", "Dallas", "Austin", "Fort Worth", "El Paso", "Arlington", "Corpus Christi", "Plano", "Lubbock"],
    "FL": ["Jacksonville", "Miami", "Tampa", "Orlando", "St. Petersburg", "Hialeah", "Tallahassee", "Fort Lauderdale", "Port St. Lucie", "Cape Coral"],
    "IL": ["Chicago", "Aurora", "Rockford", "Joliet", "Naperville", "Springfield", "Peoria", "Elgin", "Waukegan", "Champaign"],
    "PA": ["Philadelphia", "Pittsburgh", "Allentown", "Erie", "Reading", "Scranton", "Bethlehem", "Lancaster", "Harrisburg", "Altoona"],
    "OH": ["Columbus", "Cleveland", "Cincinnati", "Toledo", "Akron", "Dayton", "Parma", "Canton", "Youngstown", "Lorain"],
    "GA": ["Atlanta", "Augusta", "Columbus", "Macon", "Savannah", "Athens", "Sandy Springs", "Roswell", "Albany", "Johns Creek"],
    "NC": ["Charlotte", "Raleigh", "Greensboro", "Durham", "Winston-Salem", "Fayetteville", "Cary", "Wilmington", "High Point", "Greenville"],
    "MI": ["Detroit", "Grand Rapids", "Warren", "Sterling Heights", "Lansing", "Ann Arbor", "Flint", "Dearborn", "Livonia", "Westland"]
  };

  const [formData, setFormData] = useState<FormData>({
    requiredSkills: [],
    preferredSkills: [],
    recencyMustHaveSkills: "Current +1",
    additionalJobTitles: [],
    city: "",
    state: "",
    miles: "50",
    yearsExperience: "",
    degrees: [],
    certificationsLicenses: [],
    industry: "No",
    confidential: "No",
  });

  const [vacancyId, setVacancyId] = useState("");
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [templateMessage, setTemplateMessage] = useState("");

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState("");
  const [saveWithGenerate, setSaveWithGenerate] = useState(false);

  const [currentInputs, setCurrentInputs] = useState({
    requiredSkills: "",
    preferredSkills: "",
    additionalJobTitles: "",
    degrees: "",
    certificationsLicenses: "",
  });

  // Combobox states
  const [openCityCombobox, setOpenCityCombobox] = useState(false);
  const [citySearchValue, setCitySearchValue] = useState("");

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    // Clear city when state changes
    if (name === "state") {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        city: "", // Clear city when state changes
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
      }));
    }
  };

  const handleTagInput = (field: keyof FormData, value: string) => {
    setCurrentInputs(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const removeTag = (field: keyof FormData, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }));
  };

  const addSkill = (field: keyof FormData, value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: prev[field].includes(value.trim())
          ? prev[field] as string[]
          : [...(prev[field] as string[]), value.trim()]
      }));
      setCurrentInputs(prev => ({ ...prev, [field]: "" }));
    }
  };

  const renderSkillBadges = (skills: string[], field: keyof FormData, variant: "default" | "secondary" | "destructive" | "outline" = "default") => {
    if (skills.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {skills.map((skill, index) => (
          <Badge key={index} variant={variant} className="flex items-center gap-1 px-2 py-1">
            {skill}
            <button
              type="button"
              onClick={() => removeTag(field, index)}
              className="ml-1 hover:bg-black/20 rounded-full p-0.5 transition-colors"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>
    );
  };

  const normalizeTemplate = (raw: any) => {
    // deep clone to strip out any Proxies/non-serializables if they sneak in
    const safe = JSON.parse(JSON.stringify(raw ?? {}));

    // fix small value mismatches so the UI always has a valid option
    const recency = safe.recencyMustHaveSkills;
    const normalizedRecency =
      recency === "Current + 1" ? "Current +1"
      : recency === "Current +1" ? "Current +1"
      : recency === "Current" ? "Current"
      : "Current +1";

    const industry =
      typeof safe.industry === "string" && safe.industry.toLowerCase() === "yes"
        ? "Yes"
        : "No";

    return {
      requiredSkills: Array.isArray(safe.requiredSkills) ? safe.requiredSkills : [],
      preferredSkills: Array.isArray(safe.preferredSkills) ? safe.preferredSkills : [],
      recencyMustHaveSkills: normalizedRecency,
      additionalJobTitles: Array.isArray(safe.additionalJobTitles) ? safe.additionalJobTitles : [],
      city: typeof safe.city === "string" ? safe.city : "",
      state: typeof safe.state === "string" ? safe.state : "",
      miles: typeof safe.miles === "string" || typeof safe.miles === "number" ? String(safe.miles) : "50",
      yearsExperience: typeof safe.yearsExperience === "string" || typeof safe.yearsExperience === "number" ? String(safe.yearsExperience) : "",
      degrees: Array.isArray(safe.degrees) ? safe.degrees : [],
      certificationsLicenses: Array.isArray(safe.certificationsLicenses) ? safe.certificationsLicenses : [],
      industry,
      confidential: safe.confidential === "Yes" ? "Yes" : "No",
    } as FormData;
  };

  const handleLoadTemplate = async () => {
    if (!vacancyId.trim()) {
      setTemplateMessage("Error: Please enter a vacancy ID");
      return;
    }

    setIsLoadingTemplate(true);
    setTemplateMessage("");

    try {
      const result = await getVacancyTemplate({ vacancy_id: vacancyId.trim() });

      const success = !!result?.data?.success;
      const msg = typeof result?.data?.message === "string" ? result.data.message : "";

      if (success && 'data' in result.data && result.data.data) {
        setFormData(normalizeTemplate(result.data.data));
        setCurrentInputs({ requiredSkills: "", preferredSkills: "", additionalJobTitles: "", degrees: "", certificationsLicenses: "" });
        setTemplateMessage("Template loaded successfully!");
      } else {
        setTemplateMessage(`Error: ${msg || "Failed to load template"}`);
      }
    } catch (error) {
      setTemplateMessage("Error: Failed to load template");
      console.error("Load template error:", error);
    } finally {
      setIsLoadingTemplate(false);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage("");

    // Validation for required fields
    if (!formData.city.trim()) {
      setSubmitMessage("Error: City is required");
      setIsSubmitting(false);
      return;
    }

    if (!formData.state.trim()) {
      setSubmitMessage("Error: State is required");
      setIsSubmitting(false);
      return;
    }

    if (!formData.yearsExperience.trim()) {
      setSubmitMessage("Error: Years of Experience is required");
      setIsSubmitting(false);
      return;
    }

    try {
      // Save template if vacancy ID is provided
      if (vacancyId.trim()) {
        const templateData = {
          vacancy_id: vacancyId.trim(),
          requiredSkills: formData.requiredSkills,
          preferredSkills: formData.preferredSkills,
          recencyMustHaveSkills: formData.recencyMustHaveSkills,
          additionalJobTitles: formData.additionalJobTitles,
          city: formData.city,
          state: formData.state,
          miles: formData.miles,
          yearsExperience: formData.yearsExperience,
          degrees: formData.degrees,
          certificationsLicenses: formData.certificationsLicenses,
          industry: formData.industry,
          confidential: formData.confidential,
        };

        console.log("=== SAVE TEMPLATE DATA ===");
        console.log("Template Data:", JSON.stringify(templateData, null, 2));
        console.log("Save with Generate:", saveWithGenerate);
        console.log("==========================");

        const saveResult = await saveVacancyTemplate(templateData, saveWithGenerate);
        console.log("Save Result:", saveResult);
        
        const saveOk = !!saveResult?.data?.success;
        const saveMsg = typeof saveResult?.data?.message === "string" ? saveResult.data.message : "";

        if (!saveOk) {
          setSubmitMessage(`Error saving template: ${saveMsg || "Failed to save template"}`);
          return;
        }
      }

      const formPayload = {
        required_skills: formData.requiredSkills.join(', '),
        preferred_skills: formData.preferredSkills.join(', '),
        recency_must_have_skills: formData.recencyMustHaveSkills,
        additional_job_titles: formData.additionalJobTitles.join(', '),
        city: formData.city,
        state: formData.state,
        miles: formData.miles,
        years_experience: formData.yearsExperience,
        degrees: formData.degrees.join(', '),
        certifications_licenses: formData.certificationsLicenses.join(', '),
        industry: formData.industry,
        confidential: formData.confidential,
      };

      const result = await submitCatalystMatchForm(formPayload);
      const ok = !!result?.data?.success;
      const msg = typeof result?.data?.message === "string" ? result.data.message : "";

      const successMessage = vacancyId.trim() 
        ? `Template saved and form submitted successfully!`
        : "Form submitted successfully!";
      
      setSubmitMessage(ok ? successMessage : `Error: ${msg || "Failed to submit form"}`);
    } catch (error) {
      setSubmitMessage("Error: Failed to submit form");
      console.error("Submit error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClear = () => {
    setFormData({
      requiredSkills: [],
      preferredSkills: [],
      recencyMustHaveSkills: "Current +1",
      additionalJobTitles: [],
      city: "",
      state: "",
      miles: "50",
      yearsExperience: "",
      degrees: [],
      certificationsLicenses: [],
      industry: "No",
      confidential: "No",
    });
    setCurrentInputs({
      requiredSkills: "",
      preferredSkills: "",
      additionalJobTitles: "",
      degrees: "",
      certificationsLicenses: "",
    });
    setVacancyId("");
    setTemplateMessage("");
    setSubmitMessage("");
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Catalyst Match Form</CardTitle>
          <CardDescription>
            Configure your job requirements and preferences to find the best candidates.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Load Template Section */}
            <Card className="bg-slate-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Load Template</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-end gap-3">
                  <div className="flex-1">
                    <label htmlFor="vacancyId" className="block text-sm font-medium text-gray-700 mb-2">
                      Vacancy ID
                    </label>
                    <Input
                      id="vacancyId"
                      value={vacancyId}
                      onChange={(e) => setVacancyId(e.target.value)}
                      placeholder="Enter vacancy ID (e.g., CR-123456)"
                      className="h-9"
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={handleLoadTemplate}
                    disabled={isLoadingTemplate || !vacancyId.trim()}
                    variant="outline"
                    className="h-9"
                  >
                    {isLoadingTemplate ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Loading...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Load Template
                      </>
                    )}
                  </Button>
                </div>
                {templateMessage && (
                  <div className={`mt-3 p-3 rounded-md text-sm ${
                    templateMessage.includes('Error')
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : 'bg-green-50 text-green-700 border border-green-200'
                  }`}>
                    {templateMessage}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Skills Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Required Skills */}
              <div className="space-y-2">
                <label htmlFor="requiredSkills" className="block text-sm font-medium text-gray-700">
                  Required Skills <span className="text-red-500">*</span>
                  <span className="text-xs text-gray-500 block mt-1">
                    (Optional for App, Nursing and Therapy, Clinical, Corporate Finance & Corporate Services)
                  </span>
                </label>
                <Input
                  id="requiredSkills"
                  value={currentInputs.requiredSkills}
                  onChange={(e) => handleTagInput('requiredSkills', e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      addSkill('requiredSkills', e.currentTarget.value.trim());
                      e.preventDefault();
                    }
                  }}
                  placeholder="e.g. Java, Phlebotomy (press Enter to add)"
                  maxLength={128}
                  className="h-9"
                />
                {renderSkillBadges(formData.requiredSkills, 'requiredSkills', 'default')}
              </div>

              {/* Preferred Skills */}
              <div className="space-y-2">
                <label htmlFor="preferredSkills" className="block text-sm font-medium text-gray-700">
                  Preferred Skills
                  <span className="text-xs text-gray-500 block mt-1">(Optional)</span>
                </label>
                <Input
                  id="preferredSkills"
                  value={currentInputs.preferredSkills}
                  onChange={(e) => handleTagInput('preferredSkills', e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      addSkill('preferredSkills', e.currentTarget.value.trim());
                      e.preventDefault();
                    }
                  }}
                  placeholder="e.g. JavaScript, speech therapy (press Enter to add)"
                  maxLength={128}
                  className="h-9"
                />
                {renderSkillBadges(formData.preferredSkills, 'preferredSkills', 'secondary')}
              </div>
            </div>

            {/* Recency and Additional Job Titles */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Recency of Must-Have Skills */}
              <div className="space-y-2">
                <label htmlFor="recencyMustHaveSkills" className="block text-sm font-medium text-gray-700">
                  Recency of Must-Have Skills
                </label>
                <Select value={formData.recencyMustHaveSkills} onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, recencyMustHaveSkills: value }))
                }>
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select recency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Current +1">Current +1</SelectItem>
                    <SelectItem value="Current">Current</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Job Titles */}
              <div className="space-y-2">
                <label htmlFor="additionalJobTitles" className="block text-sm font-medium text-gray-700">
                  Additional Job Titles
                </label>
                <Input
                  id="additionalJobTitles"
                  value={currentInputs.additionalJobTitles}
                  onChange={(e) => handleTagInput('additionalJobTitles', e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      addSkill('additionalJobTitles', e.currentTarget.value.trim());
                      e.preventDefault();
                    }
                  }}
                  placeholder="Business analysts, product specialist (press Enter to add)"
                  maxLength={128}
                  className="h-9"
                />
                {renderSkillBadges(formData.additionalJobTitles, 'additionalJobTitles', 'outline')}
              </div>
            </div>

            {/* Location Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Location & Experience</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* City with Combobox */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    City <span className="text-red-500">*</span>
                  </label>
                  <Popover open={openCityCombobox} onOpenChange={setOpenCityCombobox}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openCityCombobox}
                        className="w-full justify-between h-9"
                      >
                        {formData.city || "Select city..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput
                          placeholder="Search cities..."
                          value={citySearchValue}
                          onValueChange={setCitySearchValue}
                        />
                        <CommandList>
                          <CommandEmpty>No city found.</CommandEmpty>
                          <CommandGroup>
                            {formData.state && citiesByState[formData.state] ? (
                              citiesByState[formData.state]
                                .filter(city =>
                                  city.toLowerCase().includes(citySearchValue.toLowerCase())
                                )
                                .map((city) => (
                                  <CommandItem
                                    key={city}
                                    value={city}
                                    onSelect={(currentValue) => {
                                      setFormData(prev => ({ ...prev, city: currentValue }));
                                      setOpenCityCombobox(false);
                                      setCitySearchValue("");
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        formData.city === city ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {city}
                                  </CommandItem>
                                ))
                            ) : (
                              <CommandItem disabled>
                                Please select a state first
                              </CommandItem>
                            )}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

            {/* State */}
            <div>
              <label htmlFor="state" className={labelClasses}>
                State (Required):
              </label>
              <select
                id="state"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                className={inputClasses}
                required
              >
                <option value="">Select State</option>
                <option value="AL">Alabama</option>
                <option value="AK">Alaska</option>
                <option value="AZ">Arizona</option>
                <option value="AR">Arkansas</option>
                <option value="CA">California</option>
                <option value="CO">Colorado</option>
                <option value="CT">Connecticut</option>
                <option value="DE">Delaware</option>
                <option value="FL">Florida</option>
                <option value="GA">Georgia</option>
                <option value="HI">Hawaii</option>
                <option value="ID">Idaho</option>
                <option value="IL">Illinois</option>
                <option value="IN">Indiana</option>
                <option value="IA">Iowa</option>
                <option value="KS">Kansas</option>
                <option value="KY">Kentucky</option>
                <option value="LA">Louisiana</option>
                <option value="ME">Maine</option>
                <option value="MD">Maryland</option>
                <option value="MA">Massachusetts</option>
                <option value="MI">Michigan</option>
                <option value="MN">Minnesota</option>
                <option value="MS">Mississippi</option>
                <option value="MO">Missouri</option>
                <option value="MT">Montana</option>
                <option value="NE">Nebraska</option>
                <option value="NV">Nevada</option>
                <option value="NH">New Hampshire</option>
                <option value="NJ">New Jersey</option>
                <option value="NM">New Mexico</option>
                <option value="NY">New York</option>
                <option value="NC">North Carolina</option>
                <option value="ND">North Dakota</option>
                <option value="OH">Ohio</option>
                <option value="OK">Oklahoma</option>
                <option value="OR">Oregon</option>
                <option value="PA">Pennsylvania</option>
                <option value="RI">Rhode Island</option>
                <option value="SC">South Carolina</option>
                <option value="SD">South Dakota</option>
                <option value="TN">Tennessee</option>
                <option value="TX">Texas</option>
                <option value="UT">Utah</option>
                <option value="VT">Vermont</option>
                <option value="VA">Virginia</option>
                <option value="WA">Washington</option>
                <option value="WV">West Virginia</option>
                <option value="WI">Wisconsin</option>
                <option value="WY">Wyoming</option>
              </select>
            </div>

            {/* Miles */}
            <div>
              <label htmlFor="miles" className={labelClasses}>
                Miles:
              </label>
              <input
                type="number"
                id="miles"
                name="miles"
                value={formData.miles}
                onChange={handleInputChange}
                className={inputClasses}
                min="1"
                max="500"
                placeholder="50"
              />
            </div>
          </div>

          {/* Years of Experience */}
          <div>
            <label htmlFor="yearsExperience" className={labelClasses}>
              Years of Experience (Required):
            </label>
            <input
              type="text"
              id="yearsExperience"
              name="yearsExperience"
              value={formData.yearsExperience}
              onChange={handleInputChange}
              className={inputClasses}
              placeholder="Enter years of experience..."
              list="yearsExperienceOptions"
              required
            />
            <datalist id="yearsExperienceOptions">
              {Array.from({ length: 31 }, (_, i) => (
                <option key={i} value={i} />
              ))}
            </datalist>
          </div>

          {/* Degrees */}
          <div>
            <label htmlFor="degrees" className={labelClasses}>
              Degrees (Required for App & Corporate Finance):
            </label>
            <input
              type="text"
              id="degrees"
              name="degrees"
              value={currentInputs.degrees}
              onChange={(e) => handleTagInput('degrees', e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  const value = e.currentTarget.value.trim();
                  setFormData(prev => ({
                    ...prev,
                    degrees: prev.degrees.includes(value)
                      ? prev.degrees
                      : [...prev.degrees, value]
                  }));
                  setCurrentInputs(prev => ({ ...prev, degrees: "" }));
                  e.preventDefault();
                }
              }}
              className={inputClasses}
              placeholder="List necessary degrees (press Enter or comma to add)"
              maxLength={128}
            />
            {formData.degrees.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-1">
                {formData.degrees.map((degree, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-md border border-orange-200"
                  >
                    {degree}
                    <button
                      type="button"
                      onClick={() => removeTag('degrees', index)}
                      className="text-orange-600 hover:text-orange-800 font-bold"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Certifications/Licenses */}
          <div>
            <label htmlFor="certificationsLicenses" className={labelClasses}>
              Certifications/Licenses (Optional):
            </label>
            <input
              type="text"
              id="certificationsLicenses"
              name="certificationsLicenses"
              value={currentInputs.certificationsLicenses}
              onChange={(e) => handleTagInput('certificationsLicenses', e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  const value = e.currentTarget.value.trim();
                  setFormData(prev => ({
                    ...prev,
                    certificationsLicenses: prev.certificationsLicenses.includes(value)
                      ? prev.certificationsLicenses
                      : [...prev.certificationsLicenses, value]
                  }));
                  setCurrentInputs(prev => ({ ...prev, certificationsLicenses: "" }));
                  e.preventDefault();
                }
              }}
              className={inputClasses}
              placeholder="List necessary certifications (press Enter or comma to add)"
              maxLength={128}
            />
            {formData.certificationsLicenses.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-1">
                {formData.certificationsLicenses.map((cert, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-teal-100 text-teal-800 rounded-md border border-teal-200"
                  >
                    {cert}
                    <button
                      type="button"
                      onClick={() => removeTag('certificationsLicenses', index)}
                      className="text-teal-600 hover:text-teal-800 font-bold"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Industry and Confidential */}
          <div className="grid grid-cols-2 gap-1.5 sm:gap-2">
            {/* Industry */}
            <div>
              <label className={labelClasses}>
                Industry:
              </label>
              <div className="flex items-center space-x-4 mt-1">
                <label className="flex items-center space-x-1">
                  <input
                    type="radio"
                    name="industry"
                    value="No"
                    checked={formData.industry === "No"}
                    onChange={handleInputChange}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-700">No</span>
                </label>
                <label className="flex items-center space-x-1">
                  <input
                    type="radio"
                    name="industry"
                    value="Yes"
                    checked={formData.industry === "Yes"}
                    onChange={handleInputChange}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-700">Yes</span>
                </label>
              </div>
            </div>

            {/* Confidential */}
            <div>
              <label className={labelClasses}>
                Confidential:
              </label>
              <div className="flex items-center space-x-4 mt-1">
                <label className="flex items-center space-x-1">
                  <input
                    type="radio"
                    name="confidential"
                    value="No"
                    checked={formData.confidential === "No"}
                    onChange={handleInputChange}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-700">No</span>
                </label>
                <label className="flex items-center space-x-1">
                  <input
                    type="radio"
                    name="confidential"
                    value="Yes"
                    checked={formData.confidential === "Yes"}
                    onChange={handleInputChange}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-700">Yes</span>
                </label>
              </div>
            </div>
          </div>

          {/* Submit Message */}
          {submitMessage && (
            <div className={`border rounded-sm p-1.5 ${submitMessage.startsWith("Error") ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}`}>
              <div className="flex items-start space-x-1.5">
                <div className="flex-shrink-0 mt-0.5">
                  {submitMessage.startsWith("Error") ? (
                    <XCircle className="h-3 w-3 text-red-600" />
                  ) : (
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  )}
                </div>
                <div className={`text-xs ${submitMessage.startsWith("Error") ? "text-red-800" : "text-green-800"} break-words`}>
                  {submitMessage}
                </div>
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row gap-1.5 pt-1.5">
            <Button
              type="submit"
              disabled={isSubmitting}
              size="sm"
              className="flex items-center justify-center gap-0.5 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 w-full sm:w-auto text-xs py-1 px-2"
              onClick={() => setSaveWithGenerate(true)}
            >
              <Send className="h-2.5 w-2.5" />
              {isSubmitting ? "Submitting..." : "Save and Generate"}
            </Button>
            {/* <Button
              type="button"
              onClick={async () => {
                setSaveWithGenerate(false);
                const formEvent = new Event('submit', { bubbles: true, cancelable: true }) as any;
                await handleSubmit(formEvent);
              }}
              disabled={isSubmitting}
              variant="outline"
              size="sm"
              className="flex items-center justify-center gap-0.5 border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto text-xs py-1 px-2"
            >
              <RotateCcw className="h-2.5 w-2.5" />
              {isSubmitting ? "Saving..." : "Save without Generate"}
            </Button> */}
            <Button
              type="button"
              onClick={handleClear}
              variant="outline"
              size="sm"
              className="flex items-center justify-center gap-0.5 border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto text-xs py-1 px-2"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CatalystMatchForm; 