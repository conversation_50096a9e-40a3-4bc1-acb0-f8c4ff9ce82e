<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.6">
  <diagram name="Page-1" id="F8x6MpdoWlj6qxZZLjR1">
    <mxGraphModel dx="1027" dy="860" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-58" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=light-dark(#d0d0d0, #959595);" vertex="1" parent="1">
          <mxGeometry x="10" y="30" width="1080" height="800" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-22" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="448" y="347" width="300" height="240" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-3" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Mercury Dataverse&lt;/font&gt;&lt;/h1&gt;&lt;p&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Changes happening in Mercury such as&lt;/font&gt;&lt;/p&gt;&lt;p&gt;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Contact create, update&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Vacancy create, update&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;create, update of any other entities&lt;/font&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="70" y="50" width="270" height="130" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-4" value="" style="endArrow=classic;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;" edge="1" parent="1" target="C6zsqA57kVRJ4ae0XqwP-5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="230" as="sourcePoint" />
            <mxPoint x="200" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-7" value="&amp;nbsp;&lt;font face=&quot;Comic Sans MS&quot;&gt;1&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;textShadow=1;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-4">
          <mxGeometry x="-0.0208" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-8" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="49" y="357" width="300" height="240" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-9" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Service Endpoint&amp;nbsp;&lt;/font&gt;&lt;div&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;for Contact&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="390" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-10" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Service Endpoint&amp;nbsp;&lt;/font&gt;&lt;div&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;for Vacancy&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="60" y="510" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-11" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Contact Update&lt;/font&gt;&lt;div&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Step&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="246" y="390" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-12" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Vacancy Update Step&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="246" y="510" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-13" value="" style="endArrow=classic;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-5" target="C6zsqA57kVRJ4ae0XqwP-8">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="550" as="sourcePoint" />
            <mxPoint x="380" y="500" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-14" value="&amp;nbsp;2&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-13">
          <mxGeometry x="-0.1174" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-15" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Service Endpoint Configuration&lt;/font&gt;&lt;/h1&gt;&lt;p&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;One time configuration process to register service endpoint and steps&amp;nbsp;&lt;/font&gt;&lt;span style=&quot;font-family: &amp;quot;Comic Sans MS&amp;quot;; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;using Plugin registration tool&lt;/span&gt;&lt;span style=&quot;font-family: &amp;quot;Comic Sans MS&amp;quot;; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&amp;nbsp;&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Configure ServiceBus Endpoint to publish events&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Select event and entity type&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Optionally choose filtering attributes to limit notifications to interested fields&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Use JSON format and Asynchronous execution mode&lt;/font&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="30" y="620" width="376" height="210" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-16" value="" style="endArrow=classic;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-9" target="C6zsqA57kVRJ4ae0XqwP-11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="536" y="490" as="sourcePoint" />
            <mxPoint x="206" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-17" value="" style="endArrow=classic;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-10" target="C6zsqA57kVRJ4ae0XqwP-12">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="500" as="sourcePoint" />
            <mxPoint x="256" y="500" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-18" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-11" target="C6zsqA57kVRJ4ae0XqwP-25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="360" as="sourcePoint" />
            <mxPoint x="490" y="420" as="targetPoint" />
            <Array as="points">
              <mxPoint x="460" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-37" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;3&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-18">
          <mxGeometry x="-0.0508" y="2" relative="1" as="geometry">
            <mxPoint x="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-19" value="" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/Service_Bus.svg;" vertex="1" parent="1">
          <mxGeometry x="462.5" y="610" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-23" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="145" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-5" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-23">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-1" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/power_platform/Dataverse.svg;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-23">
          <mxGeometry x="34.05000000000001" y="10" width="51.91" height="40" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-24" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; Azure Service Bus&lt;/font&gt;&lt;/h1&gt;&lt;p&gt;&lt;span style=&quot;font-family: &amp;quot;Comic Sans MS&amp;quot;; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Setup Azure Service Bus and the destination (queue or topic)&amp;nbsp;&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Example shows queues but it&#39;s also possible to configure with topics&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Choose one destination (queue or topic) per entity type. For example, &lt;b&gt;contact-updates&lt;/b&gt;&amp;nbsp;and &lt;b&gt;vacancy-updates&lt;/b&gt;&amp;nbsp;for event notifications related to contacts and vacancies respectively.&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Service Bus and queues setup using Terraform&lt;/font&gt;&lt;/li&gt;&lt;/ul&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="432" y="607" width="320" height="210" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-29" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="449" y="370" width="297.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-25" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-29">
          <mxGeometry x="52.5" y="20" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-26" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-29" source="C6zsqA57kVRJ4ae0XqwP-25" target="C6zsqA57kVRJ4ae0XqwP-25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="202" y="70" as="sourcePoint" />
            <mxPoint x="252" y="20" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-27" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-29" source="C6zsqA57kVRJ4ae0XqwP-25" target="C6zsqA57kVRJ4ae0XqwP-25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="292" y="130" as="sourcePoint" />
            <mxPoint x="292" y="70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-28" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-29" source="C6zsqA57kVRJ4ae0XqwP-25" target="C6zsqA57kVRJ4ae0XqwP-25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="172" y="100" as="sourcePoint" />
            <mxPoint x="172" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-30" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="449" y="490" width="297.5" height="80" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-32" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-30">
          <mxGeometry x="52.5" y="20" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-33" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-30" source="C6zsqA57kVRJ4ae0XqwP-32" target="C6zsqA57kVRJ4ae0XqwP-32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="202" y="70" as="sourcePoint" />
            <mxPoint x="252" y="20" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-34" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-30" source="C6zsqA57kVRJ4ae0XqwP-32" target="C6zsqA57kVRJ4ae0XqwP-32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="292" y="130" as="sourcePoint" />
            <mxPoint x="292" y="70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-35" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="C6zsqA57kVRJ4ae0XqwP-30" source="C6zsqA57kVRJ4ae0XqwP-32" target="C6zsqA57kVRJ4ae0XqwP-32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="172" y="100" as="sourcePoint" />
            <mxPoint x="172" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-40" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&lt;b&gt;vacancy-updates&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-30">
          <mxGeometry x="90" y="-7" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-36" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-12" target="C6zsqA57kVRJ4ae0XqwP-32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="396" y="520" as="sourcePoint" />
            <mxPoint x="520" y="520" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-38" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;3&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-36">
          <mxGeometry x="-0.1477" y="5" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-39" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&lt;b&gt;contact-updates&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="539" y="360" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-31" value="" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.service_bus_queues;pointerEvents=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="520" y="470" width="34" height="40" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-20" value="" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.service_bus_queues;pointerEvents=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="524" y="351" width="34" height="40" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-42" value="&lt;font style=&quot;font-size: 16px;&quot; face=&quot;Comic Sans MS&quot;&gt;Mercury Event Listener&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="830" y="347" width="210" height="240" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-43" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Contact Update&lt;/font&gt;&lt;div&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Processor&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="890" y="388" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-44" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;Vacancy Update Processor&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="890" y="509" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-45" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-25" target="C6zsqA57kVRJ4ae0XqwP-43">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="852" y="629.5" as="sourcePoint" />
            <mxPoint x="1018" y="629.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-46" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;4&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-45">
          <mxGeometry x="-0.0508" y="2" relative="1" as="geometry">
            <mxPoint x="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-47" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-32" target="C6zsqA57kVRJ4ae0XqwP-44">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="532" as="sourcePoint" />
            <mxPoint x="978" y="530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-48" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;4&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-47">
          <mxGeometry x="-0.0508" y="2" relative="1" as="geometry">
            <mxPoint x="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-49" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;Mercury Event Listener&lt;/font&gt;&lt;/h1&gt;&lt;p&gt;&lt;span style=&quot;font-family: &amp;quot;Comic Sans MS&amp;quot;; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Processing code that listens to the events from Azure Service Bus Queues&amp;nbsp;&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;Configurable Event Listener that listens to event notifications from Azure Service Bus&lt;/font&gt;&lt;/li&gt;&lt;li&gt;&lt;font face=&quot;Comic Sans MS&quot;&gt;JSON based configuration to configure the queue to the processor and number of threads&lt;/font&gt;&lt;/li&gt;&lt;/ul&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="770" y="607" width="320" height="210" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-52" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="876" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-51" value="" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-52">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-50" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/databases/Azure_Database_PostgreSQL_Server.svg;" vertex="1" parent="C6zsqA57kVRJ4ae0XqwP-52">
          <mxGeometry x="45" y="10" width="30" height="40" as="geometry" />
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-53" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6zsqA57kVRJ4ae0XqwP-42" target="C6zsqA57kVRJ4ae0XqwP-51">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1070" y="332" as="sourcePoint" />
            <mxPoint x="1258" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-54" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;&amp;nbsp;5&amp;nbsp;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBorderColor=default;" vertex="1" connectable="0" parent="C6zsqA57kVRJ4ae0XqwP-53">
          <mxGeometry x="-0.0508" y="2" relative="1" as="geometry">
            <mxPoint x="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C6zsqA57kVRJ4ae0XqwP-56" value="&lt;font face=&quot;Comic Sans MS&quot;&gt;process update&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="940" y="280" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
