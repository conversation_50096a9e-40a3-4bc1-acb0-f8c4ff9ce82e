#!/usr/bin/env python3
"""
FastAPI health server interface.

This module provides a simple interface for starting the FastAPI health check server.
"""

import os
import sys
import threading
from typing import Optional

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from .fastapi_health import health_app

def start_health_server(
    port: int = 8080, 
    logger: Optional[AppLogger] = None,
    host: str = "0.0.0.0"
) -> threading.Thread:
    """
    Start the FastAPI health check server.
    
    Args:
        port: Port to run the health server on
        logger: Logger instance
        host: Host to bind the server to
        
    Returns:
        Thread running the FastAPI server
    """
    import uvicorn
    
    def run_server():
        try:
            if logger:
                logger.info(f"Starting FastAPI health check server on {host}:{port}")
            else:
                print(f"Starting FastAPI health check server on {host}:{port}")
            
            uvicorn.run(
                health_app, 
                host=host, 
                port=port, 
                log_level="info",
                access_log=False  # Reduce log noise
            )
        except Exception as e:
            if logger:
                logger.error(f"FastAPI health server error: {e}")
            else:
                print(f"FastAPI health server error: {e}")
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    if logger:
        logger.info("FastAPI health check server started")
        logger.info(f"Available endpoints:")
        logger.info(f"  http://{host}:{port}/ - API root")
        logger.info(f"  http://{host}:{port}/health - Health check")
        logger.info(f"  http://{host}:{port}/ready - Readiness probe")
        logger.info(f"  http://{host}:{port}/live - Liveness probe")
        logger.info(f"  http://{host}:{port}/startup - Startup probe")
        logger.info(f"  http://{host}:{port}/metrics - Prometheus metrics")
        logger.info(f"  http://{host}:{port}/docs - Swagger documentation")
        logger.info(f"  http://{host}:{port}/redoc - ReDoc documentation")
    else:
        print("FastAPI health check server started")
        print(f"Available endpoints:")
        print(f"  http://{host}:{port}/ - API root")
        print(f"  http://{host}:{port}/health - Health check")
        print(f"  http://{host}:{port}/ready - Readiness probe")
        print(f"  http://{host}:{port}/live - Liveness probe")
        print(f"  http://{host}:{port}/startup - Startup probe")
        print(f"  http://{host}:{port}/metrics - Prometheus metrics")
        print(f"  http://{host}:{port}/docs - Swagger documentation")
        print(f"  http://{host}:{port}/redoc - ReDoc documentation")
    
    return server_thread 