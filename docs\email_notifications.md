# AppLogger Email Notifications

## Overview

The `AppLogger` class now supports automatic email notifications when error-level logs are generated. This feature is designed to help with production monitoring by immediately alerting relevant team members when critical errors occur.

## Features

- **Automatic Error Detection**: Any call to `logger.error()` triggers email notification
- **Configurable Recipients**: Support for TO, CC, and BCC email addresses
- **Rich Email Content**: HTML and plain text versions with error details and stack traces
- **Graceful Degradation**: Works even if SendGrid is not available (logs warnings instead)
- **Non-Intrusive**: Email notifications are disabled by default and must be explicitly enabled
- **Thread-Safe**: Safe to use in multi-threaded applications

## Prerequisites

### 1. SendGrid Setup

The email notifications use <PERSON><PERSON><PERSON> as the email service provider. Ensure you have:

1. **SendGrid Account**: Create an account at [SendGrid](https://sendgrid.com/)
2. **API Key**: Generate an API key from your SendGrid dashboard
3. **Environment Variable**: Set the API key in your environment:
   ```bash
   export SENDGRID_API_KEY="your_sendgrid_api_key_here"
   ```

### 2. Dependencies

The required dependencies are already included in `requirements.txt`:
- `sendgrid>=6.12.0`
- `jinja2>=3.1.6`

## Configuration

### Basic Email Notification Configuration

Add an `email_notifications` section to your logger configuration:

```python
config = {
    "name": "my_app_logger",
    "log_level": "INFO",
    "log_to_stdout": True,
    "log_file": "app.log",
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>", "<EMAIL>"],
        "cc_emails": ["<EMAIL>"],
        "bcc_emails": ["<EMAIL>"],
        "subject_prefix": "ERROR ALERT"
    }
}

logger = AppLogger(config)
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enabled` | bool | `False` | Enable/disable email notifications |
| `to_emails` | list | `[]` | Primary recipients (required if enabled) |
| `cc_emails` | list | `[]` | Carbon copy recipients |
| `bcc_emails` | list | `[]` | Blind carbon copy recipients |
| `subject_prefix` | str | `"ERROR ALERT"` | Prefix for email subject line |

### Email Address Formats

You can specify email addresses in multiple formats:

```python
"email_notifications": {
    "enabled": True,
    "to_emails": [
        "<EMAIL>",
        "<EMAIL>"
    ],
    # OR as a single string (comma/semicolon separated)
    "to_emails": "<EMAIL>, <EMAIL>"
}
```

## Usage Examples

### Example 1: Basic Error Logging with Email

```python
from common.appLogger import AppLogger

# Configure logger with email notifications
config = {
    "name": "production_app",
    "log_level": "INFO",
    "log_to_stdout": False,
    "log_file": "/mnt/incoming/logs/production.log",
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "subject_prefix": "PRODUCTION ERROR"
    }
}

logger = AppLogger(config)

# This will log the error AND send an email notification
logger.error("Database connection failed")

# This will also send an email with full exception details
try:
    # Some risky operation
    result = 1 / 0
except Exception as e:
    logger.error("Division by zero error", exc_info=True)
```

### Example 2: Using LoggerFactory

```python
from common.appLogger import LoggerFactory

# Create logger with email notifications
config = {
    "name": "api_service",
    "log_level": "ERROR",
    "log_to_stdout": True,
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "cc_emails": ["<EMAIL>"],
        "subject_prefix": "API ERROR"
    }
}

logger = LoggerFactory.get_logger("api_service", config)

# Any error will trigger email notification
logger.error("API endpoint /users failed to respond")
```

### Example 3: Production Configuration

```python
# Production configuration with comprehensive email setup
production_config = {
    "name": "catalyst_match_service",
    "log_level": "INFO",
    "log_to_stdout": False,
    "log_file": "/mnt/incoming/logs/catalyst_match.log",
    "log_mode": "append",
    "email_notifications": {
        "enabled": True,
        "to_emails": [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "cc_emails": [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "bcc_emails": [
            "<EMAIL>"
        ],
        "subject_prefix": "CATALYST MATCH ERROR"
    }
}

logger = AppLogger(production_config)
```

## Email Content

### HTML Email Format

The emails include:
- **Error Message**: The actual error text
- **Logger Name**: Which logger generated the error
- **Timestamp**: When the error occurred
- **Exception Details**: Full stack trace (if available)
- **Styling**: Professional HTML formatting with error highlighting

### Plain Text Fallback

A plain text version is also included for email clients that don't support HTML.

## Error Handling

The email notification system includes robust error handling:

1. **SendGrid Unavailable**: If SendGrid is not installed, logs a warning but continues normal logging
2. **API Key Missing**: If `SENDGRID_API_KEY` is not set, logs a warning
3. **Email Send Failure**: If email sending fails, logs a warning but doesn't affect the main application
4. **No Recipients**: If email is enabled but no recipients are configured, logs a warning

## Best Practices

### 1. Selective Enablement

Only enable email notifications for critical services:

```python
# Enable for production services
if os.getenv("ENVIRONMENT") == "production":
    config["email_notifications"]["enabled"] = True
else:
    config["email_notifications"]["enabled"] = False
```

### 2. Appropriate Recipients

Choose recipients carefully to avoid email fatigue:

```python
# Different recipients for different error types
if service_type == "critical":
    config["email_notifications"]["to_emails"] = ["<EMAIL>"]
elif service_type == "important":
    config["email_notifications"]["to_emails"] = ["<EMAIL>"]
```

### 3. Meaningful Subject Prefixes

Use descriptive subject prefixes to help with email filtering:

```python
"subject_prefix": "CATALYST_MATCH_ERROR"  # Instead of generic "ERROR ALERT"
```

### 4. Environment-Specific Configuration

```python
# Load configuration based on environment
if os.getenv("ENVIRONMENT") == "production":
    email_config = {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "subject_prefix": "PROD ERROR"
    }
elif os.getenv("ENVIRONMENT") == "staging":
    email_config = {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "subject_prefix": "STAGING ERROR"
    }
else:
    email_config = {"enabled": False}
```

## Testing

### Real Email Test

Use the provided test script to verify email functionality with actual email sending:

```bash
python tests/test_real_email_send.py
```

### Manual Testing

```python
# Test with a real email address
test_config = {
    "name": "test_logger",
    "log_level": "ERROR",
    "log_to_stdout": True,
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "subject_prefix": "TEST ERROR"
    }
}

logger = AppLogger(test_config)
logger.error("This is a test error message")
```

## Troubleshooting

### Common Issues

1. **No emails received**:
   - Check `SENDGRID_API_KEY` environment variable
   - Verify email addresses are correct
   - Check SendGrid dashboard for delivery status

2. **Import errors**:
   - Ensure `sendgrid` package is installed: `pip install sendgrid`
   - Check that `utils.email.sendgrid_helper` is available

3. **Permission errors**:
   - Verify SendGrid API key has appropriate permissions
   - Check that sender email is verified in SendGrid

### Debug Mode

Enable debug logging to troubleshoot email issues:

```python
config = {
    "name": "debug_logger",
    "log_level": "DEBUG",  # Set to DEBUG to see email-related logs
    "log_to_stdout": True,
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"]
    }
}
```

## Migration Guide

### From Existing AppLogger Usage

If you're already using AppLogger, simply add the email configuration:

```python
# Before
config = {
    "name": "my_app",
    "log_level": "INFO",
    "log_file": "app.log"
}

# After (with email notifications)
config = {
    "name": "my_app",
    "log_level": "INFO",
    "log_file": "app.log",
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"]
    }
}
```

### Backward Compatibility

The email notification feature is completely backward compatible:
- Existing configurations work without changes
- Email notifications are disabled by default
- No existing code needs to be modified

## Security Considerations

1. **API Key Security**: Store SendGrid API key in environment variables, not in code
2. **Email Content**: Be careful not to log sensitive information that might be sent via email
3. **Rate Limiting**: SendGrid has rate limits; consider this for high-volume applications
4. **Recipient Validation**: Validate email addresses before adding them to configuration

## Monitoring and Maintenance

### Email Volume Monitoring

Monitor email volume to ensure you're not overwhelming recipients:

```python
# Add email volume tracking
import time

class EmailVolumeTracker:
    def __init__(self, max_emails_per_hour=10):
        self.max_emails = max_emails_per_hour
        self.email_count = 0
        self.last_reset = time.time()
    
    def should_send_email(self):
        current_time = time.time()
        if current_time - self.last_reset > 3600:  # 1 hour
            self.email_count = 0
            self.last_reset = current_time
        
        if self.email_count < self.max_emails:
            self.email_count += 1
            return True
        return False
```

### Regular Testing

Set up regular tests to ensure email notifications are working:

```python
# Automated health check
def test_email_notifications():
    logger = get_production_logger()
    logger.error("Automated health check - email notification test")
    # This should send an email to verify the system is working
```

## Conclusion

The email notification feature provides a robust way to monitor production errors in real-time. By following the best practices outlined in this documentation, you can implement effective error alerting that helps maintain system reliability without overwhelming your team with unnecessary notifications.
