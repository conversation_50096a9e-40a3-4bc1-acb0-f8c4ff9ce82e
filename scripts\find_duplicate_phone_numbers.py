#!/usr/bin/env python3
"""
Step-by-step script to find duplicate phone numbers by processing each field separately.
"""

import re
import sys
import os
from typing import Dict, List, Tuple
from collections import defaultdict

# Add the project root to the path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.appLogger import AppLogger
from common.dbconn import getProdDBConnection
from common.secrets_env import load_secrets_env_variables
from common.db.postgres_connector import PostgresConnector
from common.db.config_postgres import PostgresEnvironment





def get_telephone2_duplicates(logger: AppLogger) -> Dict[str, int]:
    """
    Get duplicate phone numbers from telephone2 field only.
    """
    try:
        connection = getProdDBConnection()
        
        if not connection:
            logger.error("Failed to connect to database")
            return {}
        
        cursor = connection.cursor()
        
        query = """
        SELECT 
            telephone2 as phone_number,
            COUNT(*) as count
        FROM contact 
        WHERE statecode = 0
        AND telephone2 IS NOT NULL 
        AND telephone2 != ''
        GROUP BY telephone2
        HAVING COUNT(*) > 1
        ORDER BY count DESC, telephone2
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        result = {row[0]: row[1] for row in rows if row[0]}  # Filter out None/empty values
        logger.info(f"Found {len(result)} duplicate phone numbers in telephone2 field")
        return result
        
    except Exception as e:
        logger.error(f"Error getting telephone2 duplicates: {str(e)}")
        return {}


def get_recruit_mobilehome_duplicates(logger: AppLogger) -> Dict[str, int]:
    """
    Get duplicate phone numbers from recruit_mobilehome field only.
    """
    try:
        connection = getProdDBConnection()
        
        if not connection:
            logger.error("Failed to connect to database")
            return {}
        
        cursor = connection.cursor()
        
        query = """
        SELECT 
            recruit_mobilehome as phone_number,
            COUNT(*) as count
        FROM contact 
        WHERE statecode = 0
        AND recruit_mobilehome IS NOT NULL 
        AND recruit_mobilehome != ''
        GROUP BY recruit_mobilehome
        HAVING COUNT(*) > 1
        ORDER BY count DESC, recruit_mobilehome
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        result = {row[0]: row[1] for row in rows if row[0]}  # Filter out None/empty values
        logger.info(f"Found {len(result)} duplicate phone numbers in recruit_mobilehome field")
        return result
        
    except Exception as e:
        logger.error(f"Error getting recruit_mobilehome duplicates: {str(e)}")
        return {}


def normalize_phone_for_grouping(phone: str) -> str:
    """
    Normalize phone number for grouping similar formats.
    Removes +1 prefix and common formatting differences.
    """
    if not phone:
        return None
    
    # Remove all non-digit characters
    digits = re.sub(r'[^\d]', '', phone.strip())
    
    # If it's 11 digits and starts with 1, remove the 1
    if len(digits) == 11 and digits.startswith('1'):
        digits = digits[1:]
    
    # Return in format XXX-XXX-XXXX
    if len(digits) == 10:
        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
    
    return phone  # Return original if can't normalize


def combine_duplicates(telephone2_duplicates: Dict[str, int], recruit_mobilehome_duplicates: Dict[str, int], logger: AppLogger) -> Dict[str, int]:
    """
    Combine duplicates from both fields, adding up counts for phone numbers that appear in both.
    """
    combined = defaultdict(int)
    
    # Add telephone2 duplicates
    for phone, count in telephone2_duplicates.items():
        combined[phone] += count
    
    # Add recruit_mobilehome duplicates
    for phone, count in recruit_mobilehome_duplicates.items():
        combined[phone] += count
    
    # Convert to regular dict and sort by count descending
    result = dict(sorted(combined.items(), key=lambda x: (-x[1], x[0])))
    
    logger.info(f"Combined results: {len(result)} total duplicate phone numbers")
    return result


def categorize_phone_numbers(combined_duplicates: Dict[str, int], grouped_duplicates: Dict[str, tuple], logger: AppLogger) -> tuple:
    """
    Categorize phone numbers into three lists:
    1. US numbers (typically has +1, 1 and/or 10 digits)
    2. Non-numeric or less than 10 digits
    3. Non-US numbers
    """
    us_numbers = []
    non_numeric_short = []
    non_us_numbers = []
    
    # First, categorize all phone numbers from combined_duplicates
    for phone, count in combined_duplicates.items():
        # Check if this is a US number
        digits = re.sub(r'[^\d]', '', phone)
        
        # US number patterns: 10 digits, 11 digits starting with 1, or starts with +1
        if (len(digits) == 10 or 
            (len(digits) == 11 and digits.startswith('1')) or 
            phone.startswith('+1')):
            us_numbers.append((phone, [phone], count))
        else:
            # Check if it's non-numeric or short
            has_non_numeric = re.search(r'[^0-9+\-\(\)\s]', phone)
            is_short = len(digits) < 10
            
            if has_non_numeric or is_short:
                non_numeric_short.append((phone, [phone], count))
            else:
                non_us_numbers.append((phone, [phone], count))
    
    # Then, add grouped entries (these will replace individual entries if they exist)
    for normalized, (original_numbers, total_count) in grouped_duplicates.items():
        # Check if this is a US number
        is_us = False
        for phone in original_numbers:
            digits = re.sub(r'[^\d]', '', phone)
            if (len(digits) == 10 or 
                (len(digits) == 11 and digits.startswith('1')) or 
                phone.startswith('+1')):
                is_us = True
                break
        
        if is_us:
            # Remove individual entries that are part of this group
            us_numbers = [(p, nums, c) for p, nums, c in us_numbers 
                         if not any(orig in nums for orig in original_numbers)]
            us_numbers.append((normalized, original_numbers, total_count))
        else:
            # Check if it's non-numeric or short
            has_non_numeric = False
            is_short = False
            
            for phone in original_numbers:
                if re.search(r'[^0-9+\-\(\)\s]', phone):
                    has_non_numeric = True
                    break
                digits = re.sub(r'[^\d]', '', phone)
                if len(digits) < 10:
                    is_short = True
            
            if has_non_numeric or is_short:
                # Remove individual entries that are part of this group
                non_numeric_short = [(p, nums, c) for p, nums, c in non_numeric_short 
                                   if not any(orig in nums for orig in original_numbers)]
                non_numeric_short.append((normalized, original_numbers, total_count))
            else:
                # Remove individual entries that are part of this group
                non_us_numbers = [(p, nums, c) for p, nums, c in non_us_numbers 
                                if not any(orig in nums for orig in original_numbers)]
                non_us_numbers.append((normalized, original_numbers, total_count))
    
    # Sort each list by count descending
    us_numbers.sort(key=lambda x: (-x[2], x[0]))
    non_numeric_short.sort(key=lambda x: (-x[2], x[0]))
    non_us_numbers.sort(key=lambda x: (-x[2], x[0]))
    
    logger.info(f"Categorized phone numbers:")
    logger.info(f"  US numbers: {len(us_numbers)} groups")
    logger.info(f"  Non-numeric/short: {len(non_numeric_short)} groups")
    logger.info(f"  Non-US numbers: {len(non_us_numbers)} groups")
    
    return us_numbers, non_numeric_short, non_us_numbers


def group_similar_phone_numbers(combined_duplicates: Dict[str, int], logger: AppLogger) -> Dict[str, tuple]:
    """
    Group similar phone numbers and combine their counts.
    Returns dict with normalized key and (original_numbers, total_count) as value.
    """
    grouped = {}
    
    for phone, count in combined_duplicates.items():
        normalized = normalize_phone_for_grouping(phone)
        if normalized:
            if normalized not in grouped:
                grouped[normalized] = ([], 0)
            grouped[normalized][0].append(phone)
            grouped[normalized] = (grouped[normalized][0], grouped[normalized][1] + count)
        else:
            # For entries that can't be normalized, use the original phone as the key
            if phone not in grouped:
                grouped[phone] = ([], 0)
            grouped[phone][0].append(phone)
            grouped[phone] = (grouped[phone][0], grouped[phone][1] + count)
    
    # Convert to regular dict and sort by count descending
    result = {}
    for normalized, (original_numbers, total_count) in grouped.items():
        if len(original_numbers) > 1:  # Only include if there are multiple similar formats
            result[normalized] = (original_numbers, total_count)
    
    result = dict(sorted(result.items(), key=lambda x: (-x[1][1], x[0])))
    
    logger.info(f"Grouped similar phone numbers: {len(result)} groups")
    return result


def print_results(telephone2_duplicates: Dict[str, int], recruit_mobilehome_duplicates: Dict[str, int], combined_duplicates: Dict[str, int], grouped_duplicates: Dict[str, tuple], us_numbers: list, non_numeric_short: list, non_us_numbers: list, logger: AppLogger):
    """
    Print results for all scenarios including grouped similar numbers.
    """
    logger.info("\n" + "="*60)
    logger.info("STEP-BY-STEP DUPLICATE PHONE NUMBER ANALYSIS")
    logger.info("="*60)
    
    # Step 1: Telephone2 duplicates
    logger.info("\nSTEP 1: Duplicates in telephone2 field only")
    logger.info("Phone Number,Count")
    for phone, count in telephone2_duplicates.items():
        logger.info(f"{phone},{count}")
    
    # Step 2: Recruit_mobilehome duplicates
    logger.info("\nSTEP 2: Duplicates in recruit_mobilehome field only")
    logger.info("Phone Number,Count")
    for phone, count in recruit_mobilehome_duplicates.items():
        logger.info(f"{phone},{count}")
    
    # Step 3: Combined duplicates
    logger.info("\nSTEP 3: Combined duplicates (sum of both fields)")
    logger.info("Phone Number,Count")
    for phone, count in combined_duplicates.items():
        logger.info(f"{phone},{count}")
    
    # Step 4: Grouped similar phone numbers
    logger.info("\nSTEP 4: Grouped similar phone numbers")
    logger.info("Normalized Number,Original Numbers,Total Count")
    for normalized, (original_numbers, total_count) in grouped_duplicates.items():
        original_str = str(original_numbers)
        logger.info(f"{normalized},{original_str},{total_count}")
    
    # Step 5: Categorized phone numbers
    logger.info("\nSTEP 5: Categorized phone numbers")
    
    logger.info("\n1. US Numbers (typically has +1, 1 and/or 10 digits):")
    logger.info("Normalized Number,Original Numbers,Total Count")
    for normalized, original_numbers, total_count in us_numbers:
        original_str = str(original_numbers)
        logger.info(f"{normalized},{original_str},{total_count}")
    
    logger.info("\n2. Non-numeric or less than 10 digits:")
    logger.info("Normalized Number,Original Numbers,Total Count")
    for normalized, original_numbers, total_count in non_numeric_short:
        original_str = str(original_numbers)
        logger.info(f"{normalized},{original_str},{total_count}")
    
    logger.info("\n3. Non-US numbers:")
    logger.info("Normalized Number,Original Numbers,Total Count")
    for normalized, original_numbers, total_count in non_us_numbers:
        original_str = str(original_numbers)
        logger.info(f"{normalized},{original_str},{total_count}")
    
    # Summary with totals
    total_telephone2_count = sum(telephone2_duplicates.values())
    total_recruit_mobilehome_count = sum(recruit_mobilehome_duplicates.values())
    total_combined_count = sum(combined_duplicates.values())
    
    # Count by occurrence frequency (cumulative) - both phone count and total occurrences
    def get_cumulative_counts_and_occurrences(duplicates_dict):
        phone_counts = {2: 0, 3: 0, 4: 0, 5: 0}
        total_occurrences = {2: 0, 3: 0, 4: 0, 5: 0}
        
        for count in duplicates_dict.values():
            if count >= 2:
                phone_counts[2] += 1
                total_occurrences[2] += count
            if count >= 3:
                phone_counts[3] += 1
                total_occurrences[3] += count
            if count >= 4:
                phone_counts[4] += 1
                total_occurrences[4] += count
            if count >= 5:
                phone_counts[5] += 1
                total_occurrences[5] += count
        
        return phone_counts, total_occurrences
    
    telephone2_phone_counts, telephone2_occurrences = get_cumulative_counts_and_occurrences(telephone2_duplicates)
    recruit_mobilehome_phone_counts, recruit_mobilehome_occurrences = get_cumulative_counts_and_occurrences(recruit_mobilehome_duplicates)
    combined_phone_counts, combined_occurrences = get_cumulative_counts_and_occurrences(combined_duplicates)
    
    logger.info("\n" + "="*60)
    logger.info("SUMMARY TOTALS (CUMULATIVE)")
    logger.info("="*60)
    logger.info("telephone2 field:")
    logger.info(f"  Appears >2 times: {telephone2_phone_counts[2]} phone numbers ({telephone2_occurrences[2]} total occurrences)")
    logger.info(f"  Appears >3 times: {telephone2_phone_counts[3]} phone numbers ({telephone2_occurrences[3]} total occurrences)")
    logger.info(f"  Appears >4 times: {telephone2_phone_counts[4]} phone numbers ({telephone2_occurrences[4]} total occurrences)")
    logger.info(f"  Appears >5 times: {telephone2_phone_counts[5]} phone numbers ({telephone2_occurrences[5]} total occurrences)")
    
    logger.info("\nrecruit_mobilehome field:")
    logger.info(f"  Appears >2 times: {recruit_mobilehome_phone_counts[2]} phone numbers ({recruit_mobilehome_occurrences[2]} total occurrences)")
    logger.info(f"  Appears >3 times: {recruit_mobilehome_phone_counts[3]} phone numbers ({recruit_mobilehome_occurrences[3]} total occurrences)")
    logger.info(f"  Appears >4 times: {recruit_mobilehome_phone_counts[4]} phone numbers ({recruit_mobilehome_occurrences[4]} total occurrences)")
    logger.info(f"  Appears >5 times: {recruit_mobilehome_phone_counts[5]} phone numbers ({recruit_mobilehome_occurrences[5]} total occurrences)")
    
    logger.info("\nCombined (both fields):")
    logger.info(f"  Appears >2 times: {combined_phone_counts[2]} phone numbers ({combined_occurrences[2]} total occurrences)")
    logger.info(f"  Appears >3 times: {combined_phone_counts[3]} phone numbers ({combined_occurrences[3]} total occurrences)")
    logger.info(f"  Appears >4 times: {combined_phone_counts[4]} phone numbers ({combined_occurrences[4]} total occurrences)")
    logger.info(f"  Appears >5 times: {combined_phone_counts[5]} phone numbers ({combined_occurrences[5]} total occurrences)")
    logger.info("="*60)


def export_to_csv(combined_duplicates: Dict[str, int], grouped_duplicates: Dict[str, tuple], us_numbers: list, non_numeric_short: list, non_us_numbers: list, logger: AppLogger = None):
    """
    Export categorized results to 3 separate CSV files.
    """
    import csv
    
    # Export US numbers
    us_filename = "/mnt/incoming/temp/duplicate_phone_numbers_us.csv"
    with open(us_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Normalized_Number', 'Original_Numbers', 'Total_Count'])
        
        for normalized, original_numbers, total_count in us_numbers:
            original_str = str(original_numbers)
            writer.writerow([normalized, original_str, total_count])
    
    # Export non-numeric/short numbers
    non_numeric_filename = "/mnt/incoming/temp/duplicate_phone_numbers_non_numeric_short.csv"
    with open(non_numeric_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Normalized_Number', 'Original_Numbers', 'Total_Count'])
        
        for normalized, original_numbers, total_count in non_numeric_short:
            original_str = str(original_numbers)
            writer.writerow([normalized, original_str, total_count])
    
    # Export non-US numbers
    non_us_filename = "/mnt/incoming/temp/duplicate_phone_numbers_non_us.csv"
    with open(non_us_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Normalized_Number', 'Original_Numbers', 'Total_Count'])
        
        for normalized, original_numbers, total_count in non_us_numbers:
            original_str = str(original_numbers)
            writer.writerow([normalized, original_str, total_count])
    
    # Export all combined results
    all_filename = "/mnt/incoming/temp/duplicate_phone_numbers_all.csv"
    with open(all_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Phone_Number', 'Count'])
        
        for phone, count in combined_duplicates.items():
            writer.writerow([phone, count])
    
    if logger:
        logger.info(f"US numbers exported to {us_filename}")
        logger.info(f"Non-numeric/short numbers exported to {non_numeric_filename}")
        logger.info(f"Non-US numbers exported to {non_us_filename}")
        logger.info(f"All combined results exported to {all_filename}")
    else:
        print(f"US numbers exported to {us_filename}")
        print(f"Non-numeric/short numbers exported to {non_numeric_filename}")
        print(f"Non-US numbers exported to {non_us_filename}")
        print(f"All combined results exported to {all_filename}")


def normalize_phone_to_blacklist_format(phone: str) -> str:
    """
    Normalize phone number to +1XXXXXXXXXX format for blacklist storage.
    """
    if not phone:
        return None
    
    # Remove all non-digit characters
    digits = re.sub(r'[^\d]', '', phone.strip())
    
    # If it's 10 digits, add +1 prefix
    if len(digits) == 10:
        return f"+1{digits}"
    # If it's 11 digits and starts with 1, add + prefix
    elif len(digits) == 11 and digits.startswith('1'):
        return f"+{digits}"
    # If it already starts with +1 and has 11 digits, return as is
    elif phone.startswith('+1') and len(digits) == 11:
        return phone
    # If it starts with + and has 11 digits, return as is
    elif phone.startswith('+') and len(digits) == 11:
        return phone
    else:
        return None


def add_to_blacklist_database(us_numbers: list, logger: AppLogger) -> bool:
    """
    Add US phone numbers with count >= 5 to the PostgreSQL blacklist database.
    """
    try:
        # Filter US numbers with count >= 5
        blacklist_candidates = []
        for normalized, original_numbers, total_count in us_numbers:
            if total_count >= 5:
                # Try to normalize each original number to blacklist format
                for phone in original_numbers:
                    normalized_phone = normalize_phone_to_blacklist_format(phone)
                    if normalized_phone:
                        blacklist_candidates.append(normalized_phone)
        
        if not blacklist_candidates:
            logger.info("No US phone numbers with count >= 5 found for blacklist")
            return True
        
        # Remove duplicates from candidates
        blacklist_candidates = list(set(blacklist_candidates))
        logger.info(f"Found {len(blacklist_candidates)} unique US phone numbers with count >= 5 for blacklist")
        
        # Connect to PostgreSQL
        db_connector = PostgresConnector(env=PostgresEnvironment.PROD, logger=logger)
        connection = db_connector.connect()
        
        if not connection:
            logger.error("Failed to connect to PostgreSQL database")
            return False
        
        cursor = connection.cursor()
        
        # Insert phone numbers into blacklist
        insert_sql = """
        INSERT INTO telephone_blacklist (phone_number)
        VALUES (%s)
        ON CONFLICT (phone_number) 
        DO UPDATE SET modified_at = CURRENT_TIMESTAMP
        """
        
        imported_count = 0
        for phone_number in blacklist_candidates:
            try:
                cursor.execute(insert_sql, (phone_number,))
                imported_count += 1
            except Exception as e:
                logger.error(f"Error inserting phone number {phone_number}: {str(e)}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logger.info(f"Successfully added {imported_count} phone numbers to blacklist database")
        return True
        
    except Exception as e:
        logger.error(f"Error adding to blacklist database: {str(e)}")
        return False


def create_blacklist_table(logger: AppLogger) -> bool:
    """
    Create the telephone number blacklist table in PostgreSQL.
    """
    try:
        # Connect to PostgreSQL
        db_connector = PostgresConnector(env=PostgresEnvironment.PROD, logger=logger)
        connection = db_connector.connect()
        
        if not connection:
            logger.error("Failed to connect to PostgreSQL database")
            return False
        
        cursor = connection.cursor()
        
        # Create the blacklist table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS telephone_blacklist (
            id SERIAL PRIMARY KEY,
            phone_number VARCHAR(15) UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Create index on phone_number for fast lookups
        CREATE INDEX IF NOT EXISTS idx_telephone_blacklist_phone_number 
        ON telephone_blacklist(phone_number);
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        
        cursor.close()
        connection.close()
        
        logger.info("Telephone blacklist table created/verified successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating blacklist table: {str(e)}")
        return False


def main():
    """Main function to run the step-by-step duplicate phone number analysis."""
    # Setup logging
    logger_config = {
        "level": "INFO",
        "log_to_stdout": False,
        "use_json": False,
        "log_file": "/mnt/incoming/logs/duplicate_phone_numbers_step_by_step.log"
    }
    logger = AppLogger(logger_config)
    
    logger.info("Starting step-by-step duplicate phone number analysis...")
    
    # Load environment variables
    load_secrets_env_variables()
    
    try:
        # Step 0: Create blacklist table (if it doesn't exist)
        logger.info("Step 0: Creating/verifying blacklist table...")
        table_created = create_blacklist_table(logger)
        if not table_created:
            logger.error("Failed to create blacklist table. Exiting.")
            return
        
        # Step 1: Get telephone2 duplicates
        logger.info("Step 1: Analyzing telephone2 field...")
        telephone2_duplicates = get_telephone2_duplicates(logger)
        
        # Step 2: Get recruit_mobilehome duplicates
        logger.info("Step 2: Analyzing recruit_mobilehome field...")
        recruit_mobilehome_duplicates = get_recruit_mobilehome_duplicates(logger)
        
        # Step 3: Combine results
        logger.info("Step 3: Combining results...")
        combined_duplicates = combine_duplicates(telephone2_duplicates, recruit_mobilehome_duplicates, logger)
        
        if not combined_duplicates:
            logger.info("No duplicate phone numbers found.")
            return
        
        # Step 4: Group similar phone numbers
        logger.info("Step 4: Grouping similar phone numbers...")
        grouped_duplicates = group_similar_phone_numbers(combined_duplicates, logger)
        
        # Step 5: Categorize phone numbers
        logger.info("Step 5: Categorizing phone numbers...")
        us_numbers, non_numeric_short, non_us_numbers = categorize_phone_numbers(combined_duplicates, grouped_duplicates, logger)
        
        # Print results
        print_results(telephone2_duplicates, recruit_mobilehome_duplicates, combined_duplicates, grouped_duplicates, us_numbers, non_numeric_short, non_us_numbers, logger)
        
        # Export to CSV
        export_to_csv(combined_duplicates, grouped_duplicates, us_numbers, non_numeric_short, non_us_numbers, logger=logger)
        
        # Step 6: Add US phone numbers with count >= 5 to blacklist database
        logger.info("Step 6: Adding US phone numbers to blacklist database...")
        blacklist_success = add_to_blacklist_database(us_numbers, logger)
        if blacklist_success:
            logger.info("Successfully updated blacklist database")
        else:
            logger.error("Failed to update blacklist database")
        
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        raise


if __name__ == "__main__":
    main() 