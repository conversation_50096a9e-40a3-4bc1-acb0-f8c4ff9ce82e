#!/usr/bin/env python3
"""
Test functions for generator config integration.

This module tests that the generator module correctly reads configuration
from the catalyst_match_config.json file.
"""

import sys
import os
import json
from unittest.mock import patch, mock_open

# Add the project root to the path
sys.path.append(os.path.abspath('.'))

from catalyst_match.config_helper import (
    get_support_team_emails,
    get_fallback_email,
    get_support_email,
    load_catalyst_match_config
)


def test_config_loading():
    """Test that config is loaded correctly from the actual file."""
    print("Testing config loading from actual file...")
    
    try:
        config = load_catalyst_match_config()
        print(f"✓ Config loaded successfully: {config}")
        return True
    except Exception as e:
        print(f"❌ Failed to load config: {e}")
        return False


def test_support_team_emails():
    """Test that support team emails are read correctly."""
    print("Testing support team emails...")
    
    try:
        emails = get_support_team_emails()
        expected_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        if emails == expected_emails:
            print(f"✓ Support team emails match expected: {emails}")
            return True
        else:
            print(f"❌ Support team emails mismatch:")
            print(f"   Expected: {expected_emails}")
            print(f"   Got: {emails}")
            return False
    except Exception as e:
        print(f"❌ Failed to get support team emails: {e}")
        return False


def test_fallback_email():
    """Test that fallback email is read correctly."""
    print("Testing fallback email...")
    
    try:
        fallback_email = get_fallback_email()
        expected_email = "<EMAIL>"
        
        if fallback_email == expected_email:
            print(f"✓ Fallback email matches expected: {fallback_email}")
            return True
        else:
            print(f"❌ Fallback email mismatch:")
            print(f"   Expected: {expected_email}")
            print(f"   Got: {fallback_email}")
            return False
    except Exception as e:
        print(f"❌ Failed to get fallback email: {e}")
        return False


def test_support_email():
    """Test that support email is read correctly."""
    print("Testing support email...")
    
    try:
        support_email = get_support_email()
        expected_email = "<EMAIL>"
        
        if support_email == expected_email:
            print(f"✓ Support email matches expected: {support_email}")
            return True
        else:
            print(f"❌ Support email mismatch:")
            print(f"   Expected: {expected_email}")
            print(f"   Got: {support_email}")
            return False
    except Exception as e:
        print(f"❌ Failed to get support email: {e}")
        return False


def test_fallback_behavior():
    """Test fallback behavior when config file is missing."""
    print("Testing fallback behavior with missing config...")
    
    # Mock the config file to be missing
    with patch('builtins.open', side_effect=FileNotFoundError("Config file not found")):
        try:
            emails = get_support_team_emails()
            fallback_email = get_fallback_email()
            support_email = get_support_email()
            
            print(f"✓ Fallback emails loaded: {emails}")
            print(f"✓ Fallback email: {fallback_email}")
            print(f"✓ Fallback support email: {support_email}")
            return True
        except Exception as e:
            print(f"❌ Fallback behavior failed: {e}")
            return False


def test_email_logic_simulation():
    """Simulate the email logic used in job_template_processor.py."""
    print("Testing email logic simulation...")
    
    try:
        # Simulate the recipient logic from job_template_processor.py
        def simulate_recipient_logic(deliveryowner=None, owninguser=None):
            if deliveryowner and '@' in deliveryowner:
                return deliveryowner, "delivery owner"
            elif owninguser and '@' in owninguser:
                return owninguser, "owning user"
            else:
                return get_fallback_email(), "fallback"
        
        # Test cases
        test_cases = [
            ("<EMAIL>", "<EMAIL>", "<EMAIL>", "delivery owner"),
            (None, "<EMAIL>", "<EMAIL>", "owning user"),
            ("invalid", "invalid", get_fallback_email(), "fallback"),
            (None, None, get_fallback_email(), "fallback"),
        ]
        
        for deliveryowner, owninguser, expected_email, expected_source in test_cases:
            actual_email, actual_source = simulate_recipient_logic(deliveryowner, owninguser)
            if actual_email == expected_email:
                print(f"✓ {expected_source} logic works: {actual_email}")
            else:
                print(f"❌ {expected_source} logic failed:")
                print(f"   Expected: {expected_email}")
                print(f"   Got: {actual_email}")
                return False
        
        # Test CC emails
        cc_emails = get_support_team_emails()
        print(f"✓ CC emails: {cc_emails}")
        
        # Test support email
        support_email = get_support_email()
        print(f"✓ Support email: {support_email}")
        
        return True
    except Exception as e:
        print(f"❌ Email logic simulation failed: {e}")
        return False


def test_config_structure():
    """Test that the config has the expected structure."""
    print("Testing config structure...")
    
    try:
        config = load_catalyst_match_config()
        
        # Check required sections
        required_sections = ["support_team_emails", "run_parameters"]
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing required section: {section}")
                return False
        
        # Check support_team_emails structure
        support_emails = config["support_team_emails"]
        required_fields = ["recipients", "fallback_email", "support_email"]
        for field in required_fields:
            if field not in support_emails:
                print(f"❌ Missing required field in support_team_emails: {field}")
                return False
        
        # Check recipients is a list
        if not isinstance(support_emails["recipients"], list):
            print("❌ recipients field is not a list")
            return False
        
        # Check emails are strings
        for email in support_emails["recipients"]:
            if not isinstance(email, str) or '@' not in email:
                print(f"❌ Invalid email in recipients: {email}")
                return False
        
        print("✓ Config structure is valid")
        return True
    except Exception as e:
        print(f"❌ Config structure test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Testing generator config integration...\n")
    
    tests = [
        ("Config Loading", test_config_loading),
        ("Support Team Emails", test_support_team_emails),
        ("Fallback Email", test_fallback_email),
        ("Support Email", test_support_email),
        ("Fallback Behavior", test_fallback_behavior),
        ("Email Logic Simulation", test_email_logic_simulation),
        ("Config Structure", test_config_structure),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Config integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
