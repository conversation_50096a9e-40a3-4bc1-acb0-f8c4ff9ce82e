#!/usr/bin/env python3
"""
Entity processors for Dataverse entity updates.

This module provides a modular framework for handling different entity types
(contacts, vacancies) from Dataverse Service Bus messages. It includes both
the base processor interface and concrete implementations.
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from dataverse_helper.token_manager import Environment, get_token_for_env, get_dataverse_credentials_for_env
from dataverse_helper.dv_common import read_fields_from_dataverse
from dataverse_sb_listener.messaging.message_storage import MessageStorage
from dataverse_sb_listener.utils.field_change_detector import FieldChangeDetector, create_detector_for_entity

def safe_json_serializer(obj):
    """
    Custom JSON serializer for objects not serializable by default json code.
    Handles datetime objects and other complex types.
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif hasattr(obj, '__dict__'):
        return str(obj)
    else:
        return str(obj)

class ProcessorType(Enum):
    """Types of processors supported by the system."""
    CONTACT = "contact"
    VACANCY = "crimson_vacancy"
    # Add more entity types as needed

class ChannelType(Enum):
    """Types of Service Bus channels supported."""
    TOPIC = "topic"
    QUEUE = "queue"

@dataclass
class ProcessorConfig:
    """Configuration for an entity processor."""
    entity_name: str
    channel_name: str  # Topic name or queue name
    channel_type: ChannelType = ChannelType.QUEUE  # Default to queue for better reliability
    subscription_name: Optional[str] = None  # Only required for topics
    max_workers: int = 25
    max_retries: int = 3
    retry_delay: float = 5.0
    enabled: bool = True
    priority: int = 0  # Higher priority processors get processed first
    environment: Optional[str] = None  # Environment (SANDBOX, UAT, PROD)
    
    # Convenience properties for channel access
    @property
    def topic_name(self) -> str:
        """Get channel name for topics."""
        if self.channel_type == ChannelType.TOPIC:
            return self.channel_name
        raise ValueError(f"Channel type is {self.channel_type}, not a topic")
    
    @property
    def queue_name(self) -> str:
        """Get channel name for queues."""
        if self.channel_type == ChannelType.QUEUE:
            return self.channel_name
        raise ValueError(f"Channel type is {self.channel_type}, not a queue")

class BaseEntityProcessor(ABC):
    """
    Abstract base class for all entity processors.
    
    This defines the contract that all entity processors must implement,
    ensuring consistency and enabling the generic framework to work with
    any entity type.
    """
    
    def __init__(self, config: ProcessorConfig, logger=None):
        """
        Initialize the processor with configuration.
        
        Args:
            config: Processor configuration
            logger: Logger instance
        """
        self.config = config
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        self.entity_name = config.entity_name
        self.channel_name = config.channel_name
        self.channel_type = config.channel_type
        self.subscription_name = config.subscription_name
        
        # Configuration support
        self.topic_name = config.channel_name if config.channel_type == ChannelType.TOPIC else None
        
        # Initialize Dataverse connectivity
        environment_name = config.environment or 'SANDBOX'
        self.environment = Environment[environment_name]
        self.token = get_token_for_env(self.environment, logger=self.logger)
        self.credentials = get_dataverse_credentials_for_env(self.environment, logger=self.logger)
        
        # Initialize field change detector
        self.field_detector = create_detector_for_entity(config.entity_name, self.logger)
        self.dataverse_url = self.credentials["RESOURCE_URL"]
        
        # Initialize message storage
        # Check environment variable for message storage enablement
        env_enabled = os.getenv('MERCURY_EVENTS_STORAGE_ENABLED', 'true').lower()
        storage_enabled = env_enabled not in ('false', '0', 'no', 'disabled')
        
        self.message_storage = MessageStorage(
            logger=self.logger,
            enabled=storage_enabled
        )
        
    @abstractmethod
    def process_entity_update(self, entity_info: Dict[str, Any]) -> bool:
        """
        Process an entity update.
        
        Args:
            entity_info: Parsed entity information from the message
            
        Returns:
            True if processing was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def get_entity_fields(self) -> List[str]:
        """
        Get the list of fields to fetch from Dataverse for this entity.
        
        Returns:
            List of field names to fetch
        """
        pass
    
    @abstractmethod
    def validate_entity_data(self, entity_data: Dict[str, Any]) -> bool:
        """
        Validate entity data before processing.
        
        Args:
            entity_data: Entity data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        pass
    
    def pre_process(self, entity_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pre-process entity information before main processing.
        Override in subclasses if needed.
        
        Args:
            entity_info: Raw entity information
            
        Returns:
            Processed entity information
        """
        return entity_info
    
    def post_process(self, entity_info: Dict[str, Any], success: bool) -> None:
        """
        Post-process after main processing.
        Override in subclasses if needed.
        
        Args:
            entity_info: Entity information that was processed
            success: Whether processing was successful
        """
        pass
    
    def handle_error(self, entity_info: Dict[str, Any], error: Exception) -> None:
        """
        Handle processing errors.
        Override in subclasses if needed.
        
        Args:
            entity_info: Entity information that failed to process
            error: The error that occurred
        """
        if self.logger:
            self.logger.error(f"Error processing {self.entity_name}: {error}")
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """
        Get processor-specific statistics.
        Override in subclasses if needed.
        
        Returns:
            Dictionary of processor statistics
        """
        stats = {
            "entity_name": self.entity_name,
            "channel_name": self.channel_name,
            "channel_type": self.channel_type.value,
            "enabled": self.config.enabled,
            "priority": self.config.priority
        }
        
        # Add topic-specific info for configuration
        if self.channel_type == ChannelType.TOPIC:
            stats["topic_name"] = self.channel_name
            stats["subscription_name"] = self.subscription_name
        elif self.channel_type == ChannelType.QUEUE:
            stats["queue_name"] = self.channel_name
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform processor-specific health checks.
        Override in subclasses if needed.
        
        Returns:
            Dictionary with health status information
        """
        return {
            "processor_type": self.entity_name,
            "status": "healthy",
            "enabled": self.config.enabled
        }
    
    def store_message(self, msg, entity_info: Dict[str, Any]) -> Optional[str]:
        """
        Store a message for replay purposes.
        
        Args:
            msg: Service Bus message object
            entity_info: Parsed entity information
            
        Returns:
            Path to stored file or None if storage failed
        """
        return self.message_storage.store_message(msg, entity_info)
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get message storage statistics for this processor.
        
        Returns:
            Dictionary containing storage statistics
        """
        return self.message_storage.get_storage_stats()
    
    def detect_and_log_field_changes(self, entity_info: Dict[str, Any]) -> bool:
        """
        Detect and log field changes in the entity update.
        
        Args:
            entity_info: Entity information from the message
            
        Returns:
            True if message should be processed, False if it should be skipped
        """
        try:
            # Detect field changes
            changes = self.field_detector.detect_changes(entity_info)
            
            # Log the changes
            self.field_detector.log_changes(entity_info, changes)
            
            # Check if we should process this message based on field changes
            should_process = self.field_detector.should_process_message(entity_info)
            
            if should_process:
                self.logger.info(f"Processing message - relevant fields changed")
            else:
                self.logger.info(f"Skipping message - no relevant fields changed")
            
            return should_process
            
        except Exception as e:
            self.logger.error(f"Error in field change detection: {e}")
            # Default to processing the message if detection fails
            return True

class ContactProcessor(BaseEntityProcessor):
    """
    Processor for contact entity updates.
    
    This processor handles contact updates from Dataverse and stores them
    in the local database.
    """
    
    def process_entity_update(self, entity_info: Dict[str, Any]) -> bool:
        """
        Process a contact update.
        
        Args:
            entity_info: Parsed entity information from the message
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            self.logger.info(f"=== CONTACT PROCESSOR DEBUG ===")
            self.logger.info(f"Received contact update: {entity_info.get('entity_id')}")
            self.logger.info(f"Entity name: {entity_info.get('entity_name')}")
            self.logger.info(f"Message type: {entity_info.get('message_type')}")
            self.logger.info(f"Attributes count: {len(entity_info.get('attributes', {}))}")
            
            # DEBUG: Log the actual attributes
            attributes = entity_info.get('attributes', {})
            if attributes:
                try:
                    self.logger.debug(f"Contact attributes: {json.dumps(attributes, indent=2, default=safe_json_serializer)}")
                except Exception as e:
                    self.logger.debug(f"Contact attributes (fallback): {str(attributes)}")
            else:
                self.logger.debug("No attributes found in contact message")
            
            # Detect and log field changes
            should_process = self.detect_and_log_field_changes(entity_info)
            self.logger.info(f"Field detection result: should_process = {should_process}")
            
            if not should_process:
                self.logger.info("Skipping contact update - no relevant fields changed")
                return True  # Return True to indicate successful skip
            
            # Pre-process the entity info
            processed_info = self.pre_process(entity_info)
            self.logger.info(f"Pre-processed info: {processed_info.get('entity_id')}")
            
            # Validate the data
            if not self.validate_entity_data(processed_info):
                self.logger.error(f"Invalid contact data: {processed_info}")
                return False
            
            # Extract entity ID
            entity_id = processed_info.get('entity_id')
            if not entity_id:
                self.logger.error("No entity ID found in contact update")
                return False
            
            self.logger.info(f"Processing contact update for ID: {entity_id}")
            
            # Fetch latest data from Dataverse
            contact_data = self._fetch_contact_from_dataverse(entity_id)
            if not contact_data:
                self.logger.error(f"Failed to fetch contact data for ID: {entity_id}")
                return False
            
            # Store in database
            success = self._store_contact_in_database(contact_data)
            if not success:
                self.logger.error(f"Failed to store contact data for ID: {entity_id}")
                return False
            
            # Post-process
            self.post_process(processed_info, success)
            
            self.logger.info(f"Successfully processed contact update for ID: {entity_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Exception in contact processing: {e}")
            self.handle_error(entity_info, e)
            return False
    
    def get_entity_fields(self) -> List[str]:
        """Get the list of fields to fetch from Dataverse for contacts."""
        return [
                'contactid', 'fullname', 'emailaddress1', 'telephone1',
                'recruit_availability', 'address2_city', 'address2_stateorprovince',
                'address2_country', 'recruit_iscandidatecontact', 'modifiedon', 'createdon'
        ]
    
    def validate_entity_data(self, entity_data: Dict[str, Any]) -> bool:
        """
        Validate contact data before processing.
        
        Args:
            entity_data: Contact data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Basic validation - ensure we have required fields
        required_fields = ['entity_id', 'entity_name']
        for field in required_fields:
            if field not in entity_data:
                self.logger.warning(f"Missing required field '{field}' in contact data")
                return False
        
        # Validate entity name
        if entity_data.get('entity_name') != 'contact':
            self.logger.warning(f"Invalid entity name: {entity_data.get('entity_name')}")
            return False
        
        return True
    
    def pre_process(self, entity_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pre-process contact information before main processing.
        
        Args:
            entity_info: Raw entity information
            
        Returns:
            Processed entity information
        """
        # Ensure we have the entity name set correctly
        entity_info['entity_name'] = 'contact'
        return entity_info
    
    def post_process(self, entity_info: Dict[str, Any], success: bool) -> None:
        """
        Post-process after main processing.
        
        Args:
            entity_info: Contact information that was processed
            success: Whether processing was successful
        """
        if success:
            self.logger.info(f"Successfully processed contact: {entity_info.get('entity_id')}")
        else:
            self.logger.error(f"Failed to process contact: {entity_info.get('entity_id')}")
    
    def _fetch_contact_from_dataverse(self, contact_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch the latest contact data from Dataverse.
        
        Args:
            contact_id: Contact ID to fetch
            
        Returns:
            Contact data or None if fetch failed
        """
        try:
            # Fetch the contact data
            result = read_fields_from_dataverse(
                token=self.token,
                dataverse_url=self.dataverse_url,
                table_name="contact",
                fields=self.get_entity_fields(),
                whereClause=f"contactid eq {contact_id}",
                logger=self.logger
            )
            
            if result and 'value' in result and len(result['value']) > 0:
                contact_data = result['value'][0]
                self.logger.info(f"Successfully fetched contact data for ID: {contact_id}")
                return contact_data
            else:
                # Try to get more information about why the contact wasn't found
                self.logger.warning(f"No contact data found for ID: {contact_id}")
                
                # Log additional context for debugging
                if result:
                    self.logger.info(f"Dataverse response for contact {contact_id}: {result}")
                else:
                    self.logger.error(f"Dataverse query failed for contact {contact_id} - no response received")
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error fetching contact data for ID {contact_id}: {e}")
            return None
    
    def _store_contact_in_database(self, contact_data: Dict[str, Any]) -> bool:
        """
        Store contact data in the database.
        
        Args:
            contact_data: Contact data from Dataverse
            
        Returns:
            True if storage was successful, False otherwise
        """
        try:
            # TODO: Implement database storage logic here
            # This is a placeholder - replace with  actual database operations
            
            # For now, just log the data
            self.logger.info(f"Would store contact data: {contact_data.get('contactid')} - {contact_data.get('fullname')}")
            
            # Simulate processing time
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing contact data: {e}")
            return False
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """Get contact processor-specific statistics."""
        base_stats = super().get_processor_stats()
        base_stats.update({
            "processor_type": "contact",
            "entity_fields_count": len(self.get_entity_fields())
        })
        return base_stats

class VacancyProcessor(BaseEntityProcessor):
    """
    Processor for vacancy entity updates.
    
    This processor handles vacancy updates from Dataverse and stores them
    in the local database.
    """
    
    def process_entity_update(self, entity_info: Dict[str, Any]) -> bool:
        """
        Process a vacancy update.
        
        Args:
            entity_info: Parsed entity information from the message
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            self.logger.info(f"=== VACANCY PROCESSOR DEBUG ===")
            self.logger.info(f"Received vacancy update: {entity_info.get('entity_id')}")
            self.logger.info(f"Entity name: {entity_info.get('entity_name')}")
            self.logger.info(f"Message type: {entity_info.get('message_type')}")
            self.logger.info(f"Attributes count: {len(entity_info.get('attributes', {}))}")
            
            # DEBUG: Log the actual attributes
            attributes = entity_info.get('attributes', {})
            if attributes:
                try:
                    self.logger.debug(f"Vacancy attributes: {json.dumps(attributes, indent=2, default=safe_json_serializer)}")
                except Exception as e:
                    self.logger.debug(f"Vacancy attributes (fallback): {str(attributes)}")
            else:
                self.logger.debug("No attributes found in vacancy message")
            
            # Detect and log field changes
            should_process = self.detect_and_log_field_changes(entity_info)
            self.logger.info(f"Field detection result: should_process = {should_process}")
            
            if not should_process:
                self.logger.info("Skipping vacancy update - no relevant fields changed")
                return True  # Return True to indicate successful skip
            
            # Pre-process the entity info
            processed_info = self.pre_process(entity_info)
            self.logger.info(f"Pre-processed info: {processed_info.get('entity_id')}")
            
            # Validate the data
            if not self.validate_entity_data(processed_info):
                self.logger.error(f"Invalid vacancy data: {processed_info}")
                return False
            
            # Extract entity ID
            entity_id = processed_info.get('entity_id')
            if not entity_id:
                self.logger.error("No entity ID found in vacancy update")
                return False
            
            self.logger.info(f"Processing vacancy update for ID: {entity_id}")
            
            # Fetch latest data from Dataverse
            vacancy_data = self._fetch_vacancy_from_dataverse(entity_id)
            if not vacancy_data:
                self.logger.error(f"Failed to fetch vacancy data for ID: {entity_id}")
                return False
            
            # Store in database
            success = self._store_vacancy_in_database(vacancy_data)
            if not success:
                self.logger.error(f"Failed to store vacancy data for ID: {entity_id}")
                return False
            
            # Post-process
            self.post_process(processed_info, success)
            
            self.logger.info(f"Successfully processed vacancy update for ID: {entity_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Exception in vacancy processing: {e}")
            self.handle_error(entity_info, e)
            return False
    
    def get_entity_fields(self) -> List[str]:
        """Get the list of fields to fetch from Dataverse for vacancies."""
        return [
                'crimson_jobtitle', 
                'crimson_addresscity',
                'crimson_vacancyrefno', 
                'crimson_jobsummaryemail', 
                'mercury_emaildescription', 
                'crimson_jobsummary', 
                'recruit_adverttext2', 
                'recruit_adverttext3', 
                'recruit_mandatorytagcontrol2', 
                'recruit_mandatorytagcontrol0',
                'statecode',
                'modifiedon', 'createdon'
        ]
    
    def validate_entity_data(self, entity_data: Dict[str, Any]) -> bool:
        """
        Validate vacancy data before processing.
        
        Args:
            entity_data: Vacancy data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Basic validation - ensure we have required fields
        required_fields = ['entity_id', 'entity_name']
        for field in required_fields:
            if field not in entity_data:
                self.logger.warning(f"Missing required field '{field}' in vacancy data")
                return False
        
        # Validate entity name
        if entity_data.get('entity_name') != 'crimson_vacancy':
            self.logger.warning(f"Invalid entity name: {entity_data.get('entity_name')}")
            return False
        
        return True
    
    def pre_process(self, entity_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pre-process vacancy information before main processing.
        
        Args:
            entity_info: Raw entity information
            
        Returns:
            Processed entity information
        """
        # Ensure we have the entity name set correctly
        entity_info['entity_name'] = 'crimson_vacancy'
        return entity_info
    
    def post_process(self, entity_info: Dict[str, Any], success: bool) -> None:
        """
        Post-process after main processing.
        
        Args:
            entity_info: Vacancy information that was processed
            success: Whether processing was successful
        """
        if success:
            self.logger.info(f"Successfully processed vacancy: {entity_info.get('entity_id')}")
        else:
            self.logger.error(f"Failed to process vacancy: {entity_info.get('entity_id')}")
    
    def _fetch_vacancy_from_dataverse(self, vacancy_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch the latest vacancy data from Dataverse.
        
        Args:
            vacancy_id: Vacancy ID to fetch
            
        Returns:
            Vacancy data or None if fetch failed
        """
        try:
            # Fetch the vacancy data
            result = read_fields_from_dataverse(
                token=self.token,
                dataverse_url=self.dataverse_url,
                table_name="crimson_vacancy",
                fields=self.get_entity_fields(),
                whereClause=f"crimson_vacancyid eq {vacancy_id}",
                logger=self.logger
            )
            
            if result and 'value' in result and len(result['value']) > 0:
                vacancy_data = result['value'][0]
                self.logger.info(f"Successfully fetched vacancy data for ID: {vacancy_id}")
                return vacancy_data
            else:
                # Try to get more information about why the vacancy wasn't found
                self.logger.warning(f"No vacancy data found for ID: {vacancy_id}")
                
                # Log additional context for debugging
                if result:
                    self.logger.info(f"Dataverse response for vacancy {vacancy_id}: {result}")
                else:
                    self.logger.error(f"Dataverse query failed for vacancy {vacancy_id} - no response received")
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error fetching vacancy data for ID {vacancy_id}: {e}")
            return None
    
    def _store_vacancy_in_database(self, vacancy_data: Dict[str, Any]) -> bool:
        """
        Store vacancy data in the database.
        
        Args:
            vacancy_data: Vacancy data from Dataverse
            
        Returns:
            True if storage was successful, False otherwise
        """
        try:
            # TODO: Implement database storage logic here
            # This is a placeholder - replace with actual database operations
            
            # For now, just log the data
            self.logger.info(f"Would store vacancy data: {vacancy_data.get('crimson_vacancyid')} - {vacancy_data.get('crimson_jobtitle')}")
            
            # Simulate processing time
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing vacancy data: {e}")
            return False
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """Get vacancy processor-specific statistics."""
        base_stats = super().get_processor_stats()
        base_stats.update({
            "processor_type": "vacancy",
            "entity_fields_count": len(self.get_entity_fields())
        })
        return base_stats

# Use modular processor approach for flexibility

# Factory function to create processors based on configuration
def create_processor(processor_type: str, config: ProcessorConfig, logger=None) -> BaseEntityProcessor:
    """
    Factory function to create entity processors.
    
    Args:
        processor_type: Type of processor to create
        config: Processor configuration
        logger: Logger instance
        
    Returns:
        Configured entity processor instance
        
    Raises:
        ValueError: If processor type is not supported
    """
    processors = {
        'contact': ContactProcessor,
        'crimson_vacancy': VacancyProcessor,
        # Add more processors here as they are implemented
    }
    
    processor_class = processors.get(processor_type)
    if not processor_class:
        raise ValueError(f"Unsupported processor type: {processor_type}")
    
    return processor_class(config, logger)

# Default configurations for common processors
def get_default_contact_config() -> ProcessorConfig:
    """Get default configuration for contact processor."""
    return ProcessorConfig(
        entity_name='contact',
        channel_name='contact-updates',
        channel_type=ChannelType.QUEUE,
        max_workers=4,
        max_retries=3,
        retry_delay=5.0,
        enabled=True,
        priority=0
    )

def get_default_vacancy_config() -> ProcessorConfig:
    """Get default configuration for vacancy processor."""
    return ProcessorConfig(
        entity_name='crimson_vacancy',
        channel_name='vacancy-updates',
        channel_type=ChannelType.QUEUE,
        max_workers=4,
        max_retries=3,
        retry_delay=5.0,
        enabled=True,
        priority=0
    ) 