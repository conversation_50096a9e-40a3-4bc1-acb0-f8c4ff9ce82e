#!/usr/bin/env python3
"""
Resume Parser Script

This script provides functionality to parse resume files and extract information including email addresses.
It leverages the existing parse_resume_new function from generator/parse_resume_to_db.py.
"""

import os
import sys
import json
from datetime import datetime

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from generator.prompt_resume_parser import prompt_resume_parser
from matcher.openai_skills_extraction import OpenAISkills
import re
import io
from pypdf import PdfReader
from docx import Document
import docx2txt

def create_default_logger():
    """Create a default logger for the resume parser."""
    logger_config = {
        "level": "INFO",
        "log_to_stdout": False,
        "use_json": False,
        "log_file": "/mnt/incoming/logs/get_email_resume.log",
    }
    return AppLogger(logger_config)

class FileTextExtractor:
    """
    Text extractor that works with file objects instead of file paths.
    """
    
    def __init__(self, logger=None):
        self.logger = logger
    
    def __extract_text_from_pdf_file(self, file_obj):
        """
        Extract text from a PDF file object.
        
        Args:
            file_obj: File object containing PDF data
            
        Returns:
            str: Extracted text from the PDF
        """
        try:
            # Reset file pointer to beginning
            file_obj.seek(0)
            
            # Read PDF from file object
            reader = PdfReader(file_obj)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
            return text
        except Exception as e:
            self.logger.error(f"Error extracting text from PDF file object: {e}")
            return ""
    
    def __extract_text_from_docx_file(self, file_obj):
        """
        Extract text from a DOCX file object.
        
        Args:
            file_obj: File object containing DOCX data
            
        Returns:
            str: Extracted text from the DOCX
        """
        try:
            # Reset file pointer to beginning
            file_obj.seek(0)
            
            # Use docx2txt to process the file object
            text = docx2txt.process(file_obj)
            return text
        except Exception as e:
            self.logger.error(f"Error extracting text from DOCX file object: {e}")
            return ""
    
    def __extract_text_from_txt_file(self, file_obj):
        """
        Extract text from a TXT file object.
        
        Args:
            file_obj: File object containing TXT data
            
        Returns:
            str: Extracted text from the TXT
        """
        try:
            # Reset file pointer to beginning
            file_obj.seek(0)
            
            # Read text content
            text = file_obj.read().decode('utf-8', errors='ignore')
            return text
        except Exception as e:
            self.logger.error(f"Error extracting text from TXT file object: {e}")
            return ""
    
    def extract_text(self, file_obj, file_extension):
        """
        Extract text from a file object based on its extension.
        
        Args:
            file_obj: File object containing the file data
            file_extension (str): File extension (e.g., '.pdf', '.docx', '.txt')
            
        Returns:
            str: Extracted text from the file
        """
        text = ""
        ext = file_extension.lower()
        
        if ext == '.pdf':
            text = self.__extract_text_from_pdf_file(file_obj)
        elif ext == '.docx':
            text = self.__extract_text_from_docx_file(file_obj)
        elif ext == '.txt':
            text = self.__extract_text_from_txt_file(file_obj)
        else:
            self.logger.error(f"Unsupported file extension: {ext}")
            return ""
        
        # Clean the text
        if text:
            # Remove non-ASCII characters
            text = re.sub(r'[^\x00-\x7F]+', '', text)
            # Remove non-printable characters
            text = re.sub(r'[^[:print:]]+', '', text)
            # Remove Unicode characters
            text = text.encode('ascii', 'ignore').decode()
        
        return text

def extract_email_from_text(text):
    """
    Extract email addresses from text using regex.
    
    Args:
        text (str): Text to search for email addresses
        
    Returns:
        list: List of found email addresses
    """
    # Email regex pattern
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    return emails


def extract_text_from_resume_file(localfile, ext, logger=None):
    """
    Extract text from resume file without parsing skills.
    
    Args:
        localfile: File object to the resume file
        ext (str): File extension
        logger: Logger instance
        
    Returns:
        tuple: (extracted_text, status)
    """
    try:
        textextractor = FileTextExtractor(logger)
        extracted_text = textextractor.extract_text(localfile, ext)
    except Exception as e:
        if ext == ".pdf":
            ret = 'pdf'
        elif ext == ".docx":
            ret = 'docx'
        elif ext == ".doc":
            ret = 'doc'
        elif ext == ".txt":
            ret = 'txt'
        if logger:
            logger.error(f"failedpdf {ext} - Exception in Text Extraction.")
        return "", "Extraction failed" + ret

    # If the extracted text is less than 10 characters, return an error message
    if len(extracted_text) < 10:
        if ext == ".pdf":
            ret = 'pdf'
        elif ext == ".docx":
            ret = 'docx'
        elif ext == ".doc":
            ret = 'doc'
        elif ext == ".txt":
            ret = 'txt'
        if logger:
            logger.error(f"failedpdf {ext} - Insufficient text.")
        return "", "Insufficient text" + ret

    return extracted_text, "Success"

def parse_resume_file(resume_file, filename, logger=None):
    """
    Parse a resume file and extract information including email addresses.
    
    Args:
        resume_file: File object to the resume file
        filename (str): Name of the resume file
        logger: Logger instance
        
    Returns:
        dict: Dictionary containing parsed resume information
    """
    if logger is None:
        logger = create_default_logger()
    
    logger.info(f"Starting to parse resume file: {filename}")
    
    # Get file extension
    _, ext = os.path.splitext(filename)
    ext = ext.lower()
    
    # Validate file extension
    if ext not in ['.pdf', '.docx', '.doc', '.txt']:
        logger.error(f"Unsupported file extension: {ext}")
        return {
            'status': 'error',
            'error': f'Unsupported file extension: {ext}',
            'filename': filename
        }
    
    # Generate a temporary contact ID for processing
    temp_contact_id = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Extract text from the resume file
        extracted_text, status = extract_text_from_resume_file(resume_file, ext, logger)
        
        if status != "Success":
            logger.error(f"Failed to extract text from resume: {status}")
            return {
                'status': 'error',
                'error': status,
                'filename': filename
            }
        
        # Extract email addresses from the text
        emails = extract_email_from_text(extracted_text)
        
        # Prepare the result
        result = {
            'status': 'success',
            'filename': filename,
            'file_extension': ext,
            'extracted_emails': emails,
            'text_length': len(extracted_text),
            'processing_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Successfully parsed resume: {filename}")
        logger.info(f"Found {len(emails)} email addresses: {emails}")
        
        # If no email addresses found, print the extracted text
        if not emails:
            logger.info(f"No email addresses found in {filename}. Extracted text:")
            logger.info("=" * 80)
            logger.info(extracted_text)
            logger.info("=" * 80)
        
        return result
        
    except Exception as e:
        logger.error(f"Exception while parsing resume {filename}: {str(e)}")
        return {
            'status': 'error',
            'error': str(e),
            'filename': filename
        }

def main():
    """Main function for testing the resume parser."""
    logger = create_default_logger()
    logger.info("Starting resume parser")
    
    # Check if filename is provided as argument
    if len(sys.argv) == 2:
        # Process single file
        filename = sys.argv[1]
        process_single_file(filename, logger)
    elif len(sys.argv) == 1:
        # Process all files in the temp directory
        temp_dir = "/mnt/incoming/temp/tempresume/"
        process_all_files_in_directory(temp_dir, logger)
    else:
        logger.error("Usage: python get_email_resume.py [filename]")
        logger.error("If no filename is provided, processes all files in /mnt/incoming/temp/tempresume/")
        sys.exit(1)
    
    logger.info("Completed resume parser")

def process_single_file(filename, logger):
    """Process a single resume file."""
    try:
        # Open the file in binary mode
        with open(filename, 'rb') as resume_file:
            result = parse_resume_file(resume_file, filename, logger)
            
            # Log the result as JSON
            logger.info(json.dumps(result, indent=2, default=str))
    
    except FileNotFoundError:
        logger.error(f"File not found: {filename}")
        result = {
            'status': 'error',
            'error': 'File not found',
            'filename': filename
        }
        logger.info(json.dumps(result, indent=2, default=str))
    except Exception as e:
        logger.error(f"Error opening file {filename}: {str(e)}")
        result = {
            'status': 'error',
            'error': str(e),
            'filename': filename
        }
        logger.info(json.dumps(result, indent=2, default=str))

def process_all_files_in_directory(directory_path, logger):
    """Process all supported files in the specified directory."""
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    
    try:
        # Check if directory exists
        if not os.path.exists(directory_path):
            logger.error(f"Directory not found: {directory_path}")
            return
        
        # Get all files in the directory
        files = [f for f in os.listdir(directory_path) if os.path.isfile(os.path.join(directory_path, f))]
        
        # Filter for supported file extensions
        supported_files = []
        for file in files:
            _, ext = os.path.splitext(file)
            if ext.lower() in supported_extensions:
                supported_files.append(file)
        
        logger.info(f"Found {len(supported_files)} supported files in {directory_path}")
        
        if not supported_files:
            logger.info("No supported files found in directory")
            return
        
        # Process each file
        all_results = []
        for filename in supported_files:
            file_path = os.path.join(directory_path, filename)
            logger.info(f"Processing file: {filename}")
            
            try:
                # Open the file in binary mode
                with open(file_path, 'rb') as resume_file:
                    result = parse_resume_file(resume_file, filename, logger)
                    all_results.append(result)
                    
                    # Log individual result
                    logger.info(json.dumps(result, indent=2, default=str))
                    logger.info("-" * 80)  # Separator between files
                    
            except Exception as e:
                logger.error(f"Error processing file {filename}: {str(e)}")
                error_result = {
                    'status': 'error',
                    'error': str(e),
                    'filename': filename
                }
                all_results.append(error_result)
                logger.info(json.dumps(error_result, indent=2, default=str))
                logger.info("-" * 80)
        
        # Print summary
        successful_count = len([r for r in all_results if r.get('status') == 'success'])
        error_count = len([r for r in all_results if r.get('status') == 'error'])
        
        logger.info(f"Processing complete. Success: {successful_count}, Errors: {error_count}")
        
        # Print summary as JSON
        summary = {
            'summary': {
                'total_files': len(all_results),
                'successful': successful_count,
                'errors': error_count,
                'directory': directory_path
            },
            'results': all_results
        }
        logger.info("\n" + "=" * 80)
        logger.info("SUMMARY")
        logger.info("=" * 80)
        logger.info(json.dumps(summary, indent=2, default=str))
        
    except Exception as e:
        logger.error(f"Error processing directory {directory_path}: {str(e)}")
        error_result = {
            'status': 'error',
            'error': str(e),
            'directory': directory_path
        }
        logger.info(json.dumps(error_result, indent=2, default=str))

if __name__ == '__main__':
    main() 