#!/usr/bin/env python3
"""
Topic Message Sender for Azure Service Bus

This utility sends messages to a specified Azure Service Bus topic by reading from JSON files.
It can be used to test message publishing or send bulk messages to topics.

Usage:
    python topic_sender.py --topic-name <topic_name> --connection-string <connection_string> --message-file <json_file>
    python topic_sender.py --topic-name <topic_name> --connection-string-env <env_var_name> --message-file <json_file>
"""

import argparse
import json
import os
import sys
from typing import Optional, Dict, Any, List

# Add the project root to the Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

try:
    from azure.servicebus import ServiceBusClient, ServiceBusMessage
    from azure.servicebus.exceptions import ServiceBusError
    from azure.core.exceptions import AzureError
    from common.appLogger import AppLogger, LoggerFactory
except ImportError as e:
    print(f"Error: Required dependencies not found: {e}")
    print("Run: pip install azure-servicebus")
    sys.exit(1)


class TopicSender:
    """Utility class to send messages to Azure Service Bus topics."""
    
    def __init__(self, connection_string: str, logger: Optional[AppLogger] = None, logger_name: str = "topic_sender"):
        self.connection_string = connection_string
        self.client = ServiceBusClient.from_connection_string(connection_string)
        
        if logger:
            self.logger = logger
        else:
            # Use LoggerFactory to create a logger with custom name
            logger_config = {
                "name": logger_name,
                "log_level": "INFO",
                "log_to_stdout": True,
                "log_mode": "append",
                "log_file_path": f"{logger_name}.log",
                "json_log": True
            }
            self.logger = LoggerFactory.get_logger(logger_name, logger_config)
    
    def send_single_message(self, topic_name: str, message_content: Dict[str, Any], 
                           message_properties: Optional[Dict[str, Any]] = None) -> bool:
        """
        Send a single message to the specified topic.
        
        Args:
            topic_name: Name of the topic to send message to
            message_content: Dictionary containing the message content
            message_properties: Optional properties to add to the message
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            with self.client:
                sender = self.client.get_topic_sender(topic_name=topic_name)
                with sender:
                    # Convert message content to JSON string
                    message_body = json.dumps(message_content)
                    
                    # Create ServiceBusMessage
                    message = ServiceBusMessage(message_body)
                    
                    # Add properties if provided
                    if message_properties:
                        for key, value in message_properties.items():
                            message.application_properties[key] = value
                    
                    # Send the message
                    sender.send_messages(message)
                    
                    self.logger.info(f"Successfully sent message to topic: {topic_name}")
                    return True
                    
        except ServiceBusError as e:
            self.logger.error(f"Service Bus error while sending message: {e}")
            return False
        except AzureError as e:
            self.logger.error(f"Azure error while sending message: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error while sending message: {e}")
            return False
    
    def send_bulk_messages(self, topic_name: str, messages: List[Dict[str, Any]], 
                          batch_size: int = 100) -> int:
        """
        Send multiple messages to the specified topic in batches.
        
        Args:
            topic_name: Name of the topic to send messages to
            messages: List of message dictionaries
            batch_size: Number of messages to send in each batch
            
        Returns:
            Number of messages successfully sent
        """
        sent_count = 0
        
        try:
            with self.client:
                sender = self.client.get_topic_sender(topic_name=topic_name)
                with sender:
                    # Process messages in batches
                    for i in range(0, len(messages), batch_size):
                        batch = messages[i:i + batch_size]
                        batch_messages = []
                        
                        for message_content in batch:
                            # Convert message content to JSON string
                            message_body = json.dumps(message_content)
                            message = ServiceBusMessage(message_body)
                            batch_messages.append(message)
                        
                        # Send batch
                        sender.send_messages(batch_messages)
                        sent_count += len(batch_messages)
                        
                        self.logger.info(f"Sent batch of {len(batch_messages)} messages to topic: {topic_name}")
                        
                        if sent_count % 100 == 0:
                            self.logger.info(f"Sent {sent_count} messages so far...")
                    
                    self.logger.info(f"Successfully sent {sent_count} messages to topic: {topic_name}")
                    return sent_count
                    
        except ServiceBusError as e:
            self.logger.error(f"Service Bus error while sending bulk messages: {e}")
            return sent_count
        except AzureError as e:
            self.logger.error(f"Azure error while sending bulk messages: {e}")
            return sent_count
        except Exception as e:
            self.logger.error(f"Unexpected error while sending bulk messages: {e}")
            return sent_count
    
    def send_message_from_file(self, topic_name: str, file_path: str, 
                              message_properties: Optional[Dict[str, Any]] = None) -> bool:
        """
        Send a message to the topic by reading from a JSON file.
        
        Args:
            topic_name: Name of the topic to send message to
            file_path: Path to the JSON file containing the message
            message_properties: Optional properties to add to the message
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            # Read and parse JSON file
            with open(file_path, 'r') as f:
                message_content = json.load(f)
            
            # Send the message
            return self.send_single_message(topic_name, message_content, message_properties)
            
        except FileNotFoundError:
            self.logger.error(f"File not found: {file_path}")
            return False
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in file {file_path}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            return False
    
    def send_bulk_messages_from_file(self, topic_name: str, file_path: str, 
                                   batch_size: int = 100) -> int:
        """
        Send multiple messages to the topic by reading from a JSON file containing an array of messages.
        
        Args:
            topic_name: Name of the topic to send messages to
            file_path: Path to the JSON file containing an array of messages
            batch_size: Number of messages to send in each batch
            
        Returns:
            Number of messages successfully sent
        """
        try:
            # Read and parse JSON file
            with open(file_path, 'r') as f:
                messages = json.load(f)
            
            # Ensure messages is a list
            if not isinstance(messages, list):
                self.logger.error(f"JSON file {file_path} must contain an array of messages")
                return 0
            
            # Send the messages
            return self.send_bulk_messages(topic_name, messages, batch_size)
            
        except FileNotFoundError:
            self.logger.error(f"File not found: {file_path}")
            return 0
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in file {file_path}: {e}")
            return 0
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            return 0


def get_connection_string(connection_string: Optional[str] = None, 
                         env_var_name: Optional[str] = None,
                         logger: Optional[AppLogger] = None) -> str:
    """
    Get the connection string from command line argument or environment variable.
    
    Args:
        connection_string: Connection string from command line
        env_var_name: Environment variable name containing connection string
        logger: Optional AppLogger instance for logging
        
    Returns:
        Azure Service Bus connection string
        
    Raises:
        ValueError: If connection string cannot be found
    """
    if connection_string:
        return connection_string
    
    if env_var_name:
        conn_str = os.getenv(env_var_name)
        if conn_str:
            return conn_str
        else:
            raise ValueError(f"Environment variable '{env_var_name}' not found")
    
    # Try common environment variable names
    common_env_vars = [
        'AZURE_SERVICE_BUS_CONNECTION_STRING',
        'SERVICE_BUS_CONNECTION_STRING',
        'SB_CONNECTION_STRING'
    ]
    
    for env_var in common_env_vars:
        conn_str = os.getenv(env_var)
        if conn_str:
            if logger:
                logger.info(f"Using connection string from environment variable: {env_var}")
            return conn_str
    
    raise ValueError("No connection string provided. Use --connection-string or --connection-string-env")


def main():
    """Main entry point for the topic sender tool."""
    parser = argparse.ArgumentParser(
        description="Send messages to Azure Service Bus topic from JSON files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Send single message from JSON file
  python topic_sender.py --topic-name my-topic --connection-string "Endpoint=sb://..." --message-file message.json

  # Send bulk messages from JSON file containing array
  python topic_sender.py --topic-name my-topic --connection-string-env AZURE_SB_CONN_STR --message-file messages.json --bulk

  # Send with custom properties
  python topic_sender.py --topic-name my-topic --connection-string-env AZURE_SB_CONN_STR --message-file message.json --properties '{"key1":"value1","key2":"value2"}'
        """
    )
    
    parser.add_argument(
        "--topic-name",
        required=True,
        help="Name of the topic to send messages to"
    )
    
    parser.add_argument(
        "--connection-string",
        help="Azure Service Bus connection string"
    )
    
    parser.add_argument(
        "--connection-string-env",
        help="Environment variable name containing the connection string"
    )
    
    parser.add_argument(
        "--message-file",
        required=True,
        help="Path to the JSON file containing the message(s)"
    )
    
    parser.add_argument('--bulk', action='store_true',
                       help='Treat the JSON file as containing an array of messages to send in bulk')
    parser.add_argument('--batch-size', type=int, default=100, 
                       help='Number of messages to send in each batch for bulk operations (default: 100)')
    parser.add_argument('--properties', type=str,
                       help='JSON string of properties to add to messages (e.g., \'{"key1":"value1"}\')')
    parser.add_argument('--verbose', action='store_true', 
                       help='Enable verbose logging (DEBUG level)')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be sent without actually sending')
    parser.add_argument('--logger-name', type=str, default='topic_sender',
                       help='Custom name for the logger (default: topic_sender)')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = "DEBUG" if args.verbose else "INFO"
    logger_config = {
        "name": args.logger_name,
        "log_level": log_level,
        "log_to_stdout": True,
        "log_mode": "append",
        "log_file_path": f"{args.logger_name}.log",
        "json_log": True
    }
    logger = LoggerFactory.get_logger(args.logger_name, logger_config)
    
    try:
        # Get connection string
        connection_string = get_connection_string(
            connection_string=args.connection_string,
            env_var_name=args.connection_string_env,
            logger=logger
        )
        
        # Parse message properties if provided
        message_properties = None
        if args.properties:
            try:
                message_properties = json.loads(args.properties)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in --properties argument: {e}")
                sys.exit(1)
        
        # Create topic sender instance
        sender = TopicSender(connection_string, logger=logger, logger_name=args.logger_name)
        
        if args.dry_run:
            # Show what would be sent
            try:
                with open(args.message_file, 'r') as f:
                    content = json.load(f)
                
                if args.bulk and isinstance(content, list):
                    print(f"DRY RUN: Would send {len(content)} messages to topic '{args.topic_name}' from file '{args.message_file}'")
                else:
                    print(f"DRY RUN: Would send 1 message to topic '{args.topic_name}' from file '{args.message_file}'")
                    
            except Exception as e:
                logger.error(f"Error reading file for dry run: {e}")
                sys.exit(1)
        else:
            # Send messages
            if args.bulk:
                print(f"Starting to send bulk messages to topic: {args.topic_name}")
                sent_count = sender.send_bulk_messages_from_file(
                    topic_name=args.topic_name,
                    file_path=args.message_file,
                    batch_size=args.batch_size
                )
                print(f"Successfully sent {sent_count} messages to topic: {args.topic_name}")
            else:
                print(f"Starting to send message to topic: {args.topic_name}")
                success = sender.send_message_from_file(
                    topic_name=args.topic_name,
                    file_path=args.message_file,
                    message_properties=message_properties
                )
                if success:
                    print(f"Successfully sent message to topic: {args.topic_name}")
                else:
                    print(f"Failed to send message to topic: {args.topic_name}")
                    sys.exit(1)
            
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except ServiceBusError as e:
        logger.error(f"Service Bus error: {e}")
        sys.exit(1)
    except AzureError as e:
        logger.error(f"Azure error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 