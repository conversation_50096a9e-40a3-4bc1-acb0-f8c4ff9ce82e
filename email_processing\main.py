#!/usr/bin/env python3
"""
Azure Email Fetcher - Simple and Clean

Fetches <NAME_EMAIL> inbox.
Everything needed for main email processing in one focused file.
"""

import os
import sys
from datetime import datetime
from typing import List, Dict, Optional

# Add parent directory to path to import common modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from common.secrets_env import load_secrets_env_variables
from common.appLogger import AppLogger
from azure_client import AzureEmailFetcher
from config import setup_logger_and_secrets
from email_processing_helpers import (
    EmailProcessingStats, fetch_emails_with_mode, process_email_batch,
    display_failed_processing_details, get_folder_display_name, print_batch_summary
)

# Configuration constants
DEXTRA_MERCURYCV_EMAIL = '<EMAIL>'
CONTACT_ID_QUEUE = 'ContactId_Queue'


def print_emails_summary(emails: List[Dict], folder_name: str = "Inbox", logger=None):
    """Display a formatted summary of the emails using logger"""
    if logger is None:
        return  # Cannot display without logger
        
    if not emails:
        message = "📭 No emails found"
        logger.info(message)
        return
    
    header = f"\n📧 LAST {len(emails)} <NAME_EMAIL> ({folder_name})"
    separator = "=" * 80
    logger.info(header)
    logger.info(separator)
    logger.debug(f"Displaying {len(emails)} emails from {folder_name}")
    
    for i, email in enumerate(emails, 1):
        # Extract sender information
        from_info = email.get('from', {})
        sender_name = from_info.get('emailAddress', {}).get('name', 'Unknown')
        sender_email = from_info.get('emailAddress', {}).get('address', 'Unknown')
        
        # Format received date
        received_dt = email.get('receivedDateTime', '')
        if received_dt:
            try:
                dt = datetime.fromisoformat(received_dt.replace('Z', '+00:00'))
                formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
            except:
                formatted_date = received_dt
        else:
            formatted_date = "Unknown"
        
        # Email subject
        subject = email.get('subject', '(No Subject)')
        
        # Additional info
        has_attachments = email.get('hasAttachments', False)
        is_read = email.get('isRead', False)
        importance = email.get('importance', 'normal')
        
        # Format output
        email_header = f"\n{i:2d}. 📨 {subject}"
        from_line = f"    👤 From: {sender_name} <{sender_email}>"
        date_line = f"    📅 Received: {formatted_date}"
        
        logger.info(from_line)
        logger.info(date_line)
        logger.debug(f"Email {i}: {subject} from {sender_email} received {formatted_date}")
        
        # Additional indicators
        indicators = []
        if has_attachments:
            indicators.append("📎 Attachments")
        if not is_read:
            indicators.append("🆕 Unread")
        if importance == 'high':
            indicators.append("⚡ High Priority")
        
        if indicators:
            indicators_line = f"    ℹ️  {' | '.join(indicators)}"
            logger.info(indicators_line)
        
        # Preview of body if available
        body_preview = email.get('bodyPreview', '').strip()
        if body_preview:
            # Truncate long previews
            if len(body_preview) > 100:
                body_preview = body_preview[:100] + "..."
            preview_line = f"    💬 Preview: {body_preview}"
            logger.info(preview_line)
    
    summary = f"\n{separator}\n✅ Total emails displayed: {len(emails)}"
    logger.info(summary)


def process_single_email(email_fetcher, email, move_no_attachments, fwd_mercurycv_mode, logger):
    """
    Process a single email through all required steps with comprehensive error tracking.
    
    Args:
        email_fetcher: AzureEmailFetcher instance
        email: Single email dictionary to process
        move_no_attachments: If True, move emails without attachments to "Invalid Attachment" folder
        fwd_mercurycv_mode: Mercury CV forwarding mode ("move_to_queue", "no_move", or None)
        logger: Logger instance
        
    Returns:
        dict: Processing result with the following structure:
            - email_id: Email identifier
            - subject: Email subject line
            - has_attachments: Boolean indicating if email has attachments
            - processed_steps: List of successfully completed steps
            - failed_step: Step where processing failed (None if successful)
            - error_message: Error details if processing failed
            - final_status: Final location ('inbox', 'invalid_attachment', 'forwarded', 'queue')
    """
    email_id = email.get('id')
    subject = email.get('subject', '(No Subject)')
    has_attachments = email.get('hasAttachments', False)
    
    # Extract sender information for logging
    from_info = email.get('from', {})
    sender_name = from_info.get('emailAddress', {}).get('name', 'Unknown')
    sender_email = from_info.get('emailAddress', {}).get('address', 'Unknown')
    
    result = {
        'email_id': email_id,
        'subject': subject,
        'has_attachments': has_attachments,
        'processed_steps': [],
        'failed_step': None,
        'error_message': None,
        'final_status': 'inbox'  # inbox, invalid_attachment, forwarded, queue
    }
    
    try:
        # Step 1: Handle emails without attachments (if enabled)
        if move_no_attachments and not has_attachments:
            logger.info(f"📧 Processing email without attachment - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...'")
            
            try:
                # Ensure "Invalid Attachment" folder exists
                invalid_attachment_folder_id = email_fetcher.ensure_folder_exists(
                    mailbox="<EMAIL>",
                    folder_name="Invalid Attachment"
                )
                
                # Move email to Invalid Attachment folder
                success = email_fetcher.move_email(
                    mailbox="<EMAIL>",
                    email_id=email_id,
                    destination_folder_id=invalid_attachment_folder_id
                )
                
                if success:
                    result['processed_steps'].append('moved_to_invalid_attachment')
                    result['final_status'] = 'invalid_attachment'
                    logger.info(f"✅ Moved email without attachment - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...'")
                    return result  # Email processed, no further steps needed
                else:
                    raise Exception("Failed to move email to Invalid Attachment folder")
                    
            except Exception as e:
                result['failed_step'] = 'move_to_invalid_attachment'
                result['error_message'] = str(e)
                logger.error(f"❌ Failed to move email without attachment - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...', Error: {e}")
                return result
        
        # Step 2: Handle Mercury CV forwarding for emails with attachments (if enabled)
        if fwd_mercurycv_mode and has_attachments:
            logger.info(f"📤 Processing email with attachment for Mercury CV - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...'")
            
            try:
                # Forward email to Mercury CV using Microsoft Graph API
                forward_success = email_fetcher.forward_email(
                    mailbox="<EMAIL>",
                    email_id=email_id,
                    forward_to_email=DEXTRA_MERCURYCV_EMAIL,
                    comment=f"Automated forwarding from resume processing system - Email ID: {email_id}"
                )
                
                if forward_success:
                    result['processed_steps'].append('forwarded_to_mercury_cv')
                    result['final_status'] = 'forwarded'
                    logger.info(f"✅ Forwarded email to Mercury CV - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...'")
                    
                    # Log detailed email information for emails forwarded to Mercury CV
                    logger.info(f"\n📤 EMAIL FORWARDED TO MERCURY CV:")
                    logger.info("=" * 60)
                    
                    # Email details
                    attachment_status = "Yes" if has_attachments else "No"
                    body_preview = email.get('bodyPreview', '').strip()
                    if body_preview and len(body_preview) > 100:
                        body_preview = body_preview[:100] + "..."
                    
                    logger.info(f"📨 Subject: {subject}")
                    logger.info(f"👤 From: {sender_name} <{sender_email}>")
                    logger.info(f"📎 Attachments: {attachment_status}")
                    if body_preview:
                        logger.info(f"💬 Preview: {body_preview}")
                    logger.info("=" * 60)
                    
                    # Step 3: Handle moving forwarded emails to queue (if enabled)
                    if fwd_mercurycv_mode == "move_to_queue":
                        try:
                            # Ensure the queue folder exists
                            queue_folder_id = email_fetcher.ensure_folder_exists(
                                mailbox="<EMAIL>",
                                folder_name=CONTACT_ID_QUEUE
                            )
                            
                            # Move forwarded email to queue
                            move_success = email_fetcher.move_email(
                                mailbox="<EMAIL>",
                                email_id=email_id,
                                destination_folder_id=queue_folder_id
                            )
                            
                            if move_success:
                                result['processed_steps'].append('moved_to_queue')
                                result['final_status'] = 'queue'
                                logger.info(f"✅ Moved forwarded email to queue - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...'")
                            else:
                                raise Exception("Failed to move forwarded email to queue")
                                
                        except Exception as e:
                            result['failed_step'] = 'move_to_queue'
                            result['error_message'] = str(e)
                            logger.error(f"❌ Failed to move forwarded email to queue - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...', Error: {e}")
                            # Email was forwarded successfully, so don't return yet
                else:
                    raise Exception("Failed to forward email to Mercury CV")
                    
            except Exception as e:
                result['failed_step'] = 'forward_to_mercury_cv'
                result['error_message'] = str(e)
                logger.error(f"❌ Failed to forward email to Mercury CV - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...', Error: {e}")
                return result
        
        # If no processing was needed or all steps completed successfully
        logger.debug(f"📧 Email processing completed - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...', Status: {result['final_status']}")
        return result
        
    except Exception as e:
        result['failed_step'] = 'unexpected_error'
        result['error_message'] = str(e)
        logger.error(f"❌ Unexpected error processing email - ID: {email_id}, From: {sender_email}, Subject: '{subject[:50]}...', Error: {e}")
        return result


def process_all_emails_in_batches(email_fetcher, live_mode: bool, move_no_attachments: bool, 
                                  fwd_mercurycv_mode: Optional[str], logger) -> List[Dict]:
    """
    Process ALL emails in inbox using batches of 50 - modular implementation
    
    Args:
        email_fetcher: AzureEmailFetcher instance  
        live_mode: Whether in live mode or test mode
        move_no_attachments: Whether to move emails without attachments
        fwd_mercurycv_mode: Mercury CV forwarding mode
        logger: Logger instance
        
    Returns:
        List of emails remaining in inbox after processing
    """
    batch_info = f"🔄 BATCH PROCESSING MODE: Processing ALL emails in inbox"
    logger.info(batch_info)
    logger.info("=" * 70)
    
    batch_size = 10
    batch_number = 1
    total_stats = EmailProcessingStats()
    all_failed_processing = []
    all_emails_remaining = []
    mode_text = "live mode (all emails)" if live_mode else "test mode (filtered emails)"
    
    while True:
        batch_start_msg = f"📦 BATCH {batch_number}: Fetching {batch_size} emails ({mode_text})..."
        logger.info(batch_start_msg)
        
        # Fetch current batch using centralized function
        current_batch = fetch_emails_with_mode(email_fetcher, live_mode, batch_size)
        
        if not current_batch:
            completion_msg = f"✅ No more emails found. Batch processing complete after {batch_number - 1} batches."
            logger.info(completion_msg)
            break
        
        # Update total statistics
        total_stats.increment_batch(len(current_batch))
        logger.debug(f"Batch {batch_number}: Found {len(current_batch)} emails")
        
        # Process current batch if processing is enabled
        if move_no_attachments or fwd_mercurycv_mode:
            processing_msg = f"⚙️  Processing batch {batch_number} ({len(current_batch)} emails)..."
            logger.info(processing_msg)
            
            # Process batch using helper function
            batch_results = process_email_batch(
                email_fetcher, current_batch, move_no_attachments, 
                fwd_mercurycv_mode, process_single_email, logger, batch_number
            )
            
            # Aggregate results
            all_failed_processing.extend(batch_results['failed_emails'])
            all_emails_remaining.extend(batch_results['remaining_emails'])
            
            # Add batch stats to total stats
            total_stats.total_processed += batch_results['stats'].total_processed
            total_stats.with_attachments += batch_results['stats'].with_attachments
            total_stats.without_attachments += batch_results['stats'].without_attachments
            total_stats.moved_to_invalid += batch_results['stats'].moved_to_invalid
            total_stats.forwarded_to_mercury += batch_results['stats'].forwarded_to_mercury
            total_stats.moved_to_queue += batch_results['stats'].moved_to_queue
            total_stats.failed_processing += batch_results['stats'].failed_processing
            total_stats.remaining_in_inbox += batch_results['stats'].remaining_in_inbox
            
            # Show batch summary using helper
            print_batch_summary(batch_number, current_batch, batch_results, mode_text, logger)
            
        else:
            # No processing, just add to remaining emails
            all_emails_remaining.extend(current_batch)
            total_stats.remaining_in_inbox += len(current_batch)
            print_batch_summary(batch_number, current_batch, None, mode_text, logger)
        
        batch_number += 1
        
        # Safety check to prevent infinite loops
        if batch_number > 100:  # Max 100 batches (5000 emails)
            safety_msg = f"⚠️  Reached maximum batch limit (100 batches). Stopping for safety."
            logger.warning("Reached maximum batch limit in process_all_emails mode")
            break
    
    # Display final statistics using helper
    total_stats.display_statistics(move_no_attachments, fwd_mercurycv_mode, is_batch_mode=True, logger=logger)
    
    # Display failed processing details using helper
    display_failed_processing_details(all_failed_processing, logger=logger)
    
    # Show final remaining emails (limited display)
    display_count = min(len(all_emails_remaining), 20)  # Show max 20
    final_result_msg = f"📋 FINAL RESULT: Displaying last {display_count} emails remaining in inbox..."
    logger.info(final_result_msg)
    
    # Generate folder display name using helper
    folder_display_name = get_folder_display_name(live_mode, move_no_attachments, fwd_mercurycv_mode, is_batch_mode=True)
    
    # Display final email summary
    print_emails_summary(all_emails_remaining[:display_count], folder_display_name, logger)
    
    if len(all_emails_remaining) > display_count:
        note_msg = f"ℹ️  Note: Showing {display_count} of {len(all_emails_remaining)} total remaining emails"
        logger.info(note_msg)
    
    logger.info(f"✅ Batch processing completed: {total_stats.total_emails_found} emails processed across {total_stats.total_batches} batches")
    return all_emails_remaining


def process_inbox_emails(logger, count: int = 10, live_mode: bool = False, move_no_attachments: bool = False, fwd_mercurycv_mode: Optional[str] = None, process_all_emails: bool = False):
    """
    Main function: Fetch emails from inbox and process them through multiple steps using single-loop approach.
    Each email is processed individually through all required steps for fault tolerance.
    
    Args:
        logger: Logger instance
        count: Number of emails to fetch per batch (ignored if process_all_emails=True)
        live_mode: If False (default), only show emails from specific test addresses. If True, show all emails
        move_no_attachments: If True, move emails without attachments to "Invalid Attachment" folder
        fwd_mercurycv_mode: Mercury CV forwarding mode ("move_to_queue", "no_move", or None to skip)
        process_all_emails: If True, process ALL emails in inbox using batches of 50
        
    Returns:
        List of emails remaining in inbox after processing
    """
    try:
        loading_msg = "🔑 Loading secrets from GPG files..."
        logger.info(loading_msg)
        load_secrets_env_variables()

        connecting_msg = f"📧 Connecting to Azure email services..."
        logger.info(connecting_msg)
        
        email_fetcher = AzureEmailFetcher(logger=logger)

        # =================================================================
        # BATCH PROCESSING MODE: Process ALL emails in batches
        # =================================================================
        if process_all_emails:
            return process_all_emails_in_batches(
                email_fetcher, live_mode, move_no_attachments, fwd_mercurycv_mode, logger
            )

        # =================================================================
        # SINGLE BATCH MODE: STEP 1: FETCH EMAILS FROM INBOX
        # =================================================================
        if live_mode:
            step1_msg = f"\n🔴 STEP 1: Fetching {count} emails from inbox (LIVE MODE - ALL EMAILS)..."
            logger.info(step1_msg)
        else:
            step1_msg = f"\n🧪 STEP 1: Fetching {count} emails from inbox (TEST MODE - FILTERED EMAILS ONLY)..."
            logger.info(step1_msg)
            logger.debug("Test mode: filtering by specific test addresses")
        
        # Fetch emails using centralized helper function
        emails = fetch_emails_with_mode(email_fetcher, live_mode, count)
            
        if not emails:
            mode_text = "live mode" if live_mode else "test mode"
            no_emails_msg = f"ℹ️  No emails found in inbox for {mode_text}"
            logger.info(no_emails_msg)
            return []
            
        mode_text = "live mode (all emails)" if live_mode else "test mode (filtered emails)"
        found_msg = f"✅ Found {len(emails)} emails in {mode_text}"
        logger.info(found_msg)

        # =================================================================
        # STEP 2: PROCESS EMAILS (Attachment checking, Mercury CV forwarding, and moving)
        # =================================================================
        if move_no_attachments or fwd_mercurycv_mode:
            logger.info("STEP 2: Processing emails for attachments and Mercury CV forwarding")
            
            # Process emails using helper function
            batch_results = process_email_batch(
                email_fetcher, emails, move_no_attachments, 
                fwd_mercurycv_mode, process_single_email, logger
            )
            
            # Display processing statistics using helper
            batch_results['stats'].display_statistics(move_no_attachments, fwd_mercurycv_mode, is_batch_mode=False, logger=logger)
            
            emails_to_display = batch_results['remaining_emails']
            
            # Display failed processing details using helper
            display_failed_processing_details(batch_results['failed_emails'], logger=logger)
                
        else:
            logger.info("STEP 2: Skipped - no processing options specified")
            emails_to_display = emails
        
        # =================================================================
        # STEP 3: Display remaining emails in inbox
        # =================================================================
        step3_msg = f"\n📋 STEP 3: Displaying {len(emails_to_display)} emails remaining in inbox..."
        logger.info(step3_msg)
        
        # Generate folder display name using helper
        folder_display_name = get_folder_display_name(live_mode, move_no_attachments, fwd_mercurycv_mode)
        
        # Display email summary
        print_emails_summary(emails_to_display, folder_display_name, logger)
        
        logger.info("✅ Inbox email processing completed successfully")
        return emails_to_display
        
    except Exception as e:
        logger.error(f"❌ Critical error in email processing: {e}")
        critical_error_msg = f"\n💥 Critical Error: {e}"
        details_msg = "Please check the logs for more details."
        logger.error(critical_error_msg)
        logger.error(details_msg)
        return []



def main():
    """Main function - focused only on inbox email fetching"""
    # Initialize logger
    logger_config = {
        "name": "fetch_email",
        "log_level": "INFO",
        "log_to_stdout": True,
        "use_syslog": False,
        "log_file": f"/mnt/incoming/logs/fetch_email.log",
    }
    logger = AppLogger(logger_config)
    
    # Parse command line arguments
    count = 10  # Default count
    live_mode = False # Default live_mode
    move_no_attachments = False # Default move_no_attachments
    fwd_mercurycv_mode = None # Default Mercury CV mode
    process_all_emails = False # Default batch processing mode
    
    i = 1
    while i < len(sys.argv):
        arg = sys.argv[i]
        
        if arg in ["--help", "-h"]:
            print_help()
            return
        elif arg == "--count":
            if i + 1 >= len(sys.argv):
                count_error_msg = "❌ Error: --count requires a number argument"
                count_example_msg = "Example: python3 main.py --count 20"
                logger.error(count_error_msg)
                logger.error(count_example_msg)
                sys.exit(1)
            try:
                count = int(sys.argv[i + 1])
                if count <= 0:
                    positive_error_msg = "❌ Error: count must be a positive number"
                    logger.error(f"Argument parsing error: count must be positive, got {count}")
                    sys.exit(1)
                i += 1  # Skip the next argument (the count value)
                logger.debug(f"Configuration: count set to {count}")
            except ValueError:
                value_error_msg = "❌ Error: count must be a valid number"
                logger.error(f"Argument parsing error: invalid count value '{sys.argv[i + 1]}'")
                sys.exit(1)
        elif arg == "--live-not-a-test":
            live_mode = True
            logger.info("Configuration: Live mode enabled - all emails will be fetched")
        elif arg == "--move-no-attachments":
            move_no_attachments = True
            logger.info("Configuration: Move no attachments enabled")
        elif arg == "--fwd-mercurycv-move-2-q":
            fwd_mercurycv_mode = "move_to_queue"
            logger.info("Configuration: Mercury CV forwarding with queue moving enabled")
        elif arg == "--fwd-mercurycv-no-move-2-q":
            fwd_mercurycv_mode = "no_move"
            logger.info("Configuration: Mercury CV forwarding without moving enabled")
        elif arg == "--all-emails":
            process_all_emails = True
            logger.info("Configuration: Batch processing mode enabled - will process ALL emails")
        else:
            error_msg = f"❌ Unknown argument: {arg}"
            help_msg = "Use --help for usage information"
            logger.error(error_msg)
            logger.error(help_msg)
            sys.exit(1)
        
        i += 1
    
    # Display mode information and warnings
    if not live_mode:
        test_mode_msg = "🧪 Running in test mode (default). Only emails from specific test addresses will be displayed."
        safety_msg = "🛡️  Use --live-not-a-test flag to process ALL emails."
        logger.info(test_mode_msg)
        logger.info(safety_msg)
    
    if process_all_emails and count != 10:
        logger.info(f"Configuration note: --count {count} ignored in batch mode")
    
    logger.info(f"Starting email processing with config: live_mode={live_mode}, move_no_attachments={move_no_attachments}, fwd_mercurycv_mode={fwd_mercurycv_mode}, process_all_emails={process_all_emails}, count={count}")
    
    # Main functionality: fetch inbox emails
    process_inbox_emails(logger, count, live_mode, move_no_attachments, fwd_mercurycv_mode, process_all_emails)


def print_help():
    """Print help information for main functionality"""
    print("AZURE EMAIL PROCESSOR - Main Application")
    print("=" * 50)
    print("Purpose: Fetch and process <NAME_EMAIL> inbox through multiple stages")
    print("         Uses single-loop processing for fault tolerance and detailed error tracking")
    print()
    print("Usage:")
    print("  python main.py [options]")
    print()
    print("Options:")
    print("  --help                     Show this help message")
    print("  --count <N>               Fetch N emails per batch (default: 10, ignored with --all-emails)")
    print("  --live-not-a-test         Run in LIVE mode - fetch ALL emails")
    print("                            Without this flag, runs in TEST mode (default)")
    print("  --all-emails              Process ALL emails in inbox using batches of 50")
    print("                            Continues until no more emails found")
    print("  --move-no-attachments     Move emails without attachments to 'Invalid Attachment' folder")
    print("  --fwd-mercurycv-move-2-q  Forward emails with attachments to Mercury CV AND move to queue")
    print("  --fwd-mercurycv-no-move-2-q  Forward emails with attachments to Mercury CV but keep in inbox")
    print()
    print("Default Behavior (TEST MODE):")
    print("  • Only processes emails from specific test addresses:")
    print("    - <EMAIL>")
    print("    - <EMAIL>") 
    print("    - <EMAIL>")
    print("    - <EMAIL>")
    print("    - <EMAIL>")
    print("    - <EMAIL>")
    print("    - <EMAIL>")
    print("  • Safe for development and testing")
    print("  • Prevents accidental processing of production emails")
    print()
    print("Live Mode (--live-not-a-test):")
    print("  • Processes ALL emails in the inbox")
    print("  • Use with caution in production")
    print("  • Requires explicit flag to prevent accidental live runs")
    print()
    print("Processing Flow:")
    print("  NORMAL MODE:")
    print("    STEP 1: Fetch emails from inbox")
    print("    STEP 2: Process emails (attachment checking + Mercury CV forwarding)")
    print("    STEP 3: Display remaining emails with appropriate filtering labels")
    print("  BATCH MODE (--all-emails):")
    print("    Repeats: Fetch 50 → Process → Fetch next 50... until inbox empty")
    print("    Shows progress and final statistics across all batches")
    print()
    print("Single-Loop Processing:")
    print("  • Each email is processed individually through all required steps")
    print("  • Failed emails remain in inbox for potential reprocessing")
    print("  • Detailed statistics and error reporting for troubleshooting")
    print("  • Fault-tolerant: individual email failures don't affect the batch")
    print()
    print("Notes:")
    print("  • Test mode is the DEFAULT - no special flag needed")
    print("  • Use --live-not-a-test only when you want to process ALL emails")
    print("  • Multiple processing options can be combined")
    print("  • Mercury CV forwarding is fully implemented using Microsoft Graph API")
    print("  • Requires Mail.Send permission in Azure App Registration")
    print()
    print("Examples:")
    print("  python main.py --count 5")
    print("    → Fetch 5 emails in test mode")
    print()
    print("  python main.py --live-not-a-test --count 20")
    print("    → Fetch 20 emails in live mode (ALL emails)")
    print()
    print("  python main.py --all-emails")
    print("    → Process ALL emails in test mode using batches of 50")
    print()
    print("  python main.py --live-not-a-test --all-emails --move-no-attachments")
    print("    → Process ALL emails in live mode with attachment processing")
    print()
    print("  python main.py --live-not-a-test --all-emails --fwd-mercurycv-move-2-q")
    print("    → Process ALL emails in live mode with Mercury CV forwarding + queue moving")



if __name__ == "__main__":
    main() 


    