{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/hooks/useNotification.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, createContext, useContext, ReactNode } from \"react\";\r\n\r\ninterface Notification {\r\n  id: number;\r\n  message: string;\r\n  type: \"success\" | \"error\" | \"info\" | \"warning\";\r\n}\r\n\r\ninterface NotificationContextType {\r\n  notifications: Notification[];\r\n  showNotification: (message: string, type?: Notification[\"type\"]) => void;\r\n  removeNotification: (id: number) => void;\r\n}\r\n\r\nconst NotificationContext = createContext<NotificationContextType | null>(null);\r\n\r\nexport const NotificationProvider = ({ children }: { children: ReactNode }) => {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n\r\n  const showNotification = (\r\n    message: string,\r\n    type: Notification[\"type\"] = \"info\"\r\n  ) => {\r\n    const id = Date.now();\r\n    setNotifications((prev) => [...prev, { id, message, type }]);\r\n\r\n    // Auto-remove after 3 seconds\r\n    setTimeout(() => removeNotification(id), 3000);\r\n  };\r\n\r\n  const removeNotification = (id: number) => {\r\n    setNotifications((prev) => prev.filter((notif) => notif.id !== id));\r\n  };\r\n\r\n  return (\r\n    <NotificationContext.Provider\r\n      value={{ notifications, showNotification, removeNotification }}\r\n    >\r\n      {children}\r\n    </NotificationContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useNotification = () => {\r\n  const context = useContext(NotificationContext);\r\n  if (!context) {\r\n    throw new Error(\r\n      \"useNotification must be used within a NotificationProvider\"\r\n    );\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;;AADA;;AAeA,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkC;AAEnE,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAA2B;;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,mBAAmB,CACvB,SACA,OAA6B,MAAM;QAEnC,MAAM,KAAK,KAAK,GAAG;QACnB,iBAAiB,CAAC,OAAS;mBAAI;gBAAM;oBAAE;oBAAI;oBAAS;gBAAK;aAAE;QAE3D,8BAA8B;QAC9B,WAAW,IAAM,mBAAmB,KAAK;IAC3C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;IACjE;IAEA,qBACE,6LAAC,oBAAoB,QAAQ;QAC3B,OAAO;YAAE;YAAe;YAAkB;QAAmB;kBAE5D;;;;;;AAGP;GAzBa;KAAA;AA2BN,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARa"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/NotificationBar.tsx"], "sourcesContent": ["import { useNotification } from \"../hooks/useNotification\";\r\n\r\nconst NotificationBar = () => {\r\n  const { notifications, removeNotification } = useNotification();\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-50 flex flex-col gap-2\">\r\n      {notifications.map(({ id, message, type }) => (\r\n        <div\r\n          key={id}\r\n          className={`flex items-center px-4 py-2 text-white rounded-lg shadow-md transition-all \r\n            ${type === \"success\" ? \"bg-green-500\" : \"\"}\r\n            ${type === \"error\" ? \"bg-red-500\" : \"\"}\r\n            ${type === \"warning\" ? \"bg-yellow-500\" : \"\"}\r\n            ${type === \"info\" ? \"bg-blue-500\" : \"\"}`}\r\n        >\r\n          <span className=\"flex-1\">{message}</span>\r\n          <button\r\n            className=\"ml-4 text-white font-bold\"\r\n            onClick={() => removeNotification(id)}\r\n          >\r\n            ✖\r\n          </button>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationBar;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE5D,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,iBACvC,6LAAC;gBAEC,WAAW,CAAC;YACV,EAAE,SAAS,YAAY,iBAAiB,GAAG;YAC3C,EAAE,SAAS,UAAU,eAAe,GAAG;YACvC,EAAE,SAAS,YAAY,kBAAkB,GAAG;YAC5C,EAAE,SAAS,SAAS,gBAAgB,IAAI;;kCAE1C,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,mBAAmB;kCACnC;;;;;;;eAXI;;;;;;;;;;AAkBf;GAzBM;;QAC0C,4HAAA,CAAA,kBAAe;;;KADzD;uCA2BS"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/context/SkillsContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  createContext,\r\n  useState,\r\n  useContext,\r\n  ReactNode,\r\n  SetStateAction,\r\n  Dispatch,\r\n} from \"react\";\r\n\r\n// Define types\r\ninterface Skill {\r\n  id: number;\r\n  name: string;\r\n  weight: string;\r\n  subcategory_id: number;\r\n  attribute_type_id: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  is_approved: boolean;\r\n}\r\n\r\ninterface SubCategory {\r\n  id: number;\r\n  name: string;\r\n  category_id: number;\r\n  category_name: string;\r\n}\r\n\r\ninterface Weight {\r\n  id: number;\r\n  subcategory_id: number;\r\n  attribute_type_id: number;\r\n  weight_level: string;\r\n  weight_value: number;\r\n}\r\n\r\ninterface SkillsContextType {\r\n  skillsData: SubCategory[];\r\n  setSkillsData: Dispatch<SetStateAction<SubCategory[]>>;\r\n  selectedTab: string;\r\n  setSelectedTab: Dispatch<SetStateAction<string>>;\r\n  singleSkillData: Skill[] | undefined;\r\n  setSingleSkillData: Dispatch<SetStateAction<Skill[] | undefined>>;\r\n  selectedCategory: SubCategory | undefined;\r\n  setSelectedCategory: Dispatch<SetStateAction<SubCategory | undefined>>;\r\n  sendUpdatedData: any;\r\n  setSendUpdatedData: Dispatch<SetStateAction<any>>;\r\n  trackCurrentData: any;\r\n  setTrackCurrentData: Dispatch<SetStateAction<any>>;\r\n  loading: boolean;\r\n  setLoading: Dispatch<SetStateAction<boolean>>;\r\n  loadingPost: boolean;\r\n  setLoadingPost: Dispatch<SetStateAction<boolean>>;\r\n  loadingDelete: boolean;\r\n  setLoadingDelete: Dispatch<SetStateAction<boolean>>;\r\n  loadingApprovedStatus: boolean;\r\n  setLoadingApprovedStatus: Dispatch<SetStateAction<boolean>>;\r\n  isDeleteOpen: string;\r\n  setDeleteOpen: Dispatch<SetStateAction<string>>;\r\n  isAllDeleteOpen: boolean;\r\n  setAllDeleteOpen: Dispatch<SetStateAction<boolean>>;\r\n  selectedRows: Set<number>;\r\n  setSelectedRows: Dispatch<SetStateAction<Set<number>>>;\r\n  weightsOfSubcategory: Weight[];\r\n  setWeightsOfSubcategory: Dispatch<SetStateAction<Weight[]>>;\r\n  sortColumn: string | null;\r\n  setSortColumn: Dispatch<SetStateAction<string | null>>;\r\n  sortOrder: \"asc\" | \"desc\";\r\n  setSortOrder: Dispatch<SetStateAction<\"asc\" | \"desc\">>;\r\n  isOpenDiscardModal: boolean;\r\n  setIsOpenDiscardModal: Dispatch<SetStateAction<boolean>>;\r\n  updatedAction: any;\r\n  setUpdatedAction: Dispatch<SetStateAction<any>>;\r\n  selecteApproveddRows: any;\r\n  setSelectedApproveddRows: Dispatch<SetStateAction<any>>;\r\n  isHeaderTabChange: boolean;\r\n  setIsHeaderTabChange: Dispatch<SetStateAction<boolean>>;\r\n}\r\n\r\n// Create Context\r\nconst SkillsContext = createContext<SkillsContextType | undefined>(undefined);\r\n\r\n// Provider Component\r\nexport const SkillsProvider = ({ children }: { children: ReactNode }) => {\r\n  const [skillsData, setSkillsData] = useState<SubCategory[]>([]);\r\n  const [selectedTab, setSelectedTab] = useState<string>(\"Soft Skills\");\r\n  const [singleSkillData, setSingleSkillData] = useState<Skill[] | undefined>();\r\n  const [selectedCategory, setSelectedCategory] = useState<SubCategory>();\r\n  const [sendUpdatedData, setSendUpdatedData] = useState<any>({});\r\n  const [updatedAction, setUpdatedAction] = useState<any>({});\r\n  const [trackCurrentData, setTrackCurrentData] = useState<any>({});\r\n  const [loading, setLoading] = useState(false);\r\n  const [loadingPost, setLoadingPost] = useState(false);\r\n  const [loadingDelete, setLoadingDelete] = useState(false);\r\n  const [loadingApprovedStatus, setLoadingApprovedStatus] = useState(false);\r\n  const [isDeleteOpen, setDeleteOpen] = useState(\"\");\r\n  const [isAllDeleteOpen, setAllDeleteOpen] = useState(false);\r\n  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());\r\n  const [selecteApproveddRows, setSelectedApproveddRows] = useState<any>({});\r\n  const [weightsOfSubcategory, setWeightsOfSubcategory] = useState<Weight[]>(\r\n    []\r\n  );\r\n  const [sortColumn, setSortColumn] = useState<string | null>(null);\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);\r\n  const [isHeaderTabChange, setIsHeaderTabChange] = useState(false);\r\n\r\n  return (\r\n    <SkillsContext.Provider\r\n      value={{\r\n        skillsData,\r\n        setSkillsData,\r\n        selectedTab,\r\n        setSelectedTab,\r\n        singleSkillData,\r\n        setSingleSkillData,\r\n        selectedCategory,\r\n        setSelectedCategory,\r\n        sendUpdatedData,\r\n        setSendUpdatedData,\r\n        trackCurrentData,\r\n        setTrackCurrentData,\r\n        loading,\r\n        setLoading,\r\n        loadingPost,\r\n        setLoadingPost,\r\n        loadingDelete,\r\n        setLoadingDelete,\r\n        isDeleteOpen,\r\n        setDeleteOpen,\r\n        isAllDeleteOpen,\r\n        setAllDeleteOpen,\r\n        selectedRows,\r\n        setSelectedRows,\r\n        weightsOfSubcategory,\r\n        setWeightsOfSubcategory,\r\n        sortColumn,\r\n        setSortColumn,\r\n        sortOrder,\r\n        setSortOrder,\r\n        isOpenDiscardModal,\r\n        setIsOpenDiscardModal,\r\n        updatedAction,\r\n        setUpdatedAction,\r\n        selecteApproveddRows,\r\n        setSelectedApproveddRows,\r\n        loadingApprovedStatus,\r\n        setLoadingApprovedStatus,\r\n        isHeaderTabChange,\r\n        setIsHeaderTabChange,\r\n      }}\r\n    >\r\n      {children}\r\n    </SkillsContext.Provider>\r\n  );\r\n};\r\n\r\n// Hook to use SkillsContext\r\nexport const useSkills = () => {\r\n  const context = useContext(SkillsContext);\r\n  if (!context) {\r\n    throw new Error(\"useSkills must be used within a SkillsProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;;AADA;;AAgFA,iBAAiB;AACjB,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAG5D,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAA2B;;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,sBAAsB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACxE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7D,EAAE;IAEJ,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,qBACE,6LAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAxEa;KAAA;AA2EN,MAAM,YAAY;;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/providers.tsx"], "sourcesContent": ["// src/app/providers.tsx\r\n\"use client\";\r\nimport { SessionProvider } from \"next-auth/react\";\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  return <SessionProvider>{children}</SessionProvider>;\r\n}\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;AAExB;AADA;;;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBAAO,6LAAC,iJAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B;KAFgB"}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/tabRoutes.ts"], "sourcesContent": ["// utils/tabRoutes.ts\r\n\r\nexport enum TAB_ROUTE_MAP {\r\n  home = \"/\",\r\n  Vacancy = \"/candidates\",\r\n  jobs = \"/jobs\",\r\n  skillsEditor = \"/skills-editor\",\r\n  workforceIndex = \"/workforce-index\",\r\n  // subcategoryConfig = \"/subcategory-config\",\r\n  stats = \"/stats\",\r\n}\r\n\r\nexport interface TabRoute {\r\n  route: string;\r\n  show?: boolean;\r\n}\r\n\r\nexport const getTabMenus = (\r\n  entitlements?: Record<string, boolean>,\r\n  isADlogin?: boolean\r\n): TabRoute[] => {\r\n  return [\r\n    { route: TAB_ROUTE_MAP.home },\r\n    {\r\n      route: TAB_ROUTE_MAP.skillsEditor,\r\n      show: isADlogin ? entitlements?.Sub_Catregory : true,\r\n    },\r\n    {\r\n      route: TAB_ROUTE_MAP.workforceIndex,\r\n      show: isADlogin ? entitlements?.Work_force_Index : true,\r\n    },\r\n    {\r\n      route: TAB_ROUTE_MAP.Vacancy,\r\n      show: isADlogin ? entitlements?.Vacancy : true,\r\n    },\r\n    {\r\n      route: TAB_ROUTE_MAP.stats,\r\n      show: isADlogin ? true : false,\r\n    },\r\n    // {\r\n    //   route: TAB_ROUTE_MAP.subcategoryConfig,\r\n    //   show: isADlogin ? entitlements?.Sc_Score_Config : true,\r\n    // },\r\n  ].filter((tab) => tab.show !== false);\r\n};\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AAEd,IAAA,AAAK,uCAAA;;;;;;IAMV,6CAA6C;;WANnC;;AAeL,MAAM,cAAc,CACzB,cACA;IAEA,OAAO;QACL;YAAE,KAAK;QAAqB;QAC5B;YACE,KAAK;YACL,MAAM,YAAY,cAAc,gBAAgB;QAClD;QACA;YACE,KAAK;YACL,MAAM,YAAY,cAAc,mBAAmB;QACrD;QACA;YACE,KAAK;YACL,MAAM,YAAY,cAAc,UAAU;QAC5C;QACA;YACE,KAAK;YACL,MAAM,YAAY,OAAO;QAC3B;KAKD,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;AACjC"}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL =\r\n  process.env.NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL;\r\n\r\nexport const API_ENDPOINTS = {\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacancyByVacancyId: `${BASE_URL}/vacancy/:vacancy_id`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,\r\n  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,\r\n  getCatalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n\r\n  // New Stats Endpoints\r\n  getCatalystMatchStats: `${BASE_URL}/catalystmatch/stats`,\r\n  getCatalystMatchStatsByCategory: `${BASE_URL}/catalystmatch/stats?aggregate_by_category=true`,\r\n\r\n  // Applicant Stats Endpoints\r\n  getApplicantStatsSummary: `${BASE_URL}/applicants/stats/summary`,\r\n  getApplicantStatsByVacancy: `${BASE_URL}/applicants/stats/by-vacancy`,\r\n  getApplicantStatsByCategory: `${BASE_URL}/applicants/stats/by-category`,\r\n  getApplicantStatsTrends: `${BASE_URL}/applicants/stats/trends`,\r\n  \r\n  // CatalystMatch Endpoints\r\n  submitCatalystMatch: `${BASE_URL}/catalyst-match/submit`,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAiB;AAAjB,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM,+CACX,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,4CAA4C;AAEnD,MAAM,gBAAgB;IAC3B,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,sBAAsB,GAAG,SAAS,oBAAoB,CAAC;IACvD,iBAAiB,GAAG,wBAAwB,wFAAwF,CAAC;IACrI,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IAEtF,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;IAE7E,sBAAsB;IACtB,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,iCAAiC,GAAG,SAAS,+CAA+C,CAAC;IAE7F,4BAA4B;IAC5B,0BAA0B,GAAG,SAAS,yBAAyB,CAAC;IAChE,4BAA4B,GAAG,SAAS,4BAA4B,CAAC;IACrE,6BAA6B,GAAG,SAAS,6BAA6B,CAAC;IACvE,yBAAyB,GAAG,SAAS,wBAAwB,CAAC;IAE9D,0BAA0B;IAC1B,qBAAqB,GAAG,SAAS,sBAAsB,CAAC;AAC1D"}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/utils.ts"], "sourcesContent": ["import { EntitlementMap } from \"@/context/EntitlementContext\";\r\nimport moment from \"moment-timezone\";\r\nimport { isADLogin } from \"@/api/config\"; // Adjust the import path as necessary\r\n\r\nexport const VACANCY_FILTER_URL_REGEX =\r\n  /^https:\\/\\/recruiter(?:\\.([^.]+))?\\.tandymgroup\\.com/;\r\n\r\nexport const unFormattedDateWithBrowserTimezone = (\r\n  val: string,\r\n  tz: string = \"America/Los_Angeles\" // Default to PST\r\n) => {\r\n  const utcDate = moment.utc(val); // Convert input to UTC\r\n  const formattedPSTDateTime = utcDate.tz(tz).format(\"YYYY-MM-DD (hh:mm A)\"); // Convert to PST and format with AM/PM\r\n\r\n  return formattedPSTDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneWithTimezone = (val: string) => {\r\n  const browserTz = Intl.DateTimeFormat().resolvedOptions().timeZone; // get browser timezone\r\n  const utcDate = moment.utc(val);\r\n  const formattedDateTime = utcDate\r\n    .tz(browserTz)\r\n    .format(\"YYYY-MM-DD (hh:mm A z)\");\r\n  return formattedDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneInDDMMYY = (\r\n  val: string,\r\n  mode: \"STATIC\" | \"UTC_TO_LOCAL\" = \"STATIC\" // default mode for static dates\r\n): string => {\r\n  if (!moment(val).isValid()) return \"Invalid Date\";\r\n\r\n  const dateMoment =\r\n    mode === \"UTC_TO_LOCAL\"\r\n      ? moment.utc(val).local() // convert UTC to browser's local time\r\n      : moment(val); // no conversion\r\n\r\n  return dateMoment.format(\"MM-DD-YYYY\");\r\n};\r\n\r\nexport const getInitials = (name: string) => {\r\n  if (!name) return \"\";\r\n  return name\r\n    .split(\" \") // Split the name into parts by spaces\r\n    .map((part) => part.charAt(0).toUpperCase()) // Take the first letter of each part and capitalize it\r\n    .join(\"\"); // Join the initials together\r\n};\r\n\r\n// utils/entitlementUtils.ts (or inline)\r\nexport const getEntitlementKeyFromTitle = (\r\n  title: string\r\n): string | undefined => {\r\n  switch (title) {\r\n    case \"Vacancy\":\r\n      return \"Vacancy\";\r\n    case \"Workforce Readiness Index\":\r\n      return \"Work_force_Index\";\r\n    case \"Sub-Category Library\":\r\n      return \"Sub_Catregory\";\r\n    default:\r\n      return undefined;\r\n  }\r\n};\r\n\r\nexport const isUserEntitled = (\r\n  entitlementKey: string | undefined,\r\n  entitlement: EntitlementMap\r\n): boolean => {\r\n  const isADLoginValue: boolean = isADLogin();\r\n  if (!isADLoginValue || !entitlementKey) {\r\n    return true; // Default to true if not AD login or entitlement key is undefined\r\n  }\r\n  return !!entitlement[entitlementKey];\r\n};\r\n\r\nexport function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n  const code = envCode.toLowerCase();\r\n  if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n    return \"sandbox\";\r\n  }\r\n  if (code === \"ua\") {\r\n    return \"uat\";\r\n  }\r\n  if (code === \"prod\") {\r\n    return \"prod\";\r\n  }\r\n  // Optionally, handle unknown codes\r\n  return \"sandbox\";\r\n}\r\n\r\nexport const capitalizeEachWord = (str: string) =>\r\n  str.toLowerCase().replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA,kMAA0C,sCAAsC;;;AAEzE,MAAM,2BACX;AAEK,MAAM,qCAAqC,CAChD,KACA,KAAa,sBAAsB,iBAAiB;AAAlB;IAElC,MAAM,UAAU,8IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,MAAM,uBAAuB;IACxD,MAAM,uBAAuB,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,yBAAyB,uCAAuC;IAEnH,OAAO;AACT;AAEO,MAAM,iDAAiD,CAAC;IAC7D,MAAM,YAAY,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE,uBAAuB;IAC3F,MAAM,UAAU,8IAAA,CAAA,UAAM,CAAC,GAAG,CAAC;IAC3B,MAAM,oBAAoB,QACvB,EAAE,CAAC,WACH,MAAM,CAAC;IACV,OAAO;AACT;AAEO,MAAM,6CAA6C,CACxD,KACA,OAAkC,SAAS,gCAAgC;AAAjC;IAE1C,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,OAAO,IAAI,OAAO;IAEnC,MAAM,aACJ,SAAS,iBACL,8IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,sCAAsC;OAC9D,CAAA,GAAA,8IAAA,CAAA,UAAM,AAAD,EAAE,MAAM,gBAAgB;IAEnC,OAAO,WAAW,MAAM,CAAC;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,KAAK,CAAC,KAAK,sCAAsC;KACjD,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,IAAI,uDAAuD;KACnG,IAAI,CAAC,KAAK,6BAA6B;AAC5C;AAGO,MAAM,6BAA6B,CACxC;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,iBAAiB,CAC5B,gBACA;IAEA,MAAM,iBAA0B,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD;IACxC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB;QACtC,OAAO,MAAM,kEAAkE;IACjF;IACA,OAAO,CAAC,CAAC,WAAW,CAAC,eAAe;AACtC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,OAAO,QAAQ,WAAW;IAChC,IAAI;QAAC;QAAM;QAAM;KAAK,CAAC,QAAQ,CAAC,OAAO;QACrC,OAAO;IACT;IACA,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,mCAAmC;IACnC,OAAO;AACT;AAEO,MAAM,qBAAqB,CAAC,MACjC,IAAI,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW"}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { VACANCY_FILTER_URL_REGEX } from \"@/utils/utils\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const getDomainClient = () => {\r\n    const url = window.location.href;\r\n    const match = url.match(VACANCY_FILTER_URL_REGEX);\r\n    return match && match[1] ? match[1] : \"prod\";\r\n  };\r\n\r\n  if (!appInsights) {\r\n    let connectionString = \"\"; // ✅ Declare here so it's in scope\r\n\r\n    const env = getDomainClient();\r\n    switch (env) {\r\n      case \"dv\":\r\n        connectionString =\r\n          \"InstrumentationKey=a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849\";\r\n        break;\r\n      case \"qa\":\r\n        connectionString =\r\n          \"InstrumentationKey=f3dd9927-e8d5-439b-972a-da458156c3ac;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=d6e306bb-1468-40e7-99d1-12f2024ed410\";\r\n        break;\r\n      case \"sb\":\r\n        connectionString =\r\n          \"InstrumentationKey=10a2fa35-5df1-4728-b76d-50b7548e507f;IngestionEndpoint=https://eastus-5.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=8b3040c3-62e0-42e5-9bef-b49ffa496183\";\r\n        break;\r\n      case \"ua\":\r\n        connectionString =\r\n          \"InstrumentationKey=017ce8e3-75aa-4b79-821a-a254f47527df;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=7f1efc32-bc37-4cd1-bd8e-c4af6169342b\";\r\n        break;\r\n      default:\r\n        connectionString =\r\n          \"InstrumentationKey=cb49f8e1-9368-42f9-95d6-5ab16b42c75a;IngestionEndpoint=https://eastus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=1fe4a6f4-7eee-4cbe-9e7c-ed77e57398bf\";\r\n    }\r\n\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false,\r\n        enableCorsCorrelation: true,\r\n        enableRequestHeaderTracking: true,\r\n        enableResponseHeaderTracking: true,\r\n        extensions: [],\r\n        extensionConfig: {},\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n\r\n    const isIframe = window.self !== window.top;\r\n    appInsights.trackEvent({\r\n      name: \"AppInsightsInitialized\",\r\n      properties: {\r\n        isIframe,\r\n        referrer: document.referrer,\r\n        currentURL: window.location.href,\r\n      },\r\n    });\r\n  }\r\n\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI;QAChC,MAAM,QAAQ,IAAI,KAAK,CAAC,iHAAA,CAAA,2BAAwB;QAChD,OAAO,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACxC;IAEA,IAAI,CAAC,aAAa;QAChB,IAAI,mBAAmB,IAAI,kCAAkC;QAE7D,MAAM,MAAM;QACZ,OAAQ;YACN,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF;gBACE,mBACE;QACN;QAEA,cAAc,IAAI,6OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,8BAA8B;gBAC9B,YAAY,EAAE;gBACd,iBAAiB,CAAC;YACpB;QACF;QACA,YAAY,eAAe;QAE3B,MAAM,WAAW,OAAO,IAAI,KAAK,OAAO,GAAG;QAC3C,YAAY,UAAU,CAAC;YACrB,MAAM;YACN,YAAY;gBACV;gBACA,UAAU,SAAS,QAAQ;gBAC3B,YAAY,OAAO,QAAQ,CAAC,IAAI;YAClC;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n\r\nexport enum FEATURE_NAMES {\r\n  SORTLIST_CANDIDATE_FEATURE = \"Shortlist Candidate Feature\",\r\n  MERCRURY_SORTLIST_CANDIDATE_FEATURE = \"Mercury Shortlist Candidate Feature\",\r\n  THUMB_REVIEW_FEATURE = \"Thumb Review Feature\",\r\n  THUMB_REVIEW_FEATURE_FROM_MERCURY = \"Thumb Review Feature from Mercury CRM\",\r\n  REGENERATE_CANDIDATES_MATCH_FEATURE = \"Regenerate Candidates Match Feature\",\r\n  MERCURY_REGENERATE_CANDIDATES_MATCH_FEATURE = \"Mercury Regenerate Candidates Match Feature\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_LABELS {\r\n  STATE = \"State\",\r\n  CITY = \"City\",\r\n  FRESHNESS_INDEX = \"Freshness Index\",\r\n  SHORT_LISTED = \"Short Listed\",\r\n  REVIEW_DECISION = \"Review Decision\",\r\n  AI_AGENT_STATUS = \"AI Agent Status\",\r\n  TOTAL_SCORE = \"Total Score\",\r\n  AVAILABILITY_DATE = \"Availability Date\",\r\n  SEARCH_BY_NAME = \"Search by Name\",\r\n  TOTAL_SCORE_RANGE = \"Total Score Range\",\r\n  AVAILABILITY_DATE_RANGE = \"Availability Date Range\",\r\n  SEARCH_FIELDS = \"Search Fields\",\r\n  DISTANCE_RANGE = \"Distance Range\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_OTHER_LABELS {\r\n  NAME = \"name\",\r\n  CITY = \"city\",\r\n  WHYFIT = \"whyFit\",\r\n  NOREVIEW = \"NoReview\",\r\n  THUMBSUP = \"ThumbsUp\",\r\n  THUMBSDOWN = \"ThumbsDown\",\r\n  LIKE = \"like\",\r\n  DISLIKE = \"dislike\",\r\n  MISSING = \"Missing\",\r\n  SUCCESS = \"success\",\r\n  CLASSIFICATIONSCORE = \"classification score\",\r\n  OVERALLSCORE = \"overallscore\",\r\n}\r\n\r\nexport enum AI_AGENTS_RESPONSE_STATUS {\r\n  NOT_CONTACTED_BLANK = \"Not Contacted (Blank)\",\r\n  RESPONDED = \"Responded\",\r\n  CONTACTED = \"Contacted\",\r\n  NOT_INTERESTED = \"Not interested\",\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,uCAAmC;;IAAS;IAC5C,IAAI,KAAK,aAAa,OAAO,CAAC;IAC9B,IAAI,CAAC,IAAI;QACP,KAAK,OAAO,UAAU;QACtB,aAAa,OAAO,CAAC,eAAe;IACtC;IACA,OAAO;AACT;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB;AAE7B,IAAA,AAAK,uCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,+CAAA;;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,qDAAA;;;;;;;;;;;;;WAAA;;AAeL,IAAA,AAAK,mDAAA;;;;;WAAA"}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/context/EntitlementContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { TAB_ROUTE_MAP } from \"@/utils/tabRoutes\";\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n  useRef,\r\n} from \"react\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { useSession } from \"next-auth/react\";\r\n\r\n// Map of entitlements: { FeatureA: true, FeatureB: false }\r\nexport interface EntitlementMap {\r\n  [key: string]: boolean;\r\n}\r\n\r\nexport interface EntitlementContextProps {\r\n  entitlements: EntitlementMap;\r\n  setEntitlements: (value: EntitlementMap) => void;\r\n  isLoaded: boolean;\r\n}\r\n\r\nconst EntitlementContext = createContext<EntitlementContextProps | undefined>(\r\n  undefined\r\n);\r\n\r\nexport const EntitlementProvider = ({ children }: { children: ReactNode }) => {\r\n  const pathname = usePathname();\r\n  const routes = Object.values(TAB_ROUTE_MAP) as string[];\r\n  const [entitlements, setEntitlements] = useState<EntitlementMap>({});\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const calledRef = useRef(false);\r\n  const { data: session, status } = useSession();\r\n\r\n  useEffect(() => {\r\n    if (calledRef.current) return;\r\n    if (status !== \"authenticated\") return; // Wait until session is loaded\r\n    let interval: NodeJS.Timeout | null = null;\r\n\r\n    const fetchEntitlementData = async () => {\r\n      try {\r\n        const response = await trackedFetch(\r\n          \"/api/entitlements\",\r\n          {},\r\n          { context: \"Entitlements\" }\r\n        );\r\n        if (!response.ok) throw new Error(\"Network response was not ok\");\r\n\r\n        const data = await response.json();\r\n        if (!data.error) {\r\n          setEntitlements(data.entitlement);\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email || \"\",\r\n            },\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching entitlement data:\", error);\r\n        getAppInsights()?.trackException({\r\n          error: new Error(\"Entitlements api with error is \" + error),\r\n          severityLevel: 3,\r\n        });\r\n      } finally {\r\n        setIsLoaded(true);\r\n      }\r\n    };\r\n\r\n    const checkAndFetch = () => {\r\n      const ai = getAppInsights();\r\n      if (routes.includes(pathname) && ai && !calledRef.current) {\r\n        calledRef.current = true;\r\n        fetchEntitlementData();\r\n        if (interval) clearInterval(interval);\r\n      }\r\n    };\r\n\r\n    checkAndFetch();\r\n    interval = setInterval(checkAndFetch, 100);\r\n\r\n    return () => {\r\n      if (interval) clearInterval(interval);\r\n    };\r\n  }, [pathname, routes, status]);\r\n\r\n  return (\r\n    <EntitlementContext.Provider\r\n      value={{ entitlements, setEntitlements, isLoaded }}\r\n    >\r\n      {children}\r\n    </EntitlementContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useEntitlement = () => {\r\n  const context = useContext(EntitlementContext);\r\n  if (!context) {\r\n    throw new Error(\r\n      \"useEntitlement must be used within an EntitlementProvider\"\r\n    );\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAQA;AACA;AACA;;;AAbA;;;;;;;AA0BA,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACrC;AAGK,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA2B;;IACvE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU,OAAO,EAAE;YACvB,IAAI,WAAW,iBAAiB,QAAQ,+BAA+B;YACvE,IAAI,WAAkC;YAEtC,MAAM;sEAAuB;oBAC3B,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,qBACA,CAAC,GACD;4BAAE,SAAS;wBAAe;wBAE5B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;wBAElC,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,IAAI,CAAC,KAAK,KAAK,EAAE;4BACf,gBAAgB,KAAK,WAAW;4BAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;gCAC3B,MAAM;gCACN,YAAY;oCACV,OAAO,SAAS,MAAM,SAAS;gCACjC;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;4BAC/B,OAAO,IAAI,MAAM,oCAAoC;4BACrD,eAAe;wBACjB;oBACF,SAAU;wBACR,YAAY;oBACd;gBACF;;YAEA,MAAM;+DAAgB;oBACpB,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;oBACxB,IAAI,OAAO,QAAQ,CAAC,aAAa,MAAM,CAAC,UAAU,OAAO,EAAE;wBACzD,UAAU,OAAO,GAAG;wBACpB;wBACA,IAAI,UAAU,cAAc;oBAC9B;gBACF;;YAEA;YACA,WAAW,YAAY,eAAe;YAEtC;iDAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;wCAAG;QAAC;QAAU;QAAQ;KAAO;IAE7B,qBACE,6LAAC,mBAAmB,QAAQ;QAC1B,OAAO;YAAE;YAAc;YAAiB;QAAS;kBAEhD;;;;;;AAGP;GAnEa;;QACM,qIAAA,CAAA,cAAW;QAKM,iJAAA,CAAA,aAAU;;;KANjC;AAqEN,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARa"}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/AppInsights.ts"], "sourcesContent": ["\"use client\";\r\nimport { useEffect } from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { initAppInsights, getAppInsights } from \"@/library/appInsights\";\r\nimport { getOrCreateUserUuid } from \"@/library/utils\";\r\nimport { useSession } from \"next-auth/react\";\r\n\r\nconst AppInsightsClient = () => {\r\n  const pathname = usePathname();\r\n  const { data: session } = useSession();\r\n\r\n  // 1. Initialize App Insights once\r\n  useEffect(() => {\r\n    initAppInsights();\r\n    window.onerror = (msg, src, line, col, err) => {\r\n      getAppInsights()?.trackException({\r\n        error: err || new Error(String(msg)),\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  // 2. Register telemetry initializer once, but always use latest session/uuid\r\n  useEffect(() => {\r\n    const uuid = getOrCreateUserUuid();\r\n    const ai = getAppInsights();\r\n    if (!ai) return;\r\n    // Remove previous initializers if needed (optional, depends on SDK)\r\n    ai.addTelemetryInitializer((envelope) => {\r\n      const baseData: any = envelope.data?.baseData ?? {};\r\n      baseData.properties = {\r\n        ...(baseData.properties || {}),\r\n        userUuid: uuid,\r\n        email: session?.user?.email || \"\",\r\n      };\r\n      envelope.data!.baseData = baseData;\r\n    });\r\n\r\n    // Set authenticated user context\r\n    ai.setAuthenticatedUserContext(uuid);\r\n  }, [session]);\r\n\r\n  // 3. Track page views\r\n  useEffect(() => {\r\n    getAppInsights()?.trackPageView({ uri: pathname });\r\n  }, [pathname]);\r\n\r\n  return null;\r\n};\r\n\r\nexport default AppInsightsClient;\r\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;AALA;;;;;;AAOA,MAAM,oBAAoB;;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;YACd,OAAO,OAAO;+CAAG,CAAC,KAAK,KAAK,MAAM,KAAK;oBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;wBAC/B,OAAO,OAAO,IAAI,MAAM,OAAO;oBACjC;gBACF;;QACF;sCAAG,EAAE;IAEL,6EAA6E;IAC7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;YAC/B,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;YACxB,IAAI,CAAC,IAAI;YACT,oEAAoE;YACpE,GAAG,uBAAuB;+CAAC,CAAC;oBAC1B,MAAM,WAAgB,SAAS,IAAI,EAAE,YAAY,CAAC;oBAClD,SAAS,UAAU,GAAG;wBACpB,GAAI,SAAS,UAAU,IAAI,CAAC,CAAC;wBAC7B,UAAU;wBACV,OAAO,SAAS,MAAM,SAAS;oBACjC;oBACA,SAAS,IAAI,CAAE,QAAQ,GAAG;gBAC5B;;YAEA,iCAAiC;YACjC,GAAG,2BAA2B,CAAC;QACjC;sCAAG;QAAC;KAAQ;IAEZ,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,cAAc;gBAAE,KAAK;YAAS;QAClD;sCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GAxCM;;QACa,qIAAA,CAAA,cAAW;QACF,iJAAA,CAAA,aAAU;;;KAFhC;uCA0CS"}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport \"./globals.css\";\r\nimport { NotificationProvider } from \"@/hooks/useNotification\";\r\nimport NotificationBar from \"@/components/NotificationBar\";\r\nimport { SkillsProvider } from \"@/context/SkillsContext\";\r\nimport { Providers } from \"@/components/providers\";\r\nimport { EntitlementProvider } from \"@/context/EntitlementContext\";\r\nimport dynamic from \"next/dynamic\";\r\nimport React, { useEffect } from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { TAB_ROUTE_MAP } from \"@/utils/tabRoutes\";\r\nimport AppInsightsClient from \"@/components/AppInsights\";\r\n\r\n// Move client-only logic to a separate component\r\nconst EntitlementReadyWrapper = dynamic(\r\n  () => import(\"@/components/HeaderTabs/EntitlementReadyWrapper\"),\r\n  {\r\n    ssr: false,\r\n  }\r\n);\r\n\r\n// Move client-only logic to a separate component\r\nfunction ClientLayout({ children }: { children: React.ReactNode }) {\r\n  const pathname = usePathname();\r\n  const routes = Object.values(TAB_ROUTE_MAP) as string[];\r\n  return (\r\n    <>\r\n      {routes.includes(pathname) && (\r\n        <>\r\n          <NotificationBar />\r\n          <EntitlementReadyWrapper />\r\n        </>\r\n      )}\r\n      <div className=\"h-full\">\r\n        <div className=\"mx-3\">{children}</div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      if (!localStorage.getItem(\"filtersInitialized\")) {\r\n        if (\r\n          localStorage.getItem(\"recruiter-filter-store\") ||\r\n          localStorage.getItem(\"mercury-filter-store\")\r\n        ) {\r\n          localStorage.removeItem(\"mercury-filter-store\");\r\n          localStorage.removeItem(\"recruiter-filter-store\");\r\n        }\r\n        localStorage.setItem(\"filtersInitialized\", \"true\");\r\n      }\r\n    }\r\n  }, []);\r\n  return (\r\n    <html lang=\"en\">\r\n      <body>\r\n        <Providers>\r\n          <AppInsightsClient />\r\n          <EntitlementProvider>\r\n            <SkillsProvider>\r\n              <NotificationProvider>\r\n                <ClientLayout>{children}</ClientLayout>\r\n              </NotificationProvider>\r\n            </SkillsProvider>\r\n          </EntitlementProvider>\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;;AAaA,iDAAiD;AACjD,MAAM,0BAA0B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACpC;;;;;;IAEE,KAAK;;KAHH;AAON,iDAAiD;AACjD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IAC/D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa;IAC1C,qBACE;;YACG,OAAO,QAAQ,CAAC,2BACf;;kCACE,6LAAC,iIAAA,CAAA,UAAe;;;;;kCAChB,6LAAC;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAQ;;;;;;;;;;;;;AAI/B;GAhBS;;QACU,qIAAA,CAAA,cAAW;;;MADrB;AAkBM,SAAS,WAAW,EACjC,QAAQ,EAGR;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,wCAAmC;gBACjC,IAAI,CAAC,aAAa,OAAO,CAAC,uBAAuB;oBAC/C,IACE,aAAa,OAAO,CAAC,6BACrB,aAAa,OAAO,CAAC,yBACrB;wBACA,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;oBAC1B;oBACA,aAAa,OAAO,CAAC,sBAAsB;gBAC7C;YACF;QACF;+BAAG,EAAE;IACL,qBACE,6LAAC;QAAK,MAAK;kBACT,cAAA,6LAAC;sBACC,cAAA,6LAAC,2HAAA,CAAA,YAAS;;kCACR,6LAAC,4HAAA,CAAA,UAAiB;;;;;kCAClB,6LAAC,iIAAA,CAAA,sBAAmB;kCAClB,cAAA,6LAAC,4HAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,4HAAA,CAAA,uBAAoB;0CACnB,cAAA,6LAAC;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;IAnCwB;MAAA"}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}