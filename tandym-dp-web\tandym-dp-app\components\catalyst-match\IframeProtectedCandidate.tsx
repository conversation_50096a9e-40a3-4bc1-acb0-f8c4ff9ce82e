"use client";
import { useState } from "react";
import IframeAuthGate from "@/components/catalyst-match/IframeAuthGate";
import VacancyCandidates from "@/components/candidates/Candidates";

export default function IframeProtectedCandidate({
  vacancyid,
  emailId,
  entitlementResponse,
}: {
  vacancyid?: string;
  mercuryPortal?: boolean;
  emailId?: string;
  entitlementResponse?: { [key: string]: boolean };
}) {
  const [mercuryPortal, setMercuryPortal] = useState<boolean | null>(null);
  return (
    <IframeAuthGate onAuthComplete={setMercuryPortal}>
      {mercuryPortal === true && (
        <VacancyCandidates
          vacancyid={vacancyid}
          mercuryPortal={mercuryPortal}
          emailId={emailId}
          entitlementData={entitlementResponse}
        />
      )}
    </IframeAuthGate>
  );
}
