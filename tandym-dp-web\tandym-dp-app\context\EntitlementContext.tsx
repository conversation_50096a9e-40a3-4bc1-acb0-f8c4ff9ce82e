"use client";
import { usePathname } from "next/navigation";
import { TAB_ROUTE_MAP } from "@/utils/tabRoutes";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useRef,
  useMemo,
} from "react";
import { getAppInsights } from "@/library/appInsights";
import { trackedFetch } from "@/library/trackApi";
import { useSession } from "next-auth/react";

// Map of entitlements: { FeatureA: true, FeatureB: false }
export interface EntitlementMap {
  [key: string]: boolean;
}

export interface EntitlementContextProps {
  entitlements: EntitlementMap;
  setEntitlements: (value: EntitlementMap) => void;
  isLoaded: boolean;
}

const EntitlementContext = createContext<EntitlementContextProps | undefined>(
  undefined
);

export const EntitlementProvider = ({ children }: { children: ReactNode }) => {
  const pathname = usePathname();
  const routes = useMemo(() => Object.values(TAB_ROUTE_MAP) as string[], []);
  const [entitlements, setEntitlements] = useState<EntitlementMap>({});
  const [isLoaded, setIsLoaded] = useState(false);
  const calledRef = useRef(false);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (calledRef.current) return;
    if (status !== "authenticated") return; // Wait until session is loaded
    let interval: NodeJS.Timeout | null = null;

    const fetchEntitlementData = async () => {
      try {
        const response = await trackedFetch(
          "/api/entitlements",
          {},
          { context: "Entitlements" }
        );
        if (!response.ok) throw new Error("Network response was not ok");

        const data = await response.json();
        if (!data.error) {
          setEntitlements(data.entitlement);
          getAppInsights()?.trackEvent({
            name: "FE_Entitlements_Fetched",
            properties: {
              email: session?.user?.email || "",
            },
          });
        }
      } catch (error) {
        console.error("Error fetching entitlement data:", error);
        getAppInsights()?.trackException({
          error: new Error("Entitlements api with error is " + error),
          severityLevel: 3,
        });
      } finally {
        setIsLoaded(true);
      }
    };

    const checkAndFetch = () => {
      const ai = getAppInsights();
      if (routes.includes(pathname) && ai && !calledRef.current) {
        calledRef.current = true;
        fetchEntitlementData();
        if (interval) clearInterval(interval);
      }
    };

    checkAndFetch();
    interval = setInterval(checkAndFetch, 100);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [pathname, routes, status]);

  return (
    <EntitlementContext.Provider
      value={{ entitlements, setEntitlements, isLoaded }}
    >
      {children}
    </EntitlementContext.Provider>
  );
};

export const useEntitlement = () => {
  const context = useContext(EntitlementContext);
  if (!context) {
    throw new Error(
      "useEntitlement must be used within an EntitlementProvider"
    );
  }
  return context;
};
