import type { Candidate } from "@/app/candidates/helper";
import { VACANCY_FILTER_OTHER_LABELS } from "@/library/utils";
import { capitalizeEachWord } from "@/utils/utils";

/**
 * Extracts unique states and their corresponding unique cities from candidates.
 * Returns an object mapping each state to an array of its cities.
 * Ignores empty, null, undefined, and 'MISSING' values.
 */
export const extractLocations = (
  candidates: Candidate[]
): Record<string, string[]> => {
  // Map of state to Set of unique cities
  const stateToCitiesMap: Record<string, Set<string>> = {};

  const isValid = (val: string | null | undefined) =>
    val && val !== VACANCY_FILTER_OTHER_LABELS.MISSING && val.trim() !== "";

  candidates &&
    candidates?.forEach((candidate) => {
      const cityRaw = candidate?.candidate_data?.city;
      const stateRaw = candidate?.candidate_data?.state;

      if (!isValid(stateRaw)) return; // Ignore if state invalid

      const state = capitalizeEachWord(stateRaw.toLowerCase());

      if (!stateToCitiesMap[state]) {
        stateToCitiesMap[state] = new Set<string>();
      }

      if (isValid(cityRaw)) {
        const city = capitalizeEachWord(cityRaw.toLowerCase());
        // Add city under this state
        stateToCitiesMap[state].add(city);
      }
    });

  // Convert Sets to arrays for output
  const uniqueLocation: Record<string, string[]> = {};
  Object.keys(stateToCitiesMap)?.forEach((state) => {
    uniqueLocation[state] = Array.from(stateToCitiesMap[state]).sort((a, b) =>
      a.localeCompare(b)
    );
  });

  return uniqueLocation;
};
