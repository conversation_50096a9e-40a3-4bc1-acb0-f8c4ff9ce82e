#!/usr/bin/env python3
"""
Advanced Folder Manager

Comprehensive tool for managing mail folders with advanced operations.
This tool combines folder listing, creation, and other management features.
"""

import os
import sys
import json

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from config import setup_logger_and_secrets, get_azure_credentials
from azure_client import AzureEmailFetcher


class AdvancedFolderManager:
    """Advanced folder management operations"""
    
    def __init__(self, logger):
        self.logger = logger
        self.email_fetcher = AzureEmailFetcher(logger=logger)
        self.mailbox = "<EMAIL>"
    
    def fetch_folders(self):
        """Fetch all mail folders from the mailbox"""
        access_token = self.email_fetcher.get_access_token()
        
        if self.logger:
            self.logger.info(f"📁 Fetching all folders from {self.mailbox}...")
        
        # Microsoft Graph API endpoint for getting mail folders
        folders_url = f"https://graph.microsoft.com/v1.0/users/{self.mailbox}/mailFolders"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Parameters to get folder information
        params = {
            '$select': 'id,displayName,parentFolderId,childFolderCount,unreadItemCount,totalItemCount',
            '$top': 100  # Get up to 100 folders
        }
        
        try:
            import requests
            response = requests.get(folders_url, headers=headers, params=params)
            response.raise_for_status()
            
            folders_data = response.json()
            folders = folders_data.get('value', [])
            
            if self.logger:
                self.logger.info(f"✅ Successfully fetched {len(folders)} folders")
            return folders
            
        except Exception as e:
            error_msg = f"Failed to fetch folders: {e}"
            if self.logger:
                self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def create_folder(self, folder_name, parent_folder_id=None):
        """Create a new mail folder with duplicate checking"""
        access_token = self.email_fetcher.get_access_token()
        
        if self.logger:
            self.logger.info(f"📁 Creating folder '{folder_name}' in {self.mailbox}...")
        
        # First, check if folder already exists
        existing_folders = self.fetch_folders()
        
        # Check for duplicate folder names
        for folder in existing_folders:
            if folder.get('displayName', '').lower() == folder_name.lower():
                # Check if parent folder matches (both None or same ID)
                existing_parent = folder.get('parentFolderId')
                if parent_folder_id == existing_parent:
                    if self.logger:
                        self.logger.info(f"📁 Folder '{folder_name}' already exists, skipping creation")
                        self.logger.info(f"✅ Folder '{folder_name}' already exists")
                        self.logger.info(f"🆔 Existing folder ID: {folder.get('id')}")
                    return folder
        
        # Folder doesn't exist, create it
        if parent_folder_id:
            # Create folder in specific parent folder
            create_url = f"https://graph.microsoft.com/v1.0/users/{self.mailbox}/mailFolders/{parent_folder_id}/childFolders"
            location_msg = f"in parent folder {parent_folder_id[:20]}..."
        else:
            # Create folder at root level
            create_url = f"https://graph.microsoft.com/v1.0/users/{self.mailbox}/mailFolders"
            location_msg = "at root level"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Folder creation payload
        folder_data = {
            'displayName': folder_name
        }
        
        try:
            if self.logger:
                self.logger.info(f"📁 Creating new folder '{folder_name}' {location_msg}...")
            
            import requests
            response = requests.post(create_url, headers=headers, json=folder_data)
            response.raise_for_status()
            
            created_folder = response.json()
            folder_id = created_folder.get('id')
            
            if self.logger:
                self.logger.info(f"✅ Successfully created folder '{folder_name}'")
                self.logger.info(f"🆔 New folder ID: {folder_id}")
                self.logger.info(f"📍 Location: {location_msg}")
            
            return created_folder
            
        except Exception as e:
            error_msg = f"Failed to create folder '{folder_name}': {e}"
            if self.logger:
                self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def print_folders_tree(self, folders):
        """Print folders in a tree structure"""
        print(f"\n📁 FOLDER TREE - {self.mailbox}")
        print("=" * 80)
        
        # Separate root folders and child folders
        root_folders = [f for f in folders if not f.get('parentFolderId')]
        child_folders = [f for f in folders if f.get('parentFolderId')]
        
        # Print root folders
        for folder in root_folders:
            self._print_folder_node(folder, 0)
            self._print_children(folder['id'], child_folders, 1)
        
        print("\n" + "=" * 80)
    
    def _print_folder_node(self, folder, indent_level):
        """Print a single folder node"""
        indent = "  " * indent_level
        icon = "📂" if indent_level == 0 else "└─📁"
        
        display_name = folder.get('displayName', 'Unknown')
        total_count = folder.get('totalItemCount', 0)
        unread_count = folder.get('unreadItemCount', 0)
        
        print(f"{indent}{icon} {display_name} ({total_count} items, {unread_count} unread)")
        print(f"{indent}   🆔 {folder.get('id', 'Unknown')}")
    
    def _print_children(self, parent_id, all_children, indent_level):
        """Recursively print child folders"""
        children = [f for f in all_children if f.get('parentFolderId') == parent_id]
        
        for child in children:
            self._print_folder_node(child, indent_level)
            self._print_children(child['id'], all_children, indent_level + 1)
    
    def export_folders_json(self, folders, filename="folders_export.json"):
        """Export folder structure to JSON file"""
        from datetime import datetime
        export_data = {
            "mailbox": self.mailbox,
            "export_date": str(datetime.now()),
            "total_folders": len(folders),
            "folders": folders
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        print(f"✅ Exported {len(folders)} folders to {filename}")
        return filename


def print_help():
    """Print help information"""
    print("Advanced Folder Manager Tool")
    print("=" * 40)
    print("Usage:")
    print("  python3 folder_manager.py <command> [options]")
    print()
    print("Commands:")
    print("  list                      List all folders in simple format")
    print("  tree                      Show folders in tree structure")
    print("  export                    Export folder structure to JSON")
    print("  create <name>             Create folder at root level")
    print("  create <name> <parent>    Create subfolder under parent")
    print("  help                      Show this help")
    print()
    print("Examples:")
    print("  python3 folder_manager.py list")
    print("  python3 folder_manager.py tree")
    print("  python3 folder_manager.py export")
    print("  python3 folder_manager.py create 'Archive'")
    print("  python3 folder_manager.py create 'Old Resumes' AQMkAGE3NwA5YTM4...")


def main():
    """Main function"""
    print("Azure Email Fetcher - Advanced Folder Manager")
    print("This tool provides comprehensive folder management capabilities.\n")
    
    if len(sys.argv) < 2:
        print("❌ Error: Command is required")
        print()
        print_help()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command in ["help", "--help", "-h"]:
        print_help()
        return
    
    # Setup logger and create manager
    logger = setup_logger_and_secrets()
    manager = AdvancedFolderManager(logger)
    
    try:
        if command == "list":
            folders = manager.fetch_folders()
            # Simple list format (same as list_folders.py)
            from datetime import datetime
            print(f"\n📁 ALL FOLDERS IN {manager.mailbox}")
            print("=" * 80)
            
            for i, folder in enumerate(folders, 1):
                print(f"\n{i:2d}. 📂 {folder.get('displayName', 'Unknown')}")
                print(f"    🆔 ID: {folder.get('id', 'Unknown')}")
                print(f"    📊 Items: {folder.get('totalItemCount', 0)} total, {folder.get('unreadItemCount', 0)} unread")
            
            print(f"\n✅ Total folders: {len(folders)}")
        
        elif command == "tree":
            folders = manager.fetch_folders()
            manager.print_folders_tree(folders)
        
        elif command == "export":
            folders = manager.fetch_folders()
            filename = manager.export_folders_json(folders)
            print(f"Folder structure exported to: {filename}")
        
        elif command == "create":
            if len(sys.argv) < 3:
                print("❌ Error: Folder name is required")
                print("Example: python3 folder_manager.py create 'My Folder'")
                sys.exit(1)
            
            folder_name = sys.argv[2]
            parent_folder_id = sys.argv[3] if len(sys.argv) >= 4 else None
            
            created_folder = manager.create_folder(folder_name, parent_folder_id)
            print(f"\n✅ Successfully created folder: {created_folder.get('displayName')}")
            print(f"🆔 Folder ID: {created_folder.get('id')}")
        
        else:
            print(f"❌ Unknown command: {command}")
            print_help()
            sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nRun tools/test_credentials.py to verify your setup")
        sys.exit(1)


if __name__ == "__main__":
    main() 