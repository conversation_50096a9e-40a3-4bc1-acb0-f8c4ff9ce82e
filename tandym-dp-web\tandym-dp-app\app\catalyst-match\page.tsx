"use client";
import CatalystMatchForm from "@/components/CatalystMatchForm";

export default function CatalystMatch() {
  return (
    <div className="w-full px-3 sm:px-4 lg:px-6 py-3 sm:py-4 space-y-3 sm:space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="min-w-0 flex-1">
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight text-gray-900">CatalystMatch</h1>
          <p className="text-xs sm:text-sm text-muted-foreground mt-0.5 sm:mt-1">
            Configure and submit candidate requirements for intelligent talent matching
          </p>
        </div>
      </div>
      <CatalystMatchForm />
    </div>
  );
} 