import os
import json
import glob
import csv
import shutil
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
from common.db.postgres_connector import PostgresConnector, PostgresEnvironment
from common.appLogger import AppLogger, getGlobalAppLogger
from common.db.global_dbconnector import GlobalDBConnector
from common.secrets_env import load_secrets_env_variables
#
# We get the count of tagged candidates per subcategory per high/medium/low
# Once we move to database, this will become a query to generate for the UI
# Till we move to the database run it weekly/Daily.
#

"""
create a CSVfile that shows subcategory count per score.
"""

def get_json_files(directory):
    """
    Get a list of all JSON files in the specified directory.
    
    Parameters:
        directory (str): Path to the directory containing JSON files.
    
    Returns:
        list: List of JSON file paths.
    """
    pattern = os.path.join(directory, "*.json")
    return glob.glob(pattern)

def process_json_file(filepath):
    """
    Process a single JSON file to extract subcategory and score pairs.
    
    Each JSON file represents a candidate. The file is expected to have the keys:
        - "sub_cats file": a list of subcategories.
        - "score": a list of scores (as strings) corresponding to each subcategory.
    
    Parameters:
        filepath (str): The path to the JSON file.
    
    Returns:
        list of tuple: A list of (subcategory, score) pairs. Score is returned as an integer.
    """
    pairs = []
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return pairs

    subcats = data.get("sub_cats file")
    scores = data.get("score")

    # Check that both keys exist and are lists.
    if not (isinstance(subcats, list) and isinstance(scores, list)):
        return pairs

    # Ensure that the number of subcategories matches the number of scores.
    if len(subcats) != len(scores):
        print(f"Length mismatch in file {filepath}: {len(subcats)} subcategories vs {len(scores)} scores.")
        return pairs

    # Extract valid (subcategory, score) pairs.
    for cat, score_str in zip(subcats, scores):
        try:
            score = int(score_str)
        except ValueError:
            print(f"Invalid score value '{score_str}' in file {filepath}. Skipping this pair.")
            continue
        # Only consider scores between 1 and 10.
        if 1 <= score <= 10:
            pairs.append((cat, score))
        else:
            print(f"Score out of valid range (1-10): {score} in file {filepath}")
    return pairs

def aggregate_counts(file_list):
    """
    Process a list of JSON files and aggregate counts for each subcategory and score.
    
    Parameters:
        file_list (list): List of JSON file paths.
    
    Returns:
        dict: A nested dictionary where counts[subcategory][score] gives the count.
    """
    counts = defaultdict(lambda: defaultdict(int))
    for filepath in file_list:
        pairs = process_json_file(filepath)
        for cat, score in pairs:
            counts[cat][score] += 1
    return counts

def aggregate_counts_from_db(db_connector: PostgresConnector, logger: AppLogger) -> Dict[str, Dict[int, int]]:
    """
    Identical to aggregate_counts but reads from database instead of files.
    Returns a nested dictionary where counts[subcategory][score] gives the count.
    """
    try:
        conn = db_connector.connection
        cur = conn.cursor()
        schema = db_connector.schema

        # Query to get subcategory scores from the processed data table
        query = f"""
            SELECT 
                s.name as subcategory,
                cs.score
            FROM {schema}.candidate_subcategories cs
            JOIN {schema}.subcategories s ON cs.subcategory_id = s.id
            WHERE cs.score IS NOT NULL 
        """

        cur.execute(query)
        results = cur.fetchall()
        cur.close()

        # Initialize the counts dictionary - exactly like in aggregate_counts
        counts = defaultdict(lambda: defaultdict(int))

        # Populate the counts dictionary - exactly like in aggregate_counts
        for row in results:
            subcategory = row[0]
            score = int(row[1])  # Convert to int like in process_json_file
            counts[subcategory][score] += 1

        return counts

    except Exception as e:
        logger.error(f"Error getting subcategory counts from database: {e}")
        return defaultdict(lambda: defaultdict(int))

def write_counts_to_csv(counts, output_csv):
    """
    Write the aggregated counts to a CSV file.
    
    The CSV will have a header row with "subcategory" followed by score columns 1-10.
    Each row corresponds to a subcategory.
    
    Parameters:
        counts (dict): Nested dictionary with counts[subcategory][score].
        output_csv (str): Path to the output CSV file.
    """
    with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
        # Define header: first column "subcategory" then columns "1" through "10"
        fieldnames = ["subcategory"] + [str(s) for s in range(1, 11)]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # Write each subcategory row.
        for subcat in sorted(counts.keys()):
            row = {"subcategory": subcat}
            for score in range(1, 11):
                row[str(score)] = counts[subcat].get(score, 0)
            writer.writerow(row)
    print(f"CSV output saved to {output_csv}")

def aggregate_bucket_counts(counts):
    """
    Convert detailed score counts into bucket counts.
    
    Buckets are defined as:
        - High: scores 1, 2, 3, 4
        - Medium: scores 5, 6, 7, 8
        - Low: scores 9, 10
    
    Parameters:
        counts (dict): Nested dictionary with counts[subcategory][score].
    
    Returns:
        dict: A dictionary where each key is a subcategory and the value is another
              dictionary with keys 'high', 'medium', and 'low'.
    """
    bucket_counts = {}
    for subcat, score_counts in counts.items():
        high = sum(score_counts.get(s, 0) for s in [1, 2, 3, 4, 6, 7])
        medium = sum(score_counts.get(s, 0) for s in [15])
        low = sum(score_counts.get(s, 0) for s in [16, 17])
        other = sum(score_counts.get(s, 0) for s in [8, 9, 10, 11, 12, 13, 14])
        bucket_counts[subcat] = {"high": high, "medium": medium, "low": low}
    return bucket_counts

def write_bucket_counts_to_csv(bucket_counts, output_csv):
    """
    Write the bucket counts to a CSV file.
    
    The CSV will have a header row with "subcategory", "high", "medium", and "low".
    Each row corresponds to a subcategory.
    
    Parameters:
        bucket_counts (dict): Dictionary with bucket counts per subcategory.
        output_csv (str): Path to the output CSV file.
    """
    with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["subcategory", "high", "medium", "low"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for subcat in sorted(bucket_counts.keys()):
            row = {"subcategory": subcat}
            row.update(bucket_counts[subcat])
            writer.writerow(row)
    print(f"Bucket CSV output saved to {output_csv}")

def main():
    load_secrets_env_variables()
    
    # Update this to the directory containing your JSON files.
    directory = "/mnt/incoming/classify-prod/"
    
    # Update this to your desired CSV output file path.
    output_csv = "/mnt/incoming/results/subcategory_pools.csv"
    output_csv_hml = "/mnt/incoming/results/subcategory_pools_HML.csv"

    # Get the list of JSON files.
    file_list = get_json_files(directory)
    
    # Aggregate counts from the JSON files.
    counts = aggregate_counts(file_list)
    
    # Write the aggregated counts to CSV.
    write_counts_to_csv(counts, output_csv)
    bucket_counts = aggregate_bucket_counts(counts)
    write_bucket_counts_to_csv(bucket_counts, output_csv_hml)

    # Aggregate counts from the database
    dboutput_csv = "/mnt/incoming/temp/subcategory_pools_db.csv"
    dboutput_csv_hml = "/mnt/incoming/temp/subcategory_pools_HML_db.csv"

    logger = getGlobalAppLogger()
    db_connector = GlobalDBConnector.get_connector(PostgresEnvironment.PROD, logger)
    db_connector.connect()
    dbcounts = aggregate_counts_from_db(db_connector, logger)
    write_counts_to_csv(dbcounts, dboutput_csv)
    dbbucket_counts = aggregate_bucket_counts(dbcounts)
    write_bucket_counts_to_csv(dbbucket_counts, dboutput_csv_hml)

if __name__ == "__main__":
    main() 
