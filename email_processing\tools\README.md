# Email Processing Tools

This directory contains **one-time utilities** and **management tools** that are separate from the main email fetching functionality.

## 🎯 **Purpose**

These tools are designed for:
- One-time setup and testing
- Manual folder management operations
- Administrative tasks
- Advanced folder operations

They are **NOT** part of the main production email processing workflow.

## 📁 **Available Tools**

### 1. **fetch_folder_emails.py** - Fetch Emails from Specific Folder
```bash
cd tools
python3 fetch_folder_emails.py 'Invalid Attachment'
python3 fetch_folder_emails.py 'ContactId_Queue' --count 20
python3 fetch_folder_emails.py Inbox --sender '<EMAIL>' --count 5
```
**Purpose**: Fetch emails from a specific mail folder by name or ID
- Supports folder names (e.g., "Invalid Attachment") or folder IDs
- Sender filtering capabilities  
- Shows detailed email information including Email IDs
- Displays folder statistics
- Helpful error messages with folder suggestions

### 2. **test_credentials.py** - Credential Testing
```bash
cd tools
python3 test_credentials.py
```
**Purpose**: Test if Azure credentials are properly loaded and working
- Checks environment variables
- Tests API connection
- Validates access token generation

### 3. **list_folders.py** - Simple Folder Listing
```bash
cd tools
python3 list_folders.py
```
**Purpose**: List all mail folders and their IDs
- Shows folder names and IDs
- Displays item counts
- Simple, easy-to-read format

### 4. **create_folder.py** - Basic Folder Creation
```bash
cd tools
python3 create_folder.py "My New Folder"
python3 create_folder.py "Archive" "AQMkAGE3NwA5YTM4..."  # With parent
```
**Purpose**: Create new mail folders
- Creates folders at root or under parent
- Prevents duplicate folders
- Simple command-line interface

### 5. **folder_manager.py** - Advanced Folder Management
```bash
cd tools
python3 folder_manager.py list      # List folders
python3 folder_manager.py tree      # Tree view
python3 folder_manager.py export    # Export to JSON
python3 folder_manager.py create "Folder Name"
```
**Purpose**: Comprehensive folder management
- Multiple viewing formats (list, tree)
- Export folder structure to JSON
- Advanced folder operations
- Professional folder management

## 🚀 **Usage Patterns**

### **First-Time Setup**
```bash
cd tools
python3 test_credentials.py         # Verify setup
python3 list_folders.py             # See available folders
```

### **Folder Management**
```bash
cd tools
python3 folder_manager.py tree      # See folder structure
python3 create_folder.py "Processed Resumes"
python3 folder_manager.py export    # Backup folder structure
```

### **Getting Folder IDs**
```bash
cd tools
python3 list_folders.py             # Simple list with IDs
# Copy folder ID for use with main email fetcher
cd ..
python3 main.py --folder-id "AQMkAGE3NwA5YTM4..."
```

## 📋 **Tool Comparison**

| Tool | Purpose | Use Case |
|------|---------|----------|
| **test_credentials.py** | Credential testing | First-time setup, troubleshooting |
| **list_folders.py** | Simple listing | Quick folder ID lookup |
| **create_folder.py** | Basic creation | Simple folder creation needs |
| **folder_manager.py** | Advanced operations | Complex folder management |

## 🔧 **How Tools Work**

1. **Independent Operation**: Each tool can run independently
2. **Same Credentials**: Uses same Azure credentials as main app
3. **Same Logger**: Logs to same file (`/mnt/incoming/logs/fetch_email.log`)
4. **Import Path**: Automatically imports parent modules
5. **Error Handling**: Consistent error messages and troubleshooting

## 📝 **Tool Output Examples**

### test_credentials.py
```
🧪 TESTING AZURE CREDENTIALS
==================================================

1. Checking Environment Variables:
   ✅ MAILBOX_RESUMES_CLIENT_ID: a1b2c3d4...
   ✅ MAILBOX_RESUMES_CLIENT_SECRET: [SET - 32 chars]
   ✅ MAILBOX_RESUMES_TENANT_ID: e5f6g7h8...

2. Testing Azure API Connection:
   ✅ Successfully obtained access token (1456 chars)
   ✅ Azure API connection working

✅ ALL TESTS PASSED!
```

### list_folders.py
```
📁 ALL <NAME_EMAIL>
================================================================================

 1. 📂 Inbox
    🆔 ID: AQMkAGE3NwA5YTM4NC00MGIwLTRjODktOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBDAAAAA==
    📊 Items: 45 total, 12 unread

 2. 📂 Sent Items
    🆔 ID: AQMkAGE3NwA5YTM4NC00MGIwLTRjODktOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBCAAAAA==
    📊 Items: 23 total, 0 unread
```

### folder_manager.py tree
```
📁 FOLDER TREE - <EMAIL>
================================================================================
📂 Inbox (45 items, 12 unread)
   🆔 AQMkAGE3NwA5YTM4NC00MGIwLTRjODktOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBDAAAAA==
  └─📁 Archive (15 items, 0 unread)
     🆔 AQMkAGE3NwA5YTM4NC00MGIwLTRqOGItOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBFAAAAA==

📂 Sent Items (23 items, 0 unread)
   🆔 AQMkAGE3NwA5YTM4NC00MGIwLTRjODktOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBCAAAAA==
```

## 💡 **Tips**

1. **Always test credentials first** before using other tools
2. **Use list_folders.py** for quick folder ID lookup
3. **Use folder_manager.py** for complex operations
4. **Export folder structure** before making major changes
5. **Check main app help** for folder ID usage: `python3 ../main.py --help`

## 🔗 **Integration with Main App**

After using tools to get folder IDs, use them with the main email fetcher:

```bash
# Use tools to get folder ID
cd tools
python3 list_folders.py

# Use folder ID with main app
cd ..
python3 main.py --folder-id "AQMkAGE3NwA5YTM4..."
```

## 🛠️ **Troubleshooting**

If any tool fails:
1. Run `python3 test_credentials.py` first
2. Check Azure credentials in GPG secrets
3. Verify Azure App Registration permissions
4. Check network connectivity

All tools use the same troubleshooting patterns as the main application. 