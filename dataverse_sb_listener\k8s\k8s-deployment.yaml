apiVersion: v1
kind: ConfigMap
metadata:
  name: dataverse-listener-config
  labels:
    app: dataverse-listener
data:
  # Environment-specific configurations
  ENVIRONMENT: "SANDBOX"  # Change to PROD for production
  LOG_LEVEL: "INFO"
  HEALTH_CHECK_PORT: "8080"
  
  # Processor configurations (JSON string)
  # Uses queues for better reliability and simpler configuration
  PROCESSOR_CONFIGS: |
    [
      {
        "type": "contact",
        "enabled": true,
        "channel_type": "queue",
        "channel_name": "contact-updates",
        "max_workers": 4,
        "max_retries": 3,
        "retry_delay": 5.0,
        "priority": 0
      },
      {
        "type": "crimson_vacancy",
        "enabled": true,
        "channel_type": "queue",
        "channel_name": "vacancy-updates",
        "max_workers": 4,
        "max_retries": 3,
        "retry_delay": 5.0,
        "priority": 0
      }
    ]

---
apiVersion: v1
kind: Secret
metadata:
  name: dataverse-listener-secrets
  labels:
    app: dataverse-listener
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  SERVICE_BUS_CONNECTION_STRING: <base64-encoded-connection-string>
  DATAVERSE_TENANT_ID: <DATAVERSE_TENANT_ID>
  DATAVERSE_CLIENT_ID: <DATAVERSE_CLIENT_ID>
  DATAVERSE_CLIENT_SECRET: <DATAVERSE_CLIENT_SECRET>
  DATAVERSE_RESOURCE_URL: https://<DATAVERSE_HOSTNAME>.crm.dynamics.com
  # Add other secrets as needed

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dataverse-listener
  labels:
    app: dataverse-listener
spec:
  replicas: 2  # Multiple replicas for high availability
  selector:
    matchLabels:
      app: dataverse-listener
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0  # Ensure no downtime during updates
  template:
    metadata:
      labels:
        app: dataverse-listener
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      # Pod disruption budget ensures availability
      terminationGracePeriodSeconds: 60  # Allow time for graceful shutdown
      
      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      
      containers:
      - name: dataverse-listener
        image: your-registry/dataverse-listener:latest
        command: ["python"]
        args: ["dataverse_sb_listener/main.py", "--config", "configs/default_config.json"]
        
        # Environment variables
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: dataverse-listener-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: dataverse-listener-config
              key: LOG_LEVEL
        - name: HEALTH_CHECK_PORT
          valueFrom:
            configMapKeyRef:
              name: dataverse-listener-config
              key: HEALTH_CHECK_PORT
        - name: SERVICE_BUS_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: dataverse-listener-secrets
              key: SERVICE_BUS_CONNECTION_STRING
        - name: PROCESSOR_CONFIGS
          valueFrom:
            configMapKeyRef:
              name: dataverse-listener-config
              key: PROCESSOR_CONFIGS
        - name: MERCURY_EVENTS_STORAGE_ENABLED
          value: "true"
        - name: PYTHONPATH
          value: "/app"
        
        # Port configuration
        ports:
        - name: health-check
          containerPort: 8080
          protocol: TCP
        
        # Resource limits and requests
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /live
            port: 8080
            httpHeaders:
            - name: Accept
              value: application/json
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
            httpHeaders:
            - name: Accept
              value: application/json
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /startup
            port: 8080
            httpHeaders:
            - name: Accept
              value: application/json
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30  # Allow up to 5 minutes for startup
          successThreshold: 1
        
        # Volume mounts for logs and secrets
        volumeMounts:
        - name: logs-volume
          mountPath: /mnt/incoming/logs
        - name: secrets-volume
          mountPath: /etc/dev_secrets.env.gpg
          subPath: dev_secrets.env.gpg
          readOnly: true
        
        # Lifecycle hooks for graceful shutdown
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 10"]  # Give time for graceful shutdown
        
        # Security context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false  # Need to write logs
          capabilities:
            drop:
            - ALL
      
      # Volumes
      volumes:
      - name: logs-volume
        emptyDir: {}  # Use persistent volume in production
      - name: secrets-volume
        secret:
          secretName: dataverse-listener-secrets
          items:
          - key: dev_secrets.env.gpg
            path: dev_secrets.env.gpg

---
apiVersion: v1
kind: Service
metadata:
  name: dataverse-listener-service
  labels:
    app: dataverse-listener
spec:
  type: ClusterIP
  ports:
  - name: health-check
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: dataverse-listener

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: dataverse-listener-pdb
spec:
  minAvailable: 1  # Ensure at least 1 pod is always available
  selector:
    matchLabels:
      app: dataverse-listener

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: dataverse-listener-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: dataverse-listener
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60  # 1 minute
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30

---
# Example: Ingress for external health check access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dataverse-listener-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: dataverse-listener.your-domain.com
    http:
      paths:
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: dataverse-listener-service
            port:
              number: 8080
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: dataverse-listener-service
            port:
              number: 8080 