import os
import sys
from behave.__main__ import main as behave_main

# Force immediate flushing of stdout
sys.stdout.reconfigure(line_buffering=True)

# Add project root to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

if __name__ == "__main__":
    # Navigate to the project root where "features" folder exists
    os.chdir(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

    # ✅ Run multiple feature files
    feature_file = [
        "features/02_homePage.feature"
    ]

    # Run behave with selected features and no capture
    behave_main(["-f", "plain", "--no-capture", *feature_file])