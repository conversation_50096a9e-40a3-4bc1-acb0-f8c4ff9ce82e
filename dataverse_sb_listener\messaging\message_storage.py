#!/usr/bin/env python3
"""
Message storage utility for Service Bus messages.

This module provides functionality to store Service Bus messages to files
for replay and testing purposes. Messages are organized by entity type
and include metadata for identification.
"""

import sys
import os
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from pathlib import Path
import uuid

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger

class MessageStorage:
    """
    Utility class for storing Service Bus messages to files for replay purposes.
    
    Messages are stored in a configurable directory structure:
    /base_directory/
        /contact/
            /YYYY-MM-DD/
                message_id_entity_id_timestamp.json
        /crimson_vacancy/
            /YYYY-MM-DD/
                message_id_entity_id_timestamp.json
    """
    
    def __init__(self, 
                 base_directory: Optional[str] = None,
                 logger: Optional[AppLogger] = None,
                 enabled: bool = True):
        """
        Initialize the message storage utility.
        
        Args:
            base_directory: Base directory for storing messages (defaults to /mnt/incoming/mercury_events)
            logger: Logger instance
            enabled: Whether message storage is enabled
        """
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        self.enabled = enabled
        
        # Set base directory from environment variable or default
        if base_directory:
            self.base_directory = Path(base_directory)
        else:
            self.base_directory = Path(os.getenv('MERCURY_EVENTS_DIR', '/mnt/incoming/mercury_events'))
        
        # Check if message storage is enabled via environment variable
        env_enabled = os.getenv('MERCURY_EVENTS_STORAGE_ENABLED', 'true').lower()
        if env_enabled in ('false', '0', 'no', 'disabled'):
            self.enabled = False
            self.logger.info("Message storage is disabled via environment variable")
            return
        
        if not self.enabled:
            self.logger.info("Message storage is disabled")
            return
            
        # Create base directory if it doesn't exist
        try:
            self.base_directory.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Message storage initialized at: {self.base_directory}")
        except Exception as e:
            self.logger.error(f"Failed to create message storage directory {self.base_directory}: {e}")
            self.enabled = False
    
    def store_message(self, 
                     msg, 
                     entity_info: Dict[str, Any], 
                     raw_message_data: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Store a Service Bus message to file.
        
        Args:
            msg: Service Bus message object
            entity_info: Parsed entity information
            raw_message_data: Raw message data (optional, will be extracted if not provided)
            
        Returns:
            Path to stored file or None if storage failed
        """
        if not self.enabled:
            return None
            
        try:
            # Extract message metadata
            message_id = getattr(msg, 'message_id', str(uuid.uuid4()))
            entity_name = entity_info.get('entity_name', 'unknown')
            entity_id = entity_info.get('entity_id', 'unknown')
            
            # Get current timestamp
            timestamp = datetime.now(timezone.utc)
            date_str = timestamp.strftime('%Y-%m-%d')
            timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S_%f')[:-3]  # Include milliseconds
            
            # Create entity-specific directory
            entity_dir = self.base_directory / entity_name / date_str
            entity_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            filename = f"{message_id}_{entity_id}_{timestamp_str}.json"
            file_path = entity_dir / filename
            
            # Prepare message data for storage
            message_data = {
                "metadata": {
                    "message_id": message_id,
                    "entity_name": entity_name,
                    "entity_id": entity_id,
                    "stored_at": timestamp.isoformat(),
                    "original_timestamp": timestamp_str,
                    "file_path": str(file_path)
                },
                "entity_info": entity_info,
                "raw_message": raw_message_data or self._extract_raw_message(msg)
            }
            
            # Write to file with better error handling
            def make_entire_message_safe(obj):
                """Recursively make all objects JSON serializable"""
                if isinstance(obj, dict):
                    return {str(k): make_entire_message_safe(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return [make_entire_message_safe(item) for item in obj]
                elif isinstance(obj, (str, int, float, bool, type(None))):
                    return obj
                else:
                    return str(obj)
            
            # Make the entire message data safe for JSON serialization
            safe_message_data = make_entire_message_safe(message_data)
            
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(safe_message_data, f, indent=2, ensure_ascii=False)
                
                # Verify the file was written correctly
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)  # This will raise an error if JSON is invalid
                    
            except (json.JSONDecodeError, Exception) as e:
                self.logger.error(f"Failed to write valid JSON to {file_path}: {e}")
                # Try to write a simplified version
                try:
                    simplified_data = {
                        "metadata": safe_message_data.get("metadata", {}),
                        "entity_info": safe_message_data.get("entity_info", {}),
                        "raw_message": {
                            "message_id": safe_message_data.get("raw_message", {}).get("message_id"),
                            "correlation_id": safe_message_data.get("raw_message", {}).get("correlation_id"),
                            "body": safe_message_data.get("raw_message", {}).get("body"),
                            "error": "Original message data could not be serialized"
                        }
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(simplified_data, f, indent=2, ensure_ascii=False)
                    self.logger.warning(f"Wrote simplified message data to {file_path}")
                except Exception as e2:
                    self.logger.error(f"Failed to write even simplified data: {e2}")
                    return None
            
            self.logger.info(f"Stored message {message_id} for {entity_name} ({entity_id}) to {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Failed to store message: {e}")
            return None
    
    def _extract_raw_message(self, msg) -> Dict[str, Any]:
        """
        Extract raw message data from Service Bus message.
        
        Args:
            msg: Service Bus message object
            
        Returns:
            Dictionary containing raw message data
        """
        try:
            # Safely extract application_properties
            app_props = getattr(msg, 'application_properties', {})
            
            # Helper function to make objects JSON serializable
            def make_json_safe(obj):
                if isinstance(obj, dict):
                    return {make_json_safe_key(k): make_json_safe(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return [make_json_safe(item) for item in obj]
                elif isinstance(obj, bytes):
                    return make_json_safe_key(obj)  # Use the same function for values
                elif isinstance(obj, (str, int, float, bool, type(None))):
                    return obj
                else:
                    return str(obj)
            
            def make_json_safe_key(key):
                """Convert a key to a JSON-safe string, handling bytes properly."""
                if isinstance(key, bytes):
                    try:
                        return key.decode('utf-8')
                    except UnicodeDecodeError:
                        # Try other encodings if UTF-8 fails
                        for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                            try:
                                return key.decode(encoding)
                            except UnicodeDecodeError:
                                continue
                        # If all decodings fail, use string representation
                        return str(key)
                else:
                    return str(key)
            
            # Convert application properties to be JSON safe
            safe_app_props = make_json_safe(app_props)
            
            # Verify it can be serialized
            try:
                json.dumps(safe_app_props, default=str)
            except (TypeError, ValueError) as e:
                # If it still can't be serialized, convert to string representation
                safe_app_props = str(safe_app_props)
                self.logger.warning(f"Application properties could not be serialized even after conversion, converting to string: {app_props}, error: {e}")
            
            raw_data = {
                "message_id": getattr(msg, 'message_id', None),
                "correlation_id": getattr(msg, 'correlation_id', None),
                "session_id": getattr(msg, 'session_id', None),
                "reply_to": getattr(msg, 'reply_to', None),
                "reply_to_session_id": getattr(msg, 'reply_to_session_id', None),
                "to": getattr(msg, 'to', None),
                "content_type": getattr(msg, 'content_type', None),
                "subject": getattr(msg, 'subject', None),
                "application_properties": safe_app_props,
                "body": None
            }
            
            # Extract message body
            if hasattr(msg, "body"):
                if isinstance(msg.body, bytes):
                    try:
                        body_str = msg.body.decode('utf-8')
                        raw_data["body"] = body_str
                    except UnicodeDecodeError:
                        # Try other encodings
                        for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                            try:
                                body_str = msg.body.decode(encoding)
                                raw_data["body"] = body_str
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            raw_data["body"] = str(msg.body)
                elif hasattr(msg.body, '__iter__'):
                    body_bytes = b"".join(msg.body)
                    try:
                        body_str = body_bytes.decode('utf-8')
                        raw_data["body"] = body_str
                    except UnicodeDecodeError:
                        raw_data["body"] = str(body_bytes)
                else:
                    raw_data["body"] = str(msg.body)
            
            return raw_data
            
        except Exception as e:
            self.logger.error(f"Failed to extract raw message data: {e}")
            return {"error": f"Failed to extract message data: {e}"}
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get statistics about stored messages.
        
        Returns:
            Dictionary containing storage statistics
        """
        if not self.enabled or not self.base_directory.exists():
            return {"enabled": False, "error": "Storage not available"}
        
        try:
            stats = {
                "enabled": True,
                "base_directory": str(self.base_directory),
                "total_files": 0,
                "entity_counts": {},
                "date_counts": {},
                "total_size_bytes": 0
            }
            
            # Count files by entity and date
            for entity_dir in self.base_directory.iterdir():
                if entity_dir.is_dir():
                    entity_name = entity_dir.name
                    entity_count = 0
                    
                    for date_dir in entity_dir.iterdir():
                        if date_dir.is_dir():
                            date_str = date_dir.name
                            file_count = len(list(date_dir.glob("*.json")))
                            
                            entity_count += file_count
                            stats["total_files"] += file_count
                            
                            # Count by date
                            if date_str not in stats["date_counts"]:
                                stats["date_counts"][date_str] = 0
                            stats["date_counts"][date_str] += file_count
                            
                            # Calculate total size
                            for file_path in date_dir.glob("*.json"):
                                try:
                                    stats["total_size_bytes"] += file_path.stat().st_size
                                except OSError:
                                    pass
                    
                    stats["entity_counts"][entity_name] = entity_count
            
            return stats
            
        except Exception as e:
            return {"enabled": True, "error": f"Failed to get stats: {e}"}
    
    def cleanup_old_messages(self, days_to_keep: int = 30) -> Dict[str, Any]:
        """
        Clean up old message files.
        
        Args:
            days_to_keep: Number of days to keep messages
            
        Returns:
            Dictionary containing cleanup results
        """
        if not self.enabled:
            return {"enabled": False, "message": "Storage not enabled"}
        
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            deleted_files = 0
            deleted_size = 0
            
            for entity_dir in self.base_directory.iterdir():
                if entity_dir.is_dir():
                    for date_dir in entity_dir.iterdir():
                        if date_dir.is_dir():
                            try:
                                # Parse date from directory name
                                dir_date = datetime.strptime(date_dir.name, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                                
                                if dir_date < cutoff_date:
                                    # Delete all files in this directory
                                    for file_path in date_dir.glob("*.json"):
                                        try:
                                            file_size = file_path.stat().st_size
                                            file_path.unlink()
                                            deleted_files += 1
                                            deleted_size += file_size
                                        except OSError as e:
                                            self.logger.warning(f"Failed to delete {file_path}: {e}")
                                    
                                    # Try to remove the empty directory
                                    try:
                                        date_dir.rmdir()
                                    except OSError:
                                        pass  # Directory not empty or already deleted
                                        
                            except ValueError:
                                # Skip directories that don't match date format
                                continue
            
            self.logger.info(f"Cleanup completed: deleted {deleted_files} files ({deleted_size} bytes)")
            return {
                "enabled": True,
                "deleted_files": deleted_files,
                "deleted_size_bytes": deleted_size,
                "cutoff_date": cutoff_date.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
            return {"enabled": True, "error": f"Cleanup failed: {e}"}
    
    def list_messages(self, 
                     entity_name: Optional[str] = None, 
                     date: Optional[str] = None,
                     limit: int = 100) -> Dict[str, Any]:
        """
        List stored messages with optional filtering.
        
        Args:
            entity_name: Filter by entity name
            date: Filter by date (YYYY-MM-DD format)
            limit: Maximum number of messages to return
            
        Returns:
            Dictionary containing message list and metadata
        """
        if not self.enabled:
            return {"enabled": False, "error": "Storage not available"}
        
        try:
            messages = []
            count = 0
            
            # Determine which directories to scan
            if entity_name:
                entity_dirs = [self.base_directory / entity_name]
            else:
                entity_dirs = [d for d in self.base_directory.iterdir() if d.is_dir()]
            
            for entity_dir in entity_dirs:
                if not entity_dir.exists():
                    continue
                    
                # Determine which date directories to scan
                if date:
                    date_dirs = [entity_dir / date]
                else:
                    date_dirs = [d for d in entity_dir.iterdir() if d.is_dir()]
                
                for date_dir in date_dirs:
                    if not date_dir.exists():
                        continue
                        
                    for file_path in date_dir.glob("*.json"):
                        if count >= limit:
                            break
                            
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                message_data = json.load(f)
                            
                            messages.append({
                                "file_path": str(file_path),
                                "metadata": message_data.get("metadata", {}),
                                "entity_name": message_data.get("entity_info", {}).get("entity_name"),
                                "entity_id": message_data.get("entity_info", {}).get("entity_id")
                            })
                            count += 1
                            
                        except Exception as e:
                            self.logger.warning(f"Failed to read {file_path}: {e}")
            
            return {
                "enabled": True,
                "messages": messages,
                "total_returned": len(messages),
                "limit": limit
            }
            
        except Exception as e:
            return {"enabled": True, "error": f"Failed to list messages: {e}"} 