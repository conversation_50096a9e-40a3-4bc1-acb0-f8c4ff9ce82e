#!/usr/bin/env python3
"""
Test script for health server with enhanced per-entity metrics.

This script tests the health server endpoints to ensure they work correctly,
including the new per-entity metrics tracking functionality.
"""

import sys
import os
import time
import requests
import threading

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from dataverse_sb_listener.health import start_health_server, set_ready, set_healthy, update_message_processed, update_message_failed

def test_health_endpoints(base_url: str = "http://localhost:8080"):
    """Test all health endpoints."""
    endpoints = [
        ("/", "API Root"),
        ("/health", "Health Check"),
        ("/ready", "Readiness Probe"),
        ("/live", "Liveness Probe"),
        ("/startup", "Startup Probe"),
        ("/metrics", "Prometheus Metrics"),
        ("/docs", "Swagger Documentation"),
        ("/redoc", "ReDoc Documentation")
    ]
    
    print("Testing health endpoints...")
    print("=" * 50)
    
    for endpoint, description in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {description} ({endpoint}): {response.status_code}")
                if endpoint in ["/health", "/ready", "/live", "/startup"]:
                    print(f"   Response: {response.json()}")
            else:
                print(f"❌ {description} ({endpoint}): {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {description} ({endpoint}): Error - {e}")
    
    print("=" * 50)

def test_entity_metrics(base_url: str = "http://localhost:8080"):
    """Test the enhanced entity metrics functionality."""
    print("\nTesting entity metrics...")
    print("=" * 50)
    
    try:
        # Test health endpoint for entity metrics
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint with entity metrics:")
            print(f"   Total processed: {data.get('total_messages_processed', 0)}")
            print(f"   Total failed: {data.get('total_messages_failed', 0)}")
            print(f"   Overall success rate: {data.get('success_rate', 0):.1f}%")
            
            entity_metrics = data.get('entity_metrics', {})
            if entity_metrics:
                print("   Entity-specific metrics:")
                for entity, metrics in entity_metrics.items():
                    print(f"     {entity}:")
                    print(f"       - Processed: {metrics.get('processed', 0)}")
                    print(f"       - Failed: {metrics.get('failed', 0)}")
                    print(f"       - Success rate: {metrics.get('success_rate', 0):.1f}%")
                    print(f"       - Last processed: {metrics.get('last_processed', 'Never')}")
            else:
                print("   No entity metrics available yet")
        
        # Test metrics endpoint for Prometheus format
        response = requests.get(f"{base_url}/metrics", timeout=5)
        if response.status_code == 200:
            metrics_text = response.text
            print("\n✅ Prometheus metrics format:")
            
            # Check for entity-specific metrics
            if "listener_entity_messages_processed_total" in metrics_text:
                print("   Found entity-specific metrics in Prometheus format")
                # Extract and display entity metrics
                lines = metrics_text.split('\n')
                for line in lines:
                    if line.startswith('listener_entity_messages_processed_total{entity='):
                        print(f"     {line}")
                    elif line.startswith('listener_entity_messages_failed_total{entity='):
                        print(f"     {line}")
                    elif line.startswith('listener_entity_success_rate_percent{entity='):
                        print(f"     {line}")
            else:
                print("   No entity-specific metrics found in Prometheus format")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Error testing entity metrics: {e}")
    
    print("=" * 50)

def main():
    """Main test function."""
    print("Starting health server test with enhanced entity metrics...")
    
    # Start the health server
    print("Starting health server...")
    health_thread = start_health_server(port=8080)
    
    # Wait a moment for the server to start
    time.sleep(3)
    
    # Simulate some activity with different entity types
    print("Simulating activity with different entity types...")
    set_ready(True)
    set_healthy(True)
    
    # Simulate contact entity processing
    print("  - Processing contact messages...")
    update_message_processed("contact")
    update_message_processed("contact")
    update_message_processed("contact")
    update_message_failed("contact")
    
    # Simulate vacancy entity processing
    print("  - Processing vacancy messages...")
    update_message_processed("crimson_vacancy")
    update_message_processed("crimson_vacancy")
    update_message_failed("crimson_vacancy")
    
    # Simulate some generic processing (no entity specified)
    print("  - Processing generic messages...")
    update_message_processed()
    update_message_processed()
    
    # Test the endpoints
    test_health_endpoints()
    test_entity_metrics()
    
    print("\nTest completed!")
    print("You can also visit http://localhost:8080/docs to see the Swagger documentation.")
    print("Press Ctrl+C to stop the server.")
    
    try:
        # Keep the server running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...")

if __name__ == "__main__":
    main() 