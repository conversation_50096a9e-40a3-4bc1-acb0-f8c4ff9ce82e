import pytest
from unittest.mock import Mock
from dpserver.services.subcategory_pool_service import SubcategoryPoolService


@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    mock_db = Mock()
    mock_db.schema = "public"
    return mock_db

@pytest.fixture
def service(mock_logger, mock_db_connector):
    return SubcategoryPoolService(mock_logger, mock_db_connector)


def test_get_pool_data_success(service, mocker,mock_logger):
    # Arrange
    pool_data = [
        {"subcategory": "A", "subcategory_id": 1},
        {"subcategory": "B", "subcategory_id": 2}
    ]
    optimal_counts = {"A": 10, "B": 20}
    high_match_address = {1: 5, 2: 6}
    medium_match_address = {1: 2, 2: 3}
    high_match_avail = {1: 7, 2: 8}
    medium_match_avail = {1: 1, 2: 2}

    mocker.patch.object(service, "get_subcategory_pools", return_value=pool_data)
    mocker.patch.object(service, "get_optimal_counts", return_value=optimal_counts)
    mocker.patch.object(service, "high_match_count_candidates_with_address_by_subcategory", return_value=high_match_address)
    mocker.patch.object(service, "medium_match_count_candidates_with_address_by_subcategory", return_value=medium_match_address)
    mocker.patch.object(service, "high_match_count_candidates_with_availability_by_subcategory", return_value=high_match_avail)
    mocker.patch.object(service, "medium_match_count_candidates_with_availability_by_subcategory", return_value=medium_match_avail)

    # Act
    result = service.get_pool_data()

    # Assert
    assert result[0]["optimal_count"] == 10
    assert result[1]["optimal_count"] == 20
    assert result[0]["high_match_address_count"] == 5
    assert result[1]["high_match_address_count"] == 6
    assert result[0]["medium_match_address_count"] == 2
    assert result[1]["medium_match_address_count"] == 3
    assert result[0]["high_match_availibility_count"] == 7
    assert result[1]["high_match_availibility_count"] == 8
    assert result[0]["medium_match_availibility_count"] == 1
    assert result[1]["medium_match_availibility_count"] == 2
    mock_logger.error.assert_not_called()  # No errors should be logged

def test_get_pool_data_pool_data_error(service, mocker,mock_logger):
    # Arrange
    error_dict = {"error": "DB error"}
    mocker.patch.object(service, "get_subcategory_pools", return_value=error_dict)

    # Act
    result = service.get_pool_data()

    # Assert
    assert result == error_dict
    mock_logger.error.assert_not_called()    
    
def test_get_pool_data_optimal_counts_error(service, mocker,mock_logger):
    # Arrange
    pool_data = [{"subcategory": "A", "subcategory_id": 1}]
    error_dict = {"error": "Calculation error"}
    mocker.patch.object(service, "get_subcategory_pools", return_value=pool_data)
    mocker.patch.object(service, "get_optimal_counts", return_value=error_dict)
    mocker.patch.object(service, "high_match_count_candidates_with_address_by_subcategory", return_value={1: 5})
    mocker.patch.object(service, "medium_match_count_candidates_with_address_by_subcategory", return_value={1: 2})
    mocker.patch.object(service, "high_match_count_candidates_with_availability_by_subcategory", return_value={1: 7})
    mocker.patch.object(service, "medium_match_count_candidates_with_availability_by_subcategory", return_value={1: 1})

    # Act
    result = service.get_pool_data()

    # Assert
    assert result == error_dict    
    mock_logger.error.assert_not_called()    
