[{"elements": [{"keyword": "<PERSON><PERSON><PERSON>", "location": "features/01_recruiterLogin.feature:4", "name": "Access the portal with a valid regression key", "status": "passed", "steps": [{"keyword": "When", "location": "features/01_recruiterLogin.feature:5", "match": {"arguments": [], "location": "features/steps/recruiterLogin_steps.py:13"}, "name": "user generates a unique UUID as the regression key", "result": {"duration": 0.004434347152709961, "status": "passed"}, "step_type": "when"}, {"keyword": "And", "location": "features/01_recruiterLogin.feature:6", "match": {"arguments": [], "location": "features/steps/recruiterLogin_steps.py:17"}, "name": "encrypts the key using the cryptographic library", "result": {"duration": 0.03549504280090332, "status": "passed"}, "step_type": "when"}, {"keyword": "And", "location": "features/01_recruiterLogin.feature:7", "match": {"arguments": [], "location": "features/steps/recruiterLogin_steps.py:21"}, "name": "inserts the encrypted key into the database with a 30-minute expiry and is_key_allowed set to true", "result": {"duration": 1.1063892841339111, "status": "passed"}, "step_type": "when"}], "tags": ["tc:4400", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/01_recruiterLogin.feature:10", "name": "Access the portal with the regression key", "status": "passed", "steps": [{"keyword": "When", "location": "features/01_recruiterLogin.feature:11", "match": {"arguments": [], "location": "features/steps/recruiterLogin_steps.py:25"}, "name": "opens the recruiter portal URL with the regression_key as a query parameter", "result": {"duration": 3.2543134689331055, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/01_recruiterLogin.feature:12", "match": {"arguments": [], "location": "features/steps/recruiterLogin_steps.py:29"}, "name": "the user should be navigated to the portal without AD authentication", "result": {"duration": 0.04189133644104004, "status": "passed"}, "step_type": "then"}], "tags": ["automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}], "keyword": "Feature", "location": "features/01_recruiterLogin.feature:1", "name": "Validate recruiter login functionality of homePage", "status": "passed", "tags": []}, {"elements": [{"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:5", "name": "Validate the UI of HomePage", "status": "passed", "steps": [{"keyword": "Given", "location": "features/02_homePage.feature:6", "match": {"arguments": [], "location": "features/steps/homePage_steps.py:5"}, "name": "user is on the HomePage of RecruiterPortal", "result": {"duration": 0.04434537887573242, "status": "passed"}, "step_type": "given"}, {"keyword": "Then", "location": "features/02_homePage.feature:7", "match": {"arguments": [], "location": "features/steps/homePage_steps.py:9"}, "name": "user should see the welcome message", "result": {"duration": 0.027945756912231445, "status": "passed"}, "step_type": "then"}, {"keyword": "And", "location": "features/02_homePage.feature:8", "match": {"arguments": [], "location": "features/steps/homePage_steps.py:13"}, "name": "verify user should see the message below the welcome message", "result": {"duration": 0.06365299224853516, "status": "passed"}, "step_type": "then"}, {"keyword": "And", "location": "features/02_homePage.feature:9", "match": {"arguments": [], "location": "features/steps/homePage_steps.py:17"}, "name": "verify user should see the About Recruitment Portal section", "result": {"duration": 0.04818463325500488, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4630", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:12", "name": "Validate the navigation of homepage from tabs to other pages", "status": "passed", "steps": [{"keyword": "When", "location": "features/02_homePage.feature:13", "match": {"arguments": [{"name": "button_name", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Sub-Category Library\"", "result": {"duration": 1.2184290885925293, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:14", "match": {"arguments": [{"name": "page_name", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Sub-Category Library\" page", "result": {"duration": 0.5589313507080078, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:15", "match": {"arguments": [{"name": "button_name", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Workforce Readiness Index\"", "result": {"duration": 0.14604544639587402, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:16", "match": {"arguments": [{"name": "page_name", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Workforce Readiness Index\" page", "result": {"duration": 0.04750466346740723, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:17", "match": {"arguments": [{"name": "button_name", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Vacancy\"", "result": {"duration": 0.13496685028076172, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:18", "match": {"arguments": [{"name": "page_name", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Vacancy\" page", "result": {"duration": 0.08724331855773926, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:19", "match": {"arguments": [{"name": "button_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Home\"", "result": {"duration": 0.1593470573425293, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:20", "match": {"arguments": [{"name": "page_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Home\" page", "result": {"duration": 0.5438318252563477, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4631", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:23", "name": "Validate the Sub-Category Library section of HomePPage", "status": "passed", "steps": [{"keyword": "Then", "location": "features/02_homePage.feature:24", "match": {"arguments": [{"name": "section", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:29"}, "name": "verify user should see the \"Sub-Category Library\" section in HomePage", "result": {"duration": 0.03058004379272461, "status": "passed"}, "step_type": "then"}, {"keyword": "And", "location": "features/02_homePage.feature:25", "match": {"arguments": [{"name": "section", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:33"}, "name": "verify user should see the \"Sub-Category Library\" section message", "result": {"duration": 0.041769981384277344, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:26", "match": {"arguments": [{"name": "section", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:37"}, "name": "user clicks on \"Sub-Category Library\" option", "result": {"duration": 0.6750915050506592, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:27", "match": {"arguments": [{"name": "page_name", "value": "Sub-Category Library"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Sub-Category Library\" page", "result": {"duration": 0.027845382690429688, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4632", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:30", "name": "Validate user back to HomePage", "status": "passed", "steps": [{"keyword": "When", "location": "features/02_homePage.feature:31", "match": {"arguments": [{"name": "button_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Home\"", "result": {"duration": 0.09097623825073242, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:32", "match": {"arguments": [{"name": "page_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Home\" page", "result": {"duration": 0.03394794464111328, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4633", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:35", "name": "Validate the Workforce Readiness Index section of HomePage", "status": "passed", "steps": [{"keyword": "Then", "location": "features/02_homePage.feature:36", "match": {"arguments": [{"name": "section", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:29"}, "name": "verify user should see the \"Workforce Readiness Index\" section in HomePage", "result": {"duration": 0.03739428520202637, "status": "passed"}, "step_type": "then"}, {"keyword": "And", "location": "features/02_homePage.feature:37", "match": {"arguments": [{"name": "section", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:33"}, "name": "verify user should see the \"Workforce Readiness Index\" section message", "result": {"duration": 0.04001045227050781, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:38", "match": {"arguments": [{"name": "section", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:37"}, "name": "user clicks on \"Workforce Readiness Index\" option", "result": {"duration": 1.235626220703125, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:39", "match": {"arguments": [{"name": "page_name", "value": "Workforce Readiness Index"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Workforce Readiness Index\" page", "result": {"duration": 0.040355682373046875, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4634", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:42", "name": "Validate user back to HomePage", "status": "passed", "steps": [{"keyword": "When", "location": "features/02_homePage.feature:43", "match": {"arguments": [{"name": "button_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Home\"", "result": {"duration": 0.16008424758911133, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:44", "match": {"arguments": [{"name": "page_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Home\" page", "result": {"duration": 0.05193829536437988, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4635", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:47", "name": "Validate the Vacancy section of HomePage", "status": "passed", "steps": [{"keyword": "Then", "location": "features/02_homePage.feature:48", "match": {"arguments": [{"name": "section", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:29"}, "name": "verify user should see the \"Vacancy\" section in HomePage", "result": {"duration": 0.049439191818237305, "status": "passed"}, "step_type": "then"}, {"keyword": "And", "location": "features/02_homePage.feature:49", "match": {"arguments": [{"name": "section", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:33"}, "name": "verify user should see the \"Vacancy\" section message", "result": {"duration": 0.05154299736022949, "status": "passed"}, "step_type": "then"}, {"keyword": "When", "location": "features/02_homePage.feature:50", "match": {"arguments": [{"name": "section", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:37"}, "name": "user clicks on \"Vacancy\" option", "result": {"duration": 1.283811330795288, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:51", "match": {"arguments": [{"name": "page_name", "value": "Vacancy"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Vacancy\" page", "result": {"duration": 0.04800987243652344, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4636", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/02_homePage.feature:54", "name": "Validate user back to HomePage", "status": "passed", "steps": [{"keyword": "When", "location": "features/02_homePage.feature:55", "match": {"arguments": [{"name": "button_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:21"}, "name": "user clicks on \"Home\"", "result": {"duration": 0.1933753490447998, "status": "passed"}, "step_type": "when"}, {"keyword": "Then", "location": "features/02_homePage.feature:56", "match": {"arguments": [{"name": "page_name", "value": "Home"}], "location": "features/steps/homePage_steps.py:25"}, "name": "user should be redirected to the \"Home\" page", "result": {"duration": 0.0602264404296875, "status": "passed"}, "step_type": "then"}], "tags": ["tc:4637", "automation", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}], "keyword": "Feature", "location": "features/02_homePage.feature:2", "name": "Validate HomePage of RecruiterPortal", "status": "passed", "tags": ["homepageFeature"]}, {"elements": [{"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:7", "name": "Validate that user cannot login with invalid credentials if user is not authenticated", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:8", "name": "user has the URL of recruiter Portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:9", "name": "user hits the recruiter portal URL and is not authenticated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:10", "name": "user should be redirected to the microsoft login page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:11", "name": "user enters an invalid tandym email id", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:12", "name": "clicks on Next button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:13", "name": "user should see an error message for invalid email or account", "step_type": "then"}], "tags": ["tc:4402", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:17", "name": "Validate that user cannot login with invalid password if user is not authenticated", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:18", "name": "user has the URL of recruiter Portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:19", "name": "user hits the recruiter portal URL and is not authenticated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:20", "name": "user should be redirected to the microsoft login page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:21", "name": "user enters a valid tandym email id", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:22", "name": "clicks on Next button", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:23", "name": "user enters an invalid password", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:24", "name": "clicks on the Sign in button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:25", "name": "user should see an error message for invalid password", "step_type": "then"}], "tags": ["tc:4403", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:29", "name": "Validate that user cannot login with invalid OTP in Microsoft Authenticator", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:30", "name": "user has the URL of recruiter Portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:31", "name": "user hits the recruiter portal URL and is not authenticated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:32", "name": "user should be redirected to the microsoft login page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:33", "name": "user enters a valid tandym email id", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:34", "name": "clicks on Next button", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:35", "name": "user enters a valid password", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:36", "name": "clicks on the Sign in button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:37", "name": "user should be redirected to the tandym OTP page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:38", "name": "user enters an invalid OTP in the mobile microsoft authenticator app", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:39", "name": "user should see an error message for invalid OTP and remain on the OTP page", "step_type": "then"}], "tags": ["tc:4404", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:43", "name": "Validate that user can login with valid credentials if user is not authenticated", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:44", "name": "user has the URL of recruiter Portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:45", "name": "user hits the recruiter portal URL and is not authenticated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:46", "name": "user should be redirected to the microsoft login page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:47", "name": "user enters a valid tandym email id", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:48", "name": "clicks on Next button", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:49", "name": "user enters a valid password", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:50", "name": "clicks on the Sign in button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:51", "name": "user should be redirected to the tandym OTP page", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:52", "name": "user enters the OTP that we are seeing on tandym OTP page to the mobile microsoft authenticator app", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:53", "name": "user should be landed on the recruiter portal home page", "step_type": "then"}], "tags": ["tc:4405", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:57", "name": "Validate that user directly lands on the recruiter portal home page if user is already authenticated", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:58", "name": "user has the URL of recruiter Portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:59", "name": "user hits the recruiter portal URL and is already authenticated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:60", "name": "user should be landed on the recruiter portal home page", "step_type": "then"}], "tags": ["tc:4406", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:64", "name": "Verify welcome message and recruiter name is displayed on the home page", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:65", "name": "the recruiter is logged in", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:66", "name": "the home page is displayed", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:67", "name": "the page should show the welcome message \"Welcome\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:68", "name": "the tagline \"Find the Right Fit, <PERSON>.\" should be visible", "step_type": "then"}], "tags": ["tc:4407", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:75", "name": "Validate that user can view the Vacancy page", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:76", "name": "the recruiter is on the home page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:77", "name": "the recruiter clicks on the \"Vacancy\" link in the top navigation bar", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:78", "name": "the user should be navigated to the Vacancy page", "step_type": "then"}], "tags": ["tc:4408", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:83", "name": "Display vacancy list on the Vacancy page", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:84", "name": "the recruiter is on the \"Vacancy\" page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:85", "name": "the \"Vacancy List\" should be visible on the left panel", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:86", "name": "each vacancy should display the vacancy code and assigned email", "step_type": "then"}], "tags": ["tc:4409", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:91", "name": "Search for a vacancy by vacancy code", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:92", "name": "the recruiter is on the \"Vacancy\" page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:93", "name": "the recruiter enters \"CR/505497\" in the search bar", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:94", "name": "only vacancies matching the code \"CR/505497\" should be displayed in the list", "step_type": "then"}], "tags": ["tc:4410", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:99", "name": "View candidates assigned to a specific vacancy", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:100", "name": "the recruiter is on the \"Vacancy\" page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:101", "name": "the recruiter selects the vacancy \"CR/505144\"", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:102", "name": "the system should display a list of candidates for \"CR/505144\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:103", "name": "each candidate row should show", "step_type": "then", "table": {"headings": ["Name"], "rows": [["Location"], ["Miles from worksite"], ["Availability Date"], ["AI Agent status"], ["Freshness Index"], ["Total Score"], ["Parsed Resume"], ["Rating [hide]"], ["Why Fit"], ["Shortlist"]]}}], "tags": ["tc:4411", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:119", "name": "Validate default pagination and max option", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:120", "name": "the recruiter is viewing the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:121", "name": "the pagination should default to 150", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:122", "name": "pagination options should include values up to 500", "step_type": "then"}], "tags": ["tc:4412", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:126", "name": "Sort candidate list by availability date", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:127", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:128", "name": "the recruiter clicks the header of the \"Availability Date\" column", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:129", "name": "the candidates should be sorted by availability date", "step_type": "then"}], "tags": ["tc:4413", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:133", "name": "Sort candidate list by location", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:134", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:135", "name": "the recruiter clicks the header of the \"Location\" column", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:136", "name": "the candidates should be sorted alphabetically by location", "step_type": "then"}], "tags": ["tc:4414", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:141", "name": "Sort candidate list by miles from worksite", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:142", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:143", "name": "the recruiter clicks the header of the \"Miles from Worksite\" column", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:144", "name": "the candidates should be sorted by distance in ascending order", "step_type": "then"}], "tags": ["tc:4415", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:149", "name": "Sort candidate list by freshness index", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:150", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:151", "name": "the recruiter clicks the header of the \"Freshness Index\" column", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:152", "name": "the candidates should be sorted by freshness index in ascending order", "step_type": "then"}], "tags": ["tc:4416", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:157", "name": "Sort candidate list by total score", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:158", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:159", "name": "the recruiter clicks the header of the \"Total Score\" column", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:160", "name": "the candidates should be sorted by total score in descending order", "step_type": "then"}], "tags": ["tc:4417", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:165", "name": "Verify other columns are not sortable", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:166", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:167", "name": "the recruiter hovers or clicks on other column headers", "step_type": "when", "table": {"headings": ["Name"], "rows": [["AI Agent status"], ["Parsed Resume"], ["rating"], ["Why - fit"], ["Shortlist"]]}}, {"keyword": "Then", "location": "features/manualScenarios.feature:174", "name": "no sort icons or actions should be available for above columns", "step_type": "then"}], "tags": ["tc:4418", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:179", "name": "Read-only Why <PERSON> for all vacancies", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:180", "name": "the recruiter is on the \"Vacancy\" page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:181", "name": "a recruiter selects any vacancy", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:182", "name": "the \"Why Fit\" section should be non-editable", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:183", "name": "an expand icon should allow viewing full content", "step_type": "then"}], "tags": ["tc:4419", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:188", "name": "<PERSON><PERSON> Why Fit comment to clipboard", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:189", "name": "the recruiter expands the \"Why Fit\" section", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:190", "name": "a \"Copy to Clipboard\" icon should be visible", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:191", "name": "the recruiter clicks it", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:192", "name": "the comment should be copied to clipboard", "step_type": "then"}], "tags": ["tc:4420", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:197", "name": "Attempt to copy <PERSON> Fit comment when section is empty", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:198", "name": "the recruiter expands the \"Why Fit\" section for a candidate with no comment", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:199", "name": "a \"Copy to Clipboard\" icon should be visible", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:200", "name": "the recruiter clicks it", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:201", "name": "user should get error message saying \"Nothing to copy.\"", "step_type": "then"}], "tags": ["tc:4421", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:206", "name": "<PERSON><PERSON> Thumbs Down candidates", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:207", "name": "the recruiter is on the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:208", "name": "the recruiter enables \"Hide Thumbs Down\" toggle", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:209", "name": "only candidates with Thumbs Up or no rating should be shown", "step_type": "then"}], "tags": ["tc:4422", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:214", "name": "View Thumbs Down candidates", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:215", "name": "the recruiter is on the candidate list with \"Hide Thumbs Down\" enabled", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:216", "name": "the recruiter disables the toggle", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:217", "name": "all candidates including <PERSON><PERSON><PERSON> <PERSON> should be visible", "step_type": "then"}], "tags": ["tc:4423", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:221", "name": "View resume for any candidate", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:222", "name": "the recruiter clicks the \"View Resume\" icon for a candidate", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:223", "name": "a resume popup should open", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:224", "name": "the parsed resume should highlight", "step_type": "then", "table": {"headings": ["High Matching Skills"], "rows": [["Medium Matching Skills"], ["Normal Matching Skills"]]}}, {"keyword": "And", "location": "features/manualScenarios.feature:228", "name": "full work experience should be visible", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:229", "name": "the header should include a \"Resume Link\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:230", "name": "there should be no download button", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:231", "name": "the recruiter closes the popup", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:232", "name": "the recruiter should return to the vacancy page", "step_type": "then"}], "tags": ["tc:4424", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:237", "name": "Display last updated date below rating", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:238", "name": "the recruiter is viewing the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:239", "name": "below each rating (Like/Dislike), the \"Last Updated Date\" should be shown", "step_type": "then"}], "tags": ["tc:4425", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:244", "name": "Validate column alignment", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:245", "name": "the recruiter is viewing the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:246", "name": "all column headers and content should be vertically and horizontally aligned", "step_type": "then"}], "tags": ["tc:4426", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:251", "name": "Display comment, eye icon, and toast message after like or dislike action", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:252", "name": "candidates are displayed for a vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:253", "name": "the recruiter likes or dislikes a candidate", "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:254", "name": "adds a comment while performing the like or dislike action", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:255", "name": "the action is saved", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:256", "name": "a toast message saying \"Review submitted successfully\" should be displayed", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:257", "name": "an eye icon should be displayed next to the thumbs up or thumbs down icon", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:258", "name": "when the recruiter clicks on the eye icon", "step_type": "then"}, {"keyword": "Then", "location": "features/manualScenarios.feature:259", "name": "the comment should be displayed", "step_type": "then"}], "tags": ["tc:4427", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:264", "name": "Validate success toast message when user clicks  on thum up icon", "status": "failed", "steps": [{"keyword": "When", "location": "features/manualScenarios.feature:265", "name": "user clicks on thums up icon", "result": {"duration": 0, "status": "undefined"}, "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:266", "name": "validate success toast message should be display as \"You've marked this candidate as a good fit.\"", "step_type": "then"}], "tags": ["tc:4428", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:271", "name": "Validate toast message when user clicks  on thum down icon", "status": "failed", "steps": [{"keyword": "When", "location": "features/manualScenarios.feature:272", "name": "user clicks on thums down icon", "result": {"duration": 0, "status": "undefined"}, "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:273", "name": "validate success toast message should be display as \"You've marked this candidate as not a good fit.\"", "step_type": "then"}], "tags": ["tc:4429", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:279", "name": "Display Vacancy Table When Catalyst Match API Status is Completed or error", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:280", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:281", "name": "the \"/vacancies/{vacancy_id}/catalystmatchstatus\" API returns status as \"completed\" or \"error\"", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:282", "name": "the vacancy table should be enabled for all actions (shortlisting, thumbs up/down, comments, etc.)", "step_type": "then"}], "tags": ["tc:4430", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:287", "name": "Disable Vacancy (Including Refresh button) Table When Catalyst Match API Status is In Process or Queued", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:288", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:289", "name": "the \"/vacancies/{vacancy_id}/catalystmatchstatus\" API returns status as \"inprocess\" or \"queued\"", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:290", "name": "the vacancy table should be read-only with existing data displayed", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:291", "name": "actions should be enabled only for Parsed resume, eye icon, why fit popup and candidate link", "step_type": "then"}], "tags": ["tc:4431", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:297", "name": "Always Display the Refresh <PERSON><PERSON>", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:298", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:299", "name": "the Refresh button should always be visible, regardless of Catalyst Match status or timestamps", "step_type": "then"}], "tags": ["tc:4432", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:304", "name": "Display Updates Available LABEL When New Data is Present", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:305", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:306", "name": "the Catalyst Match last updated timestamp is greater than the last data table load time", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:307", "name": "display the \"Updates Available\" label to the left of the Refresh button", "step_type": "then"}], "tags": ["tc:4433", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:314", "name": "Initiate Regeneration When Regeneration <PERSON><PERSON> is Clicked", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:315", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:316", "name": "the user clicks the Regeneration button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:317", "name": "the system should call the \"/vacancies/{vacancy_id}/regenerate-catalyst-match\" API", "step_type": "then"}], "tags": ["tc:4434", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:322", "name": "Disable All Vacancy Table Functionalities After Regeneration Starts", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:323", "name": "the user has initiated Regeneration and status is \"queued\" or \"inprocess\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:324", "name": "disable actions on the vacancy table including Shortlisting, Thumbs Up/Down and Refresh button", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:325", "name": "enable for thums up/down, why fit comment, eye icon and candidate link", "step_type": "then"}], "tags": ["tc:4435", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:332", "name": "Display Last Regeneration Timestamp when completed", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:333", "name": "the regeneration status is \"completed\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:334", "name": "display a label \"Generated at: mm/dd/yy hh:mm AM EST\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:335", "name": "show an info icon with tooltip showing the initiator (user or system)", "step_type": "then"}], "tags": ["tc:4436", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:339", "name": "Display Last Regeneration Timestamp when inprocess or queued", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:340", "name": "the regeneration status is \"inprocess\" or \"queued\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:341", "name": "display a label \"Generation started at: mm/dd/yy hh:mm AM EST\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:342", "name": "show an info icon with tooltip showing the initiator (user or system)", "step_type": "then"}], "tags": ["tc:4437", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:349", "name": "validate when status is Candidate_Successful_but_no_matches_found", "status": "failed", "steps": [{"keyword": "Then", "location": "features/manualScenarios.feature:350", "name": "display ---> Generated at: mm/dd/yy hh:mm AM EST", "result": {"duration": 0, "status": "undefined"}, "step_type": "then"}], "tags": ["tc:4438", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:354", "name": "Regeneration In Progress or queued Message", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:355", "name": "the status is \"queued\" or \"inprocess\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:356", "name": "display message: \"Regeneration in progress. (Catalyst match is read-only until complete)\"", "step_type": "then"}], "tags": ["tc:4439", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:360", "name": "If there is no candidate in any vacancy", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:361", "name": "we have vacancy but there is empty candidates", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:362", "name": "API status should be null", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:363", "name": "dipslay the mesage: Catalyst match not initiated yet", "step_type": "then"}], "tags": ["tc:4440", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:368", "name": "Error During Regeneration", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:369", "name": "the status is \"error\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:370", "name": "display message: \"Error during regeneration. Try again or if persists, contact support.\"", "step_type": "then"}], "tags": ["tc:4441", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:377", "name": "Show message when Catalyst Match is completed but no candidates are found", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:378", "name": "the \"/vacancies/{vacancy_id}/catalystmatchstatus\" API returns status as \"completed\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:379", "name": "the Candidates API returns no results", "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:380", "name": "display the message below the table: \"Matching successful, but no candidates found (Code: 100)\"", "step_type": "then"}], "tags": ["tc:4442", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:385", "name": "Show Candidates When Matching is Successful", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:386", "name": "the Catalyst Match API status is \"completed\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:387", "name": "the Candidates API returns candidate data", "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:388", "name": "display the candidate list without any additional messages", "step_type": "then"}], "tags": ["tc:4443", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:393", "name": "Matching Not Yet Started", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:394", "name": "the Catalyst Match API returns status as \"\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:395", "name": "Candidates API returns no results", "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:396", "name": "below the table display message: \"Matching yet to be done. Regenerate or contact support (Code: 102)\"", "step_type": "then"}], "tags": ["tc:4444", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:402", "name": "Validate Entitlement List Mode", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:403", "name": "the Entitlement sheet is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:404", "name": "User 1 is granted only \"Update Availability\" in the entitlement list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:405", "name": "User 1 should be able to see only the \"Update Availability\" functionality", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:406", "name": "should not see the \"Regenerate\" functionality and other features", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:407", "name": "User 2 is granted only \"Regenerate\" in the entitlement list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:408", "name": "User 2 should be able to see only the \"Regenerate\" functionality", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:409", "name": "should not see the \"Update Availability\" functionality and other features", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:410", "name": "User 3 is granted both \"Update Availability\" and \"Regenerate\" in the entitlement list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:411", "name": "User 3 should be able to see both \"Update Availability\" and \"Regenerate\" functionalities", "step_type": "then"}, {"keyword": "When", "location": "features/manualScenarios.feature:412", "name": "User 4 is granted all (update, Regenerate, others) in the entitlement list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:413", "name": "User 4 should be able to see all functionalities", "step_type": "then"}], "tags": ["tc:4445", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:419", "name": "Validate 30-second polling timer in Catalyst Match Status API", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:420", "name": "the user is on the Vacancy page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:421", "name": "Catalyst Match regeneration is in progress with status as \"queued\" or \"inprocess\"", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:422", "name": "the system starts polling the \"/vacancies/{vacancy_id}/catalystmatchstatus\" API", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:423", "name": "the API should be called every 30 seconds", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:424", "name": "polling should continue until the status becomes \"completed\" or \"error\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:425", "name": "once the status changes to \"completed\" or \"error\", stop the polling", "step_type": "then"}], "tags": ["tc:4446", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:432", "name": "Open same vacancy in multiple tabs - validate updates independently", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:433", "name": "the user has Tab A and Tab B open with the same vacancy", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:434", "name": "Catalyst Match is regenerated in Tab A", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:435", "name": "30 seconds have passed or the user triggers a manual check in Tab B", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:436", "name": "the \"Updates Available\" label should be displayed if the completed_at timestamp is newer than Tab B load time", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:437", "name": "the Refresh button should remain available", "step_type": "then"}], "tags": ["tc:4447", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:442", "name": "Open different vacancies in multiple tabs - validate isolation", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:443", "name": "the user has Tab A with Vacancy A and Tab B with Vacancy B", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:444", "name": "Catalyst Match status for Vacancy A is updated", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:445", "name": "only Tab A should show \"Updates Available\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:446", "name": "Tab B should remain unchanged unless Vacancy B also gets an update", "step_type": "then"}], "tags": ["tc:4448", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:452", "name": "Display \"Updates Available\" in the same tab when Catalyst Match data is updated", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:453", "name": "the user is on Vacancy A", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:454", "name": "the table was loaded at time T1", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:455", "name": "the Catalyst Match status API returns a last updated timestamp greater than T1", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:456", "name": "display the \"Updates Available\" label next to the Refresh button", "step_type": "then"}], "tags": ["tc:4449", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:461", "name": "Hide \"Updates Available\" label after Refresh button is clicked", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:462", "name": "the \"Updates Available\" label is shown for Vacancy A", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:463", "name": "the user clicks the Refresh button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:464", "name": "the table is reloaded", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:465", "name": "the \"Updates Available\" label is hidden", "step_type": "then"}], "tags": ["tc:4450", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:470", "name": "Display \"Updates Available\" in another tab for the same vacancy", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:471", "name": "the user has Tab A and Tab B open with the same Vacancy A", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:472", "name": "Vacancy A's Catalyst Match data is updated from Tab A or backend", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:473", "name": "Tab B polling detects a newer timestamp", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:474", "name": "Tab B should show the \"Updates Available\" label without manual action", "step_type": "then"}], "tags": ["tc:4451", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:479", "name": "Show \"Updates Available\" label correctly when switching between vacancies", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:480", "name": "the user opens Vacancy A and Vacancy B in different tabs", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:481", "name": "Catalyst Match status for Vacancy A is updated", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:482", "name": "the user switches to Tab B (Vacancy A)", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:483", "name": "the \"Updates Available\" label should be displayed if timestamp > last table load", "step_type": "then"}], "tags": ["tc:4452", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:488", "name": "Do not show \"Updates Available\" label for a different vacancy", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:489", "name": "the user has Tab A on Vacancy A and Tab B on Vacancy B", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:490", "name": "Catalyst Match data is updated for Vacancy A", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:491", "name": "the user checks Tab B (Vacancy B)", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:492", "name": "the \"Updates Available\" label should not appear", "step_type": "then"}], "tags": ["tc:4453", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:497", "name": "Manually update last_updated_at in database and verify \"Updates Available\" behavior", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:498", "name": "the user is on Vacancy A and table is loaded at time T1", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:499", "name": "the Catalyst Match API is polling every 30 seconds", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:500", "name": "a developer or tester manually updates the `last_updated_at` value in the database for Vacancy A to time T2 (where T2 > T1)", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:501", "name": "on the next poll, the Catalyst Match Status API returns `last_updated_at = T2`", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:502", "name": "the UI should display the \"Updates Available\" label next to the Refresh button", "step_type": "then"}], "tags": ["tc:4454", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:508", "name": "Reload table when Catalyst Match regeneration is completed", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:513", "name": "the Catalyst Match status for the vacancy is \"completed\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:514", "name": "the regeneration completed timestamp is the current datetime", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:515", "name": "the status changes to \"completed\"", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:516", "name": "the vacancy table should reload only once", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:517", "name": "the \"Regenerate\" button should be enabled", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:518", "name": "the \"Refresh\" button should be enabled", "step_type": "then"}], "tags": ["tc:4455", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:524", "name": "Do not reload table when Catalyst Match status is queued", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:528", "name": "the Catalyst Match status for the vacancy is \"queued\"", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:529", "name": "the status remains \"queued\"", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:530", "name": "the vacancy table should not reload", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:531", "name": "the \"Regenerate\" button should remain disabled", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:532", "name": "the \"Refresh\" button should remain disabled", "step_type": "then"}], "tags": ["tc:4456", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:538", "name": "Display error when vacancy is missing and Catalyst Match returns 404", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:541", "name": "the vacancy ID is not present in the required tables", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:542", "name": "the Catalyst Match API call is made", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:543", "name": "the API should return status code 404", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:544", "name": "the system should display the message", "step_type": "then", "text": "Please ensure Catalyst Match is enabled. If it is already enabled, then please wait a few minutes.\r"}], "tags": ["tc:4457", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:553", "name": "Show empty candidate list when no candidates exist", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:556", "name": "the vacancy exists without any candidate records", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:557", "name": "the page loads the candidate list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:558", "name": "the system should display an empty state", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:559", "name": "show a message indicating that no candidates are available", "step_type": "then"}], "tags": ["tc:4458", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:564", "name": "Display \"Update Available\" text when new data is detected", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:568", "name": "the system detects a newer shortlist data timestamp than the current one", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:569", "name": "the page loads the vacancy data", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:570", "name": "the system should display the text \"Update Available\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:571", "name": "the user should be able to refresh to view updated data", "step_type": "then"}], "tags": ["tc:4459", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:577", "name": "Show shortlist button when status is empty (\"\")", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:578", "name": "the user is viewing the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:579", "name": "the candidate's shortlisting status is empty", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:580", "name": "the data is loaded or refreshed", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:581", "name": "the shortlist button should be visible and enabled", "step_type": "then"}], "tags": ["tc:4460", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:586", "name": "Show shortlisted message and disable button when status is success or failure with error 409", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:587", "name": "the user is viewing the candidate list", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "And", "location": "features/manualScenarios.feature:588", "name": "the candidate's shortlisting status is \"success\" or \"failure\" with status code 409", "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:589", "name": "the data is loaded or refreshed", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:590", "name": "the shortlist button should be disabled", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:591", "name": "the UI should display the message \"Shortlisted\"", "step_type": "then"}], "tags": ["tc:4461", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:596", "name": "Shortlist a candidate successfully", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:597", "name": "the candidate is eligible for shortlisting", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:598", "name": "the user clicks on the shortlist button", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:599", "name": "confirms in the confirmation modal", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:600", "name": "a request should be sent with valid vacancy_id, candidate_id and reviewer_email", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:601", "name": "the response should have status 200", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:602", "name": "the UI should show a success message", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:603", "name": "the shortlist button should be disabled and labeled \"Shortlisted\"", "step_type": "then"}], "tags": ["tc:4462", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:608", "name": "Try to shortlist an already shortlisted candidate", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:609", "name": "the candidate is already shortlisted", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:610", "name": "the user tries to shortlist again", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:611", "name": "the API should respond with status 409", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:612", "name": "the response body should contain the message \"<PERSON>di<PERSON> is already shortlisted for this vacancy\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:613", "name": "a warning notification should be displayed", "step_type": "then"}], "tags": ["tc:4463", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:618", "name": "Handle server error during shortlisting", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:619", "name": "the user tries to shortlist a candidate", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:620", "name": "the server is down or returns a 500 error", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:621", "name": "the API should respond with status 500", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:622", "name": "the response body should contain the message \"Failed to shortlist candidate\"", "step_type": "then"}, {"keyword": "And", "location": "features/manualScenarios.feature:623", "name": "an error notification should be displayed", "step_type": "then"}], "tags": ["tc:4464", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:628", "name": "Hide shortlist button when shortlisting from entitlement is not allowed", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:629", "name": "shortlisting from entitlement is set to false", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:630", "name": "the user views the candidate list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:631", "name": "the shortlist button should not be visible", "step_type": "then"}], "tags": ["tc:4465", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:636", "name": "Disable all shortlist buttons when IS_LOCK_FEATURE_DISABLED is true", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:637", "name": "the feature flag IS_LOCK_FEATURE_DISABLED is true", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:638", "name": "the user views the candidate list", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:639", "name": "all shortlist buttons should be disabled regardless of candidate status", "step_type": "then"}], "tags": ["tc:4466", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:645", "name": "UI loads correctly on Subcategory Library Editor page", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:646", "name": "the user navigates to the Subcategory Library Editor page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:647", "name": "the UI should load correctly", "step_type": "then"}], "tags": ["tc:4638", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:651", "name": "Sticky columns remain visible on horizontal scroll", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:652", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:653", "name": "the user scrolls horizontally", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:654", "name": "the sticky columns should remain visible", "step_type": "then"}], "tags": ["tc:4639", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:658", "name": "Verify search functionality with valid subcategory name", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:659", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:660", "name": "the user enters a valid subcategory name in the search box", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:661", "name": "the matching subcategory should be displayed", "step_type": "then"}], "tags": ["tc:4640", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:665", "name": "Verify search returns no match for invalid subcategory name", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:666", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:667", "name": "the user enters a non-existing subcategory name", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:668", "name": "no results should be displayed", "step_type": "then"}], "tags": ["tc:4641", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:672", "name": "Verify sorting by Category", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:673", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:674", "name": "the user clicks on the 'Category' header", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:675", "name": "the subcategories should be sorted by Category in ascending order", "step_type": "then"}], "tags": ["tc:4642", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:679", "name": "Verify sorting toggle on Category column", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:680", "name": "the Category column is already sorted", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:681", "name": "the user clicks the 'Category' header again", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:682", "name": "the sorting order should toggle to descending", "step_type": "then"}], "tags": ["tc:4643", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:686", "name": "Verify sorting by Created Date", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:687", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:688", "name": "the user clicks on the 'Created Date' column header", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:689", "name": "the subcategories should be sorted by Created Date", "step_type": "then"}], "tags": ["tc:4644", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:693", "name": "Enable Save button on field modification", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:694", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:695", "name": "the user modifies a field value", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:696", "name": "the Save button should become enabled", "step_type": "then"}], "tags": ["tc:4645", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:700", "name": "Disable Save button when no changes are made", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:701", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:702", "name": "the user does not modify any row", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:703", "name": "the Save button should remain disabled", "step_type": "then"}], "tags": ["tc:4646", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:707", "name": "Enable Save All button on row modification", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:708", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:709", "name": "the user modifies any row", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:710", "name": "the Save All button should become enabled", "step_type": "then"}], "tags": ["tc:4647", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:714", "name": "Save All functionality", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:715", "name": "the user has modified one or more rows", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:716", "name": "the user clicks the 'Save All' button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:717", "name": "all modified rows should be saved successfully", "step_type": "then"}], "tags": ["tc:4648", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:721", "name": "Verify checkbox selection", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:722", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:723", "name": "the user clicks the checkbox in a row", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:724", "name": "the checkbox should be selected", "step_type": "then"}], "tags": ["tc:4649", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:728", "name": "Verify checkbox deselection", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:729", "name": "a row checkbox is selected", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:730", "name": "the user clicks the checkbox again", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:731", "name": "the checkbox should be deselected", "step_type": "then"}], "tags": ["tc:4650", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:735", "name": "Enable Delete button when at least one row is selected", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:736", "name": "one or more row checkboxes are selected", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:737", "name": "the Delete button should become enabled", "step_type": "then"}], "tags": ["tc:4651", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:741", "name": "Disable Delete button when no rows are selected", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:742", "name": "no row checkboxes are selected", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:743", "name": "the Delete button should remain disabled", "step_type": "then"}], "tags": ["tc:4652", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:747", "name": "Perform delete action", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:748", "name": "one or more row checkboxes are selected", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:749", "name": "the user clicks the Delete button", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:750", "name": "the selected rows should be deleted", "step_type": "then"}], "tags": ["tc:4653", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:754", "name": "Verify save notification after editing", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:755", "name": "the user modifies a row", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:756", "name": "the user clicks Save", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:757", "name": "a confirmation notification should appear", "step_type": "then"}], "tags": ["tc:4654", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:761", "name": "Verify vertical scroll behavior", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:762", "name": "the Subcategory Library Editor page contains many rows", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:763", "name": "the user scrolls vertically", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:764", "name": "the scroll should function smoothly and correctly", "step_type": "then"}], "tags": ["tc:4655", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:768", "name": "Handle empty table state", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:769", "name": "there are no subcategories available", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:770", "name": "the table should display an appropriate empty state message", "step_type": "then"}], "tags": ["tc:4656", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:774", "name": "Verify multiple save actions", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:775", "name": "multiple rows have been modified", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:776", "name": "the user saves them individually", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:777", "name": "each save action should succeed without affecting others", "step_type": "then"}], "tags": ["tc:4657", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:781", "name": "Check lazy loading in stats section", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:782", "name": "the user opens the Subcategory Library Editor page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:783", "name": "the stats section should load lazily after initial page load", "step_type": "then"}], "tags": ["tc:4658", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:787", "name": "Verify stats data is displayed", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:788", "name": "the stats section has loaded", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:789", "name": "correct stats data should be shown", "step_type": "then"}], "tags": ["tc:4659", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:793", "name": "Update validation with no changes", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:794", "name": "the user has not modified any field", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:795", "name": "the user attempts to save", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:796", "name": "no save should occur and no update call should be triggered", "step_type": "then"}], "tags": ["tc:4660", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:800", "name": "Verify button style consistency", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:801", "name": "the Subcategory Library Editor page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "Then", "location": "features/manualScenarios.feature:802", "name": "all buttons should follow consistent style and appearance guidelines", "step_type": "then"}], "tags": ["tc:4661", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:808", "name": "Verify user has access to Catalyst Match page in CRM", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:809", "name": "the user is logged into the CRM with appropriate permissions", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:810", "name": "the user clicks on the Catalyst Match page link", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:811", "name": "the Catalyst Match page should be accessible", "step_type": "then"}], "tags": ["tc:4662", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:815", "name": "Verify user does not have access to Catalyst Match page in CRM", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:816", "name": "the user is logged into the CRM without proper permissions", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:817", "name": "the user clicks on the Catalyst Match page link", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:818", "name": "the user should see an access denied or unauthorized message", "step_type": "then"}], "tags": ["tc:4663", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:822", "name": "Verify Catalyst Match page matches Vacancy page in Recruiter portal", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:823", "name": "the user has access to both CRM and Recruiter portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:824", "name": "the user opens the Catalyst Match page in CRM", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:825", "name": "the content and layout should match the Vacancy page in the Recruiter portal", "step_type": "then"}], "tags": ["tc:4664", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:829", "name": "Verify user can make changes even when Vacancy is locked in Recruiter portal", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:830", "name": "the corresponding vacancy is locked in the <PERSON><PERSON><PERSON><PERSON> portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:831", "name": "the user opens the Catalyst Match page in CRM", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:832", "name": "clicks on thumbs up/down, adds a comment, and clicks on shortlist", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:833", "name": "all changes should be saved successfully without restriction", "step_type": "then"}], "tags": ["tc:4665", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:837", "name": "Verify user is redirected to the Candidate profile", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:838", "name": "the Catalyst Match page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:839", "name": "the user clicks on a candidate name in the table", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:840", "name": "the user should be redirected to the candidate’s profile page", "step_type": "then"}], "tags": ["tc:4666", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:844", "name": "Verify presence and functionality of Refresh icon", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:845", "name": "the Catalyst Match page is open", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:846", "name": "the user clicks on the Refresh icon above the table", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:847", "name": "the table data should reload successfully", "step_type": "then"}], "tags": ["tc:4667", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:851", "name": "Verify unauthorized user cannot access Catalyst Match page via direct URL", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:852", "name": "the user is not authorized to access the Catalyst Match page", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:853", "name": "the user attempts to open the page using a direct URL", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:854", "name": "access should be denied and the user should see an error or redirect to login/access denied page", "step_type": "then"}], "tags": ["tc:4668", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}, {"keyword": "<PERSON><PERSON><PERSON>", "location": "features/manualScenarios.feature:858", "name": "Verify user can update vacancy data even when locked in Recruiter portal", "status": "failed", "steps": [{"keyword": "Given", "location": "features/manualScenarios.feature:859", "name": "the vacancy is locked in the <PERSON><PERSON><PERSON><PERSON> portal", "result": {"duration": 0, "status": "undefined"}, "step_type": "given"}, {"keyword": "When", "location": "features/manualScenarios.feature:860", "name": "the user opens the Catalyst Match page in CRM", "step_type": "when"}, {"keyword": "And", "location": "features/manualScenarios.feature:861", "name": "updates the rating, adds a comment, and shortlists a candidate", "step_type": "when"}, {"keyword": "Then", "location": "features/manualScenarios.feature:862", "name": "the actions should be saved and reflected correctly", "step_type": "then"}], "tags": ["tc:4669", "manual", "recruiter<PERSON><PERSON><PERSON>"], "type": "scenario"}], "keyword": "Feature", "location": "features/manualScenarios.feature:1", "name": "Manual Scenarios", "status": "failed", "tags": []}]