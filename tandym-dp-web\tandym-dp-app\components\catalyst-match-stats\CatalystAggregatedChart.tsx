"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  Target,
  Star,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  Building2,
  Hash,
  Pie<PERSON>hart,
} from "lucide-react";
import {
  CatalystMatchAggregatedResult,
  formatNumber,
} from "@/app/stats/catalystmatch/helper";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CatalystAggregatedChartProps {
  aggregatedResults: CatalystMatchAggregatedResult[];
}

type ChartViewMode = "summary" | "stacked";

const CatalystAggregatedChart: React.FC<CatalystAggregatedChartProps> = ({
  aggregatedResults,
}) => {
  const [viewMode, setViewMode] = useState<ChartViewMode>("summary");
  if (!aggregatedResults || aggregatedResults.length === 0) {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Aggregated Results by Category & Subcategory
          </CardTitle>
          <div className="text-sm text-gray-500">
            No aggregated data available for the selected time range
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-12 text-gray-500">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No aggregated data available
              </h3>
              <p className="text-sm text-gray-600">
                No aggregated results were found for the selected time range.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group aggregated results by GTM and calculate totals
  const gtmData = aggregatedResults.reduce(
    (acc, result) => {
      if (!acc[result.category_name]) {
        acc[result.category_name] = {
          total: 0,
          vacancy_count: 0,
          total_rated: 0,
          liked: 0,
          disliked: 0,
          shortlisted: 0,
          subcategories: [],
        };
      }
      const count = Number(result?.total_found) || 0;
      acc[result.category_name].total += count;
      acc[result.category_name].vacancy_count +=
        Number(result.vacancy_count) || 0;
      acc[result.category_name].total_rated += Number(result.total_rated) || 0;
      acc[result.category_name].liked += Number(result.liked) || 0;
      acc[result.category_name].disliked += Number(result.disliked) || 0;
      acc[result.category_name].shortlisted += Number(result.shortlisted) || 0;
      acc[result.category_name].subcategories.push({
        name: result.subcategory_name,
        count: count,
        percentage: 0,
        vacancy_count: Number(result.vacancy_count) || 0,
        total_rated: Number(result.total_rated) || 0,
        liked: Number(result.liked) || 0,
        disliked: Number(result.disliked) || 0,
        shortlisted: Number(result.shortlisted) || 0,
      });
      return acc;
    },
    {} as Record<
      string,
      {
        total: number;
        vacancy_count: number;
        total_rated: number;
        liked: number;
        disliked: number;
        shortlisted: number;
        subcategories: Array<{
          name: string;
          count: number;
          percentage: number;
          vacancy_count: number;
          total_rated: number;
          liked: number;
          disliked: number;
          shortlisted: number;
        }>;
      }
    >
  );

  // Calculate percentages and sort by total
  const sortedGtmData = Object.entries(gtmData)
    .map(([gtm, data]) => ({
      gtm,
      total: data.total,
      vacancy_count: data.vacancy_count,
      total_rated: data.total_rated,
      liked: data.liked,
      disliked: data.disliked,
      shortlisted: data.shortlisted,
      subcategories: data.subcategories.map((sub) => ({
        ...sub,
        percentage: data.total > 0 ? (sub.count / data.total) * 100 : 0,
      })),
    }))
    .sort((a, b) => b.total - a.total)
    .filter((item) => item.total > 0);

  // Color-blind friendly color palette
  const colorPalette = [
    "#3b82f6", // Blue
    "#10b981", // Green
    "#f59e0b", // Amber
    "#ef4444", // Red
    "#8b5cf6", // Purple
    "#06b6d4", // Cyan
    "#84cc16", // Lime
    "#f97316", // Orange
    "#ec4899", // Pink
    "#6366f1", // Indigo
  ];

  const renderStackedHistogram = () => {
    const maxMatched = Math.max(...sortedGtmData.map((item) => item.total));

    return (
      <div className="space-y-4">
        {/* Stacked Histogram */}
        <div className="space-y-4">
          {sortedGtmData.map((item, itemIndex) => (
            <div key={item.gtm} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-sm border border-gray-300"
                    style={{
                      backgroundColor:
                        colorPalette[itemIndex % colorPalette.length],
                    }}
                  ></div>
                  <span className="font-medium">{item.gtm}</span>
                </div>
                <span className="text-gray-600 font-semibold">
                  {formatNumber(item.total)} matches
                </span>
              </div>

              <div className="relative h-10 bg-gray-100 rounded-md overflow-hidden border border-gray-200">
                {item.subcategories.map((subcategory, subIndex) => {
                  const width =
                    maxMatched > 0 ? (subcategory.count / maxMatched) * 100 : 0;
                  const left = item.subcategories
                    .slice(0, subIndex)
                    .reduce(
                      (acc, sub) =>
                        acc +
                        (maxMatched > 0 ? (sub.count / maxMatched) * 100 : 0),
                      0
                    );

                  // Add diagonal stripes pattern for better distinction
                  const stripePattern =
                    subIndex % 2 === 0
                      ? "repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,0.3) 2px, rgba(255,255,255,0.3) 4px)"
                      : "none";

                  return (
                    <TooltipProvider key={subcategory.name}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className="absolute h-full transition-all duration-300 hover:opacity-90 cursor-pointer border-r border-white/50"
                            style={{
                              left: `${left}%`,
                              width: `${width}%`,
                              backgroundColor:
                                colorPalette[itemIndex % colorPalette.length],
                              opacity: 0.85 + subIndex * 0.05, // Vary opacity for subcategories
                              backgroundImage: stripePattern,
                            }}
                          />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-sm border border-gray-300"
                                style={{
                                  backgroundColor:
                                    colorPalette[
                                      itemIndex % colorPalette.length
                                    ],
                                }}
                              ></div>
                              <p className="font-semibold">{item.gtm}</p>
                            </div>
                            <p className="font-medium text-blue-600">
                              {subcategory.name}
                            </p>
                            <div className="text-sm space-y-1">
                              <div className="flex justify-between">
                                <span>Matches:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.count)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Vacancies:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.vacancy_count)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Rated:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.total_rated)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Liked:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.liked)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Disliked:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.disliked)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Shortlisted:</span>
                                <span className="font-semibold">
                                  {formatNumber(subcategory.shortlisted)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Percentage:</span>
                                <span className="font-semibold">
                                  {(
                                    (subcategory.count / item.total) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </span>
                              </div>
                            </div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Compact Legend */}
        <div className="bg-gray-50 p-2 rounded-md">
          <div className="flex flex-wrap gap-3 text-xs">
            {sortedGtmData.map((item, itemIndex) => (
              <div key={item.gtm} className="flex items-center gap-1">
                <div
                  className="w-2 h-2 rounded-sm border border-gray-300"
                  style={{
                    backgroundColor:
                      colorPalette[itemIndex % colorPalette.length],
                  }}
                ></div>
                <span className="text-gray-600 font-medium">{item.gtm}</span>
                <span className="text-gray-500">
                  ({formatNumber(item.total)})
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Calculate overall statistics
  const totalMatched = sortedGtmData.reduce((sum, item) => sum + item.total, 0);
  const totalRated = sortedGtmData.reduce(
    (sum, item) => sum + item.total_rated,
    0
  );
  const totalLiked = sortedGtmData.reduce((sum, item) => sum + item.liked, 0);
  const totalDisliked = sortedGtmData.reduce(
    (sum, item) => sum + item.disliked,
    0
  );
  const totalShortlisted = sortedGtmData.reduce(
    (sum, item) => sum + item.shortlisted,
    0
  );

  return (
    <div className="space-y-4">
      {/* View Mode Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "summary" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("summary")}
            className="flex items-center gap-2"
          >
            <PieChart className="h-4 w-4" />
            Summary
          </Button>
          <Button
            variant={viewMode === "stacked" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("stacked")}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Stacked
          </Button>
        </div>
        <div className="text-sm text-gray-500">
          {sortedGtmData.length} categories with {formatNumber(totalMatched)}{" "}
          total matches
        </div>
      </div>

      {/* Color Legend */}
      <div className="text-sm text-gray-600 font-medium">
        <span className="text-blue-600 font-bold">●</span> Matched •{" "}
        <span className="text-green-600 font-bold">●</span> Rated •{" "}
        <span className="text-amber-600 font-bold">●</span> Liked •{" "}
        <span className="text-red-600 font-bold">●</span> Disliked •{" "}
        <span className="text-purple-600 font-bold">●</span> Shortlisted
      </div>

      {/* Content */}
      <div>
        {viewMode === "summary" && (
          <div className="space-y-6">
            {/* Summary Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <Target className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-semibold text-blue-900">
                    {formatNumber(totalMatched)}
                  </div>
                  <div className="text-sm text-blue-600">Total Matched</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <Star className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-semibold text-green-900">
                    {formatNumber(totalRated)}
                  </div>
                  <div className="text-sm text-green-600">Total Rated</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
                <ThumbsUp className="h-5 w-5 text-amber-600" />
                <div>
                  <div className="font-semibold text-amber-900">
                    {formatNumber(totalLiked)}
                  </div>
                  <div className="text-sm text-amber-600">Total Liked</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                <ThumbsDown className="h-5 w-5 text-red-600" />
                <div>
                  <div className="font-semibold text-red-900">
                    {formatNumber(totalDisliked)}
                  </div>
                  <div className="text-sm text-red-600">Total Disliked</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                <Bookmark className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="font-semibold text-purple-900">
                    {formatNumber(totalShortlisted)}
                  </div>
                  <div className="text-sm text-purple-600">
                    Total Shortlisted
                  </div>
                </div>
              </div>
            </div>

            {/* Category Breakdown */}
            <div className="space-y-4">
              {sortedGtmData.map((item, index) => (
                <div
                  key={`${item.gtm}-${index}`}
                  className="space-y-3 p-4 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-600" />
                        <span className="text-sm font-bold text-gray-700">
                          {item.gtm}
                        </span>
                        <span className="text-sm text-gray-500">
                          ({formatNumber(item.total)} matches)
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 space-y-1">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Target className="h-3 w-3 text-blue-600" />
                            {formatNumber(item.total)} matched
                          </span>
                          <span className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-green-600" />
                            {formatNumber(item.total_rated)} rated
                          </span>
                          <span className="flex items-center gap-1">
                            <ThumbsUp className="h-3 w-3 text-amber-600" />
                            {formatNumber(item.liked)} liked
                          </span>
                          <span className="flex items-center gap-1">
                            <ThumbsDown className="h-3 w-3 text-red-600" />
                            {formatNumber(item.disliked)} disliked
                          </span>
                          <span className="flex items-center gap-1">
                            <Bookmark className="h-3 w-3 text-purple-600" />
                            {formatNumber(item.shortlisted)} shortlisted
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Subcategory Breakdown */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-700 mb-2">
                      Subcategory Distribution:
                    </div>
                    <div className="relative h-8 bg-white rounded-md overflow-hidden border border-gray-200">
                      {item.subcategories.map((sub, subIndex) => (
                        <div
                          key={sub.name}
                          className="absolute h-full flex items-center justify-center"
                          style={{
                            left: `${item.subcategories
                              .slice(0, subIndex)
                              .reduce((sum, s) => sum + s.percentage, 0)}%`,
                            width: `${sub.percentage}%`,
                            backgroundColor:
                              colorPalette[subIndex % colorPalette.length],
                          }}
                          title={`${sub.name}: ${formatNumber(
                            sub.count
                          )} matches (${sub.percentage.toFixed(1)}%)`}
                        >
                          {sub.percentage > 8 && (
                            <span className="text-xs font-medium text-white px-2 truncate">
                              {sub.name}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Subcategory Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-3">
                      {item.subcategories.map((sub, subIndex) => (
                        <div
                          key={sub.name}
                          className="flex items-center justify-between p-2 bg-white rounded border border-gray-200"
                          style={{
                            borderLeft: `4px solid ${
                              colorPalette[subIndex % colorPalette.length]
                            }`,
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <Hash className="h-3 w-3 text-gray-500" />
                            <span className="text-xs font-medium text-gray-700">
                              {sub.name}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600">
                            {formatNumber(sub.count)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {viewMode === "stacked" && (
          <div className="space-y-4">{renderStackedHistogram()}</div>
        )}
      </div>
    </div>
  );
};

export default CatalystAggregatedChart;
