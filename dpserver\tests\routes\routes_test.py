import pytest
from unittest.mock import Mock,patch
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>
from dpserver.apis.routes import Routes
from uuid import uuid4

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_attribute_service():
    return Mock()

@pytest.fixture
def mock_subcategory_service():
    return Mock()

@pytest.fixture
def mock_vacancy_service():
    return Mock()

@pytest.fixture
def mock_parse_resume_service():
    return Mock()

@pytest.fixture
def mock_experiment_service():
    return Mock()

@pytest.fixture
def mock_postgres_connector():
    return Mock()

@pytest.fixture
def mock_env():
    return Mock()

@pytest.fixture
def test_client(
    mock_logger,
    mock_attribute_service,
    mock_subcategory_service,
    mock_vacancy_service,
    mock_parse_resume_service,
    mock_experiment_service,
    mock_postgres_connector,
    mock_env
):
    app = FastAPI()
    routes = Routes(
        logger=mock_logger,
        attribute_service=mock_attribute_service,
        subcategory_service=mock_subcategory_service,
        vacancy_service=mock_vacancy_service,
        parse_resume_service=mock_parse_resume_service,
        experiment_service=mock_experiment_service,
        postgres_db_connector=mock_postgres_connector,
        env=mock_env
    )
    app.include_router(routes.get_router())
    return TestClient(app, raise_server_exceptions=False)

def test_get_vacancies_success(test_client, mock_vacancy_service):
    # Arrange
    mock_vacancy_service.get_vacancies.return_value = {"vacancies": []}

    # Act
    response = test_client.get("/vacancies")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"vacancies": []}
    mock_vacancy_service.get_vacancies.assert_called_once()

def test_get_vacancies_exception(test_client, mock_vacancy_service):
    # Arrange
    mock_vacancy_service.get_vacancies.side_effect = Exception("Service failure")

    # Act
    response = test_client.get("/vacancies")

    # Assert
    assert response.status_code == 500
    assert "Internal Server Error" in response.text


def test_get_vacancies_active_false(test_client, mock_vacancy_service):
        mock_vacancy_service.get_vacancies.return_value = {"vacancies": ["inactive"]}
        response = test_client.get("/vacancies?active=false")
        assert response.status_code == 200
        assert response.json() == {"vacancies": ["inactive"]}
        mock_vacancy_service.get_vacancies.assert_called_once_with(active=False, archived=False)

def test_get_vacancies_archived_true(test_client, mock_vacancy_service):
        mock_vacancy_service.get_vacancies.return_value = {"vacancies": ["archived"]}
        response = test_client.get("/vacancies?archived=true")
        assert response.status_code == 200
        assert response.json() == {"vacancies": ["archived"]}
        mock_vacancy_service.get_vacancies.assert_called_once_with(active=True, archived=True)

def test_get_vacancies_active_false_archived_true(test_client, mock_vacancy_service):
        mock_vacancy_service.get_vacancies.return_value = {"vacancies": ["inactive", "archived"]}
        response = test_client.get("/vacancies?active=false&archived=true")
        assert response.status_code == 200
        assert response.json() == {"vacancies": ["inactive", "archived"]}
        mock_vacancy_service.get_vacancies.assert_called_once_with(active=False, archived=True)

def test_get_vacancies_invalid_active_param(test_client):
    response = test_client.get("/vacancies?active=notabool")
    assert response.status_code == 422
    assert "bool_parsing" in response.text

def test_get_vacancies_invalid_archived_param(test_client):
    response = test_client.get("/vacancies?archived=notabool")
    assert response.status_code == 422
    assert "bool_parsing" in response.text

def test_get_vacancies_service_returns_none(test_client, mock_vacancy_service):
    mock_vacancy_service.get_vacancies.return_value = None
    response = test_client.get("/vacancies")
    assert response.status_code == 500
    mock_vacancy_service.get_vacancies.assert_called_once_with(active=True, archived=False)

def test_get_vacancies_service_returns_empty_dict(test_client, mock_vacancy_service):
    mock_vacancy_service.get_vacancies.return_value = {}
    response = test_client.get("/vacancies")
    assert response.status_code == 200
    assert response.json() == {}
    mock_vacancy_service.get_vacancies.assert_called_once_with(active=True, archived=False)

def test_get_vacancies_service_returns_unexpected_keys(test_client, mock_vacancy_service):
    mock_vacancy_service.get_vacancies.return_value = {"unexpected": 123}
    response = test_client.get("/vacancies")
    assert response.status_code == 200
    assert response.json() == {"unexpected": 123}
    mock_vacancy_service.get_vacancies.assert_called_once_with(active=True, archived=False)

def test_get_vacancy_by_id_active_false(test_client, mock_vacancy_service):
    mock_vacancy_service.get_vacancy_by_id.return_value = {"vacancy": "inactive"}
    response = test_client.get("/vacancy/abc123?active=false")
    assert response.status_code == 200
    assert response.json() == {"vacancy": "inactive"}
    mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=False, archived=False)

def test_get_vacancy_by_id_archived_true(test_client, mock_vacancy_service):
            mock_vacancy_service.get_vacancy_by_id.return_value = {"vacancy": "archived"}
            response = test_client.get("/vacancy/abc123?archived=true")
            assert response.status_code == 200
            assert response.json() == {"vacancy": "archived"}
            mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=True, archived=True)

def test_get_vacancy_by_id_active_false_archived_true(test_client, mock_vacancy_service):
            mock_vacancy_service.get_vacancy_by_id.return_value = {"vacancy": "inactive_archived"}
            response = test_client.get("/vacancy/abc123?active=false&archived=true")
            assert response.status_code == 200
            assert response.json() == {"vacancy": "inactive_archived"}
            mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=False, archived=True)
def test_get_vacancy_by_id_invalid_active_param(test_client):
            response = test_client.get("/vacancy/abc123?active=notabool")
            assert response.status_code == 422
            assert "bool_parsing" in response.text

def test_get_vacancy_by_id_invalid_archived_param(test_client):
            response = test_client.get("/vacancy/abc123?archived=notabool")
            assert response.status_code == 422
            assert "bool_parsing" in response.text

def test_get_vacancy_by_id_service_returns_none(test_client, mock_vacancy_service):
            mock_vacancy_service.get_vacancy_by_id.return_value = None
            response = test_client.get("/vacancy/abc123")
            assert response.status_code == 500
            mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=True, archived=False)

def test_get_vacancy_by_id_service_returns_empty_dict(test_client, mock_vacancy_service):
            mock_vacancy_service.get_vacancy_by_id.return_value = {}
            response = test_client.get("/vacancy/abc123")
            assert response.status_code == 200
            assert response.json() == {}
            mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=True, archived=False)

def test_get_vacancy_by_id_service_returns_unexpected_keys(test_client, mock_vacancy_service):
            mock_vacancy_service.get_vacancy_by_id.return_value = {"unexpected": 123}
            response = test_client.get("/vacancy/abc123")
            assert response.status_code == 200
            assert response.json() == {"unexpected": 123}
            mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=True, archived=False)



def test_get_vacancy_by_id_success(test_client, mock_vacancy_service):
        mock_vacancy_service.get_vacancy_by_id.return_value = {"vacancy": "data"}
        response = test_client.get("/vacancy/abc123")
        assert response.status_code == 200
        assert response.json() == {"vacancy": "data"}
        mock_vacancy_service.get_vacancy_by_id.assert_called_once_with(vacancy_id="abc123", active=True, archived=False)


def test_start_vacancy_review_success(test_client, mock_vacancy_service):
    vacancy_id = "123"
    payload = {"reviewer": "<EMAIL>"}

    mock_vacancy_service.start_vacancy_review.return_value = {"status": "started"}

    response = test_client.post(f"/vacancies/{vacancy_id}/start", json=payload)

    assert response.status_code == 200
    assert response.json() == {"status": "started"}
    mock_vacancy_service.start_vacancy_review.assert_called_once_with(vacancy_id, "<EMAIL>")

def test_start_vacancy_review_missing_reviewer(test_client):
    vacancy_id = "123"
    payload = {}  # Missing reviewer email

    response = test_client.post(f"/vacancies/{vacancy_id}/start", json=payload)

    assert response.status_code == 400
    assert response.json() == {"detail": "reviewer email is required"}

def test_start_vacancy_review_service_error(test_client, mock_vacancy_service):
    vacancy_id = "123"
    payload = {"reviewer": "<EMAIL>"}

    mock_vacancy_service.start_vacancy_review.return_value = {
        "error": "Something went wrong",
        "status_code": 503,
        "details": "Service unavailable"
    }

    response = test_client.post(f"/vacancies/{vacancy_id}/start", json=payload)

    assert response.status_code == 503
    assert response.json() == {
        "detail": {
            "message": "Something went wrong",
            "details": "Service unavailable"
        }
    }
    mock_vacancy_service.start_vacancy_review.assert_called_once_with(vacancy_id, "<EMAIL>")

def test_complete_vacancy_review_success(test_client, mock_vacancy_service):
    vacancy_id = "456"
    payload = {"reviewer": "<EMAIL>"}

    mock_vacancy_service.complete_vacancy_review.return_value = {"status": "completed"}

    response = test_client.post(f"/vacancies/{vacancy_id}/complete", json=payload)

    assert response.status_code == 200
    assert response.json() == {"status": "completed"}
    mock_vacancy_service.complete_vacancy_review.assert_called_once_with(vacancy_id, "<EMAIL>")

def test_complete_vacancy_review_missing_reviewer(test_client):
    vacancy_id = "456"
    payload = {}  # Missing reviewer

    response = test_client.post(f"/vacancies/{vacancy_id}/complete", json=payload)

    assert response.status_code == 400
    assert response.json() == {"detail": "reviewer email is required"}

def test_complete_vacancy_review_service_error(test_client, mock_vacancy_service):
    vacancy_id = "456"
    payload = {"reviewer": "<EMAIL>"}

    mock_vacancy_service.complete_vacancy_review.return_value = {
        "error": "Completion failed",
        "status_code": 500,
        "details": "Vacancy already locked"
    }

    response = test_client.post(f"/vacancies/{vacancy_id}/complete", json=payload)

    assert response.status_code == 500
    assert response.json() == {
        "detail": {
            "message": "Completion failed",
            "details": "Vacancy already locked"
        }
    }
    mock_vacancy_service.complete_vacancy_review.assert_called_once_with(vacancy_id, "<EMAIL>")

def test_get_vacancy_review_status_success(test_client, mock_vacancy_service):
    vacancy_id = "789"
    mock_vacancy_service.get_vacancy_review_status.return_value = {"status": "in_review"}

    response = test_client.get(f"/vacancies/{vacancy_id}/status")

    assert response.status_code == 200
    assert response.json() == {"status": "in_review"}
    mock_vacancy_service.get_vacancy_review_status.assert_called_once_with(vacancy_id)

def test_get_vacancy_review_status_service_error(test_client, mock_vacancy_service):
    vacancy_id = "789"
    mock_vacancy_service.get_vacancy_review_status.return_value = {
        "error": "Vacancy not found",
        "status_code": 404,
        "details": "No vacancy with this ID"
    }

    response = test_client.get(f"/vacancies/{vacancy_id}/status")

    assert response.status_code == 404
    assert response.json() == {
        "detail": {
            "message": "Vacancy not found",
            "details": "No vacancy with this ID"
        }
    }
    mock_vacancy_service.get_vacancy_review_status.assert_called_once_with(vacancy_id)

def test_get_vacancy_review_status_service_returns_none(test_client, mock_vacancy_service):
        vacancy_id = "none"
        mock_vacancy_service.get_vacancy_review_status.return_value = None
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_get_vacancy_review_status_service_raises_exception(test_client, mock_vacancy_service):
        vacancy_id = "exception"
        mock_vacancy_service.get_vacancy_review_status.side_effect = Exception("DB error")
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_get_vacancy_review_status_service_returns_empty_dict(test_client, mock_vacancy_service):
        vacancy_id = "empty"
        mock_vacancy_service.get_vacancy_review_status.return_value = {}
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 200
        assert response.json() == {}

def test_get_vacancy_review_status_service_error_missing_status_code(test_client, mock_vacancy_service):
        vacancy_id = "missing-status-code"
        mock_vacancy_service.get_vacancy_review_status.return_value = {
            "error": "Not found"
        }
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 500
        assert response.json()["detail"]["message"] == "Not found"

def test_get_vacancy_review_status_service_error_nonint_status_code(test_client, mock_vacancy_service):
        vacancy_id = "nonint-status-code"
        mock_vacancy_service.get_vacancy_review_status.return_value = {
            "error": "Not found",
            "status_code": "not_a_number"
        }
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 500
        assert response.json()["detail"]["message"] == "Not found"

def test_get_vacancy_review_status_service_error_missing_details(test_client, mock_vacancy_service):
        vacancy_id = "missing-details"
        mock_vacancy_service.get_vacancy_review_status.return_value = {
            "error": "Not found",
            "status_code": 404
        }
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 404
        assert response.json()["detail"]["message"] == "Not found"
        assert "details" in response.json()["detail"]

def test_get_vacancy_review_status_service_returns_string(test_client, mock_vacancy_service):
        vacancy_id = "string"
        mock_vacancy_service.get_vacancy_review_status.return_value = "unexpected string"
        response = test_client.get(f"/vacancies/{vacancy_id}/status")
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_get_candidate_resume_success(test_client, mock_vacancy_service):
    contact_id = "contact123"
    expected_response = {"resume_url": "https://example.com/resume.pdf"}
    mock_vacancy_service.get_candidate_resume_from_file.return_value = expected_response

    response = test_client.get(f"/files/candidate-resume/{contact_id}")

    assert response.status_code == 200
    assert response.json() == expected_response
    mock_vacancy_service.get_candidate_resume_from_file.assert_called_once_with(contact_id)

def test_get_candidate_resume_service_error(test_client, mock_vacancy_service):
    contact_id = "contact123"
    mock_vacancy_service.get_candidate_resume_from_file.return_value = {
        "error": "Resume not found",
        "status_code": 404,
        "details": "No resume file for this contact"
    }

    response = test_client.get(f"/files/candidate-resume/{contact_id}")

    assert response.status_code == 200 
    assert response.json() == {
        "error": "Resume not found",
        "status_code": 404,
        "details": "No resume file for this contact"
    }
    mock_vacancy_service.get_candidate_resume_from_file.assert_called_once_with(contact_id)

def test_update_candidate_decision_success(test_client, mock_vacancy_service):
    payload = {"candidate_id": "123", "decision": "approved"}
    expected_response = {"success": True}

    mock_vacancy_service.update_candidate_decision_in_db.return_value = expected_response

    response = test_client.post("/candidates/update_in_db", json=payload)

    assert response.status_code == 200
    assert response.json() == expected_response
    mock_vacancy_service.update_candidate_decision_in_db.assert_called_once_with(payload)

def test_update_candidate_decision_service_error(test_client, mock_vacancy_service):
    payload = {"candidate_id": "123", "decision": "approved"}
    mock_vacancy_service.update_candidate_decision_in_db.return_value = {
        "error": "Update failed",
        "status_code": 400,
        "details": "Candidate locked for review"
    }

    response = test_client.post("/candidates/update_in_db", json=payload)

    assert response.status_code == 400
    assert response.json() == {
        "detail": {
            "message": "Update failed",
            "details": "Candidate locked for review"
        }
    }
    mock_vacancy_service.update_candidate_decision_in_db.assert_called_once_with(payload)

def test_update_candidate_decision_service_error_status_code_string(test_client, mock_vacancy_service):
        payload = {"candidate_id": "123", "decision": "approved"}
        mock_vacancy_service.update_candidate_decision_in_db.return_value = {
            "error": "Update failed",
            "status_code": "400",
            "details": "Candidate locked for review"
        }

        response = test_client.post("/candidates/update_in_db", json=payload)

        assert response.status_code == 400
        assert response.json() == {
            "detail": {
                "message": "Update failed",
                "details": "Candidate locked for review"
            }
        }
        mock_vacancy_service.update_candidate_decision_in_db.assert_called_once_with(payload)

def test_update_candidate_decision_service_error_missing_status_code(test_client, mock_vacancy_service):
        payload = {"candidate_id": "123", "decision": "approved"}
        mock_vacancy_service.update_candidate_decision_in_db.return_value = {
            "error": "Update failed"
        }

        response = test_client.post("/candidates/update_in_db", json=payload)

        assert response.status_code == 500
        assert response.json() == {
            "detail": {
                "message": "Update failed",
                "details": None
            }
        }
        mock_vacancy_service.update_candidate_decision_in_db.assert_called_once_with(payload)

def test_update_candidate_decision_service_error_missing_details(test_client, mock_vacancy_service):
        payload = {"candidate_id": "123", "decision": "approved"}
        mock_vacancy_service.update_candidate_decision_in_db.return_value = {
            "error": "Update failed",
            "status_code": 400
        }

        response = test_client.post("/candidates/update_in_db", json=payload)

        assert response.status_code == 400
        assert response.json() == {
            "detail": {
                "message": "Update failed",
                "details": None
            }
        }
        mock_vacancy_service.update_candidate_decision_in_db.assert_called_once_with(payload)

def test_get_candidate_success(test_client, mock_parse_resume_service):
    # Arrange
    contact_id = "12345"
    expected_response = {"name": "John Doe", "skills": ["Python", "FastAPI"]}
    mock_parse_resume_service.get_candidate.return_value = expected_response

    # Act
    response = test_client.get(f"/resume/{contact_id}")

    # Assert
    assert response.status_code == 200
    assert response.json() == expected_response
    mock_parse_resume_service.get_candidate.assert_called_once_with(contact_id)

def test_get_candidate_failure(test_client, mock_parse_resume_service):
    contact_id = "invalid_id"
    mock_parse_resume_service.get_candidate.side_effect = Exception("Something went wrong")

    response = test_client.get(f"/resume/{contact_id}")

    assert response.status_code == 500
    assert "Internal Server Error" in response.text

def test_get_candidates_success(test_client, mock_vacancy_service):
    # Arrange
    vacancy_id = "vac-123"
    reviewer_email = "<EMAIL>"
    expected_response = {"candidates": [{"name": "Alice"}]}
    mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response

    # Act
    response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}")

    # Assert
    assert response.status_code == 200
    assert response.json() == expected_response
    mock_vacancy_service.get_candidates_by_vacancy_id.assert_called_once_with(
        vacancy_id=vacancy_id,
        reviewer_email=reviewer_email,
        radius_override=None
    )

def test_get_candidates_with_radius_success(test_client, mock_vacancy_service):
    vacancy_id = "vac-456"
    reviewer_email = "<EMAIL>"
    radius = 15
    expected_response = {"candidates": [{"name": "Bob"}]}
    mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response

    response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}&radius={radius}")

    assert response.status_code == 200
    assert response.json() == expected_response
    mock_vacancy_service.get_candidates_by_vacancy_id.assert_called_once_with(
        vacancy_id=vacancy_id,
        reviewer_email=reviewer_email,
        radius_override="15.0"
    )


def test_get_candidates_invalid_radius_custom_logic(test_client, mock_vacancy_service):
    vacancy_id = "vac-error"
    reviewer_email = "<EMAIL>"

    # Patch float to raise ValueError inside the route
    with patch("builtins.float", side_effect=ValueError("Invalid float")):
        response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}&radius=10")

    assert response.status_code == 400
    assert response.json()["detail"] == "radius must be a valid numeric value"


def test_get_candidates_service_failure(test_client, mock_vacancy_service):
    vacancy_id = "vac-999"
    mock_vacancy_service.get_candidates_by_vacancy_id.side_effect = Exception("DB error")

    response = test_client.get(f"/candidates/{vacancy_id}")

    assert response.status_code == 500
    assert "Internal Server Error" in response.text

def test_get_candidates_service_returns_none(test_client, mock_vacancy_service):
    vacancy_id = "vac-none"
    mock_vacancy_service.get_candidates_by_vacancy_id.return_value = None
    response = test_client.get(f"/candidates/{vacancy_id}")
    assert response.status_code == 500
    # Optionally check error message
    assert "Internal Server Error" in response.text

def test_get_candidates_service_returns_empty_dict(test_client, mock_vacancy_service):
        vacancy_id = "vac-empty"
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = {}
        response = test_client.get(f"/candidates/{vacancy_id}")
        assert response.status_code == 200
        assert response.json() == {}

def test_get_candidates_service_returns_unexpected_keys(test_client, mock_vacancy_service):
        vacancy_id = "vac-unexpected"
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = {"unexpected": True}
        response = test_client.get(f"/candidates/{vacancy_id}")
        assert response.status_code == 200
        assert response.json() == {"unexpected": True}

def test_get_candidates_missing_reviewer_email(test_client, mock_vacancy_service):
        vacancy_id = "vac-missing-email"
        expected_response = {"candidates": ["no email"]}
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response
        response = test_client.get(f"/candidates/{vacancy_id}")
        assert response.status_code == 200
        assert response.json() == expected_response
        mock_vacancy_service.get_candidates_by_vacancy_id.assert_called_once_with(
            vacancy_id=vacancy_id,
            reviewer_email="",
            radius_override=None
        )

def test_get_candidates_radius_float(test_client, mock_vacancy_service):
    vacancy_id = "vac-float"
    reviewer_email = "<EMAIL>"
    radius = 12.5
    expected_response = {"candidates": ["float radius"]}
    mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response
    response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}&radius={radius}")
    assert response.status_code == 422
    # FastAPI returns 422 for invalid type (float for int)

def test_get_candidates_radius_string_number(test_client, mock_vacancy_service):
        vacancy_id = "vac-strnum"
        reviewer_email = "<EMAIL>"
        radius = "10.0"
        expected_response = {"candidates": ["string radius"]}
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response
        response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}&radius={radius}")
        assert response.status_code == 200
        assert response.json() == expected_response
        mock_vacancy_service.get_candidates_by_vacancy_id.assert_called_once_with(
            vacancy_id=vacancy_id,
            reviewer_email=reviewer_email,
            radius_override="10.0"
        )

def test_get_candidates_reviewer_email_none(test_client, mock_vacancy_service):
        vacancy_id = "vac-email-none"
        expected_response = {"candidates": ["none email"]}
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response
        response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email=")
        assert response.status_code == 200
        assert response.json() == expected_response
        mock_vacancy_service.get_candidates_by_vacancy_id.assert_called_once_with(
            vacancy_id=vacancy_id,
            reviewer_email="",
            radius_override=None
        )

def test_get_candidates_radius_none_explicit(test_client, mock_vacancy_service):
    vacancy_id = "vac-radius-none"
    reviewer_email = "<EMAIL>"
    expected_response = {"candidates": ["radius none"]}
    mock_vacancy_service.get_candidates_by_vacancy_id.return_value = expected_response
    response = test_client.get(f"/candidates/{vacancy_id}?reviewer_email={reviewer_email}&radius=")
    assert response.status_code == 422
    # FastAPI returns 422 for empty string to int param

def test_get_candidates_service_returns_error_dict(test_client, mock_vacancy_service):
        vacancy_id = "vac-error-dict"
        mock_vacancy_service.get_candidates_by_vacancy_id.return_value = {"error": "something went wrong"}
        response = test_client.get(f"/candidates/{vacancy_id}")
        assert response.status_code == 200
        assert response.json() == {"error": "something went wrong"}

def test_get_catalyst_match_status_success(test_client, mock_vacancy_service):
    mock_vacancy_service.get_catalyst_match_status_for_vacancy.return_value = {
        "catalyst_match_status_data": {
            "status": "completed",
            "initiated_at": "2024-07-01T10:00:00",
            "completed_at": "2024-07-01T10:30:00",
            "initiated_by": "<EMAIL>"
        },
        "update_timestamps": {
            "data_last_updated_at": "2024-07-01T09:50:00",
            "archived": False
        }
    }

    response = test_client.get("/vacancies/test-vacancy-id/catalystmatchstatus")
    assert response.status_code == 200
    data = response.json()

    assert data["catalyst_match_status"]["status"] == "completed"
    assert data["catalyst_match_status"]["initiated_by"] == "<EMAIL>"
    assert data["update_timestamps"]["archived"] is False

def test_get_catalyst_match_status_error_response(test_client, mock_vacancy_service):
    mock_vacancy_service.get_catalyst_match_status_for_vacancy.return_value = {
        "error": "Vacancy not found",
        "status_code": 404
    }

    response = test_client.get("/vacancies/invalid-id/catalystmatchstatus")
    assert response.status_code == 404
    error_detail = response.json()["detail"]["error"]
    assert error_detail["message"] == "Vacancy not found"
    assert error_detail["code"] == "404"

def test_get_catalyst_match_status_invalid_date(test_client, mock_vacancy_service):
    mock_vacancy_service.get_catalyst_match_status_for_vacancy.return_value = {
        "catalyst_match_status_data": {
            "status": "completed",
            "initiated_at": "invalid-date-format",
            "completed_at": "2024-07-01T10:30:00",
            "initiated_by": "<EMAIL>"
        },
        "update_timestamps": {
            "data_last_updated_at": "2024-07-01T09:50:00",
            "archived": True
        }
    }

    response = test_client.get("/vacancies/test-vacancy-id/catalystmatchstatus")
    assert response.status_code == 500
    error_detail = response.json()["detail"]["error"]
    assert error_detail["message"] == "Invalid date format received for timestamps."
    assert error_detail["code"] == "500"

def test_regenerate_catalyst_match_success(test_client, mock_vacancy_service):
    vacancy_id = "6f8a1ec1-5694-4bfa-b9c9-f9c5c037a6e3"
    payload = {"reviewer_email": "<EMAIL>"}

    mock_vacancy_service.regenerate_catalyst_match.return_value = {
        "status": "queued",
        "initiated_at": "2024-07-01T10:00:00",
        "completed_at": None,
        "initiated_by": "<EMAIL>"
    }

    response = test_client.post(f"/vacancies/{vacancy_id}/regenerate-catalyst-match", json=payload)

    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "queued"
    assert data["initiated_by"] == "<EMAIL>"

def test_regenerate_catalyst_match_invalid_uuid(test_client):
    invalid_vacancy_id = "not-a-valid-uuid"
    payload = {"reviewer_email": "<EMAIL>"}

    response = test_client.post(f"/vacancies/{invalid_vacancy_id}/regenerate-catalyst-match", json=payload)

    assert response.status_code == 400
    assert response.json()["detail"] == "Invalid vacancy_id format. Must be a valid UUID."

def test_regenerate_catalyst_match_conflict(test_client, mock_vacancy_service):
    vacancy_id = "6f8a1ec1-5694-4bfa-b9c9-f9c5c037a6e3"
    payload = {"reviewer_email": "<EMAIL>"}

    mock_vacancy_service.regenerate_catalyst_match.return_value = {
        "error": "A job is already in progress",
        "status_code": 409
    }

    response = test_client.post(f"/vacancies/{vacancy_id}/regenerate-catalyst-match", json=payload)

    assert response.status_code == 409
    error_detail = response.json()["detail"]["error"]
    assert error_detail["message"] == "A job is already in progress"
    assert error_detail["code"] == "409"

def test_regenerate_catalyst_match_internal_error(test_client, mock_vacancy_service):
    vacancy_id = "6f8a1ec1-5694-4bfa-b9c9-f9c5c037a6e3"
    payload = {"reviewer_email": "<EMAIL>"}

    mock_vacancy_service.regenerate_catalyst_match.return_value = {
        "error": "Something went wrong"
    }

    response = test_client.post(f"/vacancies/{vacancy_id}/regenerate-catalyst-match", json=payload)

    assert response.status_code == 500
    error_detail = response.json()["detail"]["error"]
    assert error_detail["message"] == "Something went wrong"
    assert error_detail["code"] == "500"

def test_regenerate_catalyst_match_missing_payload(test_client):
    vacancy_id = "6f8a1ec1-5694-4bfa-b9c9-f9c5c037a6e3"

    response = test_client.post(f"/vacancies/{vacancy_id}/regenerate-catalyst-match", json={})

    assert response.status_code == 422
    assert "reviewer_email" in str(response.content)


def test_catalyst_match_stats_individual_success(test_client, mock_vacancy_service):
    mock_vacancy_service.get_match_results_stats.return_value = {
        "match_results_stats": [
            {
                "category_name": "Technology",
                "subcategory_name": "Software Development",
                "refno": "CR/12345",
                "total_found": 150,
                "total_rated": 45,
                "liked": 30,
                "disliked": 15,
                "vacancy_id": "123e4567-e89b-12d3-a456-426614174000",
                "search_match_status": "completed",
                "created_at": "2024-01-01T12:00:00Z",
                "updated_at": "2024-01-01T13:00:00Z",
                "search_match_completed_at": "2024-01-01T12:30:00Z",
                "search_match_metadata": {
                    "initiated_by": "<EMAIL>"
                }
            }
        ],
        "total_count": 1,
        "aggregated": False,
        "status_code": 200
    }

    response = test_client.get("/catalystmatch/stats?aggregate_by_category=false")
    assert response.status_code == 200
    data = response.json()
    assert data["aggregated"] is False
    assert data["match_results_stats"][0]["category_name"] == "Technology"

def test_catalyst_match_stats_aggregated_success(test_client, mock_vacancy_service):
    mock_vacancy_service.get_match_results_stats.return_value = {
        "match_results_stats": [
            {
                "category_name": "Technology",
                "subcategory_name": "Software Development",
                "vacancy_count": 5,
                "total_found": 750,
                "total_rated": 225,
                "liked": 150,
                "disliked": 75
            }
        ],
        "total_count": 1,
        "aggregated": True,
        "status_code": 200
    }

    response = test_client.get("/catalystmatch/stats?aggregate_by_category=true")
    assert response.status_code == 200
    data = response.json()
    assert data["aggregated"] is True
    assert data["match_results_stats"][0]["vacancy_count"] == 5

@pytest.fixture
def valid_payload():
    return {
        "reviewer_email": "<EMAIL>",
        "vacancy_id": str(uuid4()),
        "candidate_id": str(uuid4())
    }

def test_shortlist_success(test_client, mock_vacancy_service, valid_payload):
    mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
        "success": True,
        "status_code": 200,
        "message": "Candidate shortlisted successfully",
        "vacancy_id": valid_payload["vacancy_id"],
        "candidate_id": valid_payload["candidate_id"],
        "shortlisted_data": {
            "status": "success",
            "shortlisted_by": valid_payload["reviewer_email"],
            "shortlisted_at": "2024-01-01T12:00:00Z",
            "crimson_vacancycandidateid": "abc123"
        }
    }

    response = test_client.post("/vacancies/shortlist", json=valid_payload)
    assert response.status_code == 200
    assert response.json()["success"] is True

def test_shortlist_bad_request(test_client, mock_vacancy_service, valid_payload):
    mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
        "success": False,
        "status_code": 400,
        "message": "Invalid reviewer email",
        "details": "Dataverse operation failed",
        "vacancy_id": valid_payload["vacancy_id"],
        "candidate_id": valid_payload["candidate_id"],
        "shortlisted_data": {
            "status": "failed",
            "shortlisted_by": valid_payload["reviewer_email"],
            "shortlisted_at": "2024-01-01T12:00:00Z",
            "crimson_vacancycandidateid": None
        }
    }

    response = test_client.post("/vacancies/shortlist", json=valid_payload)
    assert response.status_code == 400
    assert response.json()["detail"]["message"] == "Invalid reviewer email"


def test_shortlist_conflict(test_client, mock_vacancy_service, valid_payload):
    mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
        "success": False,
        "status_code": 409,
        "message": "Candidate is already shortlisted for this vacancy",
        "details": "Already shortlisted",
        "vacancy_id": valid_payload["vacancy_id"],
        "candidate_id": valid_payload["candidate_id"],
        "shortlisted_data": {
            "status": "success",
            "shortlisted_by": valid_payload["reviewer_email"],
            "shortlisted_at": "2024-01-01T12:00:00Z",
            "crimson_vacancycandidateid": "abc123"
        }
    }

    response = test_client.post("/vacancies/shortlist", json=valid_payload)
    assert response.status_code == 409
    assert "already" in response.json()["detail"]["message"].lower()


def test_shortlist_internal_server_error(test_client, mock_vacancy_service, valid_payload):
    mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
        "success": False,
        "status_code": 500,
        "message": "Internal server error while shortlisting candidate",
        "details": "Unexpected error occurred",
        "vacancy_id": valid_payload["vacancy_id"],
        "candidate_id": valid_payload["candidate_id"],
        "shortlisted_data": None
    }

    response = test_client.post("/vacancies/shortlist", json=valid_payload)
    assert response.status_code == 500
    assert "internal server error" in response.json()["detail"]["message"].lower()


def test_shortlist_string_status_code_handling(test_client, mock_vacancy_service, valid_payload):
    mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
        "success": False,
        "status_code": "oops",
        "message": "Dataverse failure",
        "details": "Invalid status code format",
        "vacancy_id": valid_payload["vacancy_id"],
        "candidate_id": valid_payload["candidate_id"],
        "shortlisted_data": None
    }

    response = test_client.post("/vacancies/shortlist", json=valid_payload)
    assert response.status_code == 500  # fallback
    assert response.json()["detail"]["message"] == "Dataverse failure"

def test_shortlist_service_returns_none(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = None
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_shortlist_service_raises_exception(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.side_effect = Exception("DB error")
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_shortlist_missing_message_key(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
            "success": False,
            "status_code": 400,
            "details": "Dataverse operation failed",
            "vacancy_id": valid_payload["vacancy_id"],
            "candidate_id": valid_payload["candidate_id"],
            "shortlisted_data": None
        }
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        assert response.status_code == 400
        assert response.json()["detail"]["message"] == "Unknown error occurred"

def test_shortlist_missing_status_code_key(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
            "success": False,
            "message": "Dataverse failure",
            "details": "Missing status code",
            "vacancy_id": valid_payload["vacancy_id"],
            "candidate_id": valid_payload["candidate_id"],
            "shortlisted_data": None
        }
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        assert response.status_code == 500
        assert response.json()["detail"]["message"] == "Dataverse failure"

def test_shortlist_returns_string(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = "unexpected string"
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_shortlist_partial_success_missing_fields(test_client, mock_vacancy_service, valid_payload):
        mock_vacancy_service.shortlist_candidate_for_vacancy.return_value = {
            "success": True,
            "status_code": 200
            # missing message, vacancy_id, candidate_id, shortlisted_data
        }
        response = test_client.post("/vacancies/shortlist", json=valid_payload)
        # The route currently returns 500 for missing fields
        assert response.status_code == 500
        assert "Internal Server Error" in response.text

def test_update_candidate_fitness_reason_success(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Good fit",
        "author_email": "<EMAIL>"
    }
    expected_response = {"success": True, "message": "Fitness reason updated"}
    mock_vacancy_service.update_candidate_fitness_reason.return_value = expected_response
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 200
    assert response.json() == expected_response
    mock_vacancy_service.update_candidate_fitness_reason.assert_called_once()

def test_update_candidate_fitness_reason_service_error(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Bad fit",
        "author_email": "<EMAIL>"
    }
    mock_vacancy_service.update_candidate_fitness_reason.return_value = {
        "error": "Update failed",
        "status_code": 400,
        "details": "Candidate locked"
    }
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 400
    assert response.json()["detail"]["message"] == "Update failed"
    assert response.json()["detail"]["details"] == "Candidate locked"

def test_update_candidate_fitness_reason_service_error_string_status_code(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Bad fit",
        "author_email": "<EMAIL>"
    }
    mock_vacancy_service.update_candidate_fitness_reason.return_value = {
        "error": "Update failed",
        "status_code": "401",
        "details": "Candidate locked"
    }
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 401
    assert response.json()["detail"]["message"] == "Update failed"

def test_update_candidate_fitness_reason_service_error_nonint_status_code(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Bad fit",
        "author_email": "<EMAIL>"
    }
    mock_vacancy_service.update_candidate_fitness_reason.return_value = {
        "error": "Update failed",
        "status_code": "not_a_number",
        "details": "Candidate locked"
    }
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 500
    assert response.json()["detail"]["message"] == "Update failed"

def test_update_candidate_fitness_reason_service_error_missing_status_code(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Bad fit",
        "author_email": "<EMAIL>"
    }
    mock_vacancy_service.update_candidate_fitness_reason.return_value = {
        "error": "Update failed",
        "details": "Candidate locked"
    }
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 500
    assert response.json()["detail"]["message"] == "Update failed"

def test_update_candidate_fitness_reason_service_returns_none(test_client, mock_vacancy_service):
    payload = {
        "vacancy_refno": "CR/12345",
        "candidate_contact_id": "contact-123",
        "fitness_reason_text": "Bad fit",
        "author_email": "<EMAIL>"
    }
    mock_vacancy_service.update_candidate_fitness_reason.return_value = None
    response = test_client.post("/candidates/fitness_reason", json=payload)
    assert response.status_code == 500
    assert "Internal Server Error" in response.text
    
def test_get_all_subcategories_success(test_client, mock_subcategory_service):
    # Arrange
    mock_subcategory_service.get_subcategories.return_value = {"subcategories": ["A", "B"]}
    # Act
    response = test_client.get("/subcategories")
    # Assert
    assert response.status_code == 200
    assert response.json() == {"subcategories": ["A", "B"]}
    mock_subcategory_service.get_subcategories.assert_called_once()
    
    
def test_get_all_subcategories_empty(test_client, mock_subcategory_service):
    # Arrange
    mock_subcategory_service.get_subcategories.return_value = {"subcategories": []}
    # Act
    response = test_client.get("/subcategories")
    # Assert
    assert response.status_code == 200
    assert response.json() == {"subcategories": []}
    mock_subcategory_service.get_subcategories.assert_called_once()    
    
    
def test_get_all_subcategories_exception(test_client, mock_subcategory_service):
    # Arrange: Mock the service to raise an exception
    mock_subcategory_service.get_subcategories.side_effect = Exception("Error fetching subcategories")
    
    # Act: Send the GET request to the route
    response = test_client.get("/subcategories")
    print("Response text:", response.text)    
    # Assert: Check that the status code is 500 and the error message is correct
    assert response.status_code == 500  # Expecting 500 due to the exception
    response_data = response.json()
    
    # Check for the 'error' key, since the response contains this key, not 'detail'
    assert "Error fetching subcategories" in response_data["detail"]

def test_get_subcategory_pools_success(test_client, mock_subcategory_service):
    # Arrange
    mock_subcategory_service.get_pool_data.return_value = [{"id": 1, "name": "Pool1"}]

    # Act
    response = test_client.get("/subcategory/pools")

    # Assert
    assert response.status_code == 200
    assert response.json() == [{"id": 1, "name": "Pool1"}]
    mock_subcategory_service.get_pool_data.assert_called_once()


def test_get_subcategory_pools_empty(test_client, mock_subcategory_service):
    # Arrange
    mock_subcategory_service.get_pool_data.return_value = []

    # Act
    response = test_client.get("/subcategory/pools")

    # Assert
    assert response.status_code == 200
    assert response.json() == []
    mock_subcategory_service.get_pool_data.assert_called_once()
    
    
def test_get_subcategory_pools_exception(test_client, mock_subcategory_service):
    # Arrange
    mock_subcategory_service.get_pool_data.side_effect = Exception("DB error")

    # Act
    response = test_client.get("/subcategory/pools")

    # Assert
    assert response.status_code == 500
    assert "DB error" in response.json()["detail"]


def test_get_candidate_stats_success(test_client):
    # Arrange
    stats_data = {"total": 100, "active": 80}
    with patch("dpserver.apis.routes.CandidateStatsService") as MockService:
        instance = MockService.return_value
        instance.get_all_stats.return_value = stats_data

        # Act
        response = test_client.get("/api/candidate-stats")

        # Assert
        assert response.status_code == 200
        assert response.json() == {"stats": stats_data}
        instance.get_all_stats.assert_called_once()
        instance.close.assert_called_once()
        
def test_get_candidate_stats_exception(test_client, mock_logger):
    # Arrange
    with patch("dpserver.apis.routes.CandidateStatsService") as MockService:
        instance = MockService.return_value
        instance.get_all_stats.side_effect = Exception("DB error")

        # Act
        response = test_client.get("/api/candidate-stats")

        # Assert
        assert response.status_code == 500
        assert response.json()["detail"] == "Error fetching candidate statistics"
        instance.close.assert_called_once()
        mock_logger.exception.assert_called()

def test_get_all_subcategory_weight_configs_success(test_client, mock_subcategory_service):
    # Arrange
    expected_data = {
        "1": {"subcategory_id": 1, "subcategory_name": "A", "weight": 10},
        "2": {"subcategory_id": 2, "subcategory_name": "B", "weight": 20}
    }
    mock_subcategory_service.get_all_subcategory_weight_configs.return_value = expected_data

    # Act
    response = test_client.get("/v1/subcategory/weight-configs")

    # Assert
    assert response.status_code == 200
    assert response.json() == expected_data
    mock_subcategory_service.get_all_subcategory_weight_configs.assert_called_once()
    #mock_logger.info.assert_called_with("Fetching all subcategory weight configurations")
    
def test_get_all_subcategory_weight_configs_empty(test_client, mock_subcategory_service):
    #Arrange
    expected_data = {}
   
    # Act    
    mock_subcategory_service.get_all_subcategory_weight_configs.return_value = expected_data
    response = test_client.get("/v1/subcategory/weight-configs")
 
    # Assert    
    assert response.status_code == 200
    assert response.json() == expected_data    
    
def test_get_all_subcategory_weight_configs_error(test_client, mock_subcategory_service):
    # Arrange: Mock the service to raise an exception
    mock_subcategory_service.get_all_subcategory_weight_configs.side_effect = Exception("DB error")

    # Act: Send the GET request to the route
    response = test_client.get("/v1/subcategory/weight-configs")

    # Assert: Check that the status code is 500 and the error message is correct
    assert response.status_code == 500
    response_data = response.json()
    assert "DB error" in response_data["detail"]
    
    
def test_update_subcategory_weight_config_success(test_client, mock_subcategory_service):
    # Arrange
    subcategory_id = 1
    updates = {"total_score_weight": 0.8}
    expected_response = {"success": True}
    mock_subcategory_service.update_subcategory_weight_config.return_value = expected_response

    # Act
    response = test_client.put(f"/v1/subcategory/weight-configs/{subcategory_id}", json=updates)

    # Assert
    assert response.status_code == 200
    assert response.json() == expected_response
    mock_subcategory_service.update_subcategory_weight_config.assert_called_once_with(
        subcategory_id=subcategory_id,
        updates=updates,
        updated_by="api_user"
    )    
    
def test_update_subcategory_weight_config_invalid_total(test_client, mock_subcategory_service):
    # Arrange
    subcategory_id = 2
    updates = {"total_score_weight": 1.5}
    mock_subcategory_service.update_subcategory_weight_config.side_effect = ValueError("Total weight exceeds 1")

    # Act
    response = test_client.put(f"/v1/subcategory/weight-configs/{subcategory_id}", json=updates)

    # Assert
    assert response.status_code == 500
    assert "Total weight exceeds 1" in response.text

def test_update_subcategory_weight_config_exception(test_client, mock_subcategory_service):
    # Arrange
    subcategory_id = 3
    updates = {"total_score_weight": 0.5}
    mock_subcategory_service.update_subcategory_weight_config.side_effect = Exception("DB error")

    # Act
    response = test_client.put(f"/v1/subcategory/weight-configs/{subcategory_id}", json=updates)

     # Assert
    response_data = response.json()    
    assert response.status_code == 500
    assert "DB error" in response_data["detail"]
        
#Endpoint: - /attributes/{subcategory_id}/update

def get_attribute_service(test_client,mock_attribute_service):
    return mock_attribute_service

def test_update_attributes_weights_success(test_client,mock_attribute_service):
    #client, router_instance = test_client
    subcategory_id = 1
    new_weights = {"attr1": 0.5, "attr2": 0.5}
    attribute_service = get_attribute_service(test_client,mock_attribute_service)
    attribute_service.update_attributes_weights.return_value = {"success": True}

    response = test_client.post(f"/attributes/{subcategory_id}/update", json=new_weights)

    assert response.status_code == 200
    assert response.json() == {"success": True}
    attribute_service.update_attributes_weights.assert_called_once_with(subcategory_id, new_weights)
        
def test_update_attributes_weights_error(test_client,mock_attribute_service):
    subcategory_id = 2
    new_weights = {"attr1": 1.2, "attr2": -0.2}
    attribute_service = get_attribute_service(test_client,mock_attribute_service)
    attribute_service.update_attributes_weights.return_value = {"error": "Invalid weights"}

    response = test_client.post(f"/attributes/{subcategory_id}/update", json=new_weights)

    assert response.status_code == 200
    assert response.json() == {"error": "Invalid weights"}
    attribute_service.update_attributes_weights.assert_called_once_with(subcategory_id, new_weights)

def test_update_attributes_weights_exception(test_client,mock_attribute_service):
    subcategory_id = 3
    new_weights = {"attr1": 0.5}
    mock_attribute_service = get_attribute_service(test_client,mock_attribute_service)
    mock_attribute_service.update_attributes_weights.side_effect = Exception("DB error")

    response = test_client.post(f"/attributes/{subcategory_id}/update", json=new_weights)

    # FastAPI returns 500 on unhandled exceptions by default
    assert response.status_code == 500
    assert "DB error" in response.text or "Internal Server Error" in response.text
    mock_attribute_service.update_attributes_weights.assert_called_once_with(subcategory_id, new_weights)

#Endpoint: - /attribute/delete/{attribute_id}    

def test_delete_attribute_by_id_success(test_client,mock_attribute_service):
    client = test_client
    attribute_id = 123
    # Arrange: set up the mock to return a success response
    mock_attribute_service.delete_attribute_by_id.return_value = {"deleted": True}

    # Act
    response = client.delete(f"/attribute/delete/{attribute_id}")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"deleted": True}
    mock_attribute_service.delete_attribute_by_id.assert_called_once_with(attribute_id)

def test_delete_attribute_by_id_not_found(test_client,mock_attribute_service):
    client = test_client
    attribute_id = 999
    # Arrange: set up the mock to return a not found response
    mock_attribute_service.delete_attribute_by_id.return_value = {"error": "Attribute not found"}

    # Act
    response = client.delete(f"/attribute/delete/{attribute_id}")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"error": "Attribute not found"}
    mock_attribute_service.delete_attribute_by_id.assert_called_once_with(attribute_id)
        
def test_delete_attribute_by_id_exception(test_client,mock_attribute_service):
    client = test_client
    attribute_id = 555
    # Arrange: set up the mock to raise an exception
    mock_attribute_service.delete_attribute_by_id.side_effect = Exception("DB error")

    # Act
    response = client.delete(f"/attribute/delete/{attribute_id}")

    # Assert
    assert response.status_code == 500
    assert "DB error" in response.text or "Internal Server Error" in response.text
    mock_attribute_service.delete_attribute_by_id.assert_called_once_with(attribute_id)


#Endpoint: - /weights/{sub_category_id}

def test_get_weights_for_subcategory_success(test_client,mock_subcategory_service):
    client = test_client
    subcategory_id = 10
    expected_result = {"weights": {"attr1": 0.5, "attr2": 0.5}}
    mock_subcategory_service.get_weights_for_subcategory.return_value = expected_result

    response = client.get(f"/weights/{subcategory_id}")

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_subcategory_service.get_weights_for_subcategory.assert_called_once_with(subcategory_id)

def test_get_weights_for_subcategory_not_found(test_client,mock_subcategory_service):
    client = test_client
    subcategory_id = 999
    expected_result = {"error": "Subcategory not found"}
    mock_subcategory_service.get_weights_for_subcategory.return_value = expected_result

    response = client.get(f"/weights/{subcategory_id}")

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_subcategory_service.get_weights_for_subcategory.assert_called_once_with(subcategory_id)

def test_get_weights_for_subcategory_exception(test_client, mock_subcategory_service):
    client = test_client
    subcategory_id = 42
    mock_subcategory_service.get_weights_for_subcategory.side_effect = Exception("DB error")

    response = client.get(f"/weights/{subcategory_id}")

    assert response.status_code == 500
    assert "DB error" in response.text or "Internal Server Error" in response.text
    mock_subcategory_service.get_weights_for_subcategory.assert_called_once_with(subcategory_id)

#Endpoint: - /attributes/{attribute_id}/subcategory

def test_change_subcategory_of_attribute_success(test_client, mock_attribute_service):
    client = test_client
    attribute_id = 5
    payload = {"subcategory_id": 20}
    expected_result = {"changed": True}
    mock_attribute_service.change_subcategory_of_attribute.return_value = expected_result

    response = client.put(f"/attributes/{attribute_id}/subcategory", json=payload)

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_attribute_service.change_subcategory_of_attribute.assert_called_once_with(attribute_id, payload)

def test_change_subcategory_of_attribute_not_found(test_client, mock_attribute_service):
    client = test_client
    attribute_id = 999
    payload = {"subcategory_id": 30}
    expected_result = {"error": "Attribute not found"}
    mock_attribute_service.change_subcategory_of_attribute.return_value = expected_result

    response = client.put(f"/attributes/{attribute_id}/subcategory", json=payload)

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_attribute_service.change_subcategory_of_attribute.assert_called_once_with(attribute_id, payload)

def test_change_subcategory_of_attribute_exception(test_client, mock_attribute_service):
    client = test_client
    attribute_id = 7
    payload = {"subcategory_id": 40}
    mock_attribute_service.change_subcategory_of_attribute.side_effect = Exception("DB error")

    response = client.put(f"/attributes/{attribute_id}/subcategory", json=payload)

    assert response.status_code == 500
    assert "DB error" in response.text or "Internal Server Error" in response.text
    mock_attribute_service.change_subcategory_of_attribute.assert_called_once_with(attribute_id, payload)

#Endpoint: -/attributes/{attribute_id}/approval

def test_set_attribute_approval_status_success(test_client, mock_attribute_service):
    client = test_client
    attribute_id = 11
    payload = {"approved": True}
    expected_result = {"status": "approved"}
    mock_attribute_service.set_attribute_approval_status.return_value = expected_result

    response = client.put(f"/attributes/{attribute_id}/approval", json=payload)

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_attribute_service.set_attribute_approval_status.assert_called_once_with(attribute_id, payload)

def test_set_attribute_approval_status_not_found(test_client,mock_attribute_service):
    client = test_client
    attribute_id = 404
    payload = {"approved": False}
    expected_result = {"error": "Attribute not found"}
    mock_attribute_service.set_attribute_approval_status.return_value = expected_result

    response = client.put(f"/attributes/{attribute_id}/approval", json=payload)

    assert response.status_code == 200
    assert response.json() == expected_result
    mock_attribute_service.set_attribute_approval_status.assert_called_once_with(attribute_id, payload)

def test_set_attribute_approval_status_exception(test_client, mock_attribute_service):
    client = test_client
    attribute_id = 500
    payload = {"approved": True}
    mock_attribute_service.set_attribute_approval_status.side_effect = Exception("DB error")

    response = client.put(f"/attributes/{attribute_id}/approval", json=payload)

    assert response.status_code == 500
    assert "DB error" in response.text or "Internal Server Error" in response.text
    mock_attribute_service.set_attribute_approval_status.assert_called_once_with(attribute_id, payload)

    assert "Internal Server Error" in response.text
