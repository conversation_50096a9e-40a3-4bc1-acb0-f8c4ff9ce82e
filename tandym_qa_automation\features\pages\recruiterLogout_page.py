from features.pages.recruiterLogin_page import load_regression_key_from_file
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

user_profile_icon = (By.XPATH, "//*[contains(@id, 'header-avatar-text')]")
logout_option = (By.XPATH, "//*[contains(@id, 'logout-dropdown')]")

def set_is_active_false(context):
    """Set is_active to false for the regression key in the database."""
    try:
        # If the regression_key is missing in context, load from file
        if not hasattr(context, "regression_key"):
            context.regression_key = load_regression_key_from_file()

        cursor = context.db_cursor
        update_query = """
            UPDATE tandymrecport.rp_regression_keys
            SET is_active = %s
            WHERE regression_key = %s
        """
        cursor.execute(update_query, (False, context.regression_key))
        context.db_connection.commit()
        context.logger.info("*********** Successfully set is_active to false for the regression key. ***********")
    except Exception as e:
        context.db_connection.rollback()
        context.logger.error(f"************* Failed to set is_active to false: {e} *************")
        raise

def click_user_profile_icon(context):
    try:
        context.logger.info("🔍 Attempting to click the user profile icon...")
        WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable(user_profile_icon)
        ).click()
        context.logger.info("************* User profile icon clicked successfully. *************")
    except Exception as e:
        context.logger.error(f"************* Failed to click user profile icon: {e} *************")
        raise

def click_logout_button(context):
    try:
        context.logger.info("************ Attempting to click the Logout button... ************")
        WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable(logout_option)
        ).click()
        context.logger.info("************ Logout button clicked successfully. ************")
    except Exception as e:
        context.logger.error(f"************* Failed to click Logout button: {e} *************")
        raise

