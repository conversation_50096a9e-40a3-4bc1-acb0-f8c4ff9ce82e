"use client";

import { useEffect, useState, useRef } from "react";
import { useSession } from "next-auth/react";
import { fetchEntitlements } from "@/api/serverActions";

interface IframeAuthGateProps {
  children: React.ReactNode;
  onAuthComplete?: (isAuthenticated: boolean) => void; // callback
}

export default function IframeAuthGate({
  children,
  onAuthComplete,
}: IframeAuthGateProps) {
  const { data: session, status } = useSession();
  const [authComplete, setAuthComplete] = useState(false);
  const popupRef = useRef<Window | null>(null);
  const emailId = session?.user?.email || null;
  const isIframe = typeof window !== "undefined" && window.self !== window.top;
  localStorage?.setItem("email", session?.user?.email || "null");

  useEffect(() => {
    const handler = async (event: MessageEvent) => {
      console.log("Received message event:", event);
      if (
        event.data === "authcomplete" ||
        event.data?.type === "authcomplete"
      ) {
        onAuthComplete?.(true);
        setAuthComplete(true);
        return <>{children}</>;
        // Try fetching session manually
      }
    };

    window.addEventListener("message", handler);
    return () => window.removeEventListener("message", handler);
  }, [onAuthComplete]);
  useEffect(() => {
    window.parent.frames.postMessage(
      {
        status: status,
        email: emailId,
        timestamp: Date.now(),
      },
      "*"
    );

    if (status !== "authenticated") {
      setAuthComplete(false);
    }
    if (status === "authenticated") {
      onAuthComplete?.(true);
      setAuthComplete(true);
      const userEmail = emailId || session?.user?.email || "";

      fetchEntitlements(userEmail)
        .then((res) => {
          if (res === false) {
            console.error("No entitlement response");
            return;
          }

          if (res.error) {
            console.error("Entitlement error:", res.error);
            return;
          }
          localStorage.setItem("emailId", userEmail);
          setAuthComplete(true);
        })
        .catch((error) => {
          console.error("Failed to fetch entitlements:", error);
        })
        .finally(() => {});
      // Send message to parent window
    }
  }, [status, onAuthComplete]);

  useEffect(() => {
    if (authComplete && isIframe) {
      // Notify parent window that auth is complete
      window.parent.postMessage({ type: "authComplete" }, "*");
      window.parent.postMessage({ type: "authenticated" }, "*");
      setAuthComplete(true);
      onAuthComplete?.(true);
    }
  }, [authComplete, isIframe]);

  const openLoginPopup = () => {
    const width = 600;
    const height = 700;
    const left = window.innerWidth / 2 - width / 2;
    const top = window.innerHeight / 2 - height / 2;

    popupRef.current = window.open(
      "/api/auth/signin/azure-ad?callbackUrl=/authcomplete",
      "MicrosoftLogin",
      `width=${width},height=${height},left=${left},top=${top}`
    );
  };

  if (!authComplete) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-white text-center">
        <h1 className="text-2xl font-bold mb-4">
          For your security, please authenticate before proceeding.
        </h1>
        <button
          onClick={openLoginPopup}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded"
        >
          Secure this Session
        </button>
      </div>
    );
  }

  return <>{children}</>;
}
