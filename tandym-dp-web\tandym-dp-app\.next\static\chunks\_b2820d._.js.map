{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/library/utils';\r\n\r\nconst buttonVariants = cva(\r\n  'inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primaryButton text-primaryButton-foreground shadow hover:bg-primaryButton/90 active:bg-primaryButton/80',\r\n        destructive:\r\n          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',\r\n        outline:\r\n          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2',\r\n        sm: 'h-8 rounded-md px-3 text-xs',\r\n        lg: 'h-10 rounded-md px-8',\r\n        icon: 'h-9 w-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : 'button';\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = 'Button';\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,sRACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/drawer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Drawer as DrawerPrimitive } from \"vaul\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Drawer = ({\r\n  shouldScaleBackground = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\r\n  <DrawerPrimitive.Root\r\n    shouldScaleBackground={shouldScaleBackground}\r\n    {...props}\r\n  />\r\n);\r\nDrawer.displayName = \"Drawer\";\r\n\r\nconst DrawerTrigger = DrawerPrimitive.Trigger;\r\n\r\nconst DrawerPortal = DrawerPrimitive.Portal;\r\n\r\nconst DrawerClose = DrawerPrimitive.Close;\r\n\r\nconst DrawerOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName;\r\n\r\nconst DrawerContent = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DrawerPortal>\r\n    <DrawerOverlay />\r\n    <DrawerPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\r\n      {children}\r\n    </DrawerPrimitive.Content>\r\n  </DrawerPortal>\r\n));\r\nDrawerContent.displayName = \"DrawerContent\";\r\n\r\nconst DrawerHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDrawerHeader.displayName = \"DrawerHeader\";\r\n\r\nconst DrawerFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDrawerFooter.displayName = \"DrawerFooter\";\r\n\r\nconst DrawerTitle = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName;\r\n\r\nconst DrawerDescription = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Drawer,\r\n  DrawerPortal,\r\n  DrawerOverlay,\r\n  DrawerTrigger,\r\n  DrawerClose,\r\n  DrawerContent,\r\n  DrawerHeader,\r\n  DrawerFooter,\r\n  DrawerTitle,\r\n  DrawerDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,SAAS,CAAC,EACd,wBAAwB,IAAI,EAC5B,GAAG,OAC+C,iBAClD,6LAAC,yIAAA,CAAA,SAAe,CAAC,IAAI;QACnB,uBAAuB;QACtB,GAAG,KAAK;;;;;;KANP;AASN,OAAO,WAAW,GAAG;AAErB,MAAM,gBAAgB,yIAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,yIAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,yIAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,yIAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;;;;;;MAPP;AAUN,cAAc,WAAW,GAAG,yIAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,yIAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;gBAED,GAAG,KAAK;;kCACT,6LAAC;wBAAI,WAAU;;;;;;oBACd;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;MANP;AASN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;MANP;AASN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,yIAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,yIAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,yIAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,yIAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/TPSVGIcons.tsx"], "sourcesContent": ["\"use client\";\r\ninterface TPSvgIconProps {\r\n  width?: number;\r\n  height?: number;\r\n  background?: string;\r\n  resourceName: string;\r\n  className?: string;\r\n  text?: string;\r\n  id?: string;\r\n  dataTestId?: string;\r\n  onClick?: (text: string) => void;\r\n}\r\n\r\nconst TandymLogoIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"153\"\r\n      height=\"25\"\r\n      viewBox=\"0 0 153 25\"\r\n      fill=\"none\">\r\n      <g clipPath=\"url(#clip0_4323_1601)\">\r\n        <path\r\n          d=\"M23.4613 0.909119H14.855H9.453H0.809204V5.83052H9.453V24.4573H14.855V5.83052H23.4613V0.909119Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M52.5366 24.4573H47.1V0.909119H52.9202L60.9699 11.7001L64.2461 16.7262H64.5951L64.3501 11.9473V0.909119H69.7867V24.4573H64.001L55.6368 13.5267L52.7097 9.06197H52.3261L52.5337 13.4191V24.4544L52.5366 24.4573Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M95.1952 6.35696C94.1973 4.60015 92.7783 3.25346 90.9438 2.31689C89.1068 1.3774 86.9236 0.909119 84.3914 0.909119H73.0651V24.4573H84.3914C86.9236 24.4573 89.1068 23.989 90.9438 23.0524C92.7783 22.1159 94.1973 20.7692 95.1952 19.0095C96.1931 17.2527 96.695 15.1439 96.695 12.6832C96.695 10.2225 96.196 8.11376 95.1952 6.35696ZM90.2289 16.953C89.799 17.9507 89.1126 18.6284 88.1724 18.992C87.2322 19.3556 85.9716 19.5359 84.3914 19.5359H78.5017V5.83052H84.3914C85.9716 5.83052 87.2322 6.01376 88.1724 6.37443C89.1126 6.73801 89.799 7.40988 90.2289 8.39591C90.6583 9.38193 90.8718 10.8101 90.8718 12.6832C90.8718 14.5564 90.6554 15.9583 90.2289 16.953Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M127.956 24.4573H122.519V0.909119H129.909L135.172 12.2266L137.402 17.814H137.785L139.911 12.2266L145.033 0.909119H152.388V24.4573H146.986V12.8228L147.159 9.09688H146.775L145.417 12.8228L140.747 23.0495H134.16L129.525 12.8228L128.132 9.09688H127.748L127.956 12.8228V24.4573Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M110.351 24.4573H104.255L114.464 0.909119H120.564L110.351 24.4573Z\"\r\n          fill=\"#2A70EA\"\r\n        />\r\n        <path\r\n          d=\"M39.0014 8.21545H33.0572L33.0399 8.25326L33.5908 9.62615L35.6327 14.4748H29.6251L27.3466 19.3962H37.7007L39.8291 24.4572H46.0678L39.0014 8.21545Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M109.261 12.8781L101.532 0.909119H94.8059L102.633 12.8781H109.261Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M25.9996 24.4573H19.8997L30.1124 0.909119H36.2124L25.9996 24.4573Z\"\r\n          fill=\"#2A70EA\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_4323_1601\">\r\n          <rect\r\n            width=\"152\"\r\n            height=\"24\"\r\n            fill=\"white\"\r\n            transform=\"translate(0.5 0.5)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nconst resourceToComponentHash: {\r\n  [key: string]: React.ComponentType<TPSvgIconProps>;\r\n} = {\r\n  \"tandym-logo-icon\": TandymLogoIcon,\r\n};\r\n\r\nconst TPSvgIcon = (props: TPSvgIconProps) => {\r\n  const SvgComponent =\r\n    props.resourceName && resourceToComponentHash[props.resourceName];\r\n  return (\r\n    SvgComponent && (\r\n      <SvgComponent\r\n        width={props.width}\r\n        height={props.height}\r\n        background={props.background}\r\n        className={props.className}\r\n        text={props.text}\r\n        resourceName={props.resourceName}\r\n        id={props.id}\r\n        data-testid={props.dataTestId} // Corrected to match the prop name\r\n        onClick={props.onClick}\r\n      />\r\n    )\r\n  );\r\n};\r\n\r\nexport default TPSvgIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,iBAAiB;IACrB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;;0BACL,6LAAC;gBAAE,UAAS;;kCACV,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBACC,OAAM;wBACN,QAAO;wBACP,MAAK;wBACL,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAtDM;AAwDN,MAAM,0BAEF;IACF,oBAAoB;AACtB;AAEA,MAAM,YAAY,CAAC;IACjB,MAAM,eACJ,MAAM,YAAY,IAAI,uBAAuB,CAAC,MAAM,YAAY,CAAC;IACnE,OACE,8BACE,6LAAC;QACC,OAAO,MAAM,KAAK;QAClB,QAAQ,MAAM,MAAM;QACpB,YAAY,MAAM,UAAU;QAC5B,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI;QAChB,cAAc,MAAM,YAAY;QAChC,IAAI,MAAM,EAAE;QACZ,eAAa,MAAM,UAAU;QAC7B,SAAS,MAAM,OAAO;;;;;;AAI9B;MAlBM;uCAoBS"}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/public/assets/menuIcon.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 800, height: 800, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/public/assets/quiz.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 32, height: 32, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/HeaderTabs/WebHeaderTabs.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport TPSvgIcon from \"../TPSVGIcons\";\r\nimport Link from \"next/link\";\r\nimport { Avatar, AvatarImage, AvatarFallback } from \"../ui/avatar\";\r\nimport Image from \"next/image\";\r\nimport menuIcon from \"@/public/assets/menuIcon.svg\";\r\nimport quizIcon from \"@/public/assets/quiz.svg\";\r\nimport { TAB_ROUTE_MAP } from \"@/utils/tabRoutes\";\r\n\r\ninterface webHeaderTabsProps {\r\n  avatarImage: boolean;\r\n  avatarImageURL: string | undefined;\r\n  avatarImageAltText: string;\r\n  avatarName: string;\r\n  setIsLogOut: (value: boolean) => void;\r\n  isLogOut: boolean;\r\n  setIsMenuOpen: (value: boolean) => void;\r\n  isMenuOpen: boolean;\r\n  tabsList: () => React.ReactNode;\r\n  logoutHandler: () => void;\r\n  avatarVisible: boolean;\r\n}\r\n\r\nconst WebHeaderTabs = ({\r\n  avatarImage,\r\n  avatarImageURL,\r\n  avatarImageAltText,\r\n  avatarName,\r\n  setIsLogOut,\r\n  isLogOut,\r\n  setIsMenuOpen,\r\n  isMenuOpen,\r\n  tabsList,\r\n  logoutHandler,\r\n  avatarVisible,\r\n}: webHeaderTabsProps) => {\r\n  return (\r\n    <div className=\"shadow-sm bg-white items-center h-[3.75rem] px-4 md:px-10\">\r\n      <div className=\"flex items-center justify-between\">\r\n        {/* Mobile hamburger menu Icon */}\r\n        <div className=\"md:hidden\" onClick={() => setIsMenuOpen(!isMenuOpen)}>\r\n          <Image\r\n            src={menuIcon}\r\n            alt=\"menuIcon-header-icon\"\r\n            width={32}\r\n            height={32}\r\n            className=\"hover:cursor-pointer\"\r\n          />\r\n        </div>\r\n        {/* Tandym logo click routes to the Dashboard screen */}\r\n        <Link href={TAB_ROUTE_MAP.home} className=\"flex items-center\">\r\n          <TPSvgIcon resourceName={\"tandym-logo-icon\"} id=\"Tandym-logo\" />\r\n        </Link>\r\n        {/* Tabs list */}\r\n        <div className=\"hidden md:flex items-center justify-center gap-7 mt-5\">\r\n          {tabsList()}\r\n        </div>\r\n        <div\r\n          className={`flex items-center gap-5 visible:${\r\n            avatarVisible ? \"visible\" : \"block\"\r\n          }`}\r\n        >\r\n          {/* FAQ Image */}\r\n          <Image\r\n            src={quizIcon}\r\n            alt=\"quiz-header-icon\"\r\n            width={32}\r\n            height={32}\r\n            className=\"hover:cursor-pointer\"\r\n          />\r\n          {/* Avatar component */}\r\n          {avatarVisible && (\r\n            <Avatar\r\n              className=\"size-px-32 hover:cursor-pointer\"\r\n              id=\"header-avatar\"\r\n              onClick={() => setIsLogOut(!isLogOut)}\r\n            >\r\n              {avatarImage ? (\r\n                <AvatarImage\r\n                  src={avatarImageURL || undefined}\r\n                  alt={avatarImageAltText || \"User avatar\"}\r\n                  id=\"header-avatar-image\"\r\n                />\r\n              ) : (\r\n                avatarName.length > 0 && (\r\n                  <AvatarFallback\r\n                    className=\"bg-blue-500 text-white\"\r\n                    id=\"header-avatar-text\"\r\n                  >\r\n                    {avatarName}\r\n                  </AvatarFallback>\r\n                )\r\n              )}\r\n            </Avatar>\r\n          )}\r\n          {/* Logout popup */}\r\n          {isLogOut && avatarVisible && (\r\n            <div\r\n              className=\"hidden md:absolute bg-white border border-gray-200 cursor-pointer right-6 rounded-lg top-12 mt-1 p-2 md:block shadow-md\"\r\n              id=\"logout-dropdown\"\r\n              onClick={logoutHandler}\r\n            >\r\n              <p className=\"text-sm text-gray-700 hover:text-gray-900\">\r\n                Log out\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WebHeaderTabs;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAwBA,MAAM,gBAAgB,CAAC,EACrB,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,UAAU,EACV,WAAW,EACX,QAAQ,EACR,aAAa,EACb,UAAU,EACV,QAAQ,EACR,aAAa,EACb,aAAa,EACM;IACnB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;oBAAY,SAAS,IAAM,cAAc,CAAC;8BACvD,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,kSAAA,CAAA,UAAQ;wBACb,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;8BAId,6LAAC,+JAA<PERSON>,CAAA,UAAI;oBAAC,MAAM,qHAAA,CAAA,gBAAa,CAAC,IAAI;oBAAE,WAAU;8BACxC,cAAA,6LAAC,4HAAA,CAAA,UAAS;wBAAC,cAAc;wBAAoB,IAAG;;;;;;;;;;;8BAGlD,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBACC,WAAW,CAAC,gCAAgC,EAC1C,gBAAgB,YAAY,SAC5B;;sCAGF,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,0RAAA,CAAA,UAAQ;4BACb,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;wBAGX,+BACC,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,IAAG;4BACH,SAAS,IAAM,YAAY,CAAC;sCAE3B,4BACC,6LAAC,8HAAA,CAAA,cAAW;gCACV,KAAK,kBAAkB;gCACvB,KAAK,sBAAsB;gCAC3B,IAAG;;;;;uCAGL,WAAW,MAAM,GAAG,mBAClB,6LAAC,8HAAA,CAAA,iBAAc;gCACb,WAAU;gCACV,IAAG;0CAEF;;;;;;;;;;;wBAOV,YAAY,+BACX,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,SAAS;sCAET,cAAA,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE;KAxFM;uCA0FS"}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/HeaderTabs/MobileHeaderTabs.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { DialogTitle, DialogDescription } from \"@radix-ui/react-dialog\";\r\nimport { DrawerContent } from \"@/components/ui/drawer\";\r\nimport { Button } from \"../ui/button\";\r\n\r\ninterface MobileHeaderTabsProps {\r\n  setIsMenuOpen: (value: boolean) => void;\r\n  tabsList: () => React.ReactNode;\r\n  logoutHandler: () => void;\r\n  avatarVisible?: boolean;\r\n}\r\n\r\nconst MobileHeaderTabs = ({\r\n  setIsMenuOpen,\r\n  tabsList,\r\n  logoutHandler,\r\n  avatarVisible,\r\n}: MobileHeaderTabsProps) => {\r\n  const clickHandler = () => {\r\n    logoutHandler();\r\n    setIsMenuOpen(false);\r\n  };\r\n  return (\r\n    <DrawerContent className=\"h-screen w-5/6 rounded-none\">\r\n      <div className=\"fixed inset-0 z-50 flex flex-col justify-between ml-4\">\r\n        <div className=\"flex flex-col items-start mt-5\">\r\n          {/* Tabs List */}\r\n          <DialogTitle>{tabsList()}</DialogTitle>\r\n          <DialogDescription>{\"\"}</DialogDescription>\r\n        </div>\r\n        {/* Logout Button */}\r\n        {avatarVisible && (\r\n          <div className=\"mb-4\">\r\n            <Button variant=\"outline\" onClick={clickHandler}>\r\n              Log out\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </DrawerContent>\r\n  );\r\n};\r\n\r\nexport default MobileHeaderTabs;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAFA;;;;;AAWA,MAAM,mBAAmB,CAAC,EACxB,aAAa,EACb,QAAQ,EACR,aAAa,EACb,aAAa,EACS;IACtB,MAAM,eAAe;QACnB;QACA,cAAc;IAChB;IACA,qBACE,6LAAC,8HAAA,CAAA,gBAAa;QAAC,WAAU;kBACvB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qKAAA,CAAA,cAAW;sCAAE;;;;;;sCACd,6LAAC,qKAAA,CAAA,oBAAiB;sCAAE;;;;;;;;;;;;gBAGrB,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAc;;;;;;;;;;;;;;;;;;;;;;AAQ7D;KA7BM;uCA+BS"}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/HeaderTabs/HeaderTabsMenu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useMemo, useState } from \"react\"; // Removed useEffect\r\nimport { Tabs } from \"../ui/tabs\";\r\nimport { TabsList } from \"@radix-ui/react-tabs\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport { useSkills } from \"@/context/SkillsContext\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { Drawer } from \"@/components/ui/drawer\";\r\nimport WebHeaderTabs from \"@/components/HeaderTabs/WebHeaderTabs\";\r\nimport MobileHeaderTabs from \"./MobileHeaderTabs\";\r\nimport { useSession, signOut } from \"next-auth/react\";\r\nimport { getInitials } from \"@/utils/utils\";\r\nimport { isADLogin } from \"@/api/config\";\r\nimport { getTabMenus } from \"@/utils/tabRoutes\";\r\nimport { useEntitlement } from \"@/context/EntitlementContext\";\r\nimport { clearUserUuid } from \"@/library/utils\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\n\r\ninterface HeaderTabsMenuProps {\r\n  routeNameMap: Record<string, string>;\r\n}\r\n\r\ninterface SubCategory {\r\n  id: number;\r\n  name: string;\r\n  category_id: number;\r\n  category_name: string;\r\n}\r\n\r\nconst HeaderTabsMenu: React.FC<HeaderTabsMenuProps> = ({ routeNameMap }) => {\r\n  const pathname = usePathname();\r\n  const { data: session } = useSession();\r\n  const userName = session?.user?.name;\r\n  const userEmail = session?.user?.email;\r\n  localStorage.setItem(\"userName\", userEmail || \"\"); // Store userName in localStorage\r\n  const avatarName = getInitials(userName ?? \"\");\r\n  const avatarImage = false;\r\n\r\n  const isADlogin = isADLogin();\r\n  const { entitlements, isLoaded } = useEntitlement();\r\n  const {\r\n    setIsOpenDiscardModal,\r\n    trackCurrentData,\r\n    setSkillsData,\r\n    isHeaderTabChange,\r\n  } = useSkills(); // Removed setSkillsData\r\n\r\n  const [isLogOut, setIsLogOut] = useState(false);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  const parsedEntitlement = useMemo(() => {\r\n    if (typeof entitlements === \"string\") {\r\n      try {\r\n        return JSON.parse(entitlements);\r\n      } catch (error) {\r\n        console.error(\"Failed to parse entitlement:\", error);\r\n        return {};\r\n      }\r\n    }\r\n    return entitlements || {};\r\n  }, [entitlements]);\r\n  // Constants for styling\r\n  const MOBILE_BREAKPOINT = 767;\r\n  const avatarVisible = userName?.length || avatarImage ? true : false;\r\n\r\n  // Fetch subcategories data\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        const responseSubCategories = await trackedFetch(\r\n          `/api/subcategories`,\r\n          {},\r\n          { context: \"getSubCategories\" }\r\n        );\r\n        const subCategories: SubCategory[] = await responseSubCategories.json();\r\n        setSkillsData(\r\n          Array.isArray(subCategories)\r\n            ? subCategories.sort((a, b) => a.name.localeCompare(b.name))\r\n            : []\r\n        );\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_Subcategories_Fetched\",\r\n          properties: {\r\n            email: session?.user?.email,\r\n          },\r\n        });\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      } catch (error: any) {\r\n        console.error(\"Error fetching data:\", error);\r\n        getAppInsights()?.trackException({\r\n          error: new Error(\"Subcategories api with error is \" + error),\r\n          severityLevel: 3,\r\n        });\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [setSkillsData]);\r\n\r\n  const tabs = useMemo(() => {\r\n    if (!isLoaded) return [];\r\n    return getTabMenus(parsedEntitlement, isADlogin);\r\n  }, [parsedEntitlement, isADlogin, isLoaded]);\r\n\r\n  const tabsList = () => (\r\n    <>\r\n      {tabs.map((tab, index) => (\r\n        <Tabs key={`tabs-${index}`} className=\"flex items-center\">\r\n          <TabsList>\r\n            <Link\r\n              href={!isHeaderTabChange ? tab.route : \" \"}\r\n              onClick={() => {\r\n                if (Object.keys(trackCurrentData).length) {\r\n                  setIsOpenDiscardModal(true);\r\n                } else {\r\n                  setIsMenuOpen(false);\r\n                  isHeaderTabChange && setIsOpenDiscardModal(true);\r\n                }\r\n              }}\r\n            >\r\n              <Button\r\n                className=\"bg-transparent shadow-none p-0 m-0 hover:bg-transparent\"\r\n                variant=\"ghost\"\r\n              >\r\n                <p\r\n                  className={`pb-2 text-base ${\r\n                    tab.route === pathname\r\n                      ? \"text-[#2A70EA] font-medium border-b-2 border-[#2A70EA] inline-block\"\r\n                      : \"text-[#707070] font-light\"\r\n                  }`}\r\n                >\r\n                  {routeNameMap[tab.route]}\r\n                </p>\r\n              </Button>\r\n            </Link>\r\n          </TabsList>\r\n        </Tabs>\r\n      ))}\r\n    </>\r\n  );\r\n\r\n  const logoutHandler = () => {\r\n    const tenetId = process.env.NEXT_PUBLIC_AZURE_TENANT_ID;\r\n    const postLogoutRedirect =\r\n      process.env.NEXTAUTH_URL ?? \"http://tandymgroup.com/\";\r\n    const logoutUrl = `https://login.microsoftonline.com/${tenetId}/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent(\r\n      postLogoutRedirect\r\n    )}&prompt=select_account`;\r\n    clearUserUuid();\r\n    getAppInsights()?.trackEvent({\r\n      name: `FE_Logout_Button_Clicked`,\r\n    });\r\n    localStorage.removeItem(\"mercury-filter-store\");\r\n    localStorage.removeItem(\"recruiter-filter-store\");\r\n    signOut({ redirect: false }).then(() => {\r\n      window.location.href = logoutUrl;\r\n    });\r\n  };\r\n\r\n  if (!isLoaded) return null;\r\n\r\n  return (\r\n    <Drawer\r\n      direction=\"left\"\r\n      open={isMenuOpen}\r\n      onClose={() => setIsMenuOpen(false)}\r\n      dismissible\r\n    >\r\n      {isMenuOpen ? (\r\n        <MobileHeaderTabs\r\n          setIsMenuOpen={setIsMenuOpen}\r\n          tabsList={tabsList}\r\n          logoutHandler={logoutHandler}\r\n          avatarVisible={avatarVisible}\r\n        />\r\n      ) : (\r\n        <WebHeaderTabs\r\n          avatarImage={avatarImage}\r\n          avatarImageURL=\"https://images.unsplash.com/photo-*************-f34c92461ad9\"\r\n          avatarImageAltText=\"avatar-image\"\r\n          avatarName={avatarName}\r\n          setIsLogOut={setIsLogOut}\r\n          isLogOut={isLogOut}\r\n          setIsMenuOpen={setIsMenuOpen}\r\n          isMenuOpen={isMenuOpen}\r\n          tabsList={tabsList}\r\n          logoutHandler={logoutHandler}\r\n          avatarVisible={avatarVisible}\r\n        />\r\n      )}\r\n    </Drawer>\r\n  );\r\n};\r\n\r\nexport default HeaderTabsMenu;\r\n"], "names": [], "mappings": ";;;;AAEA,gRAA6D,oBAAoB;AACjF;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;AA4IoB;;;AAhJpB;;;;;;;;;;;;;;;;;;;AAgCA,MAAM,iBAAgD,CAAC,EAAE,YAAY,EAAE;;IACrE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,SAAS,MAAM;IAChC,MAAM,YAAY,SAAS,MAAM;IACjC,aAAa,OAAO,CAAC,YAAY,aAAa,KAAK,iCAAiC;IACpF,MAAM,aAAa,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,YAAY;IAC3C,MAAM,cAAc;IAEpB,MAAM,YAAY,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD;IAC1B,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAChD,MAAM,EACJ,qBAAqB,EACrB,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EAClB,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,KAAK,wBAAwB;IAEzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAChC,IAAI,OAAO,iBAAiB,UAAU;gBACpC,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO,CAAC;gBACV;YACF;YACA,OAAO,gBAAgB,CAAC;QAC1B;oDAAG;QAAC;KAAa;IACjB,wBAAwB;IACxB,MAAM,oBAAoB;IAC1B,MAAM,gBAAgB,UAAU,UAAU,cAAc,OAAO;IAE/D,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI;wBACF,MAAM,wBAAwB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAC7C,CAAC,kBAAkB,CAAC,EACpB,CAAC,GACD;4BAAE,SAAS;wBAAmB;wBAEhC,MAAM,gBAA+B,MAAM,sBAAsB,IAAI;wBACrE,cACE,MAAM,OAAO,CAAC,iBACV,cAAc,IAAI;kEAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;mEACxD,EAAE;wBAER,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;4BAC3B,MAAM;4BACN,YAAY;gCACV,OAAO,SAAS,MAAM;4BACxB;wBACF;oBACA,8DAA8D;oBAChE,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;4BAC/B,OAAO,IAAI,MAAM,qCAAqC;4BACtD,eAAe;wBACjB;oBACF;gBACF;;YACA;QACF;mCAAG;QAAC;KAAc;IAElB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE;YACnB,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,OAAO,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB;QACxC;uCAAG;QAAC;QAAmB;QAAW;KAAS;IAE3C,MAAM,WAAW,kBACf;sBACG,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,4HAAA,CAAA,OAAI;oBAAuB,WAAU;8BACpC,cAAA,6LAAC,mKAAA,CAAA,WAAQ;kCACP,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,oBAAoB,IAAI,KAAK,GAAG;4BACvC,SAAS;gCACP,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,EAAE;oCACxC,sBAAsB;gCACxB,OAAO;oCACL,cAAc;oCACd,qBAAqB,sBAAsB;gCAC7C;4BACF;sCAEA,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC;oCACC,WAAW,CAAC,eAAe,EACzB,IAAI,KAAK,KAAK,WACV,wEACA,6BACJ;8CAED,YAAY,CAAC,IAAI,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;mBAxBvB,CAAC,KAAK,EAAE,OAAO;;;;;;IAkChC,MAAM,gBAAgB;QACpB,MAAM;QACN,MAAM,qBACJ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,IAAI;QAC9B,MAAM,YAAY,CAAC,kCAAkC,EAAE,QAAQ,6CAA6C,EAAE,mBAC5G,oBACA,sBAAsB,CAAC;QACzB,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD;QACZ,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;YAC3B,MAAM,CAAC,wBAAwB,CAAC;QAClC;QACA,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM,GAAG,IAAI,CAAC;YAChC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,WAAU;QACV,MAAM;QACN,SAAS,IAAM,cAAc;QAC7B,WAAW;kBAEV,2BACC,6LAAC,gJAAA,CAAA,UAAgB;YACf,eAAe;YACf,UAAU;YACV,eAAe;YACf,eAAe;;;;;iCAGjB,6LAAC,6IAAA,CAAA,UAAa;YACZ,aAAa;YACb,gBAAe;YACf,oBAAmB;YACnB,YAAY;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;YACZ,UAAU;YACV,eAAe;YACf,eAAe;;;;;;;;;;;AAKzB;GAlKM;;QACa,qIAAA,CAAA,cAAW;QACF,iJAAA,CAAA,aAAU;QAQD,iIAAA,CAAA,iBAAc;QAM7C,4HAAA,CAAA,YAAS;;;KAhBT;uCAoKS"}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}