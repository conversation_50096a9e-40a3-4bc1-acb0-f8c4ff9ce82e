"""
Azure Email Client - Simplified for Main Email Fetching

Contains the AzureEmailFetcher class focused on email fetching functionality.
Folder management operations have been moved to separate tools.
"""

import requests
import json
from datetime import datetime
from typing import List, Dict, Optional

import os


def get_azure_credentials():
    """Get Azure credentials from environment variables"""
    return {
        "client_id": os.getenv("MAILBOX_RESUMES_CLIENT_ID"),
        "client_secret": os.getenv("MAILBOX_RESUMES_CLIENT_SECRET"),
        "tenant_id": os.getenv("MAILBOX_RESUMES_TENANT_ID")
    }


class AzureEmailFetcher:
    """
    Fetches emails from Azure mailbox using Microsoft Graph API.
    Simplified version focused on email fetching for main production use.
    """
    
    def __init__(self, logger=None):
        self.logger = logger
        self.access_token = None
        
        # Get Azure credentials from environment variables (loaded from GPG secrets)
        credentials = get_azure_credentials()
        self.client_id = credentials["client_id"]
        self.client_secret = credentials["client_secret"]
        self.tenant_id = credentials["tenant_id"]
        
        # Validate required credentials
        self._validate_credentials()
    
    def _validate_credentials(self):
        """Validate that all required Azure credentials are present"""
        missing_creds = []
        
        if not self.client_id:
            missing_creds.append("MAILBOX_RESUMES_CLIENT_ID")
        if not self.client_secret:
            missing_creds.append("MAILBOX_RESUMES_CLIENT_SECRET")
        if not self.tenant_id:
            missing_creds.append("MAILBOX_RESUMES_TENANT_ID")
            
        if missing_creds:
            error_msg = f"Missing required Azure credentials: {', '.join(missing_creds)}"
            if self.logger:
                self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        # if self.logger:
        #     self.logger.info("✅ All Azure credentials found")
        #     self.logger.info(f"Using Client ID: {self.client_id}")
        #     self.logger.info(f"Using Tenant ID: {self.tenant_id}")
    
    def _filter_emails_by_sender(self, emails: List[Dict], sender_filter: Optional[List[str]]) -> List[Dict]:
        """
        Filter emails by sender email addresses (client-side filtering).
        
        Args:
            emails: List of email dictionaries to filter
            sender_filter: List of sender email addresses to filter by
            
        Returns:
            List of filtered emails that match the sender criteria
        """
        if not sender_filter or not emails:
            return emails
        
        # Convert sender filter to lowercase for case-insensitive comparison
        target_senders = [sender.lower() for sender in sender_filter]
        
        if self.logger:
            # self.logger.info(f"🔍 Looking for emails from: {', '.join(sender_filter)}")
            self.logger.info(f"📧 Found {len(emails)} total emails to check")
            
            # Show first few emails with received dates for order verification
            if emails:
                # self.logger.info(f"📅 Email order verification (showing first 3):")
                for i, email in enumerate(emails[:3]):
                    received_dt = email.get('receivedDateTime', 'Unknown')
                    from_info = email.get('from', {})
                    sender = from_info.get('emailAddress', {}).get('address', 'Unknown')
                    subject = email.get('subject', '(No Subject)')[:30]
                    # self.logger.info(f"  {i+1}. {received_dt} | {sender} | {subject}...")
        
        # Collect all unique sender emails for debugging
        all_senders = set()
        filtered_emails = []
        
        # Filter emails client-side
        for email in emails:
            from_info = email.get('from', {})
            sender_email = from_info.get('emailAddress', {}).get('address', '').lower()
            all_senders.add(sender_email)
            
            if sender_email in target_senders:
                filtered_emails.append(email)
        
        if self.logger:
            self.logger.info(f"🔍 Client-side filtered from {len(emails)} to {len(filtered_emails)} emails")
            
            # Show all unique senders found
            unique_senders = sorted(list(all_senders))
            if unique_senders:
                # self.logger.info(f"📨 Found {len(unique_senders)} unique senders in emails:")
                
                # Display senders in groups of 10 per line for better readability
                for i in range(0, len(unique_senders), 10):
                    batch = unique_senders[i:i+10]
                    batch_num = i // 10 + 1
                    self.logger.info(f"📨 Batch {batch_num}: {', '.join(batch)}")
                    
                # Also show a compact list if there are many senders
                # if len(unique_senders) > 20:
                #     self.logger.info(f"📨 All senders (compact): {', '.join(unique_senders)}")
            
            # Show breakdown by target sender
            for sender in sender_filter:
                sender_count = sum(1 for email in filtered_emails 
                                 if email.get('from', {}).get('emailAddress', {}).get('address', '').lower() == sender.lower())
                if sender_count > 0:
                    self.logger.info(f"  ✅ Found {sender_count} emails from {sender}")
                else:
                    self.logger.info(f"  ❌ No emails found from {sender}")
        
        return filtered_emails
    
    def get_access_token(self) -> str:
        """
        Get access token using client credentials flow.
        This is the standard pattern for service-to-service authentication.
        """
        if self.access_token:
            return self.access_token
            
        # if self.logger:
        #     self.logger.info("🔑 Getting access token from Azure...")
        
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        token_data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        try:
            response = requests.post(token_url, data=token_data)
            response.raise_for_status()
            
            token_response = response.json()
            self.access_token = token_response['access_token']
            
            # if self.logger:
            #     self.logger.info("✅ Successfully obtained access token")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to get access token: {e}"
            if self.logger:
                self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def fetch_emails(self, mailbox: str = "<EMAIL>", count: int = 10, folder_id: Optional[str] = None, sender_filter: Optional[List[str]] = None) -> List[Dict]:
        """
        Fetch the last N emails from the specified mailbox folder.
        
        Args:
            mailbox: Email address of the mailbox to fetch from
            count: Number of emails to fetch (default 10)
            folder_id: Specific folder ID to fetch from (defaults to Inbox)
            sender_filter: List of email addresses to filter by sender (optional)
            
        Returns:
            List of email dictionaries with subject, sender, received date, etc.
        """
        # If multiple senders are provided, use specialized method for better performance
        if sender_filter and len(sender_filter) > 1:
            return self._fetch_emails_multiple_senders(mailbox, count, folder_id, sender_filter)
        
        # Single sender filter or no filter - use regular approach
        access_token = self.get_access_token()
        
        # Default to Inbox folder if no folder specified
        if folder_id is None:
            folder_id = "AQMkAGE3NwA5YTM4NC00MGIwLTRjODktOGZlYy0zMWRjNDg2OWNiNDYALgAAA04TRJiGUpdItGJHKQYSuaABAATk6rnVzzJIkNA2Eyz2AN0AAAIBDAAAAA=="
            folder_name = "Inbox"
        else:
            folder_name = f"folder {folder_id[:20]}..."
        
        if self.logger:
            if sender_filter:
                self.logger.info(f"📧 Fetching last {count} emails from {mailbox} ({folder_name}) filtered by sender: {sender_filter[0]}")
            else:
                self.logger.info(f"📧 Fetching last {count} emails from {mailbox} ({folder_name})...")
        
        # Microsoft Graph API endpoint for getting messages from specific folder
        messages_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/mailFolders/{folder_id}/messages"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # Build query parameters
        params = {
            "$top": count,
            "$orderby": "receivedDateTime asc",
            "$select": "id,subject,from,receivedDateTime,hasAttachments,isRead,importance,bodyPreview"
        }
        
        # Remove server-side filtering as it causes 400 Bad Request
        # We'll do client-side filtering instead
        # if sender_filter and len(sender_filter) == 1:
        #     params["$filter"] = f"from/emailAddress/address eq '{sender_filter[0]}'"
        #     if self.logger:
        #         self.logger.info(f"🔍 Using filter: {params['$filter']}")
        
        # if self.logger:
        #     if sender_filter:
        #         self.logger.info(f"📧 Fetching emails from {mailbox} ({folder_name}) - will apply client-side filtering")
        #     else:
        #         self.logger.info(f"📧 Fetching emails from {mailbox} ({folder_name}) - no filtering")
        
        try:
            response = requests.get(messages_url, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            all_emails = data.get("value", [])
            
            # Apply client-side filtering using modular method
            emails = self._filter_emails_by_sender(all_emails, sender_filter)
            
            # if self.logger:
            #     self.logger.info(f"✅ Successfully processed {len(emails)} emails")
            
            # Debug output for filtered results
            if sender_filter:
                print(f"🔍 DEBUG: API returned {len(all_emails)} emails, filtered to {len(emails)} emails")
            
            return emails
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to fetch emails: {e}"
            if self.logger:
                self.logger.error(error_msg)
                
                # Try to get more details from the response
                try:
                    error_details = response.json()
                    self.logger.error(f"Error details: {json.dumps(error_details, indent=2)}")
                except:
                    pass
                    
            raise Exception(error_msg)
    
    def _fetch_emails_multiple_senders(self, mailbox: str, count: int, folder_id: Optional[str], sender_filter: List[str]) -> List[Dict]:
        """
        Fetch emails from multiple senders using client-side filtering.
        This avoids complex OData filter syntax issues and API limitations.
        """
        if self.logger:
            self.logger.info(f"📧 Fetching emails for {len(sender_filter)} senders using client-side filtering...")
        
        # Fetch more emails than requested to account for filtering
        fetch_count = count * 3  # Get 3x more emails to account for filtering
        
        # Fetch all emails without server-side filtering
        all_emails = self.fetch_emails(
            mailbox=mailbox,
            count=fetch_count,
            folder_id=folder_id,
            sender_filter=None  # No filtering - get all emails
        )
        
        # Apply client-side filtering using modular method
        filtered_emails = self._filter_emails_by_sender(all_emails, sender_filter)
        
        # Sort by received date (newest first) and limit to requested count
        filtered_emails.sort(key=lambda x: x.get('receivedDateTime', ''), reverse=True)
        result_emails = filtered_emails[:count]
        
        if self.logger:
            self.logger.info(f"✅ Final result: {len(result_emails)} emails from target senders")
        
        return result_emails
    
    def fetch_folders(self, mailbox: str = "<EMAIL>") -> List[Dict]:
        """
        Fetch all mail folders from the specified mailbox.
        
        Args:
            mailbox: Email address of the mailbox to fetch folders from
            
        Returns:
            List of folder dictionaries with id, displayName, childFolderCount, etc.
        """
        access_token = self.get_access_token()
        
        # if self.logger:
        #     self.logger.info(f"📁 Fetching folders from {mailbox}...")
        
        # Microsoft Graph API endpoint for getting mail folders
        folders_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/mailFolders"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        params = {
            "$select": "id,displayName,childFolderCount,totalItemCount,unreadItemCount"
        }
        
        try:
            response = requests.get(folders_url, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            folders = data.get("value", [])
            
            # if self.logger:
            #     self.logger.info(f"✅ Successfully fetched {len(folders)} folders")
            return folders
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to fetch folders: {e}"
            if self.logger:
                self.logger.error(error_msg)
                
                # Try to get more details from the response
                try:
                    error_details = response.json()
                    self.logger.error(f"Error details: {json.dumps(error_details, indent=2)}")
                except:
                    pass
                    
            raise Exception(error_msg)
    
    def create_folder(self, mailbox: str, folder_name: str, parent_folder_id: Optional[str] = None) -> Dict:
        """
        Create a new mail folder in the specified mailbox.
        
        Args:
            mailbox: Email address of the mailbox
            folder_name: Name of the folder to create
            parent_folder_id: ID of parent folder (None for root level)
            
        Returns:
            Dictionary with folder information including the new folder ID
        """
        access_token = self.get_access_token()
        
        if self.logger:
            self.logger.info(f"📁 Creating folder '{folder_name}' in {mailbox}...")
        
        # Determine the endpoint based on parent folder
        if parent_folder_id:
            folders_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/mailFolders/{parent_folder_id}/childFolders"
        else:
            folders_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/mailFolders"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "displayName": folder_name
        }
        
        try:
            response = requests.post(folders_url, headers=headers, json=data)
            response.raise_for_status()
            
            folder_info = response.json()
            
            if self.logger:
                self.logger.info(f"✅ Successfully created folder '{folder_name}' with ID: {folder_info.get('id')}")
            return folder_info
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to create folder '{folder_name}': {e}"
            if self.logger:
                self.logger.error(error_msg)
                
                # Try to get more details from the response
                try:
                    error_details = response.json()
                    self.logger.error(f"Error details: {json.dumps(error_details, indent=2)}")
                except:
                    pass
                    
            raise Exception(error_msg)
    
    def find_folder_by_name(self, mailbox: str, folder_name: str) -> Optional[Dict]:
        """
        Find a folder by name in the mailbox.
        
        Args:
            mailbox: Email address of the mailbox
            folder_name: Name of the folder to find
            
        Returns:
            Folder dictionary if found, None otherwise
        """
        folders = self.fetch_folders(mailbox)
        for folder in folders:
            if folder.get('displayName', '').lower() == folder_name.lower():
                return folder
        return None
    
    def forward_email(self, mailbox: str, email_id: str, forward_to_email: str, comment: str = "") -> bool:
        """
        Forward an email to a specified recipient using Microsoft Graph API.
        
        Required Permissions: Mail.Send (Application permission)
        
        Args:
            mailbox: Email address of the source mailbox (e.g., "<EMAIL>")
            email_id: ID of the email to forward
            forward_to_email: Email address to forward the email to  
            comment: Optional comment to include with the forwarded email
            
        Returns:
            True if successful, False otherwise
            
        API Reference: https://docs.microsoft.com/en-us/graph/api/message-forward
        """
        access_token = self.get_access_token()
        
        if self.logger:
            self.logger.info(f"📤 Forwarding email {email_id[:20]}... from {mailbox} to {forward_to_email}")
        
        # Microsoft Graph API endpoint for forwarding messages
        forward_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/messages/{email_id}/forward"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # Build the request body according to Microsoft Graph API specification
        data = {
            "comment": comment,
            "toRecipients": [
                {
                    "emailAddress": {
                        "address": forward_to_email,
                        "name": forward_to_email  # Use email address as name if no display name available
                    }
                }
            ]
        }
        
        try:
            response = requests.post(forward_url, headers=headers, json=data)
            response.raise_for_status()
            
            # Microsoft Graph forward API returns 202 Accepted on success
            if response.status_code == 202:
                if self.logger:
                    self.logger.info(f"✅ Successfully forwarded email {email_id[:20]}... to {forward_to_email}")
                return True
            else:
                if self.logger:
                    self.logger.warning(f"⚠️ Unexpected response code {response.status_code} when forwarding email {email_id[:20]}...")
                return False
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to forward email {email_id[:20]}... to {forward_to_email}: {e}"
            if self.logger:
                self.logger.error(error_msg)
                
                # Try to get more details from the response
                try:
                    if hasattr(e, 'response') and e.response:
                        error_details = e.response.json()
                        self.logger.error(f"Forward error details: {json.dumps(error_details, indent=2)}")
                except:
                    pass
                    
            return False

    def move_email(self, mailbox: str, email_id: str, destination_folder_id: str) -> bool:
        """
        Move an email to a specific folder.
        
        Args:
            mailbox: Email address of the mailbox
            email_id: ID of the email to move
            destination_folder_id: ID of the destination folder
            
        Returns:
            True if successful, False otherwise
        """
        access_token = self.get_access_token()
        
        if self.logger:
            self.logger.info(f"📧 Moving email {email_id[:20]}... to folder {destination_folder_id[:20]}...")
        
        # Microsoft Graph API endpoint for moving messages
        move_url = f"https://graph.microsoft.com/v1.0/users/{mailbox}/messages/{email_id}/move"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "destinationId": destination_folder_id
        }
        
        try:
            response = requests.post(move_url, headers=headers, json=data)
            response.raise_for_status()
            
            if self.logger:
                self.logger.info(f"✅ Successfully moved email {email_id[:20]}...")
            return True
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to move email {email_id[:20]}...: {e}"
            if self.logger:
                self.logger.error(error_msg)
                
                # Try to get more details from the response
                try:
                    error_details = response.json()
                    self.logger.error(f"Error details: {json.dumps(error_details, indent=2)}")
                except:
                    pass
                    
            # Don't raise exception for move failures, just return False
            return False
    
    def ensure_folder_exists(self, mailbox: str, folder_name: str) -> str:
        """
        Ensure a folder exists, create it if it doesn't.
        
        Args:
            mailbox: Email address of the mailbox
            folder_name: Name of the folder to ensure exists
            
        Returns:
            Folder ID of the existing or newly created folder
        """
        # Check if folder already exists
        existing_folder = self.find_folder_by_name(mailbox, folder_name)
        if existing_folder:
            if self.logger:
                self.logger.info(f"📁 Folder '{folder_name}' already exists")
            return existing_folder['id']
        
        # Create the folder if it doesn't exist
        new_folder = self.create_folder(mailbox, folder_name)
        return new_folder['id']
    
    # NOTE: Folder management methods (fetch_folders, create_folder) have been 
    # moved to separate tools in the tools/ directory for one-time use.
    # This class is now focused only on email fetching for main production use. 