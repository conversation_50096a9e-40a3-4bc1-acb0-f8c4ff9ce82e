import {
  VACANCY_FILTER_LABELS,
  VACANCY_FILTER_OTHER_LABELS,
} from "@/library/utils";
import {
  CircleX,
  FilterIcon,
  RotateCcw,
  ThumbsDown,
  ThumbsUp,
} from "lucide-react";
import { AvailabilityDateRange } from "@/components/candidates/vacancyFilterAvailabilityDateRange";
import { RangeSlider } from "@/components/utils/rangeSlider";
import { Input } from "@/components/ui/input";

export interface iFilterSlider {
  filterEntitlement: boolean;
  showFilter: boolean;
  isFunnelFill: boolean;
  showSearchFieldError: boolean;
  searchFields: string[];
  searchText: string;
  uniqueReviewValues: string[];
  reviewStatus: string[];
  uniqueShortList: string[];
  shortListed: string[];
  uniqueLocation: any;
  state: string[];
  city: string[];
  uniqueFreshnessIndex: string[];
  freshnessIndex: string[];
  uniqueAiAgentStatus: string[];
  aiAgentStatus: string[];
  totalScoreRange: [number, number];
  availabilityDateRange: { from: string; to: string };
  handleResetFilter: () => void;
  handleFilterIcon: () => void;
  toggleSearchField: (value: string) => void;
  setSearchText: (value: string) => void;
  handleCheckBoxSelection: (value: string, item: string) => void;
  setAvailabilityDateRange: React.Dispatch<
    React.SetStateAction<{ from: string; to: string }>
  >;
  setTotalScoreRange: (value: [number, number]) => void;
  distanceRange: [number, number];
  setDistanceRange: (value: [number, number]) => void;
  distance: [number, number];
  maxMinAvailabilityDateRange: { from: string; to: string };
}

export const filterSlider = ({
  filterEntitlement,
  showFilter,
  isFunnelFill,
  showSearchFieldError,
  searchFields,
  searchText,
  uniqueReviewValues,
  reviewStatus,
  uniqueShortList,
  shortListed,
  uniqueLocation,
  state,
  city,
  uniqueFreshnessIndex,
  freshnessIndex,
  availabilityDateRange,
  uniqueAiAgentStatus,
  aiAgentStatus,
  totalScoreRange,
  setAvailabilityDateRange,
  handleResetFilter,
  handleFilterIcon,
  toggleSearchField,
  setSearchText,
  handleCheckBoxSelection,
  setTotalScoreRange,
  distanceRange,
  setDistanceRange,
  distance,
  maxMinAvailabilityDateRange,
}: iFilterSlider) => {
  const onCheckBoxSelection = (label: string, selectedValue: string) => {
    // Call original handler first
    handleCheckBoxSelection(label as any, selectedValue);

    // If label is STATE, uncheck all related cities automatically
    if (
      label === VACANCY_FILTER_LABELS.STATE &&
      uniqueLocation &&
      uniqueLocation?.[selectedValue]
    ) {
      const citiesToUncheck = uniqueLocation?.[selectedValue];
      citiesToUncheck.forEach((cityName: string) => {
        if (city.includes(cityName)) {
          handleCheckBoxSelection(VACANCY_FILTER_LABELS.CITY, cityName);
        }
      });
    }
  };

  // Defensive: ensure availabilityDateRange is always an object with 'from' and 'to'
  const safeAvailabilityDateRange =
    availabilityDateRange &&
    typeof availabilityDateRange === "object" &&
    "from" in availabilityDateRange &&
    "to" in availabilityDateRange
      ? availabilityDateRange
      : { from: "", to: "" };

  return (
    <>
      {filterEntitlement && !showFilter && (
        <div className="max-h-[75vh] filter-scroll overflow-y-auto rounded-lg mr-2 border border-gray-800">
          <div className={`flex justify-between items-center p-2 `}>
            <div className="flex gap-2 items-center">
              <FilterIcon
                size={15}
                fill={isFunnelFill ? "#2a70ea" : "white"}
                color={isFunnelFill ? "#2a70ea" : "black"}
              />
              <p>Filter</p>
            </div>
            <div
              className="hover:cursor-pointer flex gap-2 items-center"
              onClick={handleResetFilter}
            >
              <RotateCcw size={15} />
              <p>Reset</p>
            </div>
            <CircleX
              className="hover:cursor-pointer"
              size={15}
              onClick={handleFilterIcon}
            />
          </div>
          <div className="p-2">
            {/* 1. Search with name and whyfit checkbox */}
            <div className="p-2">
              <p className="mb-2 font-medium">Search :</p>
              <div
                className={`px-4 py-2 border rounded ${
                  showSearchFieldError && searchText?.length > 0
                    ? "border-red-500"
                    : "border-gray-300"
                }`}
              >
                <div className="flex gap-2 mb-2">
                  <label className="text-sm flex items-center gap-1">
                    <Input
                      type="checkbox"
                      className="size-3"
                      placeholder={
                        Array.isArray(searchFields) && searchFields.length === 0
                          ? "Search..."
                          : `Searching by ${(searchFields || []).join(
                              " & "
                            )}...`
                      }
                      checked={(searchFields || []).includes(
                        VACANCY_FILTER_OTHER_LABELS.NAME
                      )}
                      onChange={() =>
                        toggleSearchField(VACANCY_FILTER_OTHER_LABELS.NAME)
                      }
                    />
                    Name
                  </label>
                  <label className="text-sm flex items-center gap-1">
                    <Input
                      type="checkbox"
                      className="size-3"
                      checked={searchFields?.includes(
                        VACANCY_FILTER_OTHER_LABELS.WHYFIT
                      )}
                      onChange={() =>
                        toggleSearchField(VACANCY_FILTER_OTHER_LABELS.WHYFIT)
                      }
                    />
                    Why Fit
                  </label>
                </div>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
                {showSearchFieldError && searchText?.length > 0 && (
                  <p className="text-red-500 text-xs mt-1">
                    Please select at least one field to search
                  </p>
                )}
              </div>
            </div>
            {/* 2. Rating */}
            <div className="p-2">
              <p className="mb-2 font-medium">Rating :</p>
              <div className="flex items-center gap-4 pl-2">
                {uniqueReviewValues &&
                  uniqueReviewValues?.map((item, index) => {
                    if (item === VACANCY_FILTER_OTHER_LABELS.THUMBSUP) {
                      return (
                        <div
                          key={item + index}
                          className={`cursor-pointer ${
                            reviewStatus?.includes(item) ? "scale-125" : ""
                          }`}
                          onClick={() =>
                            handleCheckBoxSelection(
                              VACANCY_FILTER_LABELS.REVIEW_DECISION,
                              item
                            )
                          }
                          title={VACANCY_FILTER_OTHER_LABELS.THUMBSUP}
                        >
                          <ThumbsUp
                            size={20}
                            className={`${
                              reviewStatus.includes(item)
                                ? "text-green-700"
                                : "text-gray-500"
                            }`}
                            fill={
                              reviewStatus.includes(item)
                                ? "#166534"
                                : "transparent"
                            }
                          />
                        </div>
                      );
                    } else if (
                      item === VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN
                    ) {
                      return (
                        <div
                          key={item + index}
                          className={`cursor-pointer ${
                            reviewStatus?.includes(item) ? "scale-125" : ""
                          }`}
                          onClick={() =>
                            handleCheckBoxSelection(
                              VACANCY_FILTER_LABELS.REVIEW_DECISION,
                              item
                            )
                          }
                          title={VACANCY_FILTER_OTHER_LABELS.THUMBSDOWN}
                        >
                          <ThumbsDown
                            size={20}
                            className={`${
                              reviewStatus?.includes(item)
                                ? "text-red-700"
                                : "text-gray-500"
                            }`}
                            fill={
                              reviewStatus?.includes(item)
                                ? "#7f1d1d"
                                : "transparent"
                            }
                          />
                        </div>
                      );
                    } else {
                      return (
                        <label
                          key={item + index}
                          className="flex items-center gap-1"
                        >
                          <Input
                            type="checkbox"
                            className="size-3"
                            value={item}
                            checked={reviewStatus?.includes(item)}
                            onChange={() =>
                              handleCheckBoxSelection(
                                VACANCY_FILTER_LABELS.REVIEW_DECISION,
                                item
                              )
                            }
                          />
                          <span className="text-sm">No Rating</span>
                        </label>
                      );
                    }
                  })}
              </div>
            </div>
            <div className="p-2">
              <p>Shortlisted :</p>
              <div className="flex gap-1 items-center">
                {uniqueShortList &&
                  uniqueShortList?.map((item, index) => (
                    <div className="pl-4" key={item + `${index}`}>
                      <ul key={index}>
                        <li>
                          <label className="flex items-center gap-1">
                            <Input
                              type="checkbox"
                              className="size-3"
                              value={item}
                              checked={shortListed?.includes(item)}
                              onChange={() =>
                                handleCheckBoxSelection(
                                  VACANCY_FILTER_LABELS.SHORT_LISTED,
                                  item
                                )
                              }
                            />
                            <span className="pl-1">{item}</span>
                          </label>
                        </li>
                      </ul>
                    </div>
                  ))}
              </div>
            </div>
          </div>
          {/* Distance From Work Site Filter */}
          <RangeSlider
            label="Distance (in miles)"
            min={distance?.[0]}
            max={distance?.[1]}
            step={Math.max(1, Math.floor((distanceRange?.[1] ?? 100) / 100))}
            value={[...distanceRange]}
            onChange={(val: [number, number]) => setDistanceRange([...val])}
            unit="mi"
          />
          {/* State and City Filter */}
          <div className="p-2">
            <p>
              {VACANCY_FILTER_LABELS.STATE} & {VACANCY_FILTER_LABELS.CITY} :
            </p>
            {uniqueLocation &&
              Object.keys(uniqueLocation)?.map((stateName, index) => (
                <div key={stateName + index} className="pl-4">
                  <label className="flex items-center gap-1">
                    <Input
                      type="checkbox"
                      className="size-3"
                      value={stateName}
                      checked={state?.includes(stateName)}
                      onChange={() =>
                        onCheckBoxSelection(
                          VACANCY_FILTER_LABELS.STATE,
                          stateName
                        )
                      }
                    />
                    <span className="pl-1">{stateName}</span>
                  </label>
                  {state?.includes(stateName) &&
                    uniqueLocation?.[stateName]?.length > 0 && (
                      <ul className="pl-4">
                        {uniqueLocation?.[stateName]?.map(
                          (cityName: string, index: number) => (
                            <li key={cityName + index}>
                              <label className="flex items-center gap-1">
                                <Input
                                  type="checkbox"
                                  className="size-3"
                                  value={cityName}
                                  checked={city.includes(cityName)}
                                  onChange={() =>
                                    handleCheckBoxSelection(
                                      VACANCY_FILTER_LABELS.CITY,
                                      cityName
                                    )
                                  }
                                />
                                <span className="pl-1">{cityName}</span>
                              </label>
                            </li>
                          )
                        )}
                      </ul>
                    )}
                </div>
              ))}
          </div>
          <div className="p-2">
            <p>Freshness Index :</p>
            {uniqueFreshnessIndex &&
              uniqueFreshnessIndex?.map((item, index) => (
                <div className="pl-4" key={item + `${index}`}>
                  <ul key={index}>
                    <li>
                      <label className="flex items-center gap-1">
                        <Input
                          type="checkbox"
                          className="size-3"
                          value={item}
                          checked={freshnessIndex?.includes(item)}
                          onChange={() =>
                            handleCheckBoxSelection(
                              VACANCY_FILTER_LABELS.FRESHNESS_INDEX,
                              item
                            )
                          }
                        />
                        <span className="pl-1">{item}</span>
                      </label>
                    </li>
                  </ul>
                </div>
              ))}
          </div>
          {/* Availability Date Range Filter */}
          <AvailabilityDateRange
            availabilityDateRange={safeAvailabilityDateRange}
            setAvailabilityDateRange={setAvailabilityDateRange}
            maxMinAvailabilityDateRange={maxMinAvailabilityDateRange}
          />
          {/* AI Agent Status Filter */}
          <div className="p-2">
            <p>AI Agent Status :</p>
            {uniqueAiAgentStatus &&
              uniqueAiAgentStatus?.map((item, index) => (
                <div className="pl-4" key={item + `${index}`}>
                  <ul key={index}>
                    <li>
                      <label className="flex items-center gap-1">
                        <Input
                          type="checkbox"
                          className="size-3"
                          value={item}
                          checked={aiAgentStatus?.includes(item)}
                          onChange={() =>
                            handleCheckBoxSelection(
                              VACANCY_FILTER_LABELS.AI_AGENT_STATUS,
                              item
                            )
                          }
                        />
                        <span className="pl-1">{item}</span>
                      </label>
                    </li>
                  </ul>
                </div>
              ))}
          </div>
          {/* Total Score Range Filter */}
          <RangeSlider
            label="Total Score Range"
            min={0}
            max={1}
            step={0.01}
            value={totalScoreRange}
            onChange={setTotalScoreRange}
            decimal
          />
        </div>
      )}
    </>
  );
};
