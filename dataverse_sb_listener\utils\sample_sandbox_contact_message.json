{"metadata": {"message_id": "feac69985d284aca9711f53d57599892", "entity_name": "contact", "entity_id": "cf34a24c-b50f-f011-9989-000d3a55ebc9", "stored_at": "2025-07-29T19:54:47.246599+00:00", "original_timestamp": "20250729_195447_246", "file_path": "sample_sandbox_contact_message.json"}, "entity_info": {"entity_name": "contact", "entity_id": "cf34a24c-b50f-f011-9989-000d3a55ebc9", "message_type": "Update", "correlation_id": "28920654-f6bd-4c82-8733-d35047dceab4", "request_id": "1a69d05c-9a5f-4e4f-8bb4-9572b4976f89", "stage": 40, "attributes": {"modifiedby": {"raw_value": {"__type": "EntityReference:http://schemas.microsoft.com/xrm/2011/Contracts", "Id": "bba0abd6-8c7b-ef11-a671-000d3a16b58c", "KeyAttributes": [], "LogicalName": "systemuser", "Name": null, "RowVersion": null}, "type": "other"}, "recruit_availability": {"raw_value": "/Date(1761782400000+0000)/", "parsed_date": "2025-10-30 00:00:00+00:00", "type": "date"}, "modifiedon": {"raw_value": "/Date(1753813066000)/", "parsed_date": "2025-07-29 18:17:46+00:00", "type": "date"}, "modifiedonbehalfby": {"raw_value": null, "type": "other"}, "timezoneruleversionnumber": {"raw_value": 4, "type": "other"}, "mercury_lastcontacted": {"raw_value": "/Date(1753747200000+0000)/", "parsed_date": "2025-07-29 00:00:00+00:00", "type": "date"}, "recruit_address2_state": {"raw_value": {"__type": "EntityReference:http://schemas.microsoft.com/xrm/2011/Contracts", "Id": "2259f30f-8fb4-ee11-a569-00224823f66a", "KeyAttributes": [], "LogicalName": "mercury_state", "Name": null, "RowVersion": null}, "type": "other"}, "contactid": {"raw_value": "cf34a24c-b50f-f011-9989-000d3a55ebc9", "type": "other"}, "address2_city": {"raw_value": "San Jose", "type": "other"}, "address2_composite": {"raw_value": "San Jose\nUnited States", "type": "other"}, "mercury_lastcontactedby": {"raw_value": {"__type": "EntityReference:http://schemas.microsoft.com/xrm/2011/Contracts", "Id": "9fa57da1-4bdf-ef11-a730-7c1e52580d80", "KeyAttributes": [], "LogicalName": "systemuser", "Name": null, "RowVersion": null}, "type": "other"}, "recruit_geocodingstatus": {"raw_value": null, "type": "other"}, "recruit_needsindexing": {"raw_value": true, "type": "other"}, "recruit_lastindexablechange": {"raw_value": "/Date(1753813067000)/", "parsed_date": "2025-07-29 18:17:47+00:00", "type": "date"}}, "post_entity_images": {"AsynchronousStepPrimaryName": {"logical_name": "contact", "id": "cf34a24c-b50f-f011-9989-000d3a55ebc9", "attributes": {"fullname": {"raw_value": "<PERSON><PERSON><PERSON>", "type": "other"}, "contactid": {"raw_value": "cf34a24c-b50f-f011-9989-000d3a55ebc9", "type": "other"}}}}, "pre_entity_images": {}}, "raw_message": {"message_id": "feac69985d284aca9711f53d57599892", "correlation_id": "{28920654-f6bd-4c82-8733-d35047dceab4}", "session_id": null, "reply_to": null, "reply_to_session_id": null, "to": null, "content_type": "application/json", "subject": null, "application_properties": {"http://schemas.microsoft.com/xrm/2011/Claims/Organization": "tandymgroup-sandbox.crm.dynamics.com", "http://schemas.microsoft.com/xrm/2011/Claims/User": null, "http://schemas.microsoft.com/xrm/2011/Claims/InitiatingUser": null, "http://schemas.microsoft.com/xrm/2011/Claims/EntityLogicalName": "contact", "http://schemas.microsoft.com/xrm/2011/Claims/RequestName": "Update", "http://schemas.microsoft.com/xrm/2011/Claims/InitiatingUserAgent": "python-requests/2.32.4"}, "body": "{\"BusinessUnitId\":\"22b900b1-55ac-ee11-a569-000d3a9a80bf\",\"CorrelationId\":\"28920654-f6bd-4c82-8733-d35047dceab4\",\"Depth\":1,\"InitiatingUserAgent\":\"python-requests\\/2.32.4\",\"InitiatingUserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"InitiatingUserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\",\"InputParameters\":[{\"key\":\"Target\",\"value\":{\"__type\":\"Entity:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Attributes\":[{\"key\":\"modifiedby\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\",\"KeyAttributes\":[],\"LogicalName\":\"systemuser\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"recruit_availability\",\"value\":\"\\/Date(1761782400000+0000)\\/\"},{\"key\":\"modifiedon\",\"value\":\"\\/Date(1753813066000)\\/\"},{\"key\":\"modifiedonbehalfby\",\"value\":null},{\"key\":\"timezoneruleversionnumber\",\"value\":4},{\"key\":\"mercury_lastcontacted\",\"value\":\"\\/Date(1753747200000+0000)\\/\"},{\"key\":\"recruit_address2_state\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"2259f30f-8fb4-ee11-a569-00224823f66a\",\"KeyAttributes\":[],\"LogicalName\":\"mercury_state\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"contactid\",\"value\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\"},{\"key\":\"address2_city\",\"value\":\"San Jose\"},{\"key\":\"address2_composite\",\"value\":\"San Jose\\nUnited States\"},{\"key\":\"mercury_lastcontactedby\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"9fa57da1-4bdf-ef11-a730-7c1e52580d80\",\"KeyAttributes\":[],\"LogicalName\":\"systemuser\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"recruit_geocodingstatus\",\"value\":null},{\"key\":\"recruit_needsindexing\",\"value\":true},{\"key\":\"recruit_lastindexablechange\",\"value\":\"\\/Date(1753813067000)\\/\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":\"47816705\"}}],\"IsExecutingOffline\":false,\"IsInTransaction\":false,\"IsOfflinePlayback\":false,\"IsolationMode\":1,\"MessageName\":\"Update\",\"Mode\":1,\"OperationCreatedOn\":\"\\/Date(1753813067000+0000)\\/\",\"OperationId\":\"73bf8a50-a86c-f011-bec2-6045bdd36232\",\"OrganizationId\":\"0907b5d3-30db-ee11-9048-000d3a10681b\",\"OrganizationName\":\"unq0907b5d330dbee119048000d3a106\",\"OutputParameters\":[],\"OwningExtension\":{\"Id\":\"d842871b-5a50-f011-877a-7c1e520daafb\",\"KeyAttributes\":[],\"LogicalName\":\"sdkmessageprocessingstep\",\"Name\":null,\"RowVersion\":null},\"ParentContext\":{\"BusinessUnitId\":\"22b900b1-55ac-ee11-a569-000d3a9a80bf\",\"CorrelationId\":\"28920654-f6bd-4c82-8733-d35047dceab4\",\"Depth\":1,\"InitiatingUserAgent\":\"python-requests\\/2.32.4\",\"InitiatingUserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"InitiatingUserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\",\"InputParameters\":[{\"key\":\"Target\",\"value\":{\"__type\":\"Entity:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Attributes\":[{\"key\":\"recruit_availability\",\"value\":\"\\/Date(1761782400000+0000)\\/\"},{\"key\":\"mercury_lastcontacted\",\"value\":\"\\/Date(1753747200000+0000)\\/\"},{\"key\":\"address2_city\",\"value\":\"San Jose\"},{\"key\":\"mercury_lastcontactedby\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"9fa57da1-4bdf-ef11-a730-7c1e52580d80\",\"KeyAttributes\":[],\"LogicalName\":\"systemuser\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"recruit_address2_state\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"2259f30f-8fb4-ee11-a569-00224823f66a\",\"KeyAttributes\":[],\"LogicalName\":\"mercury_state\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"contactid\",\"value\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\"},{\"key\":\"address2_composite\",\"value\":\"San Jose\\nUnited States\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":null}}],\"IsExecutingOffline\":false,\"IsInTransaction\":false,\"IsOfflinePlayback\":false,\"IsolationMode\":1,\"MessageName\":\"Update\",\"Mode\":1,\"OperationCreatedOn\":\"\\/Date(1753813067000+0000)\\/\",\"OperationId\":\"73bf8a50-a86c-f011-bec2-6045bdd36232\",\"OrganizationId\":\"0907b5d3-30db-ee11-9048-000d3a10681b\",\"OrganizationName\":\"unq0907b5d330dbee119048000d3a106\",\"OutputParameters\":[],\"OwningExtension\":{\"Id\":\"d842871b-5a50-f011-877a-7c1e520daafb\",\"KeyAttributes\":[],\"LogicalName\":\"sdkmessageprocessingstep\",\"Name\":null,\"RowVersion\":null},\"ParentContext\":{\"BusinessUnitId\":\"22b900b1-55ac-ee11-a569-000d3a9a80bf\",\"CorrelationId\":\"28920654-f6bd-4c82-8733-d35047dceab4\",\"Depth\":1,\"InitiatingUserAgent\":\"python-requests\\/2.32.4\",\"InitiatingUserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"InitiatingUserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\",\"InputParameters\":[{\"key\":\"Target\",\"value\":{\"__type\":\"Entity:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Attributes\":[{\"key\":\"recruit_availability\",\"value\":\"\\/Date(1761782400000+0000)\\/\"},{\"key\":\"mercury_lastcontacted\",\"value\":\"\\/Date(1753747200000+0000)\\/\"},{\"key\":\"address2_city\",\"value\":\"San Jose\"},{\"key\":\"mercury_lastcontactedby\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"9fa57da1-4bdf-ef11-a730-7c1e52580d80\",\"KeyAttributes\":[],\"LogicalName\":\"systemuser\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"recruit_address2_state\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"2259f30f-8fb4-ee11-a569-00224823f66a\",\"KeyAttributes\":[],\"LogicalName\":\"mercury_state\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"contactid\",\"value\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":null}},{\"key\":\"ReturnRowVersion\",\"value\":null}],\"IsExecutingOffline\":false,\"IsInTransaction\":false,\"IsOfflinePlayback\":false,\"IsolationMode\":1,\"MessageName\":\"Update\",\"Mode\":1,\"OperationCreatedOn\":\"\\/Date(1753813067000+0000)\\/\",\"OperationId\":\"73bf8a50-a86c-f011-bec2-6045bdd36232\",\"OrganizationId\":\"0907b5d3-30db-ee11-9048-000d3a10681b\",\"OrganizationName\":\"unq0907b5d330dbee119048000d3a106\",\"OutputParameters\":[],\"OwningExtension\":{\"Id\":\"d842871b-5a50-f011-877a-7c1e520daafb\",\"KeyAttributes\":[],\"LogicalName\":\"sdkmessageprocessingstep\",\"Name\":null,\"RowVersion\":null},\"ParentContext\":null,\"PostEntityImages\":[],\"PreEntityImages\":[],\"PrimaryEntityId\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"PrimaryEntityName\":\"none\",\"RequestId\":\"1a69d05c-9a5f-4e4f-8bb4-9572b4976f89\",\"SecondaryEntityName\":\"none\",\"SharedVariables\":[{\"key\":\"IsAutoTransact\",\"value\":false},{\"key\":\"AcceptLang\",\"value\":\"\"},{\"key\":\"ChangedEntityTypes\",\"value\":[{\"__type\":\"KeyValuePairOfstringstring:#System.Collections.Generic\",\"key\":\"customeraddress\",\"value\":\"Update\"},{\"__type\":\"KeyValuePairOfstringstring:#System.Collections.Generic\",\"key\":\"contact\",\"value\":\"Update\"},{\"__type\":\"KeyValuePairOfstringstring:#System.Collections.Generic\",\"key\":\"fileattachment\",\"value\":\"Create\"}]}],\"Stage\":30,\"UserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"UserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\"},\"PostEntityImages\":[],\"PreEntityImages\":[],\"PrimaryEntityId\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"PrimaryEntityName\":\"contact\",\"RequestId\":\"1a69d05c-9a5f-4e4f-8bb4-9572b4976f89\",\"SecondaryEntityName\":\"none\",\"SharedVariables\":[{\"key\":\"IsAutoTransact\",\"value\":true},{\"key\":\"AcceptLang\",\"value\":\"\"}],\"Stage\":30,\"UserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"UserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\"},\"PostEntityImages\":[{\"key\":\"AsynchronousStepPrimaryName\",\"value\":{\"Attributes\":[{\"key\":\"fullname\",\"value\":\"Satish Gannu\"},{\"key\":\"contactid\",\"value\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":null}}],\"PreEntityImages\":[],\"PrimaryEntityId\":\"cf34a24c-b50f-f011-9989-000d3a55ebc9\",\"PrimaryEntityName\":\"contact\",\"RequestId\":\"1a69d05c-9a5f-4e4f-8bb4-9572b4976f89\",\"SecondaryEntityName\":\"none\",\"SharedVariables\":[{\"key\":\"IsAutoTransact\",\"value\":true},{\"key\":\"AcceptLang\",\"value\":\"\"}],\"Stage\":40,\"UserAzureActiveDirectoryObjectId\":\"b15e91d8-2cd2-4ae7-a7e4-c31dca73e2d2\",\"UserId\":\"bba0abd6-8c7b-ef11-a671-000d3a16b58c\"}"}}