import datetime
import os
import sys
import tempfile
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import uuid
from cryptography.fernet import Fernet
import psycopg2
from dp_portal.config.azure_config_fetcher import fetch_config_from_azure_for_dp_portal
from dp_portal.db.postgres_connector import PostgresConnector
from dp_portal.appLogger import AppLogger
from datetime import datetime, timedelta, timezone
from selenium import webdriver

class BasePage:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 20)

class RecruiterHomePage(BasePage):
    URL = "https://recruiter.dv.tandymgroup.com/"

    _header_locator = (By.XPATH, "//p[contains(text(), 'About Recruitment Portal')]")

    def open(self):
        self.driver.get(self.URL)

    @property
    def header(self):
        return self.wait.until(EC.visibility_of_element_located(self._header_locator))

    def is_loaded(self):
     actual_text = self.header.text
     print(f"Header text found: '{actual_text}'")  # print what you found
     assert actual_text == "About Recruitment Portal", f"Expected header text to be 'About Recruitment Portal' but got '{actual_text}'"
     print("Page loaded successfully with correct header.")


def generate_uuid_step(context):
        regression_key = str(uuid.uuid4())
        context.regression_key = regression_key
        print(f"Generated regression key: {regression_key }")

def key_encrypts(context):
    encrypted_key = context.fernet.encrypt(context.regression_key.encode())  
    context.encrypted_regression_key = encrypted_key.decode()
    print(f"Encrypted regression key: {context.encrypted_regression_key}")

    # Save encrypted key to temp file for reuse
    temp_file = os.path.join(tempfile.gettempdir(), "encrypted_regression_key.txt")
    with open(temp_file, "w") as f:
        f.write(context.encrypted_regression_key)


def insert_token_in_db(context):
    try:
        # Prepare values
        regression_key = context.encrypted_regression_key
        created_at = datetime.now(timezone.utc) 
        expires_at = created_at + timedelta(days=5)
        is_active = True
        is_regression_allowed = True
        created_by = "<EMAIL>"
        created_by_name  = "Paridhi Jain"
        
        cursor = context.db_cursor

        # Step 1: Check if any entry exists
        check_query = "SELECT id FROM tandymrecport.rp_regression_keys LIMIT 1"
        cursor.execute(check_query)
        result = cursor.fetchone()

        if result:
            # Step 2: Update the existing entry
            update_query = """
                UPDATE tandymrecport.rp_regression_keys
                SET regression_key = %s,
                    created_at = %s,
                    expires_at = %s,
                    is_active = %s,
                    is_regression_allowed = %s,
                    created_by = %s,
                    created_by_name = %s
                WHERE id = %s
            """
            cursor.execute(update_query, (
                regression_key,
                created_at,
                expires_at,
                is_active,
                is_regression_allowed,
                created_by,
                created_by_name,
                result[0]  
            ))
            context.logger.info(f"🔄 Updated existing regression key (id: {result[0]})")
        else:
            # Step 3: No row found → insert new
            row_id = str(uuid.uuid4())
            insert_query = """
                INSERT INTO tandymrecport.rp_regression_keys (
                    id, regression_key, created_at, expires_at, is_active, is_regression_allowed, created_by, created_by_name
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (
                row_id,
                regression_key,
                created_at,
                expires_at,
                is_active,
                is_regression_allowed,
                created_by,
                created_by_name
            ))
            context.logger.info(f"Inserted new regression key (id: {row_id})")

        # Commit after insert or update
        context.db_connection.commit()

    except Exception as e:
        context.db_connection.rollback()
        context.logger.error(f"Failed to insert/update regression key: {e}")
        raise

def load_regression_key_from_file():
    """Load encrypted regression_key from a temp file (written during login)."""
    temp_file = os.path.join(tempfile.gettempdir(), "encrypted_regression_key.txt")
    if not os.path.exists(temp_file):
        raise FileNotFoundError("encrypted_regression_key file not found. Ensure login ran first.")

    with open(temp_file, "r") as f:
        return f.read().strip()
    
def open_recruiter_portal(context):
    """Open the recruiter portal with the encrypted regression key."""
    try:
        # Load regression key from temp file
        regression_key = load_regression_key_from_file()
        context.regression_key = regression_key
        print(f"Using regression key: {regression_key}")

        URLWITH_KEY = f"{RecruiterHomePage.URL}?regression_key={regression_key}"
        print(f"Opening URL: {URLWITH_KEY}")
        context.driver.get(URLWITH_KEY)

        # 🔍 Add debugging here
        print(f" Page title after loading portal: {context.driver.title}")
        print(f" Current URL: {context.driver.current_url}")

        # Initialize home page object
        context.home_page = RecruiterHomePage(context.driver)

        # Verify page loaded correctly
        context.home_page.is_loaded()
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located(RecruiterHomePage._header_locator)
        )

    except Exception as e:
        print(f"Error opening recruiter portal: {e}")
        raise   

def navigated_to_portal(context):
    """Verify user is navigated to the portal without AD authentication."""
    try:
        # Check if the home page is loaded
        context.home_page.is_loaded()
        print("User successfully navigated to the portal without AD authentication.")
    except AssertionError as e:
        print(f"Navigation failed: {e}")
        raise



