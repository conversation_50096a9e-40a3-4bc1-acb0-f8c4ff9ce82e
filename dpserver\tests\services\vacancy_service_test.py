import pytest
import uuid
import json
from datetime import datetime, timezone
from unittest import mock
from unittest.mock import Mock,patch
from unittest.mock import mock_open,MagicMock
from common.db.postgres_connector import PostgresConnector
from fastapi import HTTPException
from dpserver.services.vacancy_service import VacancyService
import psycopg2
from common.db.config_postgres import PostgresEnvironment

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    mock = MagicMock(spec=PostgresConnector)

    # Mock connection
    mock_conn = MagicMock()
    mock_conn.closed = False

    # Mock cursor context manager
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_conn.cursor.return_value.__exit__.return_value = False   

    # Attach to mock connector
    mock.connection = mock_conn
    mock.connect.return_value = mock_conn
    mock.schema = "test_schema"
    mock.config = {"dbname": "test_db"}

    return mock

@pytest.fixture(autouse=True)
def mock_env_token_and_creds():
    mock_token_obj = MagicMock()
    mock_token_obj.get_token.return_value = "fake-token"

    with patch("dpserver.services.vacancy_service.get_token_for_env", return_value=mock_token_obj), \
         patch("dpserver.services.vacancy_service.get_dataverse_credentials_for_env", return_value={
             "RESOURCE_URL": "https://dummy.crm.dynamics.com",
             "TENANT_ID": "dummy-tenant-id",
             "CLIENT_ID": "dummy-client-id",
             "CLIENT_SECRET": "dummy-secret"
         }):
        yield

@pytest.fixture
def vacancy_service_instance(mock_logger, mock_db_connector):
    return VacancyService(logger=mock_logger, db_connector=mock_db_connector)

@pytest.fixture
def mock_cursor():
    cursor = MagicMock()
    return cursor

@pytest.fixture
def mock_conn(mock_cursor):
    mock_conn = Mock()
    mock_conn.cursor.return_value = mock_cursor
    return mock_conn

@pytest.fixture(autouse=True)
def patch_db_connection(vacancy_service_instance, mock_conn):
    with patch.object(vacancy_service_instance, '_get_active_db_connection', return_value=mock_conn):
        yield

@pytest.fixture
def fitness_data():
    return {
        "vacancy_refno": "VAC123",
        "candidate_contact_id": "123e4567-e89b-12d3-a456-************",
        "fitness_reason_text": "Great fit for the role",
        "author_email": "<EMAIL>"
    }

def make_data(**kwargs):
    # Helper to create input data dict
    defaults = {
        "candidate_contact_id": "123e4567-e89b-12d3-a456-************",
        "vote": "like",
        "comment": "Good fit",
        "reviewer_email": "<EMAIL>",
        "vacancy_refno": "VAC123"
    }
    defaults.update(kwargs)
    return defaults

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_both_sources(mock_v0, mock_v1, vacancy_service_instance):
    # v1 returns two vacancies, v0 returns one (one duplicate)
    v1_vacancies = [{"vacancy_id": "1"}, {"vacancy_id": "2"}]
    v0_vacancies = [{"vacancy_id": "2"}, {"vacancy_id": "3"}]
    mock_v1.return_value = {"vacancies": v1_vacancies}
    mock_v0.return_value = {"vacancies": v0_vacancies}
    result = vacancy_service_instance.get_vacancies()
    ids = [v["vacancy_id"] for v in result["vacancies"]]
    assert set(ids) == {"1", "2", "3"}
    assert len(result["vacancies"]) == 3

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_v1_only(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancies": [{"vacancy_id": "1"}]}
    mock_v0.return_value = {"vacancies": []}
    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == [{"vacancy_id": "1"}]

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_v0_only(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancies": []}
    mock_v0.return_value = {"vacancies": [{"vacancy_id": "2"}]}
    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == [{"vacancy_id": "2"}]

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_duplicates(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancies": [{"vacancy_id": "1"}, {"vacancy_id": "2"}]}
    mock_v0.return_value = {"vacancies": [{"vacancy_id": "2"}, {"vacancy_id": "3"}]}
    result = vacancy_service_instance.get_vacancies()
    ids = [v["vacancy_id"] for v in result["vacancies"]]
    assert set(ids) == {"1", "2", "3"}
    assert len(result["vacancies"]) == 3

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_v1_exception(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.side_effect = Exception("v1 error")
    mock_v0.return_value = {"vacancies": [{"vacancy_id": "2"}]}
    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == [{"vacancy_id": "2"}]

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_v0_exception(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancies": [{"vacancy_id": "1"}]}
    mock_v0.side_effect = Exception("v0 error")
    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == [{"vacancy_id": "1"}]

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancies_v0')
def test_get_vacancies_empty(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancies": []}
    mock_v0.return_value = {"vacancies": []}
    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == []

def test_get_vacancies_no_description(vacancy_service_instance):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    mock_cursor.description = None
    mock_cursor.fetchall.return_value = []
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = Mock(return_value=mock_conn)

    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == []

def test_get_vacancies_empty_fetch(vacancy_service_instance):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    mock_cursor.description = [
        ("vacancy_id",), ("subcategory",), ("refno",), ("vacancy_data",),
        ("reviewer_config",), ("is_locked",), ("locked_by",), ("archived",)
    ]
    mock_cursor.fetchall.return_value = []
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = Mock(return_value=mock_conn)

    result = vacancy_service_instance.get_vacancies()
    assert result["vacancies"] == []

def test_start_review_success(vacancy_service_instance, mock_conn, mock_cursor, monkeypatch):
    monkeypatch.setattr(vacancy_service_instance, "_get_active_db_connection", lambda: mock_conn)

    vacancy_id = "abc123"
    reviewer_email = "<EMAIL>"

    current_config = {
        "current": {},
        "history": [],
        "review_complete": False
    }

    mock_cursor.fetchone.side_effect = [
        (current_config, False, None),  # SELECT result
        (vacancy_id, current_config, reviewer_email)  # UPDATE RETURNING result
    ]

    result = vacancy_service_instance.start_vacancy_review(vacancy_id, reviewer_email)

    assert result["status_code"] == 200
    assert result["message"] == "Review process started successfully"
    mock_conn.commit.assert_called()
    mock_cursor.close.assert_called()

def test_invalid_email_domain(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************" 
    invalid_email = "<EMAIL>"  # invalid domain

    response = vacancy_service_instance.start_vacancy_review(vacancy_id, invalid_email)

    assert response["status_code"] == 400
    assert response["error"] == "Invalid reviewer email domain"
    assert "tandymgroup.com" in response["details"]

def test_review_in_progress_by_other(vacancy_service_instance, mock_conn, mock_cursor, monkeypatch):
    monkeypatch.setattr(vacancy_service_instance, "_get_active_db_connection", lambda: mock_conn)

    mock_cursor.fetchone.return_value = (
        {"current": {"reviewer": "<EMAIL>"}}, False, None
    )

    result = vacancy_service_instance.start_vacancy_review("abc123", "<EMAIL>")

    assert result["status_code"] == 409
    assert "currently being reviewed by" in result["error"]

def test_exception_during_review_start(vacancy_service_instance, mock_conn, mock_cursor, monkeypatch):
    monkeypatch.setattr(vacancy_service_instance, "_get_active_db_connection", lambda: mock_conn)

    mock_cursor.fetchone.side_effect = Exception("DB error")

    result = vacancy_service_instance.start_vacancy_review("abc123", "<EMAIL>")

    assert result["status_code"] == 500
    assert "Error starting review process" in result["error"]
    mock_conn.rollback.assert_called()
    mock_cursor.close.assert_called()
    
def test_vacancy_already_locked(vacancy_service_instance, mock_cursor):
    mock_cursor.fetchone.return_value = ({"current": {"reviewer": "<EMAIL>"}}, True, "someone")
    result = vacancy_service_instance.complete_vacancy_review("id", "<EMAIL>")
    assert result["status_code"] == 409
    assert "Vacancy is already locked" in result["error"]

def test_unauthorized_reviewer(vacancy_service_instance, mock_cursor):
    mock_cursor.fetchone.return_value = ({"current": {"reviewer": "<EMAIL>"}}, False, "someone")
    result = vacancy_service_instance.complete_vacancy_review("id", "<EMAIL>")
    assert result["status_code"] == 403
    assert "Only the assigned reviewer can complete the review" in result["error"]

def test_successful_review_completion(vacancy_service_instance, mock_cursor, mock_conn):
    reviewer_email = "<EMAIL>"
    current_config = {
        "current": {"reviewer": reviewer_email, "review_start_timestamp": "2024-06-01T00:00:00Z"},
        "history": []
    }

    mock_cursor.fetchone.side_effect = [
        (current_config, False, reviewer_email),  # SELECT result
        ("vac-id", {"some": "config"}, True, reviewer_email)  # UPDATE RETURNING
    ]

    result = vacancy_service_instance.complete_vacancy_review("vac-id", reviewer_email)

    assert result["status_code"] == 200
    assert result["vacancy_id"] == "vac-id"
    assert result["locked_by"] == reviewer_email
    mock_conn.commit.assert_called_once()

def test_complete_vacancy_review_not_found(vacancy_service_instance, mock_cursor):
    mock_cursor.fetchone.return_value = None
    result = vacancy_service_instance.complete_vacancy_review("missing-id", "<EMAIL>")
    assert result["status_code"] == 404
    assert "not found" in result["error"]

def test_complete_vacancy_review_failed_update(vacancy_service_instance, mock_cursor):
    reviewer_email = "<EMAIL>"
    current_config = {
        "current": {"reviewer": reviewer_email, "review_start_timestamp": "2024-06-01T00:00:00Z"},
        "history": []
    }
    # SELECT returns valid row, UPDATE returns None
    mock_cursor.fetchone.side_effect = [
        (current_config, False, reviewer_email),
        None
    ]
    result = vacancy_service_instance.complete_vacancy_review("vac-id", reviewer_email)
    assert result["status_code"] == 500
    assert "Failed to complete review" in result["error"]

def test_complete_vacancy_review_exception(vacancy_service_instance, mock_cursor, mock_conn):
    # Simulate exception during SELECT
    mock_cursor.execute.side_effect = Exception("DB error")
    result = vacancy_service_instance.complete_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 500
    assert "Error completing review process" in result["error"]

def test_get_review_status_success(vacancy_service_instance, mock_cursor, mock_conn):
    # Setup mock
    vacancy_service_instance._get_active_db_connection = Mock(return_value=mock_conn)
    vacancy_service_instance.db.connect = Mock()

    vacancy_id = "123e4567-e89b-12d3-a456-************"
    expected_row = ({"current": {"reviewer": "<EMAIL>"}}, False, "<EMAIL>")
    mock_cursor.fetchone.return_value = expected_row

    # Act
    result = vacancy_service_instance.get_vacancy_review_status(vacancy_id)

    # Assert
    assert result["vacancy_id"] == vacancy_id
    assert result["reviewer_config"] == expected_row[0]
    assert result["is_locked"] == expected_row[1]
    assert result["locked_by"] == expected_row[2]
    mock_cursor.close.assert_called_once()

def test_get_review_status_vacancy_not_found(vacancy_service_instance, mock_cursor, mock_conn):
    vacancy_service_instance._get_active_db_connection = Mock(return_value=mock_conn)
    vacancy_service_instance.db.connect = Mock()

    mock_cursor.fetchone.return_value = None

    result = vacancy_service_instance.get_vacancy_review_status("vacancy-not-found")

    assert "error" in result
    assert "not found" in result["error"]
    mock_cursor.close.assert_called_once()

def test_get_review_status_exception_handled(vacancy_service_instance, mock_cursor, mock_conn):
    vacancy_service_instance._get_active_db_connection = Mock(return_value=mock_conn)
    vacancy_service_instance.db.connect = Mock()

    mock_cursor.execute.side_effect = Exception("DB error")

    result = vacancy_service_instance.get_vacancy_review_status("some-id")

    assert "error" in result
    assert "Error getting review status" in result["error"]
    assert "DB error" in result["error"]
    mock_cursor.close.assert_called_once()

def test_db_exception(vacancy_service_instance, mock_conn):
    # Simulate cursor() raising exception
    mock_conn.cursor.side_effect = Exception("DB error")
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    result = vacancy_service_instance.get_vacancy_review_status("vacancy-uuid")
    assert result["error"].startswith("Error getting review status:")
    assert result["status_code"] == 500
    mock_conn.close.assert_called_once()

def test_successful_review_status(vacancy_service_instance, mock_conn, mock_cursor):
    # Setup mock connection and cursor
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    # Simulate DB row found
    mock_cursor.fetchone.return_value = ("config", True, "<EMAIL>")
    result = vacancy_service_instance.get_vacancy_review_status("vacancy-uuid")
    assert result["vacancy_id"] == "vacancy-uuid"
    assert result["reviewer_config"] == "config"
    assert result["is_locked"] is True
    assert result["locked_by"] == "<EMAIL>"
    assert result["status_code"] == 200
    mock_cursor.close.assert_called_once()
    mock_conn.close.assert_called_once()

@patch("dpserver.services.vacancy_service.read_json_file")
def test_get_candidate_resume_success(mock_read_json_file, vacancy_service_instance, mock_logger):
    contact_id = "abc-123"
    mock_read_json_file.return_value = {"name": "John Doe"}

    result = vacancy_service_instance.get_candidate_resume_from_file(contact_id)

    assert result == {"candidate": {"name": "John Doe"}}
    # Match exact logger message (even though it's wrong)
    mock_logger.info.assert_called_once_with("Fetching candidate resume from the file.{contact_id}")

@patch("dpserver.services.vacancy_service.read_json_file", side_effect=Exception("File not found"))
def test_get_candidate_resume_error(mock_read_json_file, vacancy_service_instance, mock_logger):
    contact_id = "abc-123"
    
    result = vacancy_service_instance.get_candidate_resume_from_file(contact_id)

    assert result == {"error": "Error fetching candidate resume"}
    assert mock_logger.error.call_count == 1

    logged_message = mock_logger.error.call_args[0][0]
    assert logged_message.startswith("Error fetching candidate resume:")

def test_missing_candidate_contact_id_or_vacancy_refno(vacancy_service_instance):
    data = {"vacancy_refno": None, "candidate_contact_id": None}
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "candidate_contact_id and vacancy_refno" in result["error"]

def test_update_candidate_decision_in_db_missing_reviewer_email(vacancy_service_instance):
    # reviewer_email is missing
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "vac123",
        "vote": "like",
        "comment": "Good fit"
        # reviewer_email omitted
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert result["error"] == "reviewer_email is required"

def test_invalid_candidate_contact_id_format(vacancy_service_instance):
    data = {"candidate_contact_id": "not-a-uuid", "vacancy_refno": "vac123", "reviewer_email": "<EMAIL>"}
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "Invalid candidate_contact_id format" in result["error"]

def test_vacancy_locked(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.side_effect = Exception("Vacancy is locked")
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "vac123",
        "reviewer_email": "<EMAIL>"
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 500
    assert "Error updating candidate decision in DB" in result["error"]

def test_vacancy_locked_by_other(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.side_effect = Exception("currently being reviewed by someone else")
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "vac123",
        "reviewer_email": "<EMAIL>"
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 500
    assert "Error updating candidate decision in DB" in result["error"]

def test_reviewer_config_mismatch(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.side_effect = Exception("Only the assigned reviewer can modify the vacancy")
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "vac123",
        "reviewer_email": "<EMAIL>"
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 500
    assert "Error updating candidate decision in DB" in result["error"]
    
@pytest.mark.parametrize("radius_override, vacancy_data_row, job_location, expected_radius", [
    ("10", None, None, "10"),  # radius_override used
    (None, {"job_template": {"job_location": [{"city": "Boston", "state": "MA", "radius": "25"}]}},
     [{"city": "Boston", "state": "MA", "radius": "25"}], "25"),  # radius from vacancy_data
    (None, {"job_template": {"job_location": [{"city": "remote", "state": "remote"}]}},
     [{"city": "remote", "state": "remote"}], None),  # remote job, no radius
    (None, None, None, None)  # no vacancy_data
])

def test_get_candidates_by_vacancy_id_variants(vacancy_service_instance, mock_conn, mock_cursor, radius_override,
                                                vacancy_data_row, job_location, expected_radius):
    # Mock vacancy_data fetch
    mock_cursor.fetchone.side_effect = [(vacancy_data_row,)] if vacancy_data_row else [None]

    # Mock candidates query
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]

    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {"city": "Boston", "state": "MA", "availability": "2023-01-01", "placement_status": "Available", "modified": "2022-12-01"},
        {"job_titles": 1.0, "soft_skills": 0.9, "technical_skills": 0.8, "tools_platforms": 0.7, "degrees_certs": 0.6, "job_title_recency": 0.5, "industry_relevance": 0.4},
        12.5,  # distance_from_work_site
        [{"reviewer_email": "<EMAIL>", "vote": "yes", "comment": "Great fit", "feedback_date": "2024-01-01"}],
        {"current": {"reason": "Qualified", "author": "Admin", "timestamp": "2024-01-01"}},
        "response", datetime(2024, 1, 1, tzinfo=timezone.utc),
        {"status": "shortlisted", "shortlisted_at": "2024-01-01", "shortlisted_by": "admin", "crimson_vacancycandidateid": "abc123"},
        0.88,  # calculated_normalized_score
        1.0,   # total_intermediate_score
        "REF123"
    ]]

    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="123e4567-e89b-12d3-a456-************",
        reviewer_email="<EMAIL>",
        radius_override=radius_override
    )

    assert "candidates" in result
    assert isinstance(result["candidates"], list)
    assert result["candidates"][0]["candidate_data"]["recruiter_review_decision"]["vote"] == "yes"
    assert result["candidates"][0]["candidate_data"]["classification score"]["jobtitlescore"] == 1.0

def test_get_candidates_by_vacancy_id_no_results(vacancy_service_instance, mock_conn, mock_cursor):
    # Arrange
    mock_cursor.fetchone.return_value = None
    mock_cursor.fetchall.return_value = []
    mock_cursor.description = None

    # Act
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="abc",
        reviewer_email="<EMAIL>"
    )

    # Assert candidates list
    assert result["candidates"] == []

    # Convert the returned timestamp to datetime for robust comparison
    result_time_str = result["system_data_retrieval_time"]
    result_time = datetime.fromisoformat(result_time_str.replace("Z", "+00:00"))
    now = datetime.now(timezone.utc)

    # Allow small time drift (e.g., 60 seconds)
    assert abs((now - result_time).total_seconds()) < 60

def test_get_candidates_by_vacancy_id_exception(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.execute.side_effect = Exception("DB failure")

    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="abc",
        reviewer_email="<EMAIL>"
    )

    assert result["error"].startswith("Error fetching candidates")

VALID_UUID = str(uuid.uuid4())
NOW = datetime.now(timezone.utc)

def test_valid_vacancy_with_all_data(vacancy_service_instance, mock_cursor):
    mock_cursor.fetchone.side_effect = [
        (1,),  # vacancy check
        (NOW, NOW, False),  # timestamps
        ("completed", NOW, NOW, "<EMAIL>")  # catalyst match
    ]

    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy(VALID_UUID)

    assert result["status_code"] == 200
    assert result["update_timestamps"]["match_results_generated_at"] == NOW.isoformat()
    assert result["catalyst_match_status_data"]["status"] == "completed"

# def test_vacancy_id_not_found(vacancy_service_instance, mock_cursor):
#     mock_cursor.fetchone.return_value = None

#     result = vacancy_service_instance.get_catalyst_match_status_for_vacancy(VALID_UUID)

#     assert result["status_code"] == 404
#     assert "Vacancy with ID" in result["error"]

# @patch("dpserver.services.vacancy_service.uuid")
# def test_invalid_uuid_format(mock_uuid, vacancy_service_instance):
#     mock_uuid.UUID.side_effect = ValueError("bad uuid")
#     result = vacancy_service_instance.get_catalyst_match_status_for_vacancy("invalid-uuid")
#     assert result["status_code"] == 400
#     assert "Invalid vacancy_id format" in result["error"]

# def test_vacancy_id_not_found_test(vacancy_service_instance, mock_conn):
#     mock_cursor = mock_conn.cursor.return_value
#     mock_cursor.fetchone.side_effect = [None]  # No vacancy found

#     response = vacancy_service_instance.get_catalyst_match_status_for_vacancy(str(uuid.uuid4()))

#     assert response["status_code"] == 404
#     assert "not found" in response["error"]


def test_no_shortlist_processed_record(vacancy_service_instance, mock_conn):
    vacancy_id = str(uuid.uuid4())
    mock_cursor = mock_conn.cursor.return_value

    # Simulate vacancy_id found
    mock_cursor.fetchone.side_effect = [
        (1,),        # Vacancy exists
        None,        # No timestamps
        ('in_progress', None, None, '<EMAIL>')  # Catalyst job exists
    ]

    response = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)

    assert response["status_code"] == 200
    assert response["update_timestamps"]["match_results_generated_at"] is None
    assert response["catalyst_match_status_data"]["status"] == 'in_progress'

def test_no_catalyst_match_record_found(vacancy_service_instance, mock_conn):
    vacancy_id = str(uuid.uuid4())
    mock_cursor = mock_conn.cursor.return_value

    # Simulate vacancy_id found and timestamps exist
    mock_cursor.fetchone.side_effect = [
        (1,),                      # Vacancy exists
        (None, None, False),       # Timestamps
        None                       # No catalyst match row
    ]

    response = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)

    assert response["status_code"] == 404
    assert "Catalyst Match has not yet been enabled" in response["error"]

def test_successful_match_status_fetch(vacancy_service_instance, mock_conn):
    vacancy_id = str(uuid.uuid4())
    mock_cursor = mock_conn.cursor.return_value

    from datetime import datetime

    # Vacancy exists, timestamps exist, catalyst match exists
    mock_cursor.fetchone.side_effect = [
        (1,),  # vacancy exists
        (datetime(2024, 1, 1), datetime(2024, 1, 2), False),  # timestamps
        ('completed', datetime(2024, 1, 1), datetime(2024, 1, 3), '<EMAIL>')  # catalyst
    ]

    response = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)

    assert response["status_code"] == 200
    assert response["update_timestamps"]["match_results_generated_at"] == "2024-01-01T00:00:00"
    assert response["catalyst_match_status_data"]["status"] == "completed"
    assert response["catalyst_match_status_data"]["initiated_by"] == "<EMAIL>"

def test_invalid_vacancy_id_format(vacancy_service_instance):
    response = vacancy_service_instance.regenerate_catalyst_match("invalid-uuid", "<EMAIL>")
    assert response["status_code"] == 400
    assert "Invalid vacancy_id format" in response["error"]


def test_job_already_in_progress(vacancy_service_instance, mock_conn):
    mock_cursor = mock_conn.cursor.return_value

    # Simulate: job is "inprocess"
    mock_cursor.fetchone.side_effect = [
        ("inprocess", datetime.now(timezone.utc), "<EMAIL>")
    ]

    response = vacancy_service_instance.regenerate_catalyst_match(str(uuid.uuid4()), "<EMAIL>")

    assert response["status_code"] == 409
    assert "already exists" in response["error"]

@patch("generator.populate_vacancy_skills.populate_vacancy_tags")
def test_exception_during_processing(mock_populate_tags, vacancy_service_instance, mock_conn):
    vacancy_id = str(uuid.uuid4())
    reviewer_email = "<EMAIL>"

    # Simulate error when calling .cursor()
    mock_conn.cursor.side_effect = Exception("DB error")

    response = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)

    assert response["status_code"] == 500
    assert "Server error while enqueuing" in response["error"]

def test_generic_exception_rollback(vacancy_service_instance, mock_conn):
    mock_conn.cursor.side_effect = Exception("unexpected")

    result = vacancy_service_instance.regenerate_catalyst_match(
        vacancy_id=VALID_UUID,
        reviewer_email="<EMAIL>"
    )

    assert result["status_code"] == 500
    assert "Server error" in result["error"]

def test_missing_candidate_contact_id(vacancy_service_instance):
    data = make_data(candidate_contact_id=None)
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "candidate_contact_id" in result["error"]

def test_missing_vacancy_refno(vacancy_service_instance):
    data = make_data(vacancy_refno=None)
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "vacancy_refno" in result["error"]

def test_missing_reviewer_email(vacancy_service_instance):
    data = make_data(reviewer_email=None)
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "reviewer_email" in result["error"]

def test_invalid_candidate_contact_id(vacancy_service_instance):
    data = make_data(candidate_contact_id="not-a-uuid")
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 400
    assert "Invalid candidate_contact_id format" in result["error"]

def test_update_candidate_decision_in_db_vacancy_not_found(vacancy_service_instance, mock_conn, mock_cursor):
    # Simulate DB returns no vacancy for the given refno
    mock_cursor.fetchone.return_value = None
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "nonexistent-refno",
        "reviewer_email": "<EMAIL>",
        "vote": "like",
        "comment": "Good fit"
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    assert result["status_code"] == 404
    assert "Vacancy with refno nonexistent-refno not found." in result["error"]

def test_update_candidate_decision_in_db_no_base_application_record(vacancy_service_instance, mock_conn, mock_cursor):
    sequence = [
        ("vacancy-uuid",),  # vacancy_id found
        (None,),            # feedback_row (no feedbacks found)
        (None,),            # detail_row for fitness_reason
        None                # updated_row_original_table (no record found)
    ]
    mock_cursor.fetchone.side_effect = sequence
    mock_cursor.execute.return_value = None
    mock_cursor.rowcount = 0  # Ensure rowcount is an int, not MagicMock
    data = {
        "candidate_contact_id": str(uuid.uuid4()),
        "vacancy_refno": "vac123",
        "reviewer_email": "<EMAIL>",
        "vote": "like",
        "comment": "Good fit"
    }
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    print(result)  # For debug
    assert result["status_code"] == 404
    assert "No base application record found for vacancy_id vacancy-uuid and contact_id" in result["error"]

@patch("dpserver.services.vacancy_service.VacancyService._get_active_db_connection")
def test_successful_update(mock_conn, vacancy_service_instance):
    mock_cursor = MagicMock()
    mock_cursor.fetchone.side_effect = [
        ("vacancy-uuid",),  # vacancy_id
        [],                 # feedback_row
        (None,),            # detail_row for fitness_reason
        ("application_id",)         # updated_row_original_table
    ]
    mock_conn.return_value.cursor.return_value = mock_cursor
    mock_conn.return_value.commit = MagicMock()
    data = make_data()
    result = vacancy_service_instance.update_candidate_decision_in_db(data)
    # Success case does not return status_code
    assert result.get("status_code") is None
    assert result["message"] == "Candidate decision updated successfully in DB"

def test_update_candidate_decision_in_db_unexpected_exception(vacancy_service_instance, mock_conn, mock_cursor):
        # Simulate an unexpected exception during the database operation
        mock_cursor.fetchone.side_effect = Exception("Unexpected DB error")
        data = {
            "candidate_contact_id": str(uuid.uuid4()),
            "vacancy_refno": "VAC123",
            "reviewer_email": "<EMAIL>",
            "vote": "like",
            "comment": "Good fit"
        }
        result = vacancy_service_instance.update_candidate_decision_in_db(data)
        assert result["status_code"] == 500
        assert "Error updating candidate decision in DB" in result["error"]
        assert "Unexpected DB error" in result["error"]

@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v0')
def test_v1_success(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"status_code": 200, "data": "v1"}
    mock_v0.return_value = {"status_code": 404, "error": "not found"}
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy("vac123")
    assert result["status_code"] == 200
    assert result["data"] == "v1"

@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v0')
def test_v0_success(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"status_code": 404, "error": "not found"}
    mock_v0.return_value = {"status_code": 200, "data": "v0"}
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy("vac123")
    assert result["status_code"] == 200
    assert result["data"] == "v0"

@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v0')
def test_both_fail(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"status_code": 404, "error": "not found"}
    mock_v0.return_value = {"status_code": 404, "error": "not found"}
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy("vac123")
    assert result["status_code"] == 404
    assert "Catalyst Match has not yet been enabled" in result["error"]

@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v0')
def test_v1_exception(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.side_effect = Exception("v1 error")
    mock_v0.return_value = {"status_code": 200, "data": "v0"}
    # The method does not catch exceptions, so we expect pytest.raises
    with pytest.raises(Exception) as excinfo:
        vacancy_service_instance.get_catalyst_match_status_for_vacancy("vac123")
    assert "v1 error" in str(excinfo.value)

@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_catalyst_match_status_for_vacancy_v0')
def test_v0_exception(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"status_code": 404, "error": "not found"}
    mock_v0.side_effect = Exception("v0 error")
    with pytest.raises(Exception) as excinfo:
        vacancy_service_instance.get_catalyst_match_status_for_vacancy("vac123")
    assert "v0 error" in str(excinfo.value)

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v0')
def test_get_vacancy_by_id_v1_found(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = {"vacancy": {"vacancy_id": "v1-id"}}
    mock_v0.return_value = None
    result = vacancy_service_instance.get_vacancy_by_id("v1-id")
    assert result == {"vacancy": {"vacancy_id": "v1-id"}}

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v0')
def test_get_vacancy_by_id_v0_found(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = None
    mock_v0.return_value = {"vacancy": {"vacancy_id": "v0-id"}}
    result = vacancy_service_instance.get_vacancy_by_id("v0-id")
    assert result == {"vacancy": {"vacancy_id": "v0-id"}}

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v0')
def test_get_vacancy_by_id_not_found(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.return_value = None
    mock_v0.return_value = None
    result = vacancy_service_instance.get_vacancy_by_id("not-found-id")
    assert result == {"vacancy": None}

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v0')
def test_get_vacancy_by_id_v1_exception_v0_success(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.side_effect = Exception("v1 error")
    mock_v0.return_value = {"vacancy": {"vacancy_id": "v0-id"}}
    result = vacancy_service_instance.get_vacancy_by_id("v0-id")
    assert result == {"vacancy": {"vacancy_id": "v0-id"}}

@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v1')
@patch('dpserver.services.vacancy_service.VacancyService.get_vacancy_by_id_v0')
def test_get_vacancy_by_id_both_exceptions(mock_v0, mock_v1, vacancy_service_instance):
    mock_v1.side_effect = Exception("v1 error")
    mock_v0.side_effect = Exception("v0 error")
    result = vacancy_service_instance.get_vacancy_by_id("err-id")
    assert result == {"vacancy": None}

def assert_candidates_or_error(result):
    if "candidates" in result:
        assert isinstance(result["candidates"], list)
    else:
        assert "error" in result


def test_get_candidates_by_vacancy_id_resume_data_none(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", None,
        None, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_category_scores_none(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        None, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_feedbacks_none(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_no_feedback_for_reviewer(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, [
            {"reviewer_email": "<EMAIL>", "vote": "no", "comment": "Not a fit", "feedback_date": "2024-01-01"}
        ], None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_remote_job_location(vacancy_service_instance, mock_conn, mock_cursor):
    # Simulate remote job location, radius should not be applied
    mock_cursor.fetchone.side_effect = [[{"job_template": {"job_location": [{"city": "remote", "state": "remote"}]}}]]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_fitness_reason_not_dict(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, None, "not-a-dict", None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_shortlisted_none(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_info_bot_response_date_string(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [[
        "cid123", "<EMAIL>", "Test Candidate", {},
        {}, 10.0, None, None, None, "2024-01-01T00:00:00", None, 0.5, 1.0, "REF123"
    ]]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)


def test_get_candidates_by_vacancy_id_multiple_candidates(vacancy_service_instance, mock_conn, mock_cursor):
    mock_cursor.fetchone.return_value = [(None,)]
    mock_cursor.description = [
        ("candidate_contactid",), ("email",), ("candidate_name",), ("resume_data",),
        ("category_intermediate_scores",), ("distance_from_work_site",), ("feedbacks",),
        ("fitness_reason",), ("info_bot_response",), ("info_bot_response_date",),
        ("shortlisted",), ("calculated_normalized_score",), ("total_intermediate_score",),
        ("vacancy_refno",)
    ]
    mock_cursor.fetchall.return_value = [
        ["cid123", "<EMAIL>", "Test Candidate", {}, {}, 10.0, None, None, None, None, None, 0.5, 1.0, "REF123"],
        ["cid456", "<EMAIL>", "Other Candidate", {}, {}, 15.0, None, None, None, None, None, 0.7, 1.0, "REF456"]
    ]
    result = vacancy_service_instance.get_candidates_by_vacancy_id(
        vacancy_id="vac-id",
        reviewer_email="<EMAIL>"
    )
    assert_candidates_or_error(result)

def test_start_vacancy_review_success(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    # Simulate valid reviewer email
    # Simulate vacancy exists, not locked, not being reviewed
    mock_cursor.fetchone.side_effect = [({"history": []}, False, None), ("vac-id", {}, "<EMAIL>")]
    mock_cursor.execute.return_value = None
    mock_cursor.fetchone.return_value = ("vac-id", {}, "<EMAIL>")
    mock_conn.commit.return_value = None
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 200
    assert result["message"] == "Review process started successfully"
    assert result["vacancy_id"] == "vac-id"
    assert result["locked_by"] == "<EMAIL>"
    mock_cursor.close.assert_called()
    mock_conn.close.assert_called()

@pytest.mark.parametrize("reviewer_email", ["<EMAIL>", "<EMAIL>", "<EMAIL>"])
def test_start_vacancy_review_invalid_email(vacancy_service_instance, mock_conn, mock_cursor, reviewer_email):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    result = vacancy_service_instance.start_vacancy_review("vac-id", reviewer_email)
    assert result["status_code"] == 400
    assert "Invalid reviewer email domain" in result["error"]
    assert "tandymgroup.com" in result["details"]
    mock_conn.close.assert_called()

def test_start_vacancy_review_vacancy_not_found(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.fetchone.return_value = None
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 404
    assert "not found" in result["error"]
    mock_cursor.close.assert_called()
    mock_conn.close.assert_called()

def test_start_vacancy_review_locked(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.fetchone.return_value = ({"history": []}, True, "someone")
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 409
    assert "locked" in result["error"]
    assert "completed and locked" in result["details"]
    mock_cursor.close.assert_called()
    mock_conn.close.assert_called()

def test_start_vacancy_review_in_progress_by_other(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.fetchone.return_value = ({"current": {"reviewer": "<EMAIL>"}, "history": []}, False, None)
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 409
    assert "currently being reviewed by" in result["error"]
    assert "active review process" in result["details"]
    mock_cursor.close.assert_called()
    mock_conn.close.assert_called()

def test_start_vacancy_review_failed_update(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.return_value = mock_cursor
    # Simulate vacancy exists, not locked, not being reviewed
    mock_cursor.fetchone.side_effect = [({"history": []}, False, None)]
    # Simulate update returns None (failed)
    mock_cursor.execute.return_value = None
    mock_cursor.fetchone.return_value = None
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 500
    assert "Error starting review process" in result["error"]
    mock_cursor.close.assert_called()
    mock_conn.close.assert_called()

def test_start_vacancy_review_exception(vacancy_service_instance, mock_conn, mock_cursor):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    mock_conn.cursor.side_effect = Exception("DB error")
    result = vacancy_service_instance.start_vacancy_review("vac-id", "<EMAIL>")
    assert result["status_code"] == 500
    assert "Error starting review process" in result["error"]
    assert "DB error" in result["error"]
    mock_conn.close.assert_called()

def test_get_catalyst_match_status_for_vacancy_v1_success(vacancy_service_instance):
    vacancy_id = "test-id"
    expected_result = {"status_code": 200, "data": "v1"}
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v1 = MagicMock(return_value=expected_result)
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v0 = MagicMock()
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)
    assert result == expected_result
    vacancy_service_instance.logger.info.assert_any_call(f"Found data using v1 (new tables) for vacancy_id: {vacancy_id}")

def test_get_catalyst_match_status_for_vacancy_v0_success(vacancy_service_instance):
    vacancy_id = "test-id"
    v1_result = {"status_code": 404}
    v0_result = {"status_code": 200, "data": "v0"}
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v1 = MagicMock(return_value=v1_result)
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v0 = MagicMock(return_value=v0_result)
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)
    assert result == v0_result
    vacancy_service_instance.logger.info.assert_any_call(f"Found data using v0 (original tables) for vacancy_id: {vacancy_id}")

def test_get_catalyst_match_status_for_vacancy_both_fail(vacancy_service_instance):
    vacancy_id = "test-id"
    v1_result = {"status_code": 404}
    v0_result = {"status_code": 404}
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v1 = MagicMock(return_value=v1_result)
    vacancy_service_instance.get_catalyst_match_status_for_vacancy_v0 = MagicMock(return_value=v0_result)
    result = vacancy_service_instance.get_catalyst_match_status_for_vacancy(vacancy_id)
    assert result["status_code"] == 404
    assert "error" in result
    vacancy_service_instance.logger.warning.assert_any_call(
        f"No Catalyst match data found for vacancy_id: {vacancy_id} in either v1 or v0"
    )

def test_regenerate_catalyst_match_invalid_uuid(vacancy_service_instance):
    vacancy_id = "not-a-uuid"
    reviewer_email = "<EMAIL>"
    mock_conn = MagicMock()
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
    assert result["status_code"] == 400
    assert "Invalid vacancy_id format" in result["error"]
    vacancy_service_instance.logger.error.assert_any_call(f"Invalid vacancy_id format: {vacancy_id}")

def test_regenerate_catalyst_match_job_in_progress(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************"
    reviewer_email = "<EMAIL>"
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    mock_cursor.fetchone.return_value = ("inprocess", "2025-08-12T12:00:00", reviewer_email)
    result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
    assert result["status_code"] == 409
    assert "error" in result
    vacancy_service_instance.logger.warning.assert_any_call(
        f"Conflict: Active Catalyst match job already inprocess for vacancy_id {vacancy_id}. Details: {mock_cursor.fetchone.return_value}"
    )

def test_regenerate_catalyst_match_queue_job_error(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************"
    reviewer_email = "<EMAIL>"
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    mock_cursor.fetchone.side_effect = [None, ("queued", "2025-08-12T12:00:00", reviewer_email)]
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.get_vacancy_from_vacancy_id.return_value = {"vacancy_refno": "REF123"}
    vacancy_service_instance.env = "test"
    vacancy_service_instance.db = MagicMock()
    mock_pipeline = MagicMock()
    mock_pipeline.queue_job.return_value = {"error": "fail"}
    with patch("dpserver.services.vacancy_service.VacancyRun", return_value=MagicMock()), \
         patch("dpserver.services.vacancy_service.CatalystMatchPipeline", return_value=mock_pipeline):
        result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
        assert result["status_code"] == 500
        assert "Failed to queue regeneration job" in result["error"]
        vacancy_service_instance.logger.error.assert_any_call("Failed to queue job: fail")

def test_regenerate_catalyst_match_new_job_none(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************"
    reviewer_email = "<EMAIL>"
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    mock_cursor.fetchone.side_effect = [None, None]
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.get_vacancy_from_vacancy_id.return_value = {"vacancy_refno": "REF123"}
    vacancy_service_instance.env = "test"
    vacancy_service_instance.db = MagicMock()
    mock_pipeline = MagicMock()
    mock_pipeline.queue_job.return_value = {}
    with patch("dpserver.services.vacancy_service.VacancyRun", return_value=MagicMock()), \
         patch("dpserver.services.vacancy_service.CatalystMatchPipeline", return_value=mock_pipeline):
        result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
        assert result["status_code"] == 500
        assert "Failed to enqueue regeneration job" in result["error"]
        vacancy_service_instance.logger.error.assert_any_call(
            f"Failed to enqueue regeneration job for vacancy_id: {vacancy_id} - no row returned."
        )

def test_regenerate_catalyst_match_exception(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************"
    reviewer_email = "<EMAIL>"
    vacancy_service_instance._get_active_db_connection = MagicMock(side_effect=Exception("DB error"))
    result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
    assert result["status_code"] == 500
    assert "Server error while enqueuing match generation job." in result["error"]
    vacancy_service_instance.logger.error.assert_called()

def test_regenerate_catalyst_match_success(vacancy_service_instance):
    vacancy_id = "123e4567-e89b-12d3-a456-************"
    reviewer_email = "<EMAIL>"
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    mock_cursor.fetchone.side_effect = [
        None,
        ("queued", "2025-08-12T12:00:00", reviewer_email)
    ]
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.get_vacancy_from_vacancy_id.return_value = {"vacancy_refno": "REF123"}
    vacancy_service_instance.env = "test"
    vacancy_service_instance.db = MagicMock()
    mock_pipeline = MagicMock()
    mock_pipeline.queue_job.return_value = {}
    with patch("dpserver.services.vacancy_service.VacancyRun", return_value=MagicMock()), \
         patch("dpserver.services.vacancy_service.CatalystMatchPipeline", return_value=mock_pipeline):
        result = vacancy_service_instance.regenerate_catalyst_match(vacancy_id, reviewer_email)
        assert result["status"] == "queued"
        assert result["initiated_by"] == reviewer_email
        vacancy_service_instance.logger.info.assert_any_call(f"Successfully enqueued regeneration job for vacancy_id: {vacancy_id}")

def test_missing_fields(vacancy_service_instance):
    data = {"vacancy_refno": None, "candidate_contact_id": None, "author_email": None}
    result = vacancy_service_instance.update_candidate_fitness_reason(data)
    assert result["status_code"] == 400
    assert "required" in result["error"]

def test_update_candidate_fitness_reason_invalid_candidate_contact_id(vacancy_service_instance, fitness_data):
    data = fitness_data.copy()
    data["candidate_contact_id"] = "not-a-uuid"
    result = vacancy_service_instance.update_candidate_fitness_reason(data)
    assert result["status_code"] == 400
    assert "Invalid candidate_contact_id format" in result["error"]

def test_vacancy_not_found(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    mock_cursor.fetchone.side_effect = [None]
    result = vacancy_service_instance.update_candidate_fitness_reason(fitness_data)
    assert result["status_code"] == 404
    assert "Vacancy with refno" in result["error"]

def test_update_candidate_fitness_reason_db_exception(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_conn.cursor.side_effect = Exception("DB error")
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    result = vacancy_service_instance.update_candidate_fitness_reason(fitness_data)
    assert result["status_code"] == 500
    assert "unexpected error" in result["error"].lower()

def test_upsert_fail(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    # vacancy found, detail found, upsert returns None
    mock_cursor.fetchone.side_effect = [
        ("vacancy-uuid",),  # vacancy found
        (None,),            # detail_row
        None                # upserted_row
    ]
    result = vacancy_service_instance.update_candidate_fitness_reason(fitness_data)
    assert result["status_code"] == 500
    assert "Failed to update fitness reason" in result["error"]

def test_update_reason_success(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    # vacancy found, detail found (existing reason), upsert returns row
    existing_fitness_json = {
        "current": {"author": "old", "timestamp": "2024-01-01T00:00:00", "reason": "Old reason"},
        "history": []
    }
    mock_cursor.fetchone.side_effect = [
        ("vacancy-uuid",),  # vacancy found
        (existing_fitness_json,),  # detail_row
        ("vacancy-uuid", "123e4567-e89b-12d3-a456-************")  # upserted_row
    ]
    result = vacancy_service_instance.update_candidate_fitness_reason(fitness_data)
    assert result["status_code"] == 200
    assert "updated" in result["message"]

def test_clear_reason_success(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    # vacancy found, detail found (existing reason), upsert returns row
    existing_fitness_json = {
        "current": {"author": "old", "timestamp": "2024-01-01T00:00:00", "reason": "Old reason"},
        "history": []
    }
    data = fitness_data.copy()
    data["fitness_reason_text"] = ""  # clear
    mock_cursor.fetchone.side_effect = [
        ("vacancy-uuid",),  # vacancy found
        (existing_fitness_json,),  # detail_row
        ("vacancy-uuid", "123e4567-e89b-12d3-a456-************")  # upserted_row
    ]
    result = vacancy_service_instance.update_candidate_fitness_reason(data)
    assert result["status_code"] == 200
    assert "cleared" in result["message"]

def test_history_logic(vacancy_service_instance, fitness_data):
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    vacancy_service_instance._get_active_db_connection = MagicMock(return_value=mock_conn)
    # existing_fitness_json with current and history
    existing_fitness_json = {
        "current": {"author": "old", "timestamp": "2024-01-01T00:00:00", "reason": "Old reason"},
        "history": [
            {"author": "older", "timestamp": "2023-01-01T00:00:00", "reason": "Older reason"}
        ]
    }
    mock_cursor.fetchone.side_effect = [
        ("vacancy-uuid",),  # vacancy found
        (existing_fitness_json,),  # detail_row
        ("vacancy-uuid", "123e4567-e89b-12d3-a456-************")  # upserted_row
    ]
    result = vacancy_service_instance.update_candidate_fitness_reason(fitness_data)
    assert result["status_code"] == 200
    assert "updated" in result["message"]

def make_shortlist_data(**kwargs):
    defaults = {
        "status": "success",
        "shortlisted_at": "2025-08-12T12:00:00",
        "shortlisted_by": "<EMAIL>",
        "crimson_vacancycandidateid": "crimson-id",
        "mercury_shortlistsource_id": "mercury-id",
        "mercury_shortlistsource_name": "mercury-name"
    }
    defaults.update(kwargs)
    return defaults

@pytest.fixture
def shortlist_args():
    return {
        "reviewer_email": "<EMAIL>",
        "vacancy_id": "123e4567-e89b-12d3-a456-************",
        "candidate_id": "987e6543-e21b-12d3-a456-************"
    }

def test_shortlist_already_shortlisted_in_dataverse(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {
        "is_shortlisted": True,
        "shortlist_info": make_shortlist_data()
    }
    mock_cursor.fetchone.return_value = (shortlist_args["vacancy_id"], shortlist_args["candidate_id"], json.dumps(make_shortlist_data()))
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is True
    assert result["status_code"] == 200
    assert "already shortlisted" in result["message"].lower()

def test_shortlist_candidate_already_shortlisted_in_dataverse(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    # Candidate is already shortlisted in Dataverse
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    shortlist_info = {
        "crimson_vacancycandidateid": "abc123",
        "mercury_shortlistsource_id": "mercury-id",
        "mercury_shortlistsource_name": "mercury-name",
        "shortlisted_at": "2025-08-12T12:00:00",
        "shortlisted_by": "<EMAIL>"
    }
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {
        "is_shortlisted": True,
        "shortlist_info": shortlist_info
    }
    mock_cursor.fetchone.return_value = (shortlist_args["vacancy_id"], shortlist_args["candidate_id"], json.dumps(shortlist_info))
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is True
    assert result["status_code"] == 200
    assert "already shortlisted" in result["message"].lower()

# def test_shortlist_candidate_not_shortlisted_in_dataverse(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
#     # Candidate is NOT shortlisted in Dataverse
#     vacancy_service_instance._get_active_db_connection = lambda: mock_conn
#     vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
#     vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {
#         "is_shortlisted": False,
#         "shortlist_info": None
#     }
#     mock_cursor.fetchone.side_effect = [None, None]
#     vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.return_value = {
#         "crimson_vacancycandidateid": "abc123",
#         "mercury_shortlistsource_id": "mercury-id",
#         "mercury_shortlistsource_name": "mercury-name",
#         "shortlisted_at": "2025-08-12T12:00:00",
#         "shortlisted_by": "<EMAIL>"
#     }
#     result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
#     assert result["success"] is True
#     assert result["status_code"] == 200
#     assert "shortlisted successfully" in result["message"].lower()




# def test_shortlist_candidate_dataverse_unexpected_format(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
#     # Dataverse returns unexpected format (missing is_shortlisted)
#     vacancy_service_instance._get_active_db_connection = lambda: mock_conn
#     vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
#     vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {}
#     mock_cursor.fetchone.side_effect = [None, None]
#     vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.return_value = {
#         "crimson_vacancycandidateid": "abc123",
#         "mercury_shortlistsource_id": "mercury-id",
#         "mercury_shortlistsource_name": "mercury-name",
#         "shortlisted_at": "2025-08-12T12:00:00",
#         "shortlisted_by": "<EMAIL>"
#     }
#     result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
#     assert result["success"] is True
#     assert result["status_code"] == 200
#     assert "shortlisted successfully" in result["message"].lower()

def test_shortlist_dataverse_success(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {"is_shortlisted": False}
    mock_cursor.fetchone.side_effect = [
        None,
        (shortlist_args["vacancy_id"], shortlist_args["candidate_id"], json.dumps(make_shortlist_data()))  # upsert returns a row
    ]
    vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.return_value = make_shortlist_data()
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is True
    assert result["status_code"] == 200
    assert "shortlisted successfully" in result["message"].lower()

def test_shortlist_dataverse_other_error(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {"is_shortlisted": False}
    mock_cursor.fetchone.side_effect = [None, None]
    class DataverseError(Exception): pass
    vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.side_effect = DataverseError("Some other error")
    mock_cursor.fetchone.return_value = (shortlist_args["vacancy_id"], shortlist_args["candidate_id"], json.dumps({"status": "failed"}))
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is False
    assert result["status_code"] == 500
    assert "internal server error" in result["details"].lower()

def test_shortlist_unexpected_exception(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {"is_shortlisted": False}
    mock_cursor.fetchone.side_effect = [None, None]
    vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.side_effect = Exception("Unexpected error")
    mock_cursor.fetchone.return_value = (shortlist_args["vacancy_id"], shortlist_args["candidate_id"], json.dumps({"status": "failed"}))
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is False
    assert result["status_code"] == 500
    assert "internal server error" in result["details"].lower()

def test_shortlist_db_update_fail(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = lambda: mock_conn
    vacancy_service_instance.vacancy_dataverse_helper = MagicMock()
    vacancy_service_instance.vacancy_dataverse_helper.check_candidate_shortlisted.return_value = {"is_shortlisted": False}
    mock_cursor.fetchone.side_effect = [None, None]
    vacancy_service_instance.vacancy_dataverse_helper.shortlist_candidate_for_vacancy.return_value = make_shortlist_data()
    mock_cursor.fetchone.return_value = None  # DB update fails
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is False
    assert result["status_code"] == 500
    assert "internal server error" in result["details"].lower()

def test_shortlist_top_level_exception(vacancy_service_instance, mock_conn, mock_cursor, shortlist_args):
    vacancy_service_instance._get_active_db_connection = MagicMock(side_effect=Exception("DB error"))
    result = vacancy_service_instance.shortlist_candidate_for_vacancy(**shortlist_args)
    assert result["success"] is False
    assert result["status_code"] == 500
    assert "error updating shortlisted status" in result["message"].lower()

