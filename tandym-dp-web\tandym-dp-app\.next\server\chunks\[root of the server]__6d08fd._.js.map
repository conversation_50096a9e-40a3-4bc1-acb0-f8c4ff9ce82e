{"version": 3, "sources": [], "sections": [{"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL =\r\n  process.env.NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL;\r\n\r\nexport const API_ENDPOINTS = {\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacancyByVacancyId: `${BASE_URL}/vacancy/:vacancy_id`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,\r\n  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,\r\n  getCatalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n\r\n  // New Stats Endpoints\r\n  getCatalystMatchStats: `${BASE_URL}/catalystmatch/stats`,\r\n  getCatalystMatchStatsByCategory: `${BASE_URL}/catalystmatch/stats?aggregate_by_category=true`,\r\n\r\n  // Applicant Stats Endpoints\r\n  getApplicantStatsSummary: `${BASE_URL}/applicants/stats/summary`,\r\n  getApplicantStatsByVacancy: `${BASE_URL}/applicants/stats/by-vacancy`,\r\n  getApplicantStatsByCategory: `${BASE_URL}/applicants/stats/by-category`,\r\n  getApplicantStatsTrends: `${BASE_URL}/applicants/stats/trends`,\r\n  \r\n  // CatalystMatch Endpoints\r\n  submitCatalystMatch: `${BASE_URL}/catalyst-match/submit`,\r\n  \r\n  // Vacancy Template Endpoints\r\n  getVacancyTemplate: `${BASE_URL}/vacancy-template/get`,\r\n  saveVacancyTemplate: `${BASE_URL}/vacancy-template/save`,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM,+CACX,QAAQ,GAAG,CAAC,4CAA4C;AAEnD,MAAM,gBAAgB;IAC3B,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,sBAAsB,GAAG,SAAS,oBAAoB,CAAC;IACvD,iBAAiB,GAAG,wBAAwB,wFAAwF,CAAC;IACrI,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IAEtF,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;IAE7E,sBAAsB;IACtB,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,iCAAiC,GAAG,SAAS,+CAA+C,CAAC;IAE7F,4BAA4B;IAC5B,0BAA0B,GAAG,SAAS,yBAAyB,CAAC;IAChE,4BAA4B,GAAG,SAAS,4BAA4B,CAAC;IACrE,6BAA6B,GAAG,SAAS,6BAA6B,CAAC;IACvE,yBAAyB,GAAG,SAAS,wBAAwB,CAAC;IAE9D,0BAA0B;IAC1B,qBAAqB,GAAG,SAAS,sBAAsB,CAAC;IAExD,6BAA6B;IAC7B,oBAAoB,GAAG,SAAS,qBAAqB,CAAC;IACtD,qBAAqB,GAAG,SAAS,sBAAsB,CAAC;AAC1D"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/utils.ts"], "sourcesContent": ["import { EntitlementMap } from \"@/context/EntitlementContext\";\r\nimport moment from \"moment-timezone\";\r\nimport { isADLogin } from \"@/api/config\"; // Adjust the import path as necessary\r\n\r\nexport const VACANCY_FILTER_URL_REGEX =\r\n  /^https:\\/\\/recruiter(?:\\.([^.]+))?\\.tandymgroup\\.com/;\r\n\r\nexport const unFormattedDateWithBrowserTimezone = (\r\n  val: string,\r\n  tz: string = \"America/Los_Angeles\" // Default to PST\r\n) => {\r\n  const utcDate = moment.utc(val); // Convert input to UTC\r\n  const formattedPSTDateTime = utcDate.tz(tz).format(\"YYYY-MM-DD (hh:mm A)\"); // Convert to PST and format with AM/PM\r\n\r\n  return formattedPSTDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneWithTimezone = (val: string) => {\r\n  const browserTz = Intl.DateTimeFormat().resolvedOptions().timeZone; // get browser timezone\r\n  const utcDate = moment.utc(val);\r\n  const formattedDateTime = utcDate\r\n    .tz(browserTz)\r\n    .format(\"YYYY-MM-DD (hh:mm A z)\");\r\n  return formattedDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneInDDMMYY = (\r\n  val: string,\r\n  mode: \"STATIC\" | \"UTC_TO_LOCAL\" = \"STATIC\" // default mode for static dates\r\n): string => {\r\n  if (!moment(val).isValid()) return \"Invalid Date\";\r\n\r\n  const dateMoment =\r\n    mode === \"UTC_TO_LOCAL\"\r\n      ? moment.utc(val).local() // convert UTC to browser's local time\r\n      : moment(val); // no conversion\r\n\r\n  return dateMoment.format(\"MM-DD-YYYY\");\r\n};\r\n\r\nexport const getInitials = (name: string) => {\r\n  if (!name) return \"\";\r\n  return name\r\n    .split(\" \") // Split the name into parts by spaces\r\n    .map((part) => part.charAt(0).toUpperCase()) // Take the first letter of each part and capitalize it\r\n    .join(\"\"); // Join the initials together\r\n};\r\n\r\n// utils/entitlementUtils.ts (or inline)\r\nexport const getEntitlementKeyFromTitle = (\r\n  title: string\r\n): string | undefined => {\r\n  switch (title) {\r\n    case \"Vacancy\":\r\n      return \"Vacancy\";\r\n    case \"Workforce Readiness Index\":\r\n      return \"Work_force_Index\";\r\n    case \"Sub-Category Library\":\r\n      return \"Sub_Catregory\";\r\n    default:\r\n      return undefined;\r\n  }\r\n};\r\n\r\nexport const isUserEntitled = (\r\n  entitlementKey: string | undefined,\r\n  entitlement: EntitlementMap\r\n): boolean => {\r\n  const isADLoginValue: boolean = isADLogin();\r\n  if (!isADLoginValue || !entitlementKey) {\r\n    return true; // Default to true if not AD login or entitlement key is undefined\r\n  }\r\n  return !!entitlement[entitlementKey];\r\n};\r\n\r\nexport function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n  const code = envCode.toLowerCase();\r\n  if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n    return \"sandbox\";\r\n  }\r\n  if (code === \"ua\") {\r\n    return \"uat\";\r\n  }\r\n  if (code === \"prod\") {\r\n    return \"prod\";\r\n  }\r\n  // Optionally, handle unknown codes\r\n  return \"sandbox\";\r\n}\r\n\r\nexport const capitalizeEachWord = (str: string) =>\r\n  str.toLowerCase().replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA,gMAA0C,sCAAsC;;;AAEzE,MAAM,2BACX;AAEK,MAAM,qCAAqC,CAChD,KACA,KAAa,sBAAsB,iBAAiB;AAAlB;IAElC,MAAM,UAAU,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,MAAM,uBAAuB;IACxD,MAAM,uBAAuB,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,yBAAyB,uCAAuC;IAEnH,OAAO;AACT;AAEO,MAAM,iDAAiD,CAAC;IAC7D,MAAM,YAAY,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE,uBAAuB;IAC3F,MAAM,UAAU,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC;IAC3B,MAAM,oBAAoB,QACvB,EAAE,CAAC,WACH,MAAM,CAAC;IACV,OAAO;AACT;AAEO,MAAM,6CAA6C,CACxD,KACA,OAAkC,SAAS,gCAAgC;AAAjC;IAE1C,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,OAAO,IAAI,OAAO;IAEnC,MAAM,aACJ,SAAS,iBACL,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,sCAAsC;OAC9D,CAAA,GAAA,6IAAA,CAAA,UAAM,AAAD,EAAE,MAAM,gBAAgB;IAEnC,OAAO,WAAW,MAAM,CAAC;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,KAAK,CAAC,KAAK,sCAAsC;KACjD,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,IAAI,uDAAuD;KACnG,IAAI,CAAC,KAAK,6BAA6B;AAC5C;AAGO,MAAM,6BAA6B,CACxC;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,iBAAiB,CAC5B,gBACA;IAEA,MAAM,iBAA0B,CAAA,GAAA,+GAAA,CAAA,YAAS,AAAD;IACxC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB;QACtC,OAAO,MAAM,kEAAkE;IACjF;IACA,OAAO,CAAC,CAAC,WAAW,CAAC,eAAe;AACtC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,OAAO,QAAQ,WAAW;IAChC,IAAI;QAAC;QAAM;QAAM;KAAK,CAAC,QAAQ,CAAC,OAAO;QACrC,OAAO;IACT;IACA,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,mCAAmC;IACnC,OAAO;AACT;AAEO,MAAM,qBAAqB,CAAC,MACjC,IAAI,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { VACANCY_FILTER_URL_REGEX } from \"@/utils/utils\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const getDomainClient = () => {\r\n    const url = window.location.href;\r\n    const match = url.match(VACANCY_FILTER_URL_REGEX);\r\n    return match && match[1] ? match[1] : \"prod\";\r\n  };\r\n\r\n  if (!appInsights) {\r\n    let connectionString = \"\"; // ✅ Declare here so it's in scope\r\n\r\n    const env = getDomainClient();\r\n    switch (env) {\r\n      case \"dv\":\r\n        connectionString =\r\n          \"InstrumentationKey=a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849\";\r\n        break;\r\n      case \"qa\":\r\n        connectionString =\r\n          \"InstrumentationKey=f3dd9927-e8d5-439b-972a-da458156c3ac;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=d6e306bb-1468-40e7-99d1-12f2024ed410\";\r\n        break;\r\n      case \"sb\":\r\n        connectionString =\r\n          \"InstrumentationKey=10a2fa35-5df1-4728-b76d-50b7548e507f;IngestionEndpoint=https://eastus-5.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=8b3040c3-62e0-42e5-9bef-b49ffa496183\";\r\n        break;\r\n      case \"ua\":\r\n        connectionString =\r\n          \"InstrumentationKey=017ce8e3-75aa-4b79-821a-a254f47527df;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=7f1efc32-bc37-4cd1-bd8e-c4af6169342b\";\r\n        break;\r\n      default:\r\n        connectionString =\r\n          \"InstrumentationKey=cb49f8e1-9368-42f9-95d6-5ab16b42c75a;IngestionEndpoint=https://eastus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=1fe4a6f4-7eee-4cbe-9e7c-ed77e57398bf\";\r\n    }\r\n\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false,\r\n        enableCorsCorrelation: true,\r\n        enableRequestHeaderTracking: true,\r\n        enableResponseHeaderTracking: true,\r\n        extensions: [],\r\n        extensionConfig: {},\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n\r\n    const isIframe = window.self !== window.top;\r\n    appInsights.trackEvent({\r\n      name: \"AppInsightsInitialized\",\r\n      properties: {\r\n        isIframe,\r\n        referrer: document.referrer,\r\n        currentURL: window.location.href,\r\n      },\r\n    });\r\n  }\r\n\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI;QAChC,MAAM,QAAQ,IAAI,KAAK,CAAC,gHAAA,CAAA,2BAAwB;QAChD,OAAO,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACxC;IAEA,IAAI,CAAC,aAAa;QAChB,IAAI,mBAAmB,IAAI,kCAAkC;QAE7D,MAAM,MAAM;QACZ,OAAQ;YACN,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF;gBACE,mBACE;QACN;QAEA,cAAc,IAAI,4OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,8BAA8B;gBAC9B,YAAY,EAAE;gBACd,iBAAiB,CAAC;YACpB;QACF;QACA,YAAY,eAAe;QAE3B,MAAM,WAAW,OAAO,IAAI,KAAK,OAAO,GAAG;QAC3C,YAAY,UAAU,CAAC;YACrB,MAAM;YACN,YAAY;gBACV;gBACA,UAAU,SAAS,QAAQ;gBAC3B,YAAY,OAAO,QAAQ,CAAC,IAAI;YAClC;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n\r\nexport enum FEATURE_NAMES {\r\n  SORTLIST_CANDIDATE_FEATURE = \"Shortlist Candidate Feature\",\r\n  MERCRURY_SORTLIST_CANDIDATE_FEATURE = \"Mercury Shortlist Candidate Feature\",\r\n  THUMB_REVIEW_FEATURE = \"Thumb Review Feature\",\r\n  THUMB_REVIEW_FEATURE_FROM_MERCURY = \"Thumb Review Feature from Mercury CRM\",\r\n  REGENERATE_CANDIDATES_MATCH_FEATURE = \"Regenerate Candidates Match Feature\",\r\n  MERCURY_REGENERATE_CANDIDATES_MATCH_FEATURE = \"Mercury Regenerate Candidates Match Feature\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_LABELS {\r\n  STATE = \"State\",\r\n  CITY = \"City\",\r\n  FRESHNESS_INDEX = \"Freshness Index\",\r\n  SHORT_LISTED = \"Short Listed\",\r\n  REVIEW_DECISION = \"Review Decision\",\r\n  AI_AGENT_STATUS = \"AI Agent Status\",\r\n  TOTAL_SCORE = \"Total Score\",\r\n  AVAILABILITY_DATE = \"Availability Date\",\r\n  SEARCH_BY_NAME = \"Search by Name\",\r\n  TOTAL_SCORE_RANGE = \"Total Score Range\",\r\n  AVAILABILITY_DATE_RANGE = \"Availability Date Range\",\r\n  SEARCH_FIELDS = \"Search Fields\",\r\n  DISTANCE_RANGE = \"Distance Range\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_OTHER_LABELS {\r\n  NAME = \"name\",\r\n  CITY = \"city\",\r\n  WHYFIT = \"whyFit\",\r\n  NOREVIEW = \"NoReview\",\r\n  THUMBSUP = \"ThumbsUp\",\r\n  THUMBSDOWN = \"ThumbsDown\",\r\n  LIKE = \"like\",\r\n  DISLIKE = \"dislike\",\r\n  MISSING = \"Missing\",\r\n  SUCCESS = \"success\",\r\n  CLASSIFICATIONSCORE = \"classification score\",\r\n  OVERALLSCORE = \"overallscore\",\r\n}\r\n\r\nexport enum AI_AGENTS_RESPONSE_STATUS {\r\n  NOT_CONTACTED_BLANK = \"Not Contacted (Blank)\",\r\n  RESPONDED = \"Responded\",\r\n  CONTACTED = \"Contacted\",\r\n  NOT_INTERESTED = \"Not interested\",\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,wCAAmC,OAAO;;IAC1C,IAAI;AAMN;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB;AAE7B,IAAA,AAAK,uCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,+CAAA;;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,qDAAA;;;;;;;;;;;;;WAAA;;AAeL,IAAA,AAAK,mDAAA;;;;;WAAA"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/auth.ts"], "sourcesContent": ["import { API_ENDPOINTS } from \"@/api/config\";\r\nimport AzureADProvider from \"next-auth/providers/azure-ad\";\r\nimport { trackedFetch } from \"./trackApi\";\r\nimport { getAppInsights } from \"./appInsights\";\r\n\r\nexport const authOptions = {\r\n  providers: [\r\n    AzureADProvider({\r\n      clientId: process.env.RECRUITER_SSO_CLIENT_ID! as string,\r\n      clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET! as string,\r\n      tenantId: process.env.NEXT_PUBLIC_AZURE_TENANT_ID! as string,\r\n    }),\r\n  ],\r\n  secret: process.env.NEXTAUTH_SECRET!,\r\n  pages: {\r\n    signIn: `${process.env.NEXTAUTH_URL}/login`, // Custom sign-in page URL\r\n  },\r\n  // cookies: {\r\n  //   sessionToken: {\r\n  //     name: `__Secure-next-auth.session-token`,\r\n  //     options: {\r\n  //       httpOnly: true,\r\n  //       sameSite: \"none\",  // Required for third-party iframes\r\n  //       secure: true,      // Required when SameSite=None\r\n  //       path: \"/\",\r\n  //     },\r\n  //   },\r\n  // },\r\n  callbacks: {\r\n    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {\r\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\r\n      if (new URL(url).origin === baseUrl) return url;\r\n      return baseUrl;\r\n    },\r\n\r\n    async session({ session }: any) {\r\n      if (\r\n        process.env.NEXT_PUBLIC_AD_LOGIN !== \"true\" &&\r\n        process.env.IS_ENTITLEMENT_ENABLED !== \"true\"\r\n      ) {\r\n        return session;\r\n      }\r\n      const portal_name = \"recruiter\";\r\n      let entitlement = {};\r\n      try {\r\n        // Caling an entitlements API to fetch user entitlements\r\n        const url = `${\r\n          API_ENDPOINTS.getEntitlements\r\n        }?email_id=${encodeURIComponent(\r\n          session?.user?.email\r\n        )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n        const res = await trackedFetch(url, {}, { context: \"getEntitlements\" });\r\n\r\n        if (res.ok) {\r\n          const data = await res.json();\r\n          entitlement = data.entitlement;\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email,\r\n            },\r\n          });\r\n        } else {\r\n          console.error(\"Failed to fetch entitlements:\", res.statusText);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching entitlements:\", err);\r\n      }\r\n      return {\r\n        ...session,\r\n        entitlement,\r\n      };\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAe,AAAD,EAAE;YACd,UAAU,QAAQ,GAAG,CAAC,uBAAuB;YAC7C,cAAc,QAAQ,GAAG,CAAC,2BAA2B;YACrD,QAAQ;QACV;KACD;IACD,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;IAC7C;IACA,aAAa;IACb,oBAAoB;IACpB,gDAAgD;IAChD,iBAAiB;IACjB,wBAAwB;IACxB,+DAA+D;IAC/D,0DAA0D;IAC1D,mBAAmB;IACnB,SAAS;IACT,OAAO;IACP,KAAK;IACL,WAAW;QACT,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAoC;YAC/D,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAC5C,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAO;YAC5B,uCAGE;;YAEF;YACA,MAAM,cAAc;YACpB,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,wDAAwD;gBACxD,MAAM,MAAM,GACV,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAC9B,UAAU,EAAE,mBACX,SAAS,MAAM,OACf,aAAa,EAAE,mBAAmB,cAAc;gBAClD,MAAM,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,GAAG;oBAAE,SAAS;gBAAkB;gBAErE,IAAI,IAAI,EAAE,EAAE;oBACV,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,cAAc,KAAK,WAAW;oBAC9B,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;wBAC3B,MAAM;wBACN,YAAY;4BACV,OAAO,SAAS,MAAM;wBACxB;oBACF;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC,IAAI,UAAU;gBAC/D;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;YACA,OAAO;gBACL,GAAG,OAAO;gBACV;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import { authOptions } from \"@/library/auth\";\r\nimport NextAuth from \"next-auth\";\r\n\r\nconst handler = NextAuth(authOptions);\r\n\r\nexport { handler as GET, handler as POST };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,iHAAA,CAAA,cAAW"}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}