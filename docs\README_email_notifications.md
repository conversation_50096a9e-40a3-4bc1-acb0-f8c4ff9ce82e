# AppLogger Email Notifications

## Quick Start

The `AppLogger` class now supports automatic email notifications when error-level logs are generated. This helps with production monitoring by immediately alerting team members when critical errors occur.

## Basic Usage

```python
from common.appLogger import AppLogger

# Configure logger with email notifications
config = {
    "name": "my_service",
    "log_level": "INFO",
    "log_file": "app.log",
    "email_notifications": {
        "enabled": True,
        "to_emails": ["<EMAIL>"],
        "subject_prefix": "ERROR ALERT"
    }
}

logger = AppLogger(config)

# This will log the error AND send an email notification
logger.error("Database connection failed")
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enabled` | bool | `False` | Enable/disable email notifications |
| `to_emails` | list | `[]` | Primary recipients (required if enabled) |
| `cc_emails` | list | `[]` | Carbon copy recipients |
| `bcc_emails` | list | `[]` | Blind carbon copy recipients |
| `subject_prefix` | str | `"ERROR ALERT"` | Prefix for email subject line |

## Prerequisites

1. **SendGrid API Key**: Set environment variable `SENDGRID_API_KEY`
2. **Dependencies**: Already included in `requirements.txt`

## Testing

Test the email notification functionality with real email sending:
```bash
python tests/test_real_email_send.py
```

## Documentation

For detailed documentation, see [docs/email_notifications.md](email_notifications.md)

## Example

For a working example, see the test file: [tests/test_real_email_send.py](../tests/test_real_email_send.py)
