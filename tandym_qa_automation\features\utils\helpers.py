from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.common.exceptions import NoSuchElementException

class CommonHelper:

    def __init__(self, driver, timeout=10):
        self.driver = driver
        self.wait = WebDriverWait(driver, timeout)

    ############################# wait methods #############################

    def wait_for_element_visible(self, locator, timeout=None):
        try:
            wait = WebDriverWait(self.driver, timeout or self.wait._timeout)
            return wait.until(EC.visibility_of_element_located(locator))
        except TimeoutException:
            return None

    def wait_for_element_clickable(self, locator, timeout=None):
        try:
            wait = WebDriverWait(self.driver, timeout or self.wait._timeout)
            return wait.until(EC.element_to_be_clickable(locator))
        except TimeoutException:
            return None
        
    ############################### click elements ###############################

    def click_element(self, locator):
        element = self.wait_for_element_clickable(locator)
        if element:
            element.click()
        else:
            raise Exception(f"Element not clickable: {locator}")

    def send_keys_to_element(self, locator, text):
        element = self.wait_for_element_visible(locator)
        if element:
            element.clear()
            element.send_keys(text)
        else:
            raise Exception(f"Element not visible: {locator}")

    def get_element_text(self, locator):
        element = self.wait_for_element_visible(locator)
        if element:
            return element.text
        else:
            return ""

    def is_element_present(self, locator):
        try:
            self.driver.find_element(*locator)
            return True
        except:
            return False
    
    def is_element_visible(self, locator):
        element = self.wait_for_element_visible(locator)
        return element is not None
    
    ########################################### scroll methods ##########################################
    
    def scroll_to_element(self, locator):
        element = self.wait_for_element_visible(locator)
        if element:
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        else:
            raise Exception(f"Element not found for scrolling: {locator}")
        
    def scroll_to_top(self):
        self.driver.execute_script("window.scrollTo(0, 0);")

    def scroll_to_bottom(self):
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

    ######################################### refresh methods ##########################################

    def refresh_page(self):
        self.driver.refresh()
        self.wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))

    def get_current_url(self):
        return self.driver.current_url
    
    ########################################## Javascript methods ##########################################

    def javascript_click(self, locator):
        element = self.wait_for_element_clickable(locator)
        if element:
            self.driver.execute_script("arguments[0].click();", element)
        else:
            raise Exception(f"Element not clickable for JavaScript click: {locator}")

    def javascript_scroll_to_element(self, locator):
        element = self.wait_for_element_visible(locator)
        if element:
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        else:
            raise Exception(f"Element not found for JavaScript scroll: {locator}")
        
    ############################# loader methods #############################
        
    def wait_until_loader_completes(self):
        try:
            loader = self.driver.find_element(By.CLASS_NAME, "animate-spin")
            if loader.is_displayed():
               print("🔄 Spinner is visible, waiting for it to disappear...")
               WebDriverWait(self.driver, 30).until(
                EC.invisibility_of_element_located((By.CLASS_NAME, "animate-spin"))
            )
            print("############### Spinner disappeared. Continuing... ###############")
        except NoSuchElementException:
            print("############### Spinner not found. Continuing without waiting. ###############")
        except Exception as e:
             print(f" ################ Unexpected error while waiting for spinner: {e} ################")

    ############################## wait methods ##############################

    def implicit_wait(self, seconds):
        """Set implicit wait for the driver."""
        self.driver.implicitly_wait(seconds)

    def explicit_wait(self, seconds):
        """Set explicit wait timeout for the driver."""
        self.wait._timeout = seconds
        self.wait._poll = 0.5

    
        
    