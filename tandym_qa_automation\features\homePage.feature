Feature: Validate HomePage of RecruiterPortal

    @tc:4630
    Scenario: Validate the UI of HomePage
        Given user is on the HomePage of RecruiterPortal
        Then user should see the welcome message
        And verify user should see the message below the welcome message
        And verify user shoukd see the About Recruitment Portal section

    @tc:4631
    Scenario: Validate the navigation of homepage from tabs to other pages
        When user clicks on "Sub-Category Library"
        Then user should be redirected to the Sub-Category Library page
        When user clicks on "Workforce Readiness Index"
        Then user should be redirected to the Workforce Readiness Index page
        When user clicks on "Vacancy"
        Then user should be redirected to the Vacancy page
        When user clicks on "Home"
        Then user should be redirected to the Home page

    @tc:4632
    Scenario: Validate the Sub-Category Library section of HomePPage
        Then verify user should see the "Sub-Category Library" section in HomePage
        And verify user should see the "Sub-Category Library" section message
        When user clicks on "Sub-Category Library" option
        Then user should be redirected to the Sub-Category Library page

    @tc:4633
    Scenario: Validate user back to HomePage
        When user clicks on "Home" option from any page
        Then user should be redirected to the HomePage

    @tc:4634
    Scenario: Validate the Workforce Readiness Index section of HomePage
        Then verify user should see the "Workforce Readiness Index" section in HomePage
        And verify user should see the "Workforce Readiness Index" section message
        When user clicks on "Workforce Readiness Index" option
        Then user should be redirected to the Workforce Readiness Index page

    @tc:4635
    Scenario: Validate user back to HomePage
        When user clicks on "Home" option from any page
        Then user should be redirected to the HomePage

    @tc:4636
    Scenario: Validate the Vacancy section of HomePage
        Then verify user should see the "Vacancy" section in HomePage
        And verify user should see the "Vacancy" section message
        When user clicks on "Vacancy" option
        Then user should be redirected to the Vacancy page

    @tc:4637
    Scenario: Validate user back to HomePage
        When user clicks on "Home" option from any page
        Then user should be redirected to the HomePage