import sys
import os
from behave import given, when, then
from features.pages.recruiterLogout_page import set_is_active_false, click_user_profile_icon, click_logout_button


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

@when('user should set is_active to false for the regression key in the database')
def step_set_is_active_false(context):
    set_is_active_false(context)

@when(u'user clicks on the user profile icon')
def step_click_user_profile_icon(context):
    click_user_profile_icon(context)

@when(u'user clicks on the Logout button')
def step_click_logout_button(context):
    click_logout_button(context)
