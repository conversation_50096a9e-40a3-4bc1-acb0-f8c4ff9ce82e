import pytest
from unittest.mock import Mock, MagicMock
from fastapi import HTT<PERSON>Exception
from dpserver.services.attribute_service import AttributeService  # Adjust import as needed

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    mock_db = Mock()
    mock_db.connection = Mock()
    mock_db.schema = "test_schema"
    return mock_db

@pytest.fixture
def service(mock_logger, mock_db_connector):
    return AttributeService(mock_logger, mock_db_connector)

#Method: - update_attributes_weights
def test_update_attributes_weights_success(service, mock_logger, mock_db_connector):
    subcategory_id = 1
    new_weights = {101: 0.5, 102: 0.5}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    result = service.update_attributes_weights(subcategory_id, new_weights)

    assert result == {"subcategory_id": subcategory_id, "updated_weights": new_weights}
    mock_logger.info.assert_called_with(f"Updating attribute weights for subcategory with ID: {subcategory_id}")
    assert mock_conn.commit.called
    assert mock_cursor.close.called
    # Check that execute was called for each attribute
    assert mock_cursor.execute.call_count == 2 * len(new_weights)
    for attribute_id, new_weight in new_weights.items():
        query = f"""
                    UPDATE {mock_db_connector.schema}.attributes
                    SET weight = %s, updated_at = NOW()
                    WHERE id = %s AND subcategory_id = %s;
                """
        mock_cursor.execute.assert_any_call("SET TRANSACTION READ WRITE")
        mock_cursor.execute.assert_any_call(query, (new_weight, attribute_id, subcategory_id))
                
def test_update_attributes_weights_exception(service, mock_logger, mock_db_connector):
    subcategory_id = 2
    new_weights = {201: 0.7}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    # Simulate exception on execute
    mock_cursor.execute.side_effect = Exception("DB error")

    result = service.update_attributes_weights(subcategory_id, new_weights)

    assert result == {"error": "Error updating attribute weights"}
    mock_logger.error.assert_called()
    assert mock_conn.rollback.called        

#Method: - delete_attribute_by_id        
def test_update_attributes_weights_empty_weights(service, mock_logger, mock_db_connector):
    subcategory_id = 3
    new_weights = {}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    result = service.update_attributes_weights(subcategory_id, new_weights)

    assert result == {"subcategory_id": subcategory_id, "updated_weights": new_weights}
    mock_logger.info.assert_called_with(f"Updating attribute weights for subcategory with ID: {subcategory_id}")
    assert mock_conn.commit.called
    assert mock_cursor.close.called
    # No execute calls for empty weights except maybe "SET TRANSACTION READ WRITE"
    assert mock_cursor.execute.call_count == 0 or all(
        call[0][0] == "SET TRANSACTION READ WRITE" for call in mock_cursor.execute.call_args_list
    )    
        
def test_delete_attribute_by_id_success(service, mock_logger, mock_db_connector):
    attribute_id = 123
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    result = service.delete_attribute_by_id(attribute_id)

    assert result == {"deleted": True}
    mock_logger.info.assert_called_with(f"Soft deleting attribute with ID: {attribute_id}")
    assert mock_conn.commit.called
    assert mock_cursor.close.called
    query = f"""
                UPDATE {mock_db_connector.schema}.attributes
                SET is_deleted = TRUE, deleted_at = NOW()
                WHERE id = %s;
            """
    mock_cursor.execute.assert_any_call("SET TRANSACTION READ WRITE")
    mock_cursor.execute.assert_any_call(query, (attribute_id,))

def test_delete_attribute_by_id_exception(service, mock_logger, mock_db_connector):
    attribute_id = 456
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.execute.side_effect = Exception("DB error")

    result = service.delete_attribute_by_id(attribute_id)

    assert result == {"error": "Error soft deleting attribute"}
    mock_logger.error.assert_called()
    assert mock_conn.rollback.called

#Method: - change_subcategory_of_attribute 
def test_change_subcategory_of_attribute_duplicate_found(service, mock_logger, mock_db_connector):
    attribute_id = 1
    payload = {"new_subcategory_id": 2}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    # Step 1: Attribute exists
    mock_cursor.fetchone.side_effect = [
        ("attr_name", 10),  # First fetchone: attribute details
        (99,)               # Second fetchone: conflict found
    ]

    result = service.change_subcategory_of_attribute(attribute_id, payload)

    assert result == {
        "message": "Duplicate attribute found. Approved existing and soft-deleted current."
    }
    # Check that the correct queries were executed
    assert mock_cursor.execute.call_count == 4  # 2 selects, 2 updates
    assert mock_conn.commit.called
    assert mock_cursor.close.called
    # Logger assertions
    mock_logger.info.assert_any_call(
        f"Updating subcategory of attribute_id {attribute_id} to new_subcategory_id {payload['new_subcategory_id']}"
    )
    mock_logger.info.assert_any_call("Found conflicting attribute in target subcategory with id=99")
    mock_logger.info.assert_any_call(f"Soft deleting current attribute with id={attribute_id}")
    mock_logger.info.assert_any_call("Setting is_approved = true and is_deleted = false for attribute_id=99")

def test_change_subcategory_of_attribute_not_found(service, mock_logger, mock_db_connector):
    attribute_id = 1
    payload = {"new_subcategory_id": 2}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    # Only the first fetchone is called, and it returns None
    mock_cursor.fetchone.return_value = None

    with pytest.raises(HTTPException) as exc_info:
        service.change_subcategory_of_attribute(attribute_id, payload)
    assert exc_info.value.status_code == 404
    assert exc_info.value.detail == "Attribute not found"
    mock_logger.info.assert_called_with(
        f"Updating subcategory of attribute_id {attribute_id} to new_subcategory_id {payload['new_subcategory_id']}"
    )

def test_change_subcategory_of_attribute_db_exception(service, mock_logger, mock_db_connector):
    attribute_id = 1
    payload = {"new_subcategory_id": 2}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    # Simulate DB error on first execute
    mock_cursor.execute.side_effect = Exception("DB error")

    with pytest.raises(Exception) as exc_info:
        service.change_subcategory_of_attribute(attribute_id, payload)
    assert "Error updating subcategory." in str(exc_info.value)
    mock_logger.info.assert_called_with(
        f"Updating subcategory of attribute_id {attribute_id} to new_subcategory_id {payload['new_subcategory_id']}"
    )
    
#Method: - set_attribute_approval_status 
def test_set_attribute_approval_status_success(service, mock_logger, mock_db_connector):
    attribute_id = 1
    payload = {"is_approved": True}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor

    result = service.set_attribute_approval_status(attribute_id, payload)

    assert result == {"success": True, "message": "Approval status updated."}
    mock_logger.info.assert_called_with(f"Updating status of attribute_id {attribute_id} to is_approved as True")
    mock_cursor.execute.assert_any_call("SET TRANSACTION READ WRITE")
    update_query = f"""
                UPDATE {mock_db_connector.schema}.attributes
                SET is_approved = %s,
                    updated_at = NOW()
                WHERE id = %s;
            """
    mock_cursor.execute.assert_any_call(update_query, (True, attribute_id))
    assert mock_conn.commit.called
    assert mock_cursor.close.called

def test_set_attribute_approval_status_exception(service, mock_logger, mock_db_connector):
    attribute_id = 2
    payload = {"is_approved": False}
    mock_conn = mock_db_connector.connection
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.execute.side_effect = Exception("DB error")

    result = service.set_attribute_approval_status(attribute_id, payload)

    assert result == {"status": 500, "error": "Failed to update approval status."}
    mock_logger.error.assert_called()
    assert mock_conn.rollback.called

