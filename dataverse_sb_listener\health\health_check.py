#!/usr/bin/env python3
"""
Health check module for Service Bus listeners.
Provides health status tracking and metrics collection.
"""

import os
import sys
import time
from datetime import datetime
from typing import Dict, Any

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger

class HealthStatus:
    """Health status tracking."""
    
    def __init__(self):
        self.start_time = time.time()
        self.is_ready = False
        self.is_healthy = True
        self.last_message_time = None
        self.total_messages_processed = 0
        self.total_messages_failed = 0
        self.last_error = None
        self.service_bus_connected = False
        self.dataverse_connected = False
        
        # Per-entity metrics tracking
        self.entity_metrics = {}  # {entity_name: {"processed": 0, "failed": 0, "last_processed": None}}
        
    def update_message_processed(self, entity_name: str = None):
        """Update message processing stats."""
        self.total_messages_processed += 1
        self.last_message_time = time.time()
        
        # Track per-entity metrics
        if entity_name:
            if entity_name not in self.entity_metrics:
                self.entity_metrics[entity_name] = {"processed": 0, "failed": 0, "last_processed": None}
            self.entity_metrics[entity_name]["processed"] += 1
            self.entity_metrics[entity_name]["last_processed"] = time.time()
        
    def update_message_failed(self, entity_name: str = None):
        """Update failed message stats."""
        self.total_messages_failed += 1
        
        # Track per-entity metrics
        if entity_name:
            if entity_name not in self.entity_metrics:
                self.entity_metrics[entity_name] = {"processed": 0, "failed": 0, "last_processed": None}
            self.entity_metrics[entity_name]["failed"] += 1
        
    def set_ready(self, ready: bool = True):
        """Set readiness status."""
        self.is_ready = ready
        
    def set_healthy(self, healthy: bool = True):
        """Set health status."""
        self.is_healthy = healthy
        
    def set_service_bus_connected(self, connected: bool):
        """Set Service Bus connection status."""
        self.service_bus_connected = connected
        
    def set_dataverse_connected(self, connected: bool):
        """Set Dataverse connection status."""
        self.dataverse_connected = connected
        
    def set_last_error(self, error: str):
        """Set last error message."""
        self.last_error = error
        self.last_error_time = time.time()
        
    def get_uptime(self) -> float:
        """Get uptime in seconds."""
        return time.time() - self.start_time
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response."""
        # Prepare entity metrics for JSON response
        entity_metrics_json = {}
        for entity_name, metrics in self.entity_metrics.items():
            entity_metrics_json[entity_name] = {
                "processed": metrics["processed"],
                "failed": metrics["failed"],
                "success_rate": self._calculate_entity_success_rate(entity_name),
                "last_processed": datetime.fromtimestamp(metrics["last_processed"]).isoformat() if metrics["last_processed"] else None
            }
        
        return {
            "status": "healthy" if self.is_healthy else "unhealthy",
            "ready": self.is_ready,
            "uptime_seconds": self.get_uptime(),
            "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
            "last_message_time": datetime.fromtimestamp(self.last_message_time).isoformat() if self.last_message_time else None,
            "total_messages_processed": self.total_messages_processed,
            "total_messages_failed": self.total_messages_failed,
            "success_rate": self._calculate_success_rate(),
            "entity_metrics": entity_metrics_json,
            "connections": {
                "service_bus": self.service_bus_connected,
                "dataverse": self.dataverse_connected
            },
            "last_error": self.last_error,
            "last_error_time": datetime.fromtimestamp(self.last_error_time).isoformat() if hasattr(self, 'last_error_time') else None
        }
        
    def _calculate_success_rate(self) -> float:
        """Calculate success rate percentage."""
        total = self.total_messages_processed + self.total_messages_failed
        if total == 0:
            return 100.0
        return (self.total_messages_processed / total) * 100
    
    def _calculate_entity_success_rate(self, entity_name: str) -> float:
        """Calculate success rate percentage for a specific entity."""
        if entity_name not in self.entity_metrics:
            return 100.0
        
        metrics = self.entity_metrics[entity_name]
        total = metrics["processed"] + metrics["failed"]
        if total == 0:
            return 100.0
        return (metrics["processed"] / total) * 100

# Global health status instance
health_status = HealthStatus()

# Convenience functions for updating health status
def update_message_processed(entity_name: str = None):
    """Update message processed count."""
    health_status.update_message_processed(entity_name)

def update_message_failed(entity_name: str = None):
    """Update message failed count."""
    health_status.update_message_failed(entity_name)

def set_ready(ready: bool = True):
    """Set readiness status."""
    health_status.set_ready(ready)

def set_healthy(healthy: bool = True):
    """Set health status."""
    health_status.set_healthy(healthy)

def set_service_bus_connected(connected: bool):
    """Set Service Bus connection status."""
    health_status.set_service_bus_connected(connected)

def set_dataverse_connected(connected: bool):
    """Set Dataverse connection status."""
    health_status.set_dataverse_connected(connected)

def set_last_error(error: str):
    """Set last error message."""
    health_status.set_last_error(error)

def get_health_status() -> HealthStatus:
    """Get the health status instance."""
    return health_status 