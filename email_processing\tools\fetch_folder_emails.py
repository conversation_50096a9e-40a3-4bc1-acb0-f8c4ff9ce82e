#!/usr/bin/env python3
"""
Fetch Folder Emails Tool

Fetch emails from a specific mail folder by name or ID.
Useful for checking emails in folders like "Invalid Attachment", "ContactId_Queue", etc.
"""

import os
import sys
import json
from datetime import datetime
from typing import List, Dict, Optional

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from config import setup_logger_and_secrets
from azure_client import AzureEmailFetcher


def print_folder_emails_summary(emails: List[Dict], folder_name: str, total_in_folder: Optional[int] = None):
    """Print a formatted summary of emails from a specific folder"""
    if not emails:
        print(f"📭 No emails found in folder '{folder_name}'")
        if total_in_folder is not None and total_in_folder > 0:
            print(f"ℹ️  Note: Folder contains {total_in_folder} total emails, but none matched your criteria")
        return
    
    print(f"\n📧 EMAILS FROM FOLDER: {folder_name}")
    if total_in_folder is not None:
        print(f"📊 Showing {len(emails)} of {total_in_folder} total emails in folder")
    else:
        print(f"📊 Showing {len(emails)} emails")
    print("=" * 80)
    
    for i, email in enumerate(emails, 1):
        # Extract sender information
        from_info = email.get('from', {})
        sender_name = from_info.get('emailAddress', {}).get('name', 'Unknown')
        sender_email = from_info.get('emailAddress', {}).get('address', 'Unknown')
        
        # Format received date
        received_dt = email.get('receivedDateTime', '')
        if received_dt:
            try:
                dt = datetime.fromisoformat(received_dt.replace('Z', '+00:00'))
                formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
            except:
                formatted_date = received_dt
        else:
            formatted_date = "Unknown"
        
        # Email subject
        subject = email.get('subject', '(No Subject)')
        
        # Email ID (useful for other operations)
        email_id = email.get('id', 'Unknown ID')
        
        # Additional info
        has_attachments = email.get('hasAttachments', False)
        is_read = email.get('isRead', False)
        importance = email.get('importance', 'normal')
        
        # Format output
        # print(f"\n{i:2d}. 📨 {subject}")
        print(f"{sender_name} <{sender_email}>")
        # print(f"    📅 Received: {formatted_date}")
        # print(f"    🔍 Email ID: {email_id[:30]}...")
        
        # Additional indicators
        indicators = []
        if has_attachments:
            indicators.append("📎 Attachments")
        if not is_read:
            indicators.append("🆕 Unread")
        if importance == 'high':
            indicators.append("⚡ High Priority")
        
        # if indicators:
            # print(f"    ℹ️  {' | '.join(indicators)}")
        
        # Preview of body if available
        # body_preview = email.get('bodyPreview', '').strip()
        # if body_preview:
        #     # Truncate long previews
        #     if len(body_preview) > 100:
        #         body_preview = body_preview[:100] + "..."
        #     print(f"    💬 Preview: {body_preview}")
    
    print("\n" + "=" * 80)
    print(f"✅ Total emails displayed: {len(emails)}")


def fetch_folder_emails(logger, folder_identifier: str, count: int = 10, 
                       sender_filter: Optional[List[str]] = None, 
                       show_folder_info: bool = False):
    """
    Fetch emails from a specific folder by name or ID
    
    Args:
        logger: Logger instance
        folder_identifier: Folder name (e.g. "Invalid Attachment") or folder ID
        count: Number of emails to fetch
        sender_filter: List of sender emails to filter by (optional)
        show_folder_info: If True, show folder details before fetching emails
    """
    try:
        logger.info(f"🚀 Starting folder email fetcher for folder: {folder_identifier}")
        
        # Create email fetcher instance
        email_fetcher = AzureEmailFetcher(logger=logger)
        
        print(f"\n📁 FOLDER EMAIL FETCHER")
        print("=" * 60)
        print(f"📂 Target folder: {folder_identifier}")
        print(f"📊 Emails to fetch: {count}")
        if sender_filter:
            print(f"🔍 Sender filter: {', '.join(sender_filter)}")
        print("=" * 60)
        
        # Step 1: Determine folder ID
        folder_id = None
        folder_name = folder_identifier
        
        # Check if the identifier looks like a folder ID (long alphanumeric string)
        if len(folder_identifier) > 50 and '=' in folder_identifier:
            # Looks like a folder ID
            folder_id = folder_identifier
            folder_name = f"ID: {folder_identifier[:20]}..."
            print(f"\n📋 Using provided folder ID: {folder_identifier[:30]}...")
        else:
            # Treat as folder name, need to find the ID
            print(f"\n🔍 Looking up folder ID for: '{folder_identifier}'")
            folder_info = email_fetcher.find_folder_by_name("<EMAIL>", folder_identifier)
            
            if folder_info:
                folder_id = folder_info['id']
                folder_name = folder_info['displayName']
                print(f"✅ Found folder: '{folder_name}' (ID: {folder_id[:30]}...)")
                
                if show_folder_info:
                    print(f"📊 Folder details:")
                    print(f"    📁 Name: {folder_info.get('displayName', 'Unknown')}")
                    print(f"    📧 Total items: {folder_info.get('totalItemCount', 'Unknown')}")
                    print(f"    🆕 Unread items: {folder_info.get('unreadItemCount', 'Unknown')}")
                    print(f"    📂 Child folders: {folder_info.get('childFolderCount', 'Unknown')}")
            else:
                print(f"❌ Folder '{folder_identifier}' not found!")
                print("\n💡 Available folders:")
                
                # List available folders to help user
                try:
                    all_folders = email_fetcher.fetch_folders("<EMAIL>")
                    for folder in all_folders[:10]:  # Show first 10 folders
                        name = folder.get('displayName', 'Unknown')
                        total_items = folder.get('totalItemCount', 0)
                        print(f"    📁 {name} ({total_items} emails)")
                    
                    if len(all_folders) > 10:
                        print(f"    ... and {len(all_folders) - 10} more folders")
                        
                except Exception as e:
                    print(f"    (Could not list folders: {e})")
                
                return []
        
        # Step 2: Fetch emails from the folder
        print(f"\n📥 Fetching emails from folder '{folder_name}'...")
        
        emails = email_fetcher.fetch_emails(
            mailbox="<EMAIL>",
            count=count,
            folder_id=folder_id,
            sender_filter=sender_filter
        )
        
        if emails:
            print(f"✅ Successfully fetched {len(emails)} emails from folder '{folder_name}'")
        else:
            print(f"📭 No emails found in folder '{folder_name}'")
            
            # Get folder info to show total count
            if folder_id and not folder_identifier.startswith('ID:'):
                try:
                    folder_info = email_fetcher.find_folder_by_name("<EMAIL>", folder_name)
                    if folder_info:
                        total_count = folder_info.get('totalItemCount', 0)
                        if total_count > 0:
                            print(f"ℹ️  Note: Folder contains {total_count} total emails")
                            if sender_filter:
                                print(f"    Your sender filter may be excluding all emails")
                except:
                    pass
        
        # Step 3: Display emails
        print_folder_emails_summary(emails, folder_name)
        
        logger.info(f"✅ Folder email fetcher completed successfully")
        return emails
        
    except Exception as e:
        logger.error(f"❌ Folder email fetcher failed: {e}")
        print(f"\n❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Check that the folder name is spelled correctly")
        print("2. Verify Azure credentials are properly configured")
        print("3. Ensure the folder exists in the mailbox")
        print("4. Use tools/list_folders.py to see all available folders")
        return []


def print_help():
    """Print help information"""
    print("FOLDER EMAIL FETCHER TOOL")
    print("=" * 50)
    print("Purpose: Fetch emails from a specific mail folder by name or ID")
    print()
    print("Usage:")
    print("  python3 fetch_folder_emails.py <folder_name_or_id> [options]")
    print()
    print("Arguments:")
    print("  folder_name_or_id        Name of folder (e.g. 'Invalid Attachment') or folder ID")
    print()
    print("Options:")
    print("  --count <N>             Number of emails to fetch (default: 10)")
    print("  --sender <email>        Filter by specific sender email address")
    print("  --show-folder-info      Show detailed folder information")
    print("  --help                  Show this help message")
    print()
    print("Examples:")
    print("  python3 fetch_folder_emails.py 'Invalid Attachment'")
    print("    → Fetch 10 emails from Invalid Attachment folder")
    print()
    print("  python3 fetch_folder_emails.py 'ContactId_Queue' --count 20")
    print("    → Fetch 20 emails from ContactId_Queue folder")
    print()
    print("  python3 fetch_folder_emails.py Inbox --sender '<EMAIL>' --count 5")
    print("    → Fetch 5 emails from Inbox folder from specific sender")
    print()
    print("  python3 fetch_folder_emails.py 'Invalid Attachment' --show-folder-info")
    print("    → Show folder details and fetch emails")
    print()
    print("Common Folder Names:")
    print("  • Inbox")
    print("  • Invalid Attachment")
    print("  • ContactId_Queue")
    print("  • Sent Items")
    print("  • Drafts")
    print("  • Deleted Items")
    print()
    print("Notes:")
    print("  • Folder names are case-sensitive")
    print("  • Use quotes around folder names with spaces")
    print("  • Use tools/list_folders.py to see all available folders")
    print("  • You can also use folder IDs instead of names")


def main():
    """Main function"""
    # Check for help flag or no arguments
    if len(sys.argv) < 2 or '--help' in sys.argv or '-h' in sys.argv:
        print_help()
        return
    
    # Setup logger and load secrets
    logger = setup_logger_and_secrets()
    
    # Parse arguments
    folder_identifier = sys.argv[1]
    count = 10
    sender_filter = None
    show_folder_info = False
    
    i = 2
    while i < len(sys.argv):
        arg = sys.argv[i]
        
        if arg == "--count":
            if i + 1 >= len(sys.argv):
                print("❌ Error: --count requires a number argument")
                sys.exit(1)
            try:
                count = int(sys.argv[i + 1])
                if count <= 0:
                    print("❌ Error: count must be a positive number")
                    sys.exit(1)
                i += 1
            except ValueError:
                print("❌ Error: count must be a valid number")
                sys.exit(1)
        elif arg == "--sender":
            if i + 1 >= len(sys.argv):
                print("❌ Error: --sender requires an email address")
                sys.exit(1)
            sender_filter = [sys.argv[i + 1]]
            i += 1
        elif arg == "--show-folder-info":
            show_folder_info = True
        else:
            print(f"❌ Unknown option: {arg}")
            print("Use --help for usage information")
            sys.exit(1)
        
        i += 1
    
    # Fetch emails from folder
    fetch_folder_emails(logger, folder_identifier, count, sender_filter, show_folder_info)


if __name__ == "__main__":
    main() 