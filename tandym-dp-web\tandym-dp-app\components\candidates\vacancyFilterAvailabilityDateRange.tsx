import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { ChevronDownIcon } from "lucide-react";

interface AvailabilityDateRangeProps {
  availabilityDateRange: { from: string; to: string };
  setAvailabilityDateRange: React.Dispatch<
    React.SetStateAction<{ from: string; to: string }>
  >;
  maxMinAvailabilityDateRange: { from: string; to: string };
}

export function AvailabilityDateRange({
  availabilityDateRange,
  setAvailabilityDateRange,
  maxMinAvailabilityDateRange,
}: AvailabilityDateRangeProps) {
  const warningMessage =
    new Date(availabilityDateRange.from) > new Date(availabilityDateRange.to);

  const [fromOpen, setFromOpen] = useState(false);
  const [toOpen, setToOpen] = useState(false);

  useEffect(() => {
    if (warningMessage) {
      setAvailabilityDateRange((prev) => ({
        ...prev,
        to: "",
      }));
    }
  }, [warningMessage, setAvailabilityDateRange]);

  return (
    <div className="p-2">
      <p>Availability Date Range :</p>
      <div className="px-4 py-2">
        <div className="flex flex-col gap-2">
          {/* From Date Picker */}
          <div className="flex gap-2 items-center">
            <span className="text-sm w-12">From:</span>
            <Popover open={fromOpen} onOpenChange={setFromOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-48 justify-between font-normal"
                >
                  {availabilityDateRange?.from
                    ? new Date(availabilityDateRange.from).toLocaleDateString()
                    : "Select From date"}
                  <ChevronDownIcon
                    className={`ml-2 transition-transform ${
                      fromOpen ? "rotate-180" : ""
                    }`}
                  />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={
                    availabilityDateRange?.from
                      ? new Date(availabilityDateRange.from)
                      : undefined
                  }
                  onSelect={(date: Date | undefined) => {
                    if (date) {
                      setAvailabilityDateRange({
                        ...availabilityDateRange,
                        from: new Date(date).toString(),
                      });
                      setFromOpen(false); // close popover after selection if desired
                    } else {
                      setAvailabilityDateRange({
                        ...availabilityDateRange,
                        from: "",
                      });
                    }
                  }}
                  defaultMonth={
                    availabilityDateRange?.from
                      ? new Date(availabilityDateRange.from)
                      : undefined
                  }
                  captionLayout="dropdown"
                  fromYear={
                    maxMinAvailabilityDateRange?.from
                      ? new Date(maxMinAvailabilityDateRange.from).getFullYear()
                      : 0
                  }
                  toYear={
                    maxMinAvailabilityDateRange?.to
                      ? new Date(maxMinAvailabilityDateRange.to).getFullYear()
                      : 0
                  }
                  showOutsideDays={false}
                  required={false}
                  className="rounded-lg border"
                  disabled={{ after: new Date() }}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* To Date Picker */}
          <div className="flex gap-2 items-center">
            <span className="text-sm w-12">To:</span>
            <Popover open={toOpen} onOpenChange={setToOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-48 justify-between font-normal"
                >
                  {availabilityDateRange?.to
                    ? new Date(availabilityDateRange.to).toLocaleDateString()
                    : "Select To date"}
                  <ChevronDownIcon
                    className={`ml-2 transition-transform ${
                      toOpen ? "rotate-180" : ""
                    }`}
                  />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={
                    availabilityDateRange?.to
                      ? new Date(availabilityDateRange.to)
                      : undefined
                  }
                  onSelect={(date: Date | undefined) => {
                    if (date) {
                      setAvailabilityDateRange({
                        ...availabilityDateRange,
                        to: new Date(date).toString(),
                      });
                      setToOpen(false);
                    } else {
                      setAvailabilityDateRange({
                        ...availabilityDateRange,
                        to: "",
                      });
                    }
                  }}
                  captionLayout="dropdown"
                  showOutsideDays={false}
                  defaultMonth={
                    availabilityDateRange?.to
                      ? new Date(availabilityDateRange.to)
                      : undefined
                  }
                  required={false}
                  className="rounded-lg border"
                  fromYear={
                    maxMinAvailabilityDateRange?.from
                      ? new Date(maxMinAvailabilityDateRange.from).getFullYear()
                      : 0
                  }
                  toYear={
                    maxMinAvailabilityDateRange?.to
                      ? new Date(maxMinAvailabilityDateRange.to).getFullYear()
                      : 0
                  }
                  disabled={{
                    after: new Date(),
                    before: new Date(availabilityDateRange.from),
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
}
