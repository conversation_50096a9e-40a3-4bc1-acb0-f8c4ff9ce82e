#!/usr/bin/env python3
"""
Message replayer utility for Service Bus messages.

This utility reads stored messages from files and sends them to a Service Bus queue
for testing and replay purposes.
"""

import sys
import os
import json
import argparse
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path
import glob

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from dataverse_sb_listener.messaging.message_storage import MessageStorage

class MessageReplayer:
    """
    Utility for replaying stored Service Bus messages.
    """
    
    def __init__(self, 
                 service_bus_connection_string: str,
                 queue_name: str,
                 logger: Optional[AppLogger] = None):
        """
        Initialize the message replayer.
        
        Args:
            service_bus_connection_string: Azure Service Bus connection string
            queue_name: Name of the queue to send messages to
            logger: Logger instance
        """
        self.service_bus_connection_string = service_bus_connection_string
        self.queue_name = queue_name
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        
        # Initialize message storage for reading files
        self.message_storage = MessageStorage(logger=self.logger)
        
    def replay_messages(self, 
                       entity_name: Optional[str] = None,
                       date: Optional[str] = None,
                       message_ids: Optional[List[str]] = None,
                       delay_seconds: float = 1.0,
                       dry_run: bool = False) -> Dict[str, Any]:
        """
        Replay stored messages to Service Bus queue.
        
        Args:
            entity_name: Filter by entity name (e.g., 'contact', 'crimson_vacancy')
            date: Filter by date (YYYY-MM-DD format)
            message_ids: List of specific message IDs to replay
            delay_seconds: Delay between messages in seconds
            dry_run: If True, don't actually send messages, just simulate
            
        Returns:
            Dictionary containing replay results
        """
        results = {
            "total_messages": 0,
            "sent_messages": 0,
            "failed_messages": 0,
            "errors": []
        }
        
        try:
            # Get list of messages to replay
            messages_info = self.message_storage.list_messages(
                entity_name=entity_name,
                date=date,
                limit=1000  # High limit to get all messages
            )
            
            if not messages_info.get("enabled", False):
                results["errors"].append("Message storage not available")
                return results
            
            messages = messages_info.get("messages", [])
            
            # Filter by message IDs if specified
            if message_ids:
                messages = [msg for msg in messages if msg.get("metadata", {}).get("message_id") in message_ids]
            
            results["total_messages"] = len(messages)
            
            if not messages:
                self.logger.info("No messages found to replay")
                return results
            
            self.logger.info(f"Found {len(messages)} messages to replay")
            
            if dry_run:
                self.logger.info("DRY RUN MODE - Messages will not be sent")
                for msg_info in messages:
                    self.logger.info(f"Would replay: {msg_info.get('metadata', {}).get('message_id')} "
                                   f"for {msg_info.get('entity_name')} ({msg_info.get('entity_id')})")
                    results["sent_messages"] += 1
                    time.sleep(delay_seconds)
                return results
            
            # Connect to Service Bus
            with ServiceBusClient.from_connection_string(self.service_bus_connection_string) as client:
                with client.get_queue_sender(queue_name=self.queue_name) as sender:
                    
                    for msg_info in messages:
                        try:
                            # Read the message file
                            file_path = msg_info["file_path"]
                            with open(file_path, 'r', encoding='utf-8') as f:
                                message_data = json.load(f)
                            
                            # Extract the raw message body
                            raw_message = message_data.get("raw_message", {})
                            message_body = raw_message.get("body", "{}")
                            
                            # Create Service Bus message
                            sb_message = ServiceBusMessage(
                                body=message_body.encode('utf-8'),
                                message_id=msg_info.get("metadata", {}).get("message_id"),
                                correlation_id=raw_message.get("correlation_id"),
                                session_id=raw_message.get("session_id"),
                                reply_to=raw_message.get("reply_to"),
                                reply_to_session_id=raw_message.get("reply_to_session_id"),
                                to=raw_message.get("to"),
                                content_type=raw_message.get("content_type"),
                                subject=raw_message.get("subject"),
                                application_properties=raw_message.get("application_properties", {})
                            )
                            
                            # Send the message
                            sender.send_messages(sb_message)
                            
                            message_id = msg_info.get("metadata", {}).get("message_id")
                            entity_name = msg_info.get("entity_name")
                            entity_id = msg_info.get("entity_id")
                            
                            self.logger.info(f"Replayed message {message_id} for {entity_name} ({entity_id})")
                            results["sent_messages"] += 1
                            
                            # Delay between messages
                            if delay_seconds > 0:
                                time.sleep(delay_seconds)
                                
                        except Exception as e:
                            error_msg = f"Failed to replay message {msg_info.get('metadata', {}).get('message_id')}: {e}"
                            self.logger.error(error_msg)
                            results["errors"].append(error_msg)
                            results["failed_messages"] += 1
            
            self.logger.info(f"Replay completed: {results['sent_messages']} sent, {results['failed_messages']} failed")
            return results
            
        except Exception as e:
            error_msg = f"Replay failed: {e}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
            return results
    
    def list_available_messages(self, 
                               entity_name: Optional[str] = None,
                               date: Optional[str] = None,
                               limit: int = 50) -> Dict[str, Any]:
        """
        List available messages for replay.
        
        Args:
            entity_name: Filter by entity name
            date: Filter by date (YYYY-MM-DD format)
            limit: Maximum number of messages to return
            
        Returns:
            Dictionary containing message list
        """
        return self.message_storage.list_messages(entity_name, date, limit)
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics.
        
        Returns:
            Dictionary containing storage statistics
        """
        return self.message_storage.get_storage_stats()

def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Replay stored Service Bus messages")
    parser.add_argument("--connection-string", required=True, 
                       help="Service Bus connection string")
    parser.add_argument("--queue-name", required=True,
                       help="Name of the queue to send messages to")
    parser.add_argument("--entity-name", 
                       help="Filter by entity name (e.g., 'contact', 'crimson_vacancy')")
    parser.add_argument("--date", 
                       help="Filter by date (YYYY-MM-DD format)")
    parser.add_argument("--message-ids", nargs="+",
                       help="List of specific message IDs to replay")
    parser.add_argument("--delay", type=float, default=1.0,
                       help="Delay between messages in seconds (default: 1.0)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Don't actually send messages, just simulate")
    parser.add_argument("--list", action="store_true",
                       help="List available messages instead of replaying")
    parser.add_argument("--stats", action="store_true",
                       help="Show storage statistics")
    parser.add_argument("--limit", type=int, default=50,
                       help="Limit number of messages to list (default: 50)")
    
    args = parser.parse_args()
    
    # Initialize logger
    logger = AppLogger({"level": "INFO", "log_to_stdout": True})
    
    # Initialize replayer
    replayer = MessageReplayer(
        service_bus_connection_string=args.connection_string,
        queue_name=args.queue_name,
        logger=logger
    )
    
    try:
        if args.stats:
            # Show storage statistics
            stats = replayer.get_storage_stats()
            print(json.dumps(stats, indent=2))
            
        elif args.list:
            # List available messages
            messages = replayer.list_available_messages(
                entity_name=args.entity_name,
                date=args.date,
                limit=args.limit
            )
            print(json.dumps(messages, indent=2))
            
        else:
            # Replay messages
            results = replayer.replay_messages(
                entity_name=args.entity_name,
                date=args.date,
                message_ids=args.message_ids,
                delay_seconds=args.delay,
                dry_run=args.dry_run
            )
            print(json.dumps(results, indent=2))
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 