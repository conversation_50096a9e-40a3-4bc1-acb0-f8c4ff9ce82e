{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL =\r\n  process.env.NEXT_PUBLIC_IS_READ_ONLY_IN_RECRUITER_PORTAL;\r\n\r\nexport const API_ENDPOINTS = {\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacancyByVacancyId: `${BASE_URL}/vacancy/:vacancy_id`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,\r\n  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,\r\n  getCatalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n\r\n  // New Stats Endpoints\r\n  getCatalystMatchStats: `${BASE_URL}/catalystmatch/stats`,\r\n  getCatalystMatchStatsByCategory: `${BASE_URL}/catalystmatch/stats?aggregate_by_category=true`,\r\n\r\n  // Applicant Stats Endpoints\r\n  getApplicantStatsSummary: `${BASE_URL}/applicants/stats/summary`,\r\n  getApplicantStatsByVacancy: `${BASE_URL}/applicants/stats/by-vacancy`,\r\n  getApplicantStatsByCategory: `${BASE_URL}/applicants/stats/by-category`,\r\n  getApplicantStatsTrends: `${BASE_URL}/applicants/stats/trends`,\r\n  \r\n  // CatalystMatch Endpoints\r\n  submitCatalystMatch: `${BASE_URL}/catalyst-match/submit`,\r\n  \r\n  // Vacancy Template Endpoints\r\n  getVacancyTemplate: `${BASE_URL}/vacancy-template/get`,\r\n  saveVacancyTemplate: `${BASE_URL}/vacancy-template/save`,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM,+CACX,QAAQ,GAAG,CAAC,4CAA4C;AAEnD,MAAM,gBAAgB;IAC3B,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,sBAAsB,GAAG,SAAS,oBAAoB,CAAC;IACvD,iBAAiB,GAAG,wBAAwB,wFAAwF,CAAC;IACrI,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IAEtF,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;IAE7E,sBAAsB;IACtB,uBAAuB,GAAG,SAAS,oBAAoB,CAAC;IACxD,iCAAiC,GAAG,SAAS,+CAA+C,CAAC;IAE7F,4BAA4B;IAC5B,0BAA0B,GAAG,SAAS,yBAAyB,CAAC;IAChE,4BAA4B,GAAG,SAAS,4BAA4B,CAAC;IACrE,6BAA6B,GAAG,SAAS,6BAA6B,CAAC;IACvE,yBAAyB,GAAG,SAAS,wBAAwB,CAAC;IAE9D,0BAA0B;IAC1B,qBAAqB,GAAG,SAAS,sBAAsB,CAAC;IAExD,6BAA6B;IAC7B,oBAAoB,GAAG,SAAS,qBAAqB,CAAC;IACtD,qBAAqB,GAAG,SAAS,sBAAsB,CAAC;AAC1D"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/post.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const postData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    let message =\r\n      error.response?.data?.detail?.message ||\r\n      error.response?.data?.error ||\r\n      error.message ||\r\n      \"Unknown error\";\r\n    const err = new Error(message);\r\n    err.name = \"ApiError\";\r\n    // Attach extra info for advanced error handling\r\n    (err as any).status = error.response?.status || 500;\r\n    (err as any).data = error.response?.data ?? null;\r\n    throw err;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,OAAO,KAAa;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,IAAI,UACF,MAAM,QAAQ,EAAE,MAAM,QAAQ,WAC9B,MAAM,QAAQ,EAAE,MAAM,SACtB,MAAM,OAAO,IACb;QACF,MAAM,MAAM,IAAI,MAAM;QACtB,IAAI,IAAI,GAAG;QACX,gDAAgD;QAC/C,IAAY,MAAM,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC/C,IAAY,IAAI,GAAG,MAAM,QAAQ,EAAE,QAAQ;QAC5C,MAAM;IACR;AACF"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/put.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const updateData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,OAAO,KAAa;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/serverActions.ts"], "sourcesContent": ["// api/serverActions.ts\r\n\"use server\";\r\nimport \"server-only\";\r\nimport { cookies } from \"next/headers\";\r\nimport { API_ENDPOINTS } from \"./config\";\r\nimport { postData } from \"./post\";\r\nimport { updateData } from \"./put\";\r\nimport { EntitlementResponse } from \"./types\";\r\n\r\n// Golden rule: Never return Request, Response, NextRequest, NextResponse, IncomingMessage, ClientRequest, Socket, or raw Axios error objects\r\ntype Ok<T>  = { data: { success: true; data?: T; message?: string } };\r\ntype Err    = { data: { success: false; message: string; code?: number; details?: unknown } };\r\ntype Result<T> = Ok<T> | Err;\r\n\r\nfunction errMsg(e: unknown) {\r\n  // strings only; never return the raw error object\r\n  try {\r\n    if (e && typeof e === \"object\" && \"message\" in (e as any)) return String((e as any).message);\r\n    return String(e);\r\n  } catch { return \"Unknown error\"; }\r\n}\r\n\r\nexport async function fetchAllSubCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.subCategories);\r\n    const data = await response.json();\r\n    return data?.subcategories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function deleteAttributeTitleById(attributeId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.deleteAttribute.replace(\r\n        \":attribute_id\",\r\n        attributeId.toString()\r\n      ),\r\n      { method: \"DELETE\" }\r\n    );\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchAttributeBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchAttributesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateAttributeWeight(\r\n  subCategoryId: number,\r\n  updatedData: any\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateAttributeWeight.replace(\r\n    \":sub_category_id\",\r\n    subCategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await postData(url, updatedData);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Attribute weight updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryOfAttribute(\r\n  attributeId: number,\r\n  data: { new_subcategory_id: number }\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update attribute subcategory\");\r\n  }\r\n}\r\n\r\nexport async function updateAttributeApprovalStatus(\r\n  attributeId: number,\r\n  data: { is_approved: boolean }\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Attribute approval status updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function fetchWeightsBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchWeightsBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacancies() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacancies);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacanciesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getVacancyByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`Error while fetching vacancy with ${vacancyId}`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function fetchCandidatesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCandidatesByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateId(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\ninterface RecruiterPostReview {\r\n  hiring_decision: string;\r\n  review_message: string;\r\n  candidate_contact_id: string;\r\n  recruiter_email: string;\r\n}\r\n\r\nexport async function updateRecruiterReview(data: any) {\r\n  const url = API_ENDPOINTS.updateCandidatesReviewData;\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function updateWhyFit(data: any) {\r\n  try {\r\n    const url = API_ENDPOINTS.updateWhyFitData;\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error while updating whyfit: \", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function fetchEntitlements(\r\n  email_id: string,\r\n  saveHistoryLogs?: boolean\r\n): Promise<EntitlementResponse | false> {\r\n  try {\r\n    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === \"true\";\r\n    if (!isEntitlementEnabled) {\r\n      const response = {\r\n        error: false,\r\n        code: \"TR_01\",\r\n        message: \"Successful\",\r\n        entitlement: {\r\n          Work_force_Index: true,\r\n          Sub_Catregory: true,\r\n          Vacancy: true,\r\n          Search_Match: true,\r\n          Sc_Score_Config: true,\r\n          candidate_tunning_page: true,\r\n          Shorting_Listing: true,\r\n          Historical_Logs: true,\r\n          Regenerate: true,\r\n          Update_Availability: true,\r\n        },\r\n      };\r\n      if (saveHistoryLogs) {\r\n        // Save entitlement in cookies\r\n        const cookieStore = await cookies();\r\n        cookieStore.set(\"entitlement\", JSON.stringify(response.entitlement), {\r\n          secure: true,\r\n        });\r\n      }\r\n      return response;\r\n    }\r\n    const portal_name = \"recruiter\";\r\n    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(\r\n      email_id\r\n    )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n    const response = await fetch(url);\r\n    if (!response.ok) {\r\n      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);\r\n    }\r\n\r\n    const data: EntitlementResponse = await response.json();\r\n    if (saveHistoryLogs) {\r\n      // Save entitlement in cookies\r\n      (await cookies()).set(\"entitlement\", JSON.stringify(data.entitlement), {\r\n        secure: true,\r\n      });\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching entitlement data:\", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function getAllSubcategoryWeightConfigs() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);\r\n    const data = await response.json();\r\n    return data?.subcategory_weight_configs || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryWeightConfig(\r\n  subcategoryId: number,\r\n  data: any\r\n): Promise<Result<any>> {\r\n  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(\r\n    \":subcategory_id\",\r\n    subcategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    const safe = JSON.parse(JSON.stringify(response));\r\n    return { data: { success: true, data: safe, message: \"Subcategory weight config updated successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function postVacanciesShortlisted(data: any) {\r\n  const url = API_ENDPOINTS.vacanciesShortlisted;\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"error::\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function postHistoricalLogs(\r\n  email: string,\r\n  portalName: string,\r\n  featureName: string,\r\n  metaData: any = null\r\n) {\r\n  const url = API_ENDPOINTS.saveHistoryLogs\r\n    .replace(\"{email_id}\", email)\r\n    .replace(\"{portal_name}\", portalName)\r\n    .replace(\"{feature}\", featureName);\r\n\r\n  try {\r\n    const response = await postData(url, metaData);\r\n\r\n    if (!response || response.status >= 400) {\r\n      console.error(\r\n        `[postHistoricalLogs] Failed request: ${response.status} ${response.statusText}`\r\n      );\r\n      return null;\r\n    }\r\n\r\n    if (!response.data) {\r\n      console.warn(`[postHistoricalLogs] Response missing data:`, response);\r\n      throw new Error(`No data in response for historical logs.`);\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`[postHistoricalLogs] Exception for ${email}:`, error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport async function fetchCatalystMatchStatus(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCatalystMatchStatus.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    if (!response.ok) {\r\n      const errorData = await response.json(); // Still extract JSON body\r\n      const error = {\r\n        error: true,\r\n        status: response.status,\r\n        message: errorData.detail.error.message || \"Unknown error\",\r\n      };\r\n      throw new Error(JSON.stringify(error));\r\n    }\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    if (\r\n      typeof error === \"object\" &&\r\n      error !== null &&\r\n      \"message\" in error &&\r\n      typeof (error as any).message === \"string\"\r\n    ) {\r\n      throw new Error(\r\n        JSON.stringify({\r\n          error: true,\r\n          status: JSON.parse((error as any).message).status || 500,\r\n          message:\r\n            JSON.parse((error as any).message).message ||\r\n            \"Failed to fetch Catalyst Match Status\",\r\n        })\r\n      );\r\n    }\r\n  }\r\n}\r\n\r\nexport async function regenerateCatalystMatch(vacancy_id: string, data: any) {\r\n  const url = API_ENDPOINTS.regenerateCatalystMatch.replace(\r\n    \":vacancy_id\",\r\n    vacancy_id\r\n  );\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function submitCatalystMatchForm(payload: any): Promise<Result<null>> {\r\n  try {\r\n    const resp = await fetch(API_ENDPOINTS.submitCatalystMatch, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to submit\", code: resp.status } };\r\n    }\r\n    return { data: { success: true, message: body?.message || \"Saved\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\nexport async function getVacancyTemplate(\r\n  { vacancy_id }: { vacancy_id: string }\r\n): Promise<Result<any>> {\r\n  try {\r\n    const resp = await fetch(API_ENDPOINTS.getVacancyTemplate, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify({ vacancy_id }),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to load template\", code: resp.status } };\r\n    }\r\n\r\n    const safe = JSON.parse(JSON.stringify(body?.data ?? body));\r\n    return { data: { success: true, data: safe, message: body?.message || \"Template retrieved successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n\r\n\r\n\r\nexport async function saveVacancyTemplate(\r\n  templateData: any, \r\n  regenerate: boolean = false\r\n): Promise<Result<any>> {\r\n  try {\r\n    // Convert camelCase to snake_case and structure the payload correctly\r\n    const templatePayload = {\r\n      vacancy_id: templateData.vacancy_id,\r\n      required_skills: templateData.requiredSkills || [],\r\n      preferred_skills: templateData.preferredSkills || [],\r\n      recency_must_have_skills: templateData.recencyMustHaveSkills || \"Current +1\",\r\n      additional_job_titles: templateData.additionalJobTitles || [],\r\n      city: templateData.city || \"\",\r\n      state: templateData.state || \"\",\r\n      miles: templateData.miles || \"50\",\r\n      years_experience: templateData.yearsExperience || \"\",\r\n      degrees: templateData.degrees || [],\r\n      certifications_licenses: templateData.certificationsLicenses || [],\r\n      industry: templateData.industry || \"No\",\r\n      confidential: templateData.confidential || \"No\",\r\n      enable_catalyst_match: templateData.enableCatalystMatch || false,\r\n    };\r\n\r\n    const payload = {\r\n      template_data: templatePayload,\r\n      regenerate: regenerate\r\n    };\r\n\r\n    const resp = await fetch(API_ENDPOINTS.saveVacancyTemplate, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\", \"Accept\": \"application/json\" },\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    const body = await resp.json().catch(() => ({}));\r\n\r\n    if (!resp.ok) {\r\n      return { data: { success: false, message: body?.message || \"Failed to save template\", code: resp.status } };\r\n    }\r\n\r\n    const safe = JSON.parse(JSON.stringify(body?.data ?? body));\r\n    return { data: { success: true, data: safe, message: body?.message || \"Template saved successfully\" } };\r\n  } catch (e) {\r\n    return { data: { success: false, message: errMsg(e) } };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB;AACA;AACA;AACA;AACA;;;;;;;;;AAQA,SAAS,OAAO,CAAU;IACxB,kDAAkD;IAClD,IAAI;QACF,IAAI,KAAK,OAAO,MAAM,YAAY,aAAc,GAAW,OAAO,OAAO,AAAC,EAAU,OAAO;QAC3F,OAAO,OAAO;IAChB,EAAE,OAAM;QAAE,OAAO;IAAiB;AACpC;AAEO,eAAe,uCAAmB,GAAnB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,aAAa;QACxD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,iBAAiB,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,WAAmB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,OAAO,CACnC,iBACA,YAAY,QAAQ,KAEtB;YAAE,QAAQ;QAAS;QAErB,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BAA8B,aAAqB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAChD,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAmB,GAAnB,sBACpB,aAAqB,EACrB,WAAgB;IAEhB,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACrD,oBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QACvC,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,MAAM;gBAAM,SAAS;YAAwC;QAAE;IACjG,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;AAEO,eAAe,uCAA0B,GAA1B,6BACpB,WAAmB,EACnB,IAAoC;IAEpC,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAC5D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,WAAmB,EACnB,IAA8B;IAE9B,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QACvC,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,MAAM;gBAAM,SAAS;YAAiD;QAAE;IAC1G,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;AAEO,eAAe,uCAAyB,GAAzB,4BAA4B,aAAqB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,yBAAyB,CAAC,OAAO,CAC7C,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAY,GAAZ;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,YAAY;QACvD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAuB,GAAvB,0BAA0B,SAAiB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACzC,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,WAAW,EAAE;QAChE,MAAM;IACR;AACF;AAEO,eAAe,uCAAwB,GAAxB,2BAA2B,SAAiB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,wBAAwB,CAAC,OAAO,CAC5C,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,SAAiB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC,OAAO,CACxC,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AASO,eAAe,uCAAmB,GAAnB,sBAAsB,IAAS;IACnD,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,0BAA0B;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAU,GAAV,aAAa,IAAS;IAC1C,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,gBAAgB;QAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,iCAAiC;QAC7C,OAAO;IACT;AACF;AACO,eAAe,uCAAe,GAAf,kBACpB,QAAgB,EAChB,eAAyB;IAEzB,IAAI;QACF,MAAM,uBAAuB,QAAQ,GAAG,CAAC,sBAAsB,KAAK;QACpE,IAAI,CAAC,sBAAsB;YACzB,MAAM,WAAW;gBACf,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,aAAa;oBACX,kBAAkB;oBAClB,eAAe;oBACf,SAAS;oBACT,cAAc;oBACd,iBAAiB;oBACjB,wBAAwB;oBACxB,kBAAkB;oBAClB,iBAAiB;oBACjB,YAAY;oBACZ,qBAAqB;gBACvB;YACF;YACA,IAAI,iBAAiB;gBACnB,8BAA8B;gBAC9B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;gBAChC,YAAY,GAAG,CAAC,eAAe,KAAK,SAAS,CAAC,SAAS,WAAW,GAAG;oBACnE,QAAQ;gBACV;YACF;YACA,OAAO;QACT;QACA,MAAM,cAAc;QACpB,MAAM,MAAM,GAAG,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,UAAU,EAAE,mBACvD,UACA,aAAa,EAAE,mBAAmB,cAAc;QAClD,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAA4B,MAAM,SAAS,IAAI;QACrD,IAAI,iBAAiB;YACnB,8BAA8B;YAC9B,CAAC,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC,eAAe,KAAK,SAAS,CAAC,KAAK,WAAW,GAAG;gBACrE,QAAQ;YACV;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AACO,eAAe,uCAA4B,GAA5B;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,8BAA8B;QACzE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,8BAA8B,EAAE;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,aAAqB,EACrB,IAAS;IAET,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,mBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QACvC,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,MAAM;gBAAM,SAAS;YAAiD;QAAE;IAC1G,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,IAAS;IACtD,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,oBAAoB;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM;IACR;AACF;AAEO,eAAe,uCAAgB,GAAhB,mBACpB,KAAa,EACb,UAAkB,EAClB,WAAmB,EACnB,WAAgB,IAAI;IAEpB,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,eAAe,CACtC,OAAO,CAAC,cAAc,OACtB,OAAO,CAAC,iBAAiB,YACzB,OAAO,CAAC,aAAa;IAExB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QAErC,IAAI,CAAC,YAAY,SAAS,MAAM,IAAI,KAAK;YACvC,QAAQ,KAAK,CACX,CAAC,qCAAqC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAElF,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,QAAQ,IAAI,CAAC,CAAC,2CAA2C,CAAC,EAAE;YAC5D,MAAM,IAAI,MAAM,CAAC,wCAAwC,CAAC;QAC5D;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC,EAAE;QAC9D,OAAO;IACT;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,SAAiB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,OAAO,CAC1C,eACA,UAAU,QAAQ;QAGtB,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,IAAI,0BAA0B;YACnE,MAAM,QAAQ;gBACZ,OAAO;gBACP,QAAQ,SAAS,MAAM;gBACvB,SAAS,UAAU,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI;YAC7C;YACA,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC;QACjC;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IACE,OAAO,UAAU,YACjB,UAAU,QACV,aAAa,SACb,OAAO,AAAC,MAAc,OAAO,KAAK,UAClC;YACA,MAAM,IAAI,MACR,KAAK,SAAS,CAAC;gBACb,OAAO;gBACP,QAAQ,KAAK,KAAK,CAAC,AAAC,MAAc,OAAO,EAAE,MAAM,IAAI;gBACrD,SACE,KAAK,KAAK,CAAC,AAAC,MAAc,OAAO,EAAE,OAAO,IAC1C;YACJ;QAEJ;IACF;AACF;AAEO,eAAe,uCAAqB,GAArB,wBAAwB,UAAkB,EAAE,IAAS;IACzE,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,uBAAuB,CAAC,OAAO,CACvD,eACA;IAEF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAEO,eAAe,uCAAqB,GAArB,wBAAwB,OAAY;IACxD,IAAI;QACF,MAAM,OAAO,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,mBAAmB,EAAE;YAC1D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;gBAAoB,UAAU;YAAmB;YAC5E,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO;gBAAE,MAAM;oBAAE,SAAS;oBAAO,SAAS,MAAM,WAAW;oBAAoB,MAAM,KAAK,MAAM;gBAAC;YAAE;QACrG;QACA,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,SAAS,MAAM,WAAW;YAAQ;QAAE;IACtE,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;AAEO,eAAe,uCAAgB,GAAhB,mBACpB,EAAE,UAAU,EAA0B;IAEtC,IAAI;QACF,MAAM,OAAO,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,kBAAkB,EAAE;YACzD,QAAQ;YACR,SAAS;gBAAE,gBAAgB;gBAAoB,UAAU;YAAmB;YAC5E,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;QAEA,MAAM,OAAO,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO;gBAAE,MAAM;oBAAE,SAAS;oBAAO,SAAS,MAAM,WAAW;oBAA2B,MAAM,KAAK,MAAM;gBAAC;YAAE;QAC5G;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,MAAM,QAAQ;QACrD,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,MAAM;gBAAM,SAAS,MAAM,WAAW;YAAkC;QAAE;IAC5G,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;AAIO,eAAe,uCAAiB,GAAjB,oBACpB,YAAiB,EACjB,aAAsB,KAAK;IAE3B,IAAI;QACF,sEAAsE;QACtE,MAAM,kBAAkB;YACtB,YAAY,aAAa,UAAU;YACnC,iBAAiB,aAAa,cAAc,IAAI,EAAE;YAClD,kBAAkB,aAAa,eAAe,IAAI,EAAE;YACpD,0BAA0B,aAAa,qBAAqB,IAAI;YAChE,uBAAuB,aAAa,mBAAmB,IAAI,EAAE;YAC7D,MAAM,aAAa,IAAI,IAAI;YAC3B,OAAO,aAAa,KAAK,IAAI;YAC7B,OAAO,aAAa,KAAK,IAAI;YAC7B,kBAAkB,aAAa,eAAe,IAAI;YAClD,SAAS,aAAa,OAAO,IAAI,EAAE;YACnC,yBAAyB,aAAa,sBAAsB,IAAI,EAAE;YAClE,UAAU,aAAa,QAAQ,IAAI;YACnC,cAAc,aAAa,YAAY,IAAI;YAC3C,uBAAuB,aAAa,mBAAmB,IAAI;QAC7D;QAEA,MAAM,UAAU;YACd,eAAe;YACf,YAAY;QACd;QAEA,MAAM,OAAO,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,mBAAmB,EAAE;YAC1D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;gBAAoB,UAAU;YAAmB;YAC5E,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO;gBAAE,MAAM;oBAAE,SAAS;oBAAO,SAAS,MAAM,WAAW;oBAA2B,MAAM,KAAK,MAAM;gBAAC;YAAE;QAC5G;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,MAAM,QAAQ;QACrD,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAM,MAAM;gBAAM,SAAS,MAAM,WAAW;YAA8B;QAAE;IACxG,EAAE,OAAO,GAAG;QACV,OAAO;YAAE,MAAM;gBAAE,SAAS;gBAAO,SAAS,OAAO;YAAG;QAAE;IACxD;AACF;;;IArdsB;IAWA;IAgBA;IAgBA;IAkBA;IAiBA;IAkBA;IAgBA;IAWA;IAgBA;IAgBA;IAuBA;IAWA;IAUA;IAwDA;IAWA;IAkBA;IAWA;IAiCA;IAuCA;IAaA;IAmBA;IAyBA;;AAxaA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAkBA,iPAAA;AAiBA,iPAAA;AAkBA,iPAAA;AAgBA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAuBA,iPAAA;AAWA,iPAAA;AAUA,iPAAA;AAwDA,iPAAA;AAWA,iPAAA;AAkBA,iPAAA;AAWA,iPAAA;AAiCA,iPAAA;AAuCA,iPAAA;AAaA,iPAAA;AAmBA,iPAAA;AAyBA,iPAAA"}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/utils.ts"], "sourcesContent": ["import { EntitlementMap } from \"@/context/EntitlementContext\";\r\nimport moment from \"moment-timezone\";\r\nimport { isADLogin } from \"@/api/config\"; // Adjust the import path as necessary\r\n\r\nexport const VACANCY_FILTER_URL_REGEX =\r\n  /^https:\\/\\/recruiter(?:\\.([^.]+))?\\.tandymgroup\\.com/;\r\n\r\nexport const unFormattedDateWithBrowserTimezone = (\r\n  val: string,\r\n  tz: string = \"America/Los_Angeles\" // Default to PST\r\n) => {\r\n  const utcDate = moment.utc(val); // Convert input to UTC\r\n  const formattedPSTDateTime = utcDate.tz(tz).format(\"YYYY-MM-DD (hh:mm A)\"); // Convert to PST and format with AM/PM\r\n\r\n  return formattedPSTDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneWithTimezone = (val: string) => {\r\n  const browserTz = Intl.DateTimeFormat().resolvedOptions().timeZone; // get browser timezone\r\n  const utcDate = moment.utc(val);\r\n  const formattedDateTime = utcDate\r\n    .tz(browserTz)\r\n    .format(\"YYYY-MM-DD (hh:mm A z)\");\r\n  return formattedDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneInDDMMYY = (\r\n  val: string,\r\n  mode: \"STATIC\" | \"UTC_TO_LOCAL\" = \"STATIC\" // default mode for static dates\r\n): string => {\r\n  if (!moment(val).isValid()) return \"Invalid Date\";\r\n\r\n  const dateMoment =\r\n    mode === \"UTC_TO_LOCAL\"\r\n      ? moment.utc(val).local() // convert UTC to browser's local time\r\n      : moment(val); // no conversion\r\n\r\n  return dateMoment.format(\"MM-DD-YYYY\");\r\n};\r\n\r\nexport const getInitials = (name: string) => {\r\n  if (!name) return \"\";\r\n  return name\r\n    .split(\" \") // Split the name into parts by spaces\r\n    .map((part) => part.charAt(0).toUpperCase()) // Take the first letter of each part and capitalize it\r\n    .join(\"\"); // Join the initials together\r\n};\r\n\r\n// utils/entitlementUtils.ts (or inline)\r\nexport const getEntitlementKeyFromTitle = (\r\n  title: string\r\n): string | undefined => {\r\n  switch (title) {\r\n    case \"Vacancy\":\r\n      return \"Vacancy\";\r\n    case \"Workforce Readiness Index\":\r\n      return \"Work_force_Index\";\r\n    case \"Sub-Category Library\":\r\n      return \"Sub_Catregory\";\r\n    default:\r\n      return undefined;\r\n  }\r\n};\r\n\r\nexport const isUserEntitled = (\r\n  entitlementKey: string | undefined,\r\n  entitlement: EntitlementMap\r\n): boolean => {\r\n  const isADLoginValue: boolean = isADLogin();\r\n  if (!isADLoginValue || !entitlementKey) {\r\n    return true; // Default to true if not AD login or entitlement key is undefined\r\n  }\r\n  return !!entitlement[entitlementKey];\r\n};\r\n\r\nexport function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n  const code = envCode.toLowerCase();\r\n  if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n    return \"sandbox\";\r\n  }\r\n  if (code === \"ua\") {\r\n    return \"uat\";\r\n  }\r\n  if (code === \"prod\") {\r\n    return \"prod\";\r\n  }\r\n  // Optionally, handle unknown codes\r\n  return \"sandbox\";\r\n}\r\n\r\nexport const capitalizeEachWord = (str: string) =>\r\n  str.toLowerCase().replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA,gMAA0C,sCAAsC;;;AAEzE,MAAM,2BACX;AAEK,MAAM,qCAAqC,CAChD,KACA,KAAa,sBAAsB,iBAAiB;AAAlB;IAElC,MAAM,UAAU,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,MAAM,uBAAuB;IACxD,MAAM,uBAAuB,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,yBAAyB,uCAAuC;IAEnH,OAAO;AACT;AAEO,MAAM,iDAAiD,CAAC;IAC7D,MAAM,YAAY,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE,uBAAuB;IAC3F,MAAM,UAAU,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC;IAC3B,MAAM,oBAAoB,QACvB,EAAE,CAAC,WACH,MAAM,CAAC;IACV,OAAO;AACT;AAEO,MAAM,6CAA6C,CACxD,KACA,OAAkC,SAAS,gCAAgC;AAAjC;IAE1C,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,OAAO,IAAI,OAAO;IAEnC,MAAM,aACJ,SAAS,iBACL,6IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,sCAAsC;OAC9D,CAAA,GAAA,6IAAA,CAAA,UAAM,AAAD,EAAE,MAAM,gBAAgB;IAEnC,OAAO,WAAW,MAAM,CAAC;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,KAAK,CAAC,KAAK,sCAAsC;KACjD,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,IAAI,uDAAuD;KACnG,IAAI,CAAC,KAAK,6BAA6B;AAC5C;AAGO,MAAM,6BAA6B,CACxC;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,iBAAiB,CAC5B,gBACA;IAEA,MAAM,iBAA0B,CAAA,GAAA,+GAAA,CAAA,YAAS,AAAD;IACxC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB;QACtC,OAAO,MAAM,kEAAkE;IACjF;IACA,OAAO,CAAC,CAAC,WAAW,CAAC,eAAe;AACtC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,OAAO,QAAQ,WAAW;IAChC,IAAI;QAAC;QAAM;QAAM;KAAK,CAAC,QAAQ,CAAC,OAAO;QACrC,OAAO;IACT;IACA,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,mCAAmC;IACnC,OAAO;AACT;AAEO,MAAM,qBAAqB,CAAC,MACjC,IAAI,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW"}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { VACANCY_FILTER_URL_REGEX } from \"@/utils/utils\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const getDomainClient = () => {\r\n    const url = window.location.href;\r\n    const match = url.match(VACANCY_FILTER_URL_REGEX);\r\n    return match && match[1] ? match[1] : \"prod\";\r\n  };\r\n\r\n  if (!appInsights) {\r\n    let connectionString = \"\"; // ✅ Declare here so it's in scope\r\n\r\n    const env = getDomainClient();\r\n    switch (env) {\r\n      case \"dv\":\r\n        connectionString =\r\n          \"InstrumentationKey=a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849\";\r\n        break;\r\n      case \"qa\":\r\n        connectionString =\r\n          \"InstrumentationKey=f3dd9927-e8d5-439b-972a-da458156c3ac;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=d6e306bb-1468-40e7-99d1-12f2024ed410\";\r\n        break;\r\n      case \"sb\":\r\n        connectionString =\r\n          \"InstrumentationKey=10a2fa35-5df1-4728-b76d-50b7548e507f;IngestionEndpoint=https://eastus-5.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=8b3040c3-62e0-42e5-9bef-b49ffa496183\";\r\n        break;\r\n      case \"ua\":\r\n        connectionString =\r\n          \"InstrumentationKey=017ce8e3-75aa-4b79-821a-a254f47527df;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=7f1efc32-bc37-4cd1-bd8e-c4af6169342b\";\r\n        break;\r\n      default:\r\n        connectionString =\r\n          \"InstrumentationKey=cb49f8e1-9368-42f9-95d6-5ab16b42c75a;IngestionEndpoint=https://eastus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=1fe4a6f4-7eee-4cbe-9e7c-ed77e57398bf\";\r\n    }\r\n\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false,\r\n        enableCorsCorrelation: true,\r\n        enableRequestHeaderTracking: true,\r\n        enableResponseHeaderTracking: true,\r\n        extensions: [],\r\n        extensionConfig: {},\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n\r\n    const isIframe = window.self !== window.top;\r\n    appInsights.trackEvent({\r\n      name: \"AppInsightsInitialized\",\r\n      properties: {\r\n        isIframe,\r\n        referrer: document.referrer,\r\n        currentURL: window.location.href,\r\n      },\r\n    });\r\n  }\r\n\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI;QAChC,MAAM,QAAQ,IAAI,KAAK,CAAC,gHAAA,CAAA,2BAAwB;QAChD,OAAO,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACxC;IAEA,IAAI,CAAC,aAAa;QAChB,IAAI,mBAAmB,IAAI,kCAAkC;QAE7D,MAAM,MAAM;QACZ,OAAQ;YACN,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF,KAAK;gBACH,mBACE;gBACF;YACF;gBACE,mBACE;QACN;QAEA,cAAc,IAAI,4OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,8BAA8B;gBAC9B,YAAY,EAAE;gBACd,iBAAiB,CAAC;YACpB;QACF;QACA,YAAY,eAAe;QAE3B,MAAM,WAAW,OAAO,IAAI,KAAK,OAAO,GAAG;QAC3C,YAAY,UAAU,CAAC;YACrB,MAAM;YACN,YAAY;gBACV;gBACA,UAAU,SAAS,QAAQ;gBAC3B,YAAY,OAAO,QAAQ,CAAC,IAAI;YAClC;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n\r\nexport enum FEATURE_NAMES {\r\n  SORTLIST_CANDIDATE_FEATURE = \"Shortlist Candidate Feature\",\r\n  MERCRURY_SORTLIST_CANDIDATE_FEATURE = \"Mercury Shortlist Candidate Feature\",\r\n  THUMB_REVIEW_FEATURE = \"Thumb Review Feature\",\r\n  THUMB_REVIEW_FEATURE_FROM_MERCURY = \"Thumb Review Feature from Mercury CRM\",\r\n  REGENERATE_CANDIDATES_MATCH_FEATURE = \"Regenerate Candidates Match Feature\",\r\n  MERCURY_REGENERATE_CANDIDATES_MATCH_FEATURE = \"Mercury Regenerate Candidates Match Feature\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_LABELS {\r\n  STATE = \"State\",\r\n  CITY = \"City\",\r\n  FRESHNESS_INDEX = \"Freshness Index\",\r\n  SHORT_LISTED = \"Short Listed\",\r\n  REVIEW_DECISION = \"Review Decision\",\r\n  AI_AGENT_STATUS = \"AI Agent Status\",\r\n  TOTAL_SCORE = \"Total Score\",\r\n  AVAILABILITY_DATE = \"Availability Date\",\r\n  SEARCH_BY_NAME = \"Search by Name\",\r\n  TOTAL_SCORE_RANGE = \"Total Score Range\",\r\n  AVAILABILITY_DATE_RANGE = \"Availability Date Range\",\r\n  SEARCH_FIELDS = \"Search Fields\",\r\n  DISTANCE_RANGE = \"Distance Range\",\r\n}\r\n\r\nexport enum VACANCY_FILTER_OTHER_LABELS {\r\n  NAME = \"name\",\r\n  CITY = \"city\",\r\n  WHYFIT = \"whyFit\",\r\n  NOREVIEW = \"NoReview\",\r\n  THUMBSUP = \"ThumbsUp\",\r\n  THUMBSDOWN = \"ThumbsDown\",\r\n  LIKE = \"like\",\r\n  DISLIKE = \"dislike\",\r\n  MISSING = \"Missing\",\r\n  SUCCESS = \"success\",\r\n  CLASSIFICATIONSCORE = \"classification score\",\r\n  OVERALLSCORE = \"overallscore\",\r\n}\r\n\r\nexport enum AI_AGENTS_RESPONSE_STATUS {\r\n  NOT_CONTACTED_BLANK = \"Not Contacted (Blank)\",\r\n  RESPONDED = \"Responded\",\r\n  CONTACTED = \"Contacted\",\r\n  NOT_INTERESTED = \"Not interested\",\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,wCAAmC,OAAO;;IAC1C,IAAI;AAMN;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB;AAE7B,IAAA,AAAK,uCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,+CAAA;;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,qDAAA;;;;;;;;;;;;;WAAA;;AAeL,IAAA,AAAK,mDAAA;;;;;WAAA"}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/auth.ts"], "sourcesContent": ["import { API_ENDPOINTS } from \"@/api/config\";\r\nimport AzureADProvider from \"next-auth/providers/azure-ad\";\r\nimport { trackedFetch } from \"./trackApi\";\r\nimport { getAppInsights } from \"./appInsights\";\r\n\r\nexport const authOptions = {\r\n  providers: [\r\n    AzureADProvider({\r\n      clientId: process.env.RECRUITER_SSO_CLIENT_ID! as string,\r\n      clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET! as string,\r\n      tenantId: process.env.NEXT_PUBLIC_AZURE_TENANT_ID! as string,\r\n    }),\r\n  ],\r\n  secret: process.env.NEXTAUTH_SECRET!,\r\n  pages: {\r\n    signIn: `${process.env.NEXTAUTH_URL}/login`, // Custom sign-in page URL\r\n  },\r\n  // cookies: {\r\n  //   sessionToken: {\r\n  //     name: `__Secure-next-auth.session-token`,\r\n  //     options: {\r\n  //       httpOnly: true,\r\n  //       sameSite: \"none\",  // Required for third-party iframes\r\n  //       secure: true,      // Required when SameSite=None\r\n  //       path: \"/\",\r\n  //     },\r\n  //   },\r\n  // },\r\n  callbacks: {\r\n    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {\r\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\r\n      if (new URL(url).origin === baseUrl) return url;\r\n      return baseUrl;\r\n    },\r\n\r\n    async session({ session }: any) {\r\n      if (\r\n        process.env.NEXT_PUBLIC_AD_LOGIN !== \"true\" &&\r\n        process.env.IS_ENTITLEMENT_ENABLED !== \"true\"\r\n      ) {\r\n        return session;\r\n      }\r\n      const portal_name = \"recruiter\";\r\n      let entitlement = {};\r\n      try {\r\n        // Caling an entitlements API to fetch user entitlements\r\n        const url = `${\r\n          API_ENDPOINTS.getEntitlements\r\n        }?email_id=${encodeURIComponent(\r\n          session?.user?.email\r\n        )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n        const res = await trackedFetch(url, {}, { context: \"getEntitlements\" });\r\n\r\n        if (res.ok) {\r\n          const data = await res.json();\r\n          entitlement = data.entitlement;\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email,\r\n            },\r\n          });\r\n        } else {\r\n          console.error(\"Failed to fetch entitlements:\", res.statusText);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching entitlements:\", err);\r\n      }\r\n      return {\r\n        ...session,\r\n        entitlement,\r\n      };\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAe,AAAD,EAAE;YACd,UAAU,QAAQ,GAAG,CAAC,uBAAuB;YAC7C,cAAc,QAAQ,GAAG,CAAC,2BAA2B;YACrD,QAAQ;QACV;KACD;IACD,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;IAC7C;IACA,aAAa;IACb,oBAAoB;IACpB,gDAAgD;IAChD,iBAAiB;IACjB,wBAAwB;IACxB,+DAA+D;IAC/D,0DAA0D;IAC1D,mBAAmB;IACnB,SAAS;IACT,OAAO;IACP,KAAK;IACL,WAAW;QACT,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAoC;YAC/D,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAC5C,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAO;YAC5B,uCAGE;;YAEF;YACA,MAAM,cAAc;YACpB,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,wDAAwD;gBACxD,MAAM,MAAM,GACV,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAC9B,UAAU,EAAE,mBACX,SAAS,MAAM,OACf,aAAa,EAAE,mBAAmB,cAAc;gBAClD,MAAM,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,GAAG;oBAAE,SAAS;gBAAkB;gBAErE,IAAI,IAAI,EAAE,EAAE;oBACV,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,cAAc,KAAK,WAAW;oBAC9B,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;wBAC3B,MAAM;wBACN,YAAY;4BACV,OAAO,SAAS,MAAM;wBACxB;oBACF;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC,IAAI,UAAU;gBAC/D;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;YACA,OAAO;gBACL,GAAG,OAAO;gBACV;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from \"next-auth\";\r\nimport { authOptions } from \"@/library/auth\";\r\n\r\nconst adAuthorization = process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport async function CheckAuth(route?: string | string[]) {\r\n  const session = await getServerSession(authOptions);\r\n  const entitlement = session?.entitlement || {};\r\n\r\n const showCandidateTuningPageAuthLogin =\r\n     process.env.IS_MERCURY_AD_AUTH_ENABLED\r\n     if (showCandidateTuningPageAuthLogin === \"false\") {\r\n        return true; // If the feature flag is disabled, skip auth checks\r\n     }\r\n  let isAuthorized = false;\r\n\r\n  if (Array.isArray(route)) {\r\n    // ✅ If multiple entitlements are passed, authorize if ANY one is true\r\n    isAuthorized = route.some((r) => entitlement[r]);\r\n  } else if (typeof route === \"string\") {\r\n    // ✅ Fallback for single entitlement check\r\n    isAuthorized = entitlement[route] || false;\r\n  }\r\n\r\n  if (adAuthorization) {\r\n    if (session && isAuthorized) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  } else {\r\n    return true;\r\n  }\r\n}\r\n\r\nexport async function AuthErrorHandler() {\r\n  return new Response(\r\n    JSON.stringify({\r\n      message: \"Unauthenticated Access, please login to the portal\",\r\n    }),\r\n    {\r\n      status: 403,\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,kBAAkB,6CAAqC;AAEtD,eAAe,UAAU,KAAyB;IACvD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,iHAAA,CAAA,cAAW;IAClD,MAAM,cAAc,SAAS,eAAe,CAAC;IAE9C,MAAM,mCACF,QAAQ,GAAG,CAAC,0BAA0B;IACtC,IAAI,qCAAqC,SAAS;QAC/C,OAAO,MAAM,oDAAoD;IACpE;IACH,IAAI,eAAe;IAEnB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,sEAAsE;QACtE,eAAe,MAAM,IAAI,CAAC,CAAC,IAAM,WAAW,CAAC,EAAE;IACjD,OAAO,IAAI,OAAO,UAAU,UAAU;QACpC,0CAA0C;QAC1C,eAAe,WAAW,CAAC,MAAM,IAAI;IACvC;IAEA,wCAAqB;QACnB,IAAI,WAAW,cAAc;YAC3B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,OAAO;;IAEP;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;QACb,SAAS;IACX,IACA;QACE,QAAQ;IACV;AAEJ"}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/vacancies/catelist-match-status/route.ts"], "sourcesContent": ["import { fetchCatalystMatchStatus } from \"@/api/serverActions\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { AuthError<PERSON><PERSON><PERSON>, CheckAuth } from \"@/utils/auth-utils\";\r\nimport { APPLICATION_NAVIGATION_ROUTES } from \"@/library/utils\";\r\n\r\nexport async function GET(request: NextRequest) {\r\n  const { searchParams } = new URL(request.url);\r\n  const id = searchParams.get(\"id\");\r\n  if (!id) {\r\n    return Response.json(\r\n      { error: true, message: \"Missing vacancy id\" },\r\n      { status: 400 }\r\n    );\r\n  }\r\n  try {\r\n    const response = await CheckAuth(APPLICATION_NAVIGATION_ROUTES.VACANCY);\r\n      if (response) {\r\n    const response = await fetchCatalystMatchStatus(id);\r\n    // return Response.json({ error: false, data: response, status: 200 });\r\n    return Response.json(response, { status: 200 });\r\n      }\r\n      else {\r\n          return AuthErrorHandler();\r\n        }\r\n  } catch (error) {\r\n    console.error(\"Error in GET /api/vacancies/catelist-match-status:\", error);\r\n\r\n    let status = 500;\r\n    let errorResponse = {\r\n      error: true,\r\n      message: \"Internal server error\",\r\n    };\r\n\r\n    if (error instanceof Error) {\r\n      try {\r\n        const parsed = JSON.parse(error.message);\r\n        errorResponse = parsed;\r\n        status = parsed.status ?? 500;\r\n      } catch {\r\n        errorResponse.message = error.message;\r\n      }\r\n    }\r\n\r\n    return Response.json(errorResponse, { status });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;IAC5B,IAAI,CAAC,IAAI;QACP,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO;YAAM,SAAS;QAAqB,GAC7C;YAAE,QAAQ;QAAI;IAElB;IACA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE,kHAAA,CAAA,gCAA6B,CAAC,OAAO;QACpE,IAAI,UAAU;YAChB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,uEAAuE;YACvE,OAAO,SAAS,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QAC3C,OACK;YACD,OAAO,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD;QACxB;IACN,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QAEpE,IAAI,SAAS;QACb,IAAI,gBAAgB;YAClB,OAAO;YACP,SAAS;QACX;QAEA,IAAI,iBAAiB,OAAO;YAC1B,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,OAAO;gBACvC,gBAAgB;gBAChB,SAAS,OAAO,MAAM,IAAI;YAC5B,EAAE,OAAM;gBACN,cAAc,OAAO,GAAG,MAAM,OAAO;YACvC;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,eAAe;YAAE;QAAO;IAC/C;AACF"}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}