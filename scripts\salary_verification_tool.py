#!/usr/bin/env python3
"""
Salary Verification Tool (Shazamme)

This tool verifies salary information in Shazamme job postings and can automatically
correct salary issues by calling the update function from shazamme_job_poster.

Features:
- Fetches job postings from Shazamme API
- Compares current salaries with expected values from Dataverse
- Automatically updates incorrect salaries using shazamme_job_poster.update_vacancy_job
- Supports dry-run, interactive, and auto-repost modes
- Provides detailed reporting of salary issues and corrections
"""

import sys
import os
import json
import argparse
import requests
from datetime import datetime, timedelta
from common.appLogger import AppLogger, getGlobalAppLogger
from common.secrets_env import load_secrets_env_variables
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_vacancy import VacancyDataverseHelper
from generator.shazamme_job_poster import update_vacancy_job

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

# Shazamme API constants
SHAZAMME_API_BASE_URL = os.getenv('SHAZAMME_API_BASE_URL', "https://go.shazamme.com")
SHAZAMME_SITE_ID = os.getenv('SHAZAMME_SITE_ID', "2BA1F389-0B5A-4AA4-A95D-0B617B97763D")
SHAZAMME_ADVERTISER_ID = os.getenv('SHAZAMME_ADVERTISER_ID', "83093afd-af0b-43b9-85db-cd43dc68e833")

class SalaryVerificationTool:
    """Class for verifying and correcting salary information in Shazamme job postings."""
    
    def __init__(self, logger=None, environment=Environment.PROD):
        load_secrets_env_variables()
        self.logger = logger if logger is not None else getGlobalAppLogger()
        self.environment = environment
        
        # Shazamme API credentials
        self.shazamme_api_key = os.getenv('SHAZAMME_API_KEY')
        if not self.shazamme_api_key:
            raise ValueError("SHAZAMME_API_KEY environment variable is not set")
        
        # Dataverse credentials for fetching vacancy data
        self.token = get_token_for_env(self.environment, logger=self.logger)
        self.credentials = get_dataverse_credentials_for_env(self.environment, logger=self.logger)
        self.dataverse_url = self.credentials["RESOURCE_URL"]
        self.vacancy_helper = VacancyDataverseHelper(self.token, self.dataverse_url, logger=self.logger)
    
    def fetch_job_postings_from_shazamme(self):
        """Fetch job postings from Shazamme for the specific advertiser."""
        url = f"{SHAZAMME_API_BASE_URL}/rest/v1/Shazamme/job"
        params = {
            "siteID": SHAZAMME_SITE_ID,
            "advertiserID": SHAZAMME_ADVERTISER_ID,
            "$count": "true"
        }
        
        headers = {
            "X-API-Key": self.shazamme_api_key,
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            api_status = data.get('status', 0)
            count = data.get('count', 0)
            
            if api_status == 200:
                items = data.get('items', [])
                self.logger.info(f"Found {count} job postings in Shazamme")
                return items
            else:
                error_msg = f"API returned status {api_status} with message: {data.get('message', 'No message')}"
                self.logger.error(error_msg)
                return []
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching job postings from Shazamme: {str(e)}")
            return []
    
    def fetch_vacancy_data(self, reference_number):
        """Fetch vacancy data from Dataverse by reference number."""
        try:
            additional_headers = {
                'Prefer': 'odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
            }
            expand = (
                "owninguser($select=domainname),"
                "mercury_vacancytype($select=mercury_name),"
                "mercury_subsector($select=mercury_name),"
                "recruit_addressstate($select=mercury_code, mercury_name),"
                "mercury_pay_mc_currency($select=currencysymbol,currencyname),"
                "recruit_country($select=mercury_iso3digitcode),"
                "crimson_frequency($select=crimson_frequency),"
                "crimson_deliveryownerid($select=domainname)"
            )
            where_clause = f"crimson_vacancyrefno eq '{reference_number}'"
            vacancies = self.vacancy_helper.fetch_vacancies(['*'], where_clause, expand=expand, additional_headers=additional_headers)
            
            if vacancies and len(vacancies) > 0:
                return vacancies[0]
            else:
                self.logger.warning(f"No vacancy found with reference number: {reference_number}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error fetching vacancy data for {reference_number}: {str(e)}")
            return None
    
    def fetch_current_job_from_shazamme(self, job_id):
        """Fetch the current job posting from Shazamme to verify existing data before updating."""
        try:
            url = f"{SHAZAMME_API_BASE_URL}/rest/v1/Shazamme/job/{job_id}"
            
            headers = {
                "X-API-Key": self.shazamme_api_key,
                "Content-Type": "application/json"
            }
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                api_status = data.get('status', 0)
                
                if api_status == 200:
                    job_data = data.get('item', {})
                    self.logger.info(f"Successfully fetched current job {job_id} from Shazamme")
                    return job_data
                else:
                    error_msg = f"API returned status {api_status} with message: {data.get('message', 'No message')}"
                    self.logger.warning(f"Failed to fetch current job {job_id}: {error_msg}")
                    return None
            else:
                self.logger.warning(f"Failed to fetch current job {job_id} (HTTP {response.status_code})")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching current job {job_id} from Shazamme: {str(e)}")
            return None
    
    def get_salary_type_id(self, salary_type_name):
        """
        Get the salary type ID from Shazamme taxonomy endpoint.
        salary_type_name examples: "Annual", "Hourly", "Daily", etc.
        """
        if not salary_type_name:
            return None
        try:
            url = f"{SHAZAMME_API_BASE_URL}/rest/v1/Shazamme/taxonomy/salarytype"
            headers = {"X-API-Key": self.shazamme_api_key, "Content-Type": "application/json"}
            params = {"siteID": SHAZAMME_SITE_ID}
            response = requests.get(url, headers=headers, params=params)
            if response.status_code != 200:
                self.logger.error(f"Failed to fetch salary types (HTTP {response.status_code})")
                return None
            data = response.json()
            if data.get('status', 0) != 200:
                self.logger.error(f"Failed to fetch salary types: {data.get('message')}")
                return None
            for salary_type in data.get('items', []):
                if salary_type.get('name', '').lower() == salary_type_name.lower():
                    salary_type_id = salary_type.get('salaryTypeID')
                    self.logger.info(f"Found salary type '{salary_type_name}' with ID: {salary_type_id}")
                    return salary_type_id
            
            self.logger.warning(f"Salary type '{salary_type_name}' not found in taxonomy")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching salary types from Shazamme: {str(e)}")
            return None
    
    def get_work_type_from_shazamme(self, job_posting):
        """Get work type name from a Shazamme job posting."""
        work_type_name = job_posting.get('workType', '')
        if work_type_name in ['Direct Hire', 'Permanent']:
            return 'Direct Hire'
        elif work_type_name in ['Contractor', 'Contract', 'Contract to Hire', 'Temporary']:
            return 'Contractor'
        else:
            self.logger.warning(f"Unknown work type '{work_type_name}' for job {job_posting.get('jobID', 'Unknown')}, defaulting to 'Contractor'")
            return 'Contractor'
    
    def get_work_type_id(self, work_type_name):
        """Resolve workTypeID (UUID) from taxonomy by name (e.g., 'Direct Hire', 'Contractor')."""
        if not work_type_name:
            return None
        try:
            url = f"{SHAZAMME_API_BASE_URL}/rest/v1/Shazamme/taxonomy/worktype"
            headers = {"X-API-Key": self.shazamme_api_key, "Content-Type": "application/json"}
            params = {"siteID": SHAZAMME_SITE_ID}
            response = requests.get(url, headers=headers, params=params)
            if response.status_code != 200:
                self.logger.error(f"Failed to fetch work types (HTTP {response.status_code})")
                return None
            data = response.json()
            if data.get('status') != 200:
                self.logger.error(f"Failed to fetch work types: {data.get('message')}")
                return None
            for wt in data.get('items', []):
                if wt.get('name', '').lower() == work_type_name.lower():
                    work_type_id = wt.get('workTypeID')
                    self.logger.info(f"Found work type '{work_type_name}' with ID: {work_type_id}")
                    return work_type_id
            self.logger.warning(f"Work type '{work_type_name}' not found in taxonomy")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching work types from Shazamme: {str(e)}")
            return None
    
    def calculate_expected_salary_for_shazamme(self, job_posting):
        """Calculate expected salary; this implementation expects salary for all jobs."""
        state_name = job_posting.get('state', '')
        city_name = job_posting.get('city', '')
        work_type = self.get_work_type_from_shazamme(job_posting)
        salary_from = job_posting.get('salaryFrom', 0.00) or 0.00
        salary_to = job_posting.get('salaryTo', 0.00) or 0.00
        should_display_salary = salary_from > 0.00
        reason = f"{work_type} position - salary should be available for all jobs"
        
        return {
            'expected_salary_from': salary_from,
            'expected_salary_to': salary_to,
            'should_display_salary': should_display_salary,
            'reason': reason,
            'work_type': work_type,
            'state': state_name,
            'city': city_name
        }
    
    def verify_salary_information_from_shazamme(self, auto_repost=False, dry_run=False, interactive=False, verify_current_job=True):
        """Verify salary information for job postings from Shazamme."""
        self.logger.info(f"Fetching job postings from Shazamme for advertiser ID: {SHAZAMME_ADVERTISER_ID}")
        job_postings = self.fetch_job_postings_from_shazamme()
        total_count = len(job_postings)
        self.logger.info(f"Found {total_count} job postings to verify for advertiser {SHAZAMME_ADVERTISER_ID}")
        
        if job_postings:
            self.logger.info("Sample job posting structure:")
            sample_job = job_postings[0]
            self.logger.info(f"  Job ID: {sample_job.get('jobID', 'N/A')}")
            self.logger.info(f"  Reference Number: {sample_job.get('referenceNumber', 'N/A')}")
            self.logger.info(f"  Work Type: {sample_job.get('workType', 'N/A')}")
            self.logger.info(f"  State: {sample_job.get('state', 'N/A')}")
            self.logger.info(f"  City: {sample_job.get('city', 'N/A')}")
            self.logger.info(f"  Salary From: {sample_job.get('salaryFrom', 'N/A')}")
            self.logger.info(f"  Salary To: {sample_job.get('salaryTo', 'N/A')}")
            self.logger.info(f"  Is Display Salary: {sample_job.get('isDisplaySalary', 'N/A')}")
            self.logger.info(f"  Posted Date: {sample_job.get('postedDate', 'N/A')}")
            self.logger.info(f"  Added On UTC: {sample_job.get('addedOnUTC', 'N/A')}")
            self.logger.info(f"  Changed On UTC: {sample_job.get('changedOnUTC', 'N/A')}")
            self.logger.info(f"  Available fields: {list(sample_job.keys())}")
            detected_work_type = self.get_work_type_from_shazamme(sample_job)
            self.logger.info(f"  Detected Work Type: {detected_work_type}")
        
        results = {
            'total_job_postings': total_count,
            'direct_hire': 0,
            'contractors': 0,
            'salary_issues': [],
            'correct_salary': [],
            'no_salary_when_should': [],
            'salary_when_should_not': [],
            'summary_by_state': {},
            'summary_by_city': {},
            'jobs_with_issues': [],
            'all_contractors': [],
            'all_direct_hire': [],
            'reposted_jobs': [],
            'repost_failures': []
        }
        
        for job_posting in job_postings:
            job_id = job_posting.get('jobID', 'Unknown')
            reference_number = job_posting.get('referenceNumber', 'Unknown')
            expected = self.calculate_expected_salary_for_shazamme(job_posting)
            
            if len(results['all_contractors']) + len(results['all_direct_hire']) < 20:
                actual_salary_from = job_posting.get('salaryFrom', 0.00) or 0.00
                actual_salary_to = job_posting.get('salaryTo', 0.00) or 0.00
                self.logger.info(f"Processing job {job_id} ({reference_number}): work_type='{expected['work_type']}', state='{expected['state']}', salary_from='{actual_salary_from}', salary_to='{actual_salary_to}'")
            
            actual_salary_from = job_posting.get('salaryFrom', 0.00) or 0.00
            actual_salary_to = job_posting.get('salaryTo', 0.00) or 0.00
            is_display_salary = job_posting.get('isDisplaySalary', False)
            
            if expected['work_type'] == 'Direct Hire':
                results['direct_hire'] += 1
            else:
                results['contractors'] += 1
            
            issue_found = False
            issue_details = {
                'job_id': job_id,
                'reference_number': reference_number,
                'work_type': expected['work_type'],
                'state': expected['state'],
                'city': expected['city'],
                'expected_salary': expected['expected_salary_from'],
                'actual_salary': actual_salary_from,
                'should_display': expected['should_display_salary'],
                'is_displaying': is_display_salary,
                'reason': expected['reason'],
                'job_url': job_posting.get('jobURL', '') or job_posting.get('applicationURL', '')
            }
            
            if expected['should_display_salary'] and not is_display_salary:
                results['no_salary_when_should'].append(issue_details)
                issue_found = True
            elif not expected['should_display_salary'] and is_display_salary:
                results['salary_when_should_not'].append(issue_details)
                issue_found = True
            elif expected['should_display_salary'] and is_display_salary:
                if abs(expected['expected_salary_from'] - actual_salary_from) > 0.01:
                    results['salary_issues'].append(issue_details)
                    issue_found = True
                else:
                    results['correct_salary'].append(issue_details)
            else:
                if actual_salary_from == 0.00:
                    issue_details['reason'] = f"Job with $0 salary - {expected['reason']}"
                    results['no_salary_when_should'].append(issue_details)
                    issue_found = True
                else:
                    results['correct_salary'].append(issue_details)
            
            if issue_found:
                results['salary_issues'].append(issue_details)
            
            state = expected['state'] or 'Unknown'
            results['summary_by_state'].setdefault(state, {'total': 0, 'issues': 0, 'correct': 0})
            results['summary_by_state'][state]['total'] += 1
            if issue_found:
                results['summary_by_state'][state]['issues'] += 1
            else:
                results['summary_by_state'][state]['correct'] += 1
            
            city = expected['city'] or 'Unknown'
            results['summary_by_city'].setdefault(city, {'total': 0, 'issues': 0, 'correct': 0})
            results['summary_by_city'][city]['total'] += 1
            if issue_found:
                results['summary_by_city'][city]['issues'] += 1
            else:
                results['summary_by_city'][city]['correct'] += 1
            
            job_info = {
                'job_id': job_id,
                'reference_number': reference_number,
                'work_type': expected['work_type'],
                'state': expected['state'],
                'city': expected['city'],
                'salary_from': actual_salary_from,
                'salary_to': actual_salary_to,
                'is_display_salary': is_display_salary,
                'job_url': job_posting.get('jobURL', '') or job_posting.get('applicationURL', ''),
                'job_title': job_posting.get('jobName', ''),
                'has_issue': issue_found
            }
            
            if expected['work_type'] == 'Contractor':
                results['all_contractors'].append(job_info)
            
            if expected['work_type'] == 'Direct Hire':
                results['all_direct_hire'].append(job_info)
            
            if issue_found:
                results['jobs_with_issues'].append(job_info)
            
            if issue_found and (auto_repost or dry_run or interactive):
                if dry_run:
                    self.logger.info(f"DRY RUN: Would repost job {job_id} ({reference_number}) due to salary issue")
                elif interactive:
                    self.logger.info(f"INTERACTIVE: Found job {job_id} ({reference_number}) with salary issue")
                else:
                    self.logger.info(f"Attempting to repost job {job_id} ({reference_number}) due to salary issue")
                
                vacancy_data = self.fetch_vacancy_data(reference_number)
                if vacancy_data:
                    state_name = vacancy_data.get('recruit_addressstate.mercury_name', '')
                    city_name = vacancy_data.get('crimson_addresscity', '')
                    mercury_position_type = vacancy_data.get('<EMAIL>', '')
                    is_direct_hire = mercury_position_type == 'Permanent'
                    
                    if is_direct_hire:
                        salary_field = vacancy_data.get('mercury_permanentsalary_mc', 0.00) or 0.00
                        salary_field_name = 'mercury_permanentsalary_mc'
                    else:
                        salary_field = vacancy_data.get('mercury_pay_mc', 0.00) or 0.00
                        salary_field_name = 'mercury_pay_mc'
                    
                    if salary_field == 0.00:
                        error_msg = f"{'Direct Hire' if is_direct_hire else 'Contractor'} job {job_id} ({reference_number}) in {state_name}, {city_name} requires salary but vacancy has {salary_field_name} = $0.00"
                        self.logger.error(error_msg)
                        results['repost_failures'].append({
                            "success": False,
                            "job_id": job_id,
                            "reference_number": reference_number,
                            "error": error_msg
                        })
                        continue
                    
                    old_salary = job_posting.get('salaryFrom', 0.00) or 0.00
                    new_salary = salary_field
                    
                    if interactive and not dry_run:
                        user_response = self.get_user_confirmation(job_id, reference_number, old_salary, new_salary, state_name, city_name)
                        if user_response == 'n':
                            self.logger.info(f"User chose to exit. Exiting program.")
                            sys.exit(0)
                        elif user_response == 'z':
                            self.logger.info("Updating all remaining jobs without further confirmation")
                            interactive = False
                    
                    if dry_run:
                        results['reposted_jobs'].append({
                            "success": True,
                            "job_id": job_id,
                            "reference_number": reference_number,
                            "old_salary": old_salary,
                            "new_salary": new_salary,
                            "message": f"DRY RUN: Would repost with salary ${new_salary:,.2f}",
                            "dry_run": True
                        })
                        self.logger.info(f"DRY RUN: Would repost job {job_id} with salary ${new_salary:,.2f}")
                    else:
                        repost_result = self.repost_job_with_correct_salary(job_posting, vacancy_data, verify_current_job=verify_current_job)
                        if repost_result['success']:
                            results['reposted_jobs'].append(repost_result)
                            self.logger.info(f"Successfully reposted job {job_id} with correct salary")
                        else:
                            results['repost_failures'].append(repost_result)
                            error_msg = repost_result.get('error', repost_result.get('message', 'Unknown error'))
                            self.logger.error(f"Failed to repost job {job_id}: {error_msg}")
                else:
                    if dry_run and not auto_repost:
                        old_salary = job_posting.get('salaryFrom', 0.00) or 0.00
                        results['reposted_jobs'].append({
                            "success": True,
                            "job_id": job_id,
                            "reference_number": reference_number,
                            "old_salary": old_salary,
                            "new_salary": 0.00,
                            "message": f"DRY RUN: Would need to fetch vacancy data to determine correct salary",
                            "dry_run": True
                        })
                        self.logger.info(f"DRY RUN: Would need to fetch vacancy data for job {job_id} to determine correct salary")
                    else:
                        self.logger.warning(f"Could not fetch vacancy data for job {job_id} ({reference_number})")
        
        self.logger.info(f"Final counts - Contractors: {len(results['all_contractors'])}, Direct Hire: {len(results['all_direct_hire'])}, Jobs with Salary Issues: {len(results['jobs_with_issues'])}")
        if auto_repost:
            self.logger.info(f"Repost results - Successful: {len(results['reposted_jobs'])}, Failed: {len(results['repost_failures'])}")
        
        return results
    
    def get_user_confirmation(self, job_id, reference_number, old_salary, new_salary, state, city):
        """Get user confirmation for job update (interactive mode)."""
        self.logger.info(f"\nJob: {job_id} ({reference_number})")
        self.logger.info(f"Location: {city}, {state}")
        self.logger.info(f"Current Salary: ${old_salary:,.2f}")
        self.logger.info(f"New Salary: ${new_salary:,.2f}")
        
        while True:
            response = input("Update this job? (y/n/z for update all remaining): ").lower().strip()
            if response in ['y', 'n', 'z']:
                return response
            else:
                self.logger.info("Please enter 'y' for yes, 'n' for no, or 'z' for update all remaining")
    
    def repost_job_with_correct_salary(self, job_posting, vacancy_data, verify_current_job=True):
        """
        Repost a job to Shazamme with correct salary information from vacancy data.
        Uses the update_vacancy_job function from shazamme_job_poster.
        """
        try:
            job_id = job_posting.get('jobID', 'Unknown')
            reference_number = job_posting.get('referenceNumber', 'Unknown')
            vacancy_id = vacancy_data.get('crimson_vacancyid', 'Unknown')
            
            self.logger.info(f"Reposting job {job_id} ({reference_number}) with correct salary information using shazamme_job_poster")
            
            # Get current salary for comparison
            old_salary = job_posting.get('salaryFrom', 0.00) or 0.00
            
            # Calculate new salary from vacancy data
            mercury_position_type = vacancy_data.get('<EMAIL>', '')
            if mercury_position_type == 'Permanent':
                new_salary = vacancy_data.get('mercury_permanentsalary_mc', 0.00) or 0.00
            else:
                new_salary = vacancy_data.get('mercury_pay_mc', 0.00) or 0.00
            
            # Call the update function from shazamme_job_poster
            result = update_vacancy_job(
                vacancy_id=vacancy_id,
                environment=self.environment,
                logger=self.logger,
                dry_run=False  # We want to actually update, not dry run
            )
            
            if result['success']:
                self.logger.info(f"Successfully updated job {job_id} with salary: ${new_salary:,.2f}")
                return {
                    "success": True,
                    "job_id": job_id,
                    "reference_number": reference_number,
                    "old_salary": old_salary,
                    "new_salary": new_salary,
                    "message": f"Job updated with correct salary ${new_salary:,.2f} via shazamme_job_poster"
                }
            else:
                error_msg = result.get('error', 'Unknown error from shazamme_job_poster')
                self.logger.error(f"Failed to update job {job_id}: {error_msg}")
                return {
                    "success": False,
                    "job_id": job_id,
                    "reference_number": reference_number,
                    "old_salary": old_salary,
                    "new_salary": new_salary,
                    "error": error_msg,
                    "message": f"Update failed via shazamme_job_poster. Current: ${old_salary:,.2f}, correct: ${new_salary:,.2f}"
                }
                
        except Exception as e:
            error_msg = f"Unexpected error processing job {job_posting.get('jobID', 'Unknown')}: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "job_id": job_posting.get('jobID', 'Unknown'),
                "reference_number": job_posting.get('referenceNumber', 'Unknown'),
                "error": error_msg
            }
    
    def print_verification_report(self, results, dry_run=False):
        """Print a simplified verification report focusing on what would be updated."""
        mode = "DRY RUN" if dry_run else "LIVE"
        self.logger.info(f"\n{mode} SALARY VERIFICATION REPORT")
        self.logger.info("="*80)
        
        if 'reposted_jobs' in results and results['reposted_jobs']:
            self.logger.info(f"\nJOBS THAT WOULD BE UPDATED ({len(results['reposted_jobs'])} total):")
            self.logger.info("="*80)
            for repost in results['reposted_jobs']:
                self.logger.info(f"Job {repost['job_id']} ({repost['reference_number']})")
                self.logger.info(f"  Current Salary: ${repost['old_salary']:,.2f}")
                self.logger.info(f"  New Salary: ${repost['new_salary']:,.2f}")
                self.logger.info(f"  Status: {repost['message']}")
                self.logger.info("-" * 40)
        
        if 'repost_failures' in results and results['repost_failures']:
            self.logger.info(f"\nJOBS THAT COULD NOT BE UPDATED ({len(results['repost_failures'])} total):")
            self.logger.info("="*80)
            for failure in results['repost_failures']:
                self.logger.info(f"Job {failure['job_id']} ({failure['reference_number']})")
                self.logger.info(f"  Error: {failure['error']}")
                self.logger.info("-" * 40)
        
        self.logger.info(f"\nSUMMARY:")
        self.logger.info(f"Total Jobs Processed: {results['total_job_postings']}")
        self.logger.info(f"Direct Hire Jobs: {results['direct_hire']}")
        self.logger.info(f"Contractor Jobs: {results['contractors']}")
        self.logger.info(f"Jobs with Issues: {len(results['salary_issues'])}")
        if 'reposted_jobs' in results:
            self.logger.info(f"Jobs to Update: {len(results['reposted_jobs'])}")
        if 'repost_failures' in results:
            self.logger.info(f"Jobs with Errors: {len(results['repost_failures'])}")
        
        self.logger.info("\n" + "="*80)

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Verify Salary Information in Shazamme Job Postings")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be updated without making actual changes (works with or without --auto-repost)"
    )
    parser.add_argument(
        "--auto-repost",
        action="store_true",
        help="Automatically repost jobs with incorrect salary information"
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Ask for confirmation for each job update (y/n/z)"
    )
    parser.add_argument(
        "--verify-current-job",
        action="store_true",
        default=True,
        help="Fetch and verify current job before updating (default: True)"
    )
    parser.add_argument(
        "--no-verify-current-job",
        action="store_true",
        help="Skip fetching current job before updating (faster but less safe)"
    )
    parser.add_argument(
        "--env",
        type=int,
        choices=[0, 1, 2],
        default=2,
        help="Environment to use: 0=SANDBOX, 1=UAT, 2=PROD (default)"
    )
    return parser.parse_args()

def main():
    """Main function to execute the script."""
    logger_config = {
        "level": "INFO",
        "log_to_stdout": False,
        "log_file": "/mnt/incoming/logs/salary_verification.log",
    }
    logger = AppLogger(logger_config)

    args = parse_arguments()
    
    logger.info("Starting salary verification for Shazamme job postings")

    load_secrets_env_variables()

    if args.env == 0:
        environment = Environment.SANDBOX
    elif args.env == 1:
        environment = Environment.UAT
    else:
        environment = Environment.PROD
    logger.info(f"Using environment: {environment.name}")

    verifier = SalaryVerificationTool(logger=logger, environment=environment)
    
    verify_current_job = not args.no_verify_current_job
    
    results = verifier.verify_salary_information_from_shazamme(
        auto_repost=args.auto_repost, 
        dry_run=args.dry_run, 
        interactive=args.interactive,
        verify_current_job=verify_current_job
    )
    
    verifier.print_verification_report(results, dry_run=args.dry_run)
    
    if results['salary_issues']:
        logger.warning(f"Found {len(results['salary_issues'])} salary issues!")
        sys.exit(1)
    else:
        logger.info("No salary issues found!")
        sys.exit(0)

if __name__ == '__main__':
    main() 