# Dataverse Updates Processing System

A robust, modular system for processing Dataverse entity updates received via Azure Service Bus with **at-least-once processing guarantees** and a clean, extensible architecture.

## 🎯 **Purpose & Overview**

This system provides a unified framework for processing Dataverse webhook notifications. When entities (contacts, vacancies, etc.) are updated in Dataverse,  messages are sent to Azure Service Bus. Our system:

1. **Receives** messages from Service Bus topics or queues
2. **Parses** the complex Dataverse message format
3. **Fetches** the latest data from Dataverse using entity-specific processors
4. **Stores** the data in database
5. **Guarantees** at-least-once processing (no data loss)
6. **Supports** both publish-subscribe (topics) and point-to-point (queues) messaging patterns

## 🏗️ **Architecture Overview**

![Architecture](./HighLevelArchitecture.png)

### **Modular Processor Architecture**

```
┌─────────────────────────────────────────────────────────────────┐
│                    Generic Dataverse Listener                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ ContactProcessor│  │VacancyProcessor │  │ CustomProcessor │  │
│  │                 │  │                 │  │                 │  │
│  │ • Fetch Contact │  │ • Fetch Vacancy │  │ • Fetch Custom  │  │
│  │ • Store Contact │  │ • Store Vacancy │  │ • Store Custom  │  │
│  │ • Validate      │  │ • Validate      │  │ • Validate      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Message Processing Layer                     │
├─────────────────────────────────────────────────────────────────┤
│  • Thread Pool Management    • Retry Logic                      │
│  • Message Acknowledgment    • Error Handling                   │
│  • Statistics Collection     • Health Monitoring                │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Service Bus Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  • Topic/Queue Management                                       │
│  • Message Reception & Parsing                                  │
│  • Connection Management                                        │
│  • Channel Type Support (Topics/Queues)                         │
└─────────────────────────────────────────────────────────────────┘
```

### **Key Architectural Benefits**

- **🔧 Pluggable Processors**: Easy to add new entity types
- **🔄 Generic Framework**: Single listener handles all entity types
- **⚡ Concurrent Processing**: Thread pool-based message processing
- **🛡️ Fault Tolerance**: Automatic retry with exponential backoff
- **📊 Monitoring**: Built-in health checks and statistics
- **🔒 Clean Architecture**: Single entry point with configuration-driven setup

## 🔄 **How It Works: Message Processing Flow**

### **Configuration & Secrets Management**

The system supports multiple configuration sources with intelligent fallback mechanisms:

1. **GPG-encrypted secrets files** (test environments) - if successful, skips .env files
2. **Environment-specific .env files** (e.g., `.env.development`, `.env.production`) - only if GPG files not loaded
3. **Default .env file** (local development) - only if GPG files not loaded
4. **System environment variables** (Kubernetes/Container environments) - always available as fallback

**Key Feature**: If GPG secrets are successfully loaded, the application skips loading .env files to prevent overriding the GPG-loaded environment variables.

### **1. Startup Sequence**

### **1. Startup Sequence**

The system follows this initialization sequence:

```
1. Load Configuration → 2. Create Processors → 3. Register Processors → 4. Create Service Bus Receivers → 5. Start Listening
```

**Detailed Flow:**
1. **Configuration Loading**: Read processor configs from JSON or environment variables
2. **Processor Creation**: Use factory pattern to create processor instances
3. **Processor Registration**: Register processors with the message processor
4. **Service Bus Connection**: Create receivers for each configured queue/topic
5. **Message Processing**: Start listening and processing messages

### **2. Configuration → Processor Connection**

The connection between queues and processors happens through a **factory pattern**:

```python
# Configuration defines the mapping:
{
  "type": "contact",                    # → ContactProcessor class
  "channel_name": "contact-updates",    # → Service Bus queue name
  "max_workers": 4                      # → Thread pool size for this processor
}

# Factory function creates the connection:
def create_processor(processor_type: str, config: ProcessorConfig):
    processors = {
        'contact': ContactProcessor,           # ← Maps 'contact' to ContactProcessor
        'crimson_vacancy': VacancyProcessor,  # ← Maps 'crimson_vacancy' to VacancyProcessor
    }
    return processors[processor_type](config, logger)
```

### **3. Message Routing Flow**

When a message arrives from Service Bus:

```
Service Bus Message → Message Parser → Entity Type Extraction → Processor Lookup → Entity Processing
```

**Detailed Steps:**
1. **Service Bus Receiver** receives message from queue (`contact-updates` or `vacancy-updates`)
2. **Message Parser** extracts entity type from the message body
3. **Message Processor** looks up the appropriate processor by entity name
4. **Entity Processor** calls the specific `process_entity_update()` method
5. **Processing Complete** message is acknowledged and removed from queue

### **4. Thread Pool Management**

The system uses **separate thread pools per processor type** for true independent scaling:

- **Independent Pools**: Each processor has its own dedicated thread pool
- **Isolated Resources**: Processors cannot interfere with each other's resources
- **True Independent Scaling**: Each processor can scale independently based on load
- **Load-Based Allocation**: Allocate more threads to processors with higher message volumes

**Example:**
```json
{
  "processors": [
    {
      "type": "contact",
      "max_workers": 8,        // Contact processor gets its own 8-thread pool
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy", 
      "max_workers": 2,        // Vacancy processor gets its own 2-thread pool
      "channel_name": "vacancy-updates"
    }
  ]
}
// Contact processor: 8 dedicated threads
// Vacancy processor: 2 dedicated threads
// Total: 10 threads across 2 independent pools
```

### **5. Priority Processing**

The `priority` field determines processing order:

- **Higher Priority**: Processors with higher priority values are processed first
- **Same Priority**: Processors with same priority are processed in configuration order
- **Default**: All processors have priority 0 by default

**Example:**
```json
{
  "processors": [
    {
      "type": "contact",
      "priority": 1,           // Higher priority - processed first
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy",
      "priority": 0,           // Lower priority - processed second
      "channel_name": "vacancy-updates"
    }
  ]
}
```

## 📁 **File Structure**

```
dataverse_updates/
├── core/
│   ├── app.py                    # Main application orchestrator
│   ├── generic_listener.py       # Generic listener framework
│   └── config.py                 # Configuration management
├── processors/
│   ├── __init__.py               # Processor exports
│   └── entity_processors.py      # All processor implementations
├── messaging/
│   └── dataverse_message_processor.py  # Message processing layer
├── health/
│   └── health_check.py           # Health monitoring
├── tests/
│   ├── test_generic_framework.py # Framework tests
│   └── test_validation_fix.py    # Validation tests
├── configs/                      # Configuration files
├── k8s/                         # Kubernetes manifests
├── utils/                       # Utility functions
├── main.py                      # Application entry point
└── README.md                    # This documentation
```

## 🚀 **Quick Start**

### **1. Basic Usage**

```python
from dataverse_updates.core.app import DataverseListenerApp

# Create and run the application
app = DataverseListenerApp()
app.run()
```

### **2. Application Flow Example**

Here's what happens when you run the application:

```python
# 1. Application starts
app = DataverseListenerApp()

# 2. Configuration is loaded (from JSON or defaults)
config = app.load_config("configs/default_config.json")
# Result: {
#   "processors": [
#     {"type": "contact", "channel_name": "contact-updates", "max_workers": 4},
#     {"type": "crimson_vacancy", "channel_name": "vacancy-updates", "max_workers": 4}
#   ]
# }

# 3. Processors are created and registered
listener = app.setup_listener(config)
# - ContactProcessor created for "contact-updates" queue
# - VacancyProcessor created for "vacancy-updates" queue
# - Both processors registered with message processor

# 4. Application starts listening
app.run()
# - Service Bus receivers created for each queue
# - Thread pool started (total size: 4 + 4 = 8 threads)
# - Health check server started
# - Message processing begins
```

### **2. Custom Configuration**

```python
from dataverse_updates.processors.entity_processors import (
    create_processor, get_default_contact_config, get_default_vacancy_config
)

# Create custom processor configurations
contact_config = get_default_contact_config()
contact_config.max_workers = 8
contact_config.max_retries = 5

# Create processors
contact_processor = create_processor('contact', contact_config, logger)
vacancy_processor = create_processor('crimson_vacancy', get_default_vacancy_config(), logger)
```

### **3. Environment Setup**

```bash
# Required environment variables
export SERVICE_BUS_CONNECTION_STRING="your_service_bus_connection_string"
export ENVIRONMENT="SANDBOX"  # or "PROD"

# Optional
export LOG_LEVEL="INFO"
export HEALTH_CHECK_PORT="8080"
```

## 🔧 **Processor Configuration**

### **JSON Configuration Format**

The system supports configuration-driven processor setup using JSON with support for both Service Bus **topics** and **queues**:

#### **Using Queues (Default)**

```json
{
  "service_bus_connection_string": "your_connection_string",
  "environment": "SANDBOX",
  "health_check_port": 8080,
  "processors": [
    {
      "type": "contact",
      "enabled": true,
      "channel_type": "queue",
      "channel_name": "contact-updates",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    },
    {
      "type": "crimson_vacancy",
      "enabled": true,
      "channel_type": "queue",
      "channel_name": "vacancy-updates",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

#### **Using Topics**

```json
{
  "service_bus_connection_string": "your_connection_string",
  "environment": "SANDBOX",
  "health_check_port": 8080,
  "processors": [
    {
      "type": "contact",
      "enabled": true,
      "channel_type": "topic",
      "channel_name": "contacts",
      "subscription_name": "sandbox-test",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    },
    {
      "type": "crimson_vacancy",
      "enabled": true,
      "channel_type": "topic",
      "channel_name": "vacancies",
      "subscription_name": "sandbox-test",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

#### **Mixed Topics and Queues**

```json
{
  "service_bus_connection_string": "your_connection_string",
  "environment": "SANDBOX",
  "health_check_port": 8080,
  "processors": [
    {
      "type": "contact",
      "enabled": true,
      "channel_type": "topic",
      "channel_name": "contacts",
      "subscription_name": "sandbox-test",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    },
    {
      "type": "crimson_vacancy",
      "enabled": true,
      "channel_type": "queue",
      "channel_name": "vacancies-queue",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

### **Configuration Fields Explained**

| Field | Type | Required | Description | Default |
|-------|------|----------|-------------|---------|
| `type` | string | ✅ | Entity type (`contact`, `crimson_vacancy`) | - |
| `enabled` | boolean | ❌ | Whether processor is active | `true` |
| `channel_type` | string | ✅ | `"topic"` or `"queue"` | `"queue"` |
| `channel_name` | string | ✅ | Topic name or queue name | - |
| `subscription_name` | string | ❌ | Subscription name (topics only) | - |
| `max_workers` | integer | ❌ | **Thread allocation** for this processor (see Thread Pool Management below) | `4` |
| `max_retries` | integer | ❌ | Maximum retry attempts | `3` |
| `retry_delay` | float | ❌ | Base retry delay in seconds | `5.0` |
| `priority` | integer | ❌ | **Processing order** (higher values processed first) | `0` |

### **Thread Pool Management & Scaling**

The `max_workers` parameter controls **true independent scaling** for each processor:

#### **How It Works:**
- **Separate Pools**: Each processor has its own dedicated thread pool
- **Isolated Resources**: Processors cannot interfere with each other's resources
- **Independent Allocation**: Each processor can have different thread allocations
- **Load-Based Scaling**: Allocate more threads to processors with higher message volumes
- **No Resource Contention**: High-volume processors won't starve low-volume ones

#### **Scaling Examples:**

**Scenario 1: High Contact Volume, Low Vacancy Volume**
```json
{
  "processors": [
    {
      "type": "contact",
      "max_workers": 8,        // Contact gets its own 8-thread pool
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy",
      "max_workers": 2,        // Vacancy gets its own 2-thread pool
      "channel_name": "vacancy-updates"
    }
  ]
}
// Contact processor: 8 dedicated threads (isolated)
// Vacancy processor: 2 dedicated threads (isolated)
// Total: 10 threads across 2 independent pools
```

**Scenario 2: Balanced Processing**
```json
{
  "processors": [
    {
      "type": "contact",
      "max_workers": 4,        // Contact gets its own 4-thread pool
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy",
      "max_workers": 4,        // Vacancy gets its own 4-thread pool
      "channel_name": "vacancy-updates"
    }
  ]
}
// Contact processor: 4 dedicated threads (isolated)
// Vacancy processor: 4 dedicated threads (isolated)
// Total: 8 threads across 2 independent pools
```

**Scenario 3: Vacancy-Heavy Workload**
```json
{
  "processors": [
    {
      "type": "contact",
      "max_workers": 2,        // Contact gets its own 2-thread pool
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy",
      "max_workers": 10,       // Vacancy gets its own 10-thread pool
      "channel_name": "vacancy-updates"
    }
  ]
}
// Contact processor: 2 dedicated threads (isolated)
// Vacancy processor: 10 dedicated threads (isolated)
// Total: 12 threads across 2 independent pools
```

#### **Priority Processing Order**

The `priority` field determines **which processor gets processed first**:

- **Higher Priority**: Processors with higher priority values are processed first
- **Same Priority**: Processors with same priority are processed in configuration order
- **Use Cases**: 
  - Set higher priority for critical processors
  - Set lower priority for background processors
  - Use for load balancing between different entity types

**Priority Examples:**
```json
{
  "processors": [
    {
      "type": "contact",
      "priority": 2,           // Highest priority - processed first
      "max_workers": 6,
      "channel_name": "contact-updates"
    },
    {
      "type": "crimson_vacancy",
      "priority": 1,           // Medium priority - processed second
      "max_workers": 4,
      "channel_name": "vacancy-updates"
    },
    {
      "type": "custom_entity",
      "priority": 0,           // Lowest priority - processed last
      "max_workers": 2,
      "channel_name": "custom-updates"
    }
  ]
}
```



**Legacy Support:**
- If `topic_name` is present, the system assumes `channel_type: "topic"`
- If `queue_name` is present, the system assumes `channel_type: "queue"`
- The new `channel_name` and `channel_type` fields take precedence

### **Environment-Specific Configuration**

#### **Sandbox Environment (Topics)**
```json
{
  "environment": "SANDBOX",
  "processors": [
    {
      "type": "contact",
      "channel_type": "topic",
      "channel_name": "contacts-sandbox",
      "subscription_name": "sandbox-test",
      "max_workers": 2,
      "max_retries": 2
    }
  ]
}
```

#### **Production Environment (Queues)**
```json
{
  "environment": "PROD",
  "processors": [
    {
      "type": "contact",
      "channel_type": "queue",
      "channel_name": "contacts-prod",
      "max_workers": 8,
      "max_retries": 5,
      "retry_delay": 10.0
    }
  ]
}
```

#### **Mixed Environment Configuration**
```json
{
  "environment": "UAT",
  "processors": [
    {
      "type": "contact",
      "channel_type": "topic",
      "channel_name": "contacts-uat",
      "subscription_name": "uat-test",
      "max_workers": 4,
      "max_retries": 3
    },
    {
      "type": "crimson_vacancy",
      "channel_type": "queue",
      "channel_name": "vacancies-uat",
      "max_workers": 4,
      "max_retries": 3
    }
  ]
}
```

## ❓ **Frequently Asked Questions**

### **Q: How does the system know which processor to use for each message?**
**A:** The system uses a **two-step routing process**:
1. **Queue-based routing**: Messages from `contact-updates` queue go to ContactProcessor
2. **Entity-based routing**: Message parser extracts entity type from message body for final routing

### **Q: Can I scale contact and vacancy processors independently?**
**A:** Yes! Each processor has its own dedicated thread pool:
- Set higher `max_workers` for processors with higher message volumes
- Each processor gets its own isolated thread pool
- No resource contention between processors
- Example: Contact (8 workers) + Vacancy (2 workers) = 2 separate pools with 10 total threads

### **Q: What happens if a processor fails?**
**A:** The system has built-in fault tolerance:
- Automatic retry with exponential backoff
- Failed messages are stored for replay
- Health monitoring detects and reports issues
- Other processors continue working normally

### **Q: How do I add a new entity type?**
**A:** Three simple steps:
1. Create processor class extending `BaseEntityProcessor`
2. Add to factory function in `entity_processors.py`
3. Add configuration entry with queue name and processor type

### **Q: Can I use both topics and queues in the same application?**
**A:** Yes! The system supports mixed configurations:
- Some processors can use topics (with subscriptions)
- Others can use queues
- Each processor is configured independently

## 🔌 **Service Bus Channel Configuration**

The system supports both Azure Service Bus **topics** and **queues**, with **queues as the default** for better reliability and simpler configuration.

### **Channel Types**

#### **Queues (Default - Point-to-Point)**
- **Single Consumer**: Each message is processed by one consumer
- **Performance**: Lower overhead than topics
- **Ordering**: Messages can be processed in order
- **Load Balancing**: Multiple consumers can share the load
- **Use Case**: Direct message processing with guaranteed delivery
- **Configuration**: No subscription management needed

#### **Topics (Publish-Subscribe)**
- **Multiple Subscribers**: Multiple consumers can receive the same message
- **Filtering**: Subscriptions can filter messages based on rules
- **Scalability**: Easy to add new subscribers without changing publishers
- **Use Case**: Broadcasting events to multiple systems
- **Configuration**: Requires subscription management

### **Configuration Files**

The system includes several configuration examples:

- **`configs/default_config.json`**: Uses queues (default)
- **`configs/queue_config.json`**: Uses queues only
- **`configs/mixed_config.json`**: Mixes topics and queues

### **Configuration Examples**

#### **Queue Configuration (Default)**
```json
{
  "processors": [
    {
      "type": "contact",
      "enabled": true,
      "channel_type": "queue",
      "channel_name": "contact-updates",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

#### **Topic Configuration**
```json
{
  "processors": [
    {
      "type": "contact",
      "enabled": true,
      "channel_type": "topic",
      "channel_name": "contacts",
      "subscription_name": "sandbox-test",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

### **Infrastructure Requirements**

**Note**: Topics and queues are created as part of Infrastructure as Code (IaC) in a separate repository. This application does not create or manage Service Bus resources.

#### **For Queues:**
- Ensure the following queues exist in your Service Bus namespace:
  - `contact-updates`
  - `vacancy-updates`
- The application will automatically connect to these queues

#### **For Topics:**
- Ensure the following topics and subscriptions exist in your Service Bus namespace:
  - Topic: `contacts` with subscription: `sandbox-test`
  - Topic: `vacancies` with subscription: `sandbox-test`
- The application will automatically connect to these topics and subscriptions

### **Message Storage**

The system includes automatic message storage for replay and testing purposes:

#### **Features**
- **Automatic Storage**: Messages are stored automatically when processed
- **Configurable**: Enable/disable via environment variable
- **Organized**: Files organized by entity type and date
- **Replay Capability**: Replay stored messages to any Service Bus queue

#### **Configuration**
```bash
# Enable message storage (default: true)
export MERCURY_EVENTS_STORAGE_ENABLED=true

# Set storage directory (default: /mnt/incoming/mercury_events)
export MERCURY_EVENTS_DIR=/path/to/storage
```

#### **Storage Structure**
```
/mnt/incoming/mercury_events/
├── contact/
│   └── YYYY-MM-DD/
│       └── message_id_entity_id_timestamp.json
└── crimson_vacancy/
    └── YYYY-MM-DD/
        └── message_id_entity_id_timestamp.json
```

#### **Message Replay**
```bash
# Replay messages to a queue
python dataverse_updates/utils/message_replayer.py \
  --connection-string "your-connection-string" \
  --queue-name "test-queue" \
  --entity-name contact \
  --date 2024-01-15 \
  --delay 1.0
```

## 🔌 **Extending the System**

### **Adding a New Entity Processor**

#### **1. Create Your Processor Class**

```python
from dataverse_updates.processors.entity_processors import BaseEntityProcessor, ProcessorConfig

class CustomEntityProcessor(BaseEntityProcessor):
    """
    Processor for custom entity updates.
    """
    
    def process_entity_update(self, entity_info: Dict[str, Any]) -> bool:
        """Process a custom entity update."""
        try:
            # Pre-process
            processed_info = self.pre_process(entity_info)
            
            # Validate
            if not self.validate_entity_data(processed_info):
                return False
            
            # Extract entity ID
            entity_id = processed_info.get('entity_id')
            
            # Fetch from Dataverse
            entity_data = self._fetch_custom_entity_from_dataverse(entity_id)
            if not entity_data:
                return False
            
            # Store in database
            success = self._store_custom_entity_in_database(entity_data)
            
            # Post-process
            self.post_process(processed_info, success)
            
            return success
            
        except Exception as e:
            self.handle_error(entity_info, e)
            return False
    
    def get_entity_fields(self) -> List[str]:
        """Get fields to fetch from Dataverse."""
        return [
            'custom_entityid',
            'custom_name',
            'custom_status',
            'modifiedon',
            'createdon'
        ]
    
    def validate_entity_data(self, entity_data: Dict[str, Any]) -> bool:
        """Validate entity data."""
        required_fields = ['entity_id', 'entity_name']
        for field in required_fields:
            if field not in entity_data:
                return False
        
        if entity_data.get('entity_name') != 'custom_entity':
            return False
        
        return True
    
    def _fetch_custom_entity_from_dataverse(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Fetch entity data from Dataverse."""
        try:
            result = read_fields_from_dataverse(
                token=self.token,
                dataverse_url=self.dataverse_url,
                table_name="custom_entity",
                fields=self.get_entity_fields(),
                whereClause=f"custom_entityid eq {entity_id}",
                logger=self.logger
            )
            
            if result and 'value' in result and len(result['value']) > 0:
                return result['value'][0]
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching custom entity data: {e}")
            return None
    
    def _store_custom_entity_in_database(self, entity_data: Dict[str, Any]) -> bool:
        """Store entity data in database."""
        try:
            # Implement your database storage logic here
            self.logger.info(f"Would store custom entity: {entity_data.get('custom_entityid')}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing custom entity data: {e}")
            return False
```

#### **2. Register Your Processor**

```python
# Add to the factory function in entity_processors.py
def create_processor(processor_type: str, config: ProcessorConfig, logger=None) -> BaseEntityProcessor:
    processors = {
        'contact': ContactProcessor,
        'crimson_vacancy': VacancyProcessor,
        'custom_entity': CustomEntityProcessor,  # Add your processor
    }
    
    processor_class = processors.get(processor_type)
    if not processor_class:
        raise ValueError(f"Unsupported processor type: {processor_type}")
    
    return processor_class(config, logger)
```

#### **3. Add Configuration**

```json
{
  "processors": [
    {
      "type": "custom_entity",
      "enabled": true,
      "channel_type": "queue",
      "channel_name": "custom-entity-updates",
      "max_workers": 4,
      "max_retries": 3,
      "retry_delay": 5.0,
      "priority": 0
    }
  ]
}
```

### **Customizing Existing Processors**

#### **Extending Contact Processor**

```python
class ExtendedContactProcessor(ContactProcessor):
    """Extended contact processor with custom logic."""
    
    def pre_process(self, entity_info: Dict[str, Any]) -> Dict[str, Any]:
        """Add custom preprocessing."""
        processed_info = super().pre_process(entity_info)
        
        # Add custom logic
        processed_info['custom_field'] = 'custom_value'
        
        return processed_info
    
    def post_process(self, entity_info: Dict[str, Any], success: bool) -> None:
        """Add custom post-processing."""
        super().post_process(entity_info, success)
        
        if success:
            # Send notifications, update cache, etc.
            self._send_notification(entity_info)
    
    def _send_notification(self, entity_info: Dict[str, Any]) -> None:
        """Send notification about processed contact."""
        # Implement notification logic
        pass
```

## 📊 **Field Mappings & Dataverse Integration**

### **Contact Entity Fields**

| Dataverse Field | Description | Type | Required |
|-----------------|-------------|------|----------|
| `contactid` | Unique contact identifier | GUID | ✅ |
| `fullname` | Full name of contact | String | ❌ |
| `firstname` | First name | String | ❌ |
| `lastname` | Last name | String | ❌ |
| `emailaddress1` | Primary email address | String | ❌ |
| `telephone1` | Primary phone number | String | ❌ |
| `mobilephone` | Mobile phone number | String | ❌ |
| `address1_city` | City | String | ❌ |
| `address1_stateorprovince` | State/Province | String | ❌ |
| `address1_country` | Country | String | ❌ |
| `jobtitle` | Job title | String | ❌ |
| `companyname` | Company name | String | ❌ |
| `statecode` | Status code | Integer | ❌ |
| `recruit_availability` | Availability status | String | ❌ |
| `recruit_iscandidatecontact` | Is candidate contact | Boolean | ❌ |
| `recruit_expsalaryp` | Expected salary (permanent) | Decimal | ❌ |
| `recruit_expratec` | Expected rate (contractor) | Decimal | ❌ |
| `recruit_workpref_permanent` | Work preference - permanent | Boolean | ❌ |
| `recruit_workpref_contractor` | Work preference - contractor | Boolean | ❌ |
| `recruit_workpref_temp` | Work preference - temporary | Boolean | ❌ |
| `modifiedon` | Last modified date | DateTime | ❌ |
| `createdon` | Creation date | DateTime | ❌ |

### **Vacancy Entity Fields**

| Dataverse Field | Description | Type | Required |
|-----------------|-------------|------|----------|
| `crimson_vacancyid` | Unique vacancy identifier | GUID | ✅ |
| `crimson_vacancyrefno` | Vacancy reference number | String | ❌ |
| `crimson_jobtitle` | Job title | String | ❌ |
| `statecode` | Status code | Integer | ❌ |
| `crimson_salarymin` | Minimum salary | Decimal | ❌ |
| `crimson_salarymax` | Maximum salary | Decimal | ❌ |
| `crimson_rate` | Hourly rate | Decimal | ❌ |
| `crimson_location` | Job location | String | ❌ |
| `crimson_description` | Job description | String | ❌ |
| `crimson_jobsummaryemail` | Job summary email | String | ❌ |
| `crimson_jobsummary` | Job summary | String | ❌ |
| `crimson_mercurydescription` | Mercury description | String | ❌ |
| `crimson_category` | Job category | String | ❌ |
| `crimson_subcategory` | Job subcategory | String | ❌ |
| `crimson_adverttext2` | Advertisement text 2 | String | ❌ |
| `crimson_adverttext3` | Advertisement text 3 | String | ❌ |
| `modifiedon` | Last modified date | DateTime | ❌ |
| `createdon` | Creation date | DateTime | ❌ |

### **Custom Field Validation**

```python
def validate_entity_data(self, entity_data: Dict[str, Any]) -> bool:
    """Enhanced validation with field-specific checks."""
    
    # Basic validation
    if not super().validate_entity_data(entity_data):
        return False
    
    # Field-specific validation
    attributes = entity_data.get('attributes', {})
    
    # Validate email format
    email = attributes.get('emailaddress1')
    if email and not self._is_valid_email(email):
        self.logger.warning(f"Invalid email format: {email}")
        return False
    
    # Validate phone format
    phone = attributes.get('telephone1')
    if phone and not self._is_valid_phone(phone):
        self.logger.warning(f"Invalid phone format: {phone}")
        return False
    
    return True

def _is_valid_email(self, email: str) -> bool:
    """Validate email format."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def _is_valid_phone(self, phone: str) -> bool:
    """Validate phone format."""
    import re
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    return len(digits_only) >= 10
```

### **Configuration File Mode**

#### **Using Default Configuration (Topics)**
```bash
# Run with default topic configuration
python dataverse_updates/core/app.py
```

#### **Using Queue Configuration**
```bash
# Run with queue configuration
python dataverse_updates/core/app.py --config dataverse_updates/configs/queue_config.json
```

#### **Using Mixed Configuration**
```bash
# Run with mixed topics and queues
python dataverse_updates/core/app.py --config dataverse_updates/configs/mixed_config.json
```

#### **Custom Configuration**
```bash
# Create custom config.json
{
  "service_bus_connection_string": "your_connection_string",
  "environment": "SANDBOX",
  "processors": [
    {
      "type": "contact",
      "channel_type": "queue",
      "channel_name": "contacts-queue"
    }
  ]
}

# Run with custom config file
python dataverse_updates/core/app.py --config config.json
```

## 📈 **Monitoring & Health Checks**

### **Health Check Endpoints**

The service provides comprehensive health check endpoints for monitoring and Kubernetes integration with FastAPI-powered interactive documentation:

#### **Standard Endpoints**
- **`/health`**: Overall system health with detailed status information
- **`/ready`**: Readiness probe for Kubernetes
- **`/live`**: Liveness probe for Kubernetes  
- **`/startup`**: Startup probe for Kubernetes
- **`/metrics`**: Prometheus-style metrics for monitoring systems

#### **FastAPI Features**

The health server provides additional benefits:

- **Interactive API Documentation**: Swagger UI at `/docs`
- **Alternative Documentation**: ReDoc at `/redoc`
- **OpenAPI Schema**: Available at `/openapi.json`
- **CORS Support**: Cross-origin requests enabled
- **Better Error Handling**: Standardized HTTP error responses

#### **Testing the Health Server**

```bash
# Test the health server with enhanced entity metrics
python dataverse_sb_listener/test_health.py

# Quick verification of entity-specific metrics
python dataverse_sb_listener/verify_entity_metrics.py

# Or run the main application
python dataverse_sb_listener/main.py --config config.json
```

## 📊 **Metrics Collection for Developers**

### **Implementing Metrics in Entity Processors**

When creating new entity processors, you should integrate metrics collection to provide visibility into processing performance. Here's how to implement it:

#### **1. Import the Health Functions**

```python
from dataverse_sb_listener.health import update_message_processed, update_message_failed
```

#### **2. Update Metrics in Your Processor**

```python
class MyEntityProcessor(BaseEntityProcessor):
    def process_entity_update(self, message_data: Dict[str, Any]) -> bool:
        try:
            # Your processing logic here
            result = self._process_message(message_data)
            
            if result:
                # Update success metrics
                update_message_processed(self.entity_name)
                return True
            else:
                # Update failure metrics
                update_message_failed(self.entity_name)
                return False
                
        except Exception as e:
            # Update failure metrics on exception
            update_message_failed(self.entity_name)
            self.logger.error(f"Error processing {self.entity_name}: {e}")
            return False
```

#### **3. Metrics Collection Best Practices**

**Always include entity name:**
```python
# ✅ Good - includes entity name for detailed tracking
update_message_processed("contact")
update_message_failed("crimson_vacancy")

# ❌ Avoid - no entity tracking
update_message_processed()  # Falls back to generic tracking
```

**Handle both success and failure cases:**
```python
try:
    # Processing logic
    if success:
        update_message_processed(entity_name)
    else:
        update_message_failed(entity_name)
except Exception as e:
    update_message_failed(entity_name)
    raise
```

**Use consistent entity names:**
```python
# ✅ Consistent naming
self.entity_name = "contact"  # Use the same name throughout

# ❌ Inconsistent naming
update_message_processed("contact")
update_message_processed("contacts")  # Different names create separate metrics
```

#### **4. Available Metrics Functions**

```python
from dataverse_sb_listener.health import (
    update_message_processed,    # Track successful message processing
    update_message_failed,       # Track failed message processing
    set_ready,                   # Set readiness status
    set_healthy,                 # Set health status
    set_service_bus_connected,   # Set Service Bus connection status
    set_dataverse_connected,     # Set Dataverse connection status
    set_last_error              # Set last error message
)
```

#### **5. Monitoring and Alerting**

With the enhanced metrics, you can create sophisticated monitoring:

**Prometheus Queries:**
```prometheus
# Success rate by entity
rate(listener_entity_messages_processed_total[5m]) / 
(rate(listener_entity_messages_processed_total[5m]) + rate(listener_entity_messages_failed_total[5m]))

# Processing rate by entity
rate(listener_entity_messages_processed_total[5m])

# Error rate by entity
rate(listener_entity_messages_failed_total[5m])
```

**Grafana Dashboards:**
- Entity-specific processing rates
- Success rate trends by entity
- Error rate alerts by entity
- Overall system health

#### **6. Example: Complete Entity Processor**

```python
from dataverse_sb_listener.processors.entity_processors import BaseEntityProcessor
from dataverse_sb_listener.health import update_message_processed, update_message_failed

class ContactProcessor(BaseEntityProcessor):
    def __init__(self, config, logger):
        super().__init__(config, logger)
        self.entity_name = "contact"
    
    def process_entity_update(self, message_data: Dict[str, Any]) -> bool:
        try:
            # Extract contact data
            contact_id = message_data.get("contactid")
            if not contact_id:
                self.logger.warning("No contact ID in message")
                update_message_failed(self.entity_name)
                return False
            
            # Process the contact update
            success = self._update_contact(contact_id, message_data)
            
            if success:
                self.logger.info(f"Successfully processed contact {contact_id}")
                update_message_processed(self.entity_name)
                return True
            else:
                self.logger.error(f"Failed to process contact {contact_id}")
                update_message_failed(self.entity_name)
                return False
                
        except Exception as e:
            self.logger.exception(f"Exception processing contact: {e}")
            update_message_failed(self.entity_name)
            return False
    
    def _update_contact(self, contact_id: str, data: Dict[str, Any]) -> bool:
        # Your contact processing logic here
        pass
```

### **Health Check Response**

```json
{
  "status": "healthy",
  "ready": true,
  "uptime_seconds": 8130.5,
  "start_time": "2024-01-15T10:30:00",
  "last_message_time": "2024-01-15T12:45:30",
  "total_messages_processed": 225,
  "total_messages_failed": 3,
  "success_rate": 98.7,
  "entity_metrics": {
    "contact": {
      "processed": 150,
      "failed": 2,
      "success_rate": 98.7,
      "last_processed": "2024-01-15T12:45:30"
    },
    "crimson_vacancy": {
      "processed": 75,
      "failed": 1,
      "success_rate": 98.7,
      "last_processed": "2024-01-15T12:44:15"
    }
  },
  "connections": {
    "service_bus": true,
    "dataverse": true
  },
  "last_error": null,
  "last_error_time": null
}
```

### **Enhanced Metrics Collection**

The health server provides comprehensive metrics collection with both aggregate and per-entity statistics:

#### **Prometheus Metrics Format**

```prometheus
# Overall metrics
listener_uptime_seconds 8130.5
listener_messages_processed_total 225
listener_messages_failed_total 3
listener_success_rate_percent 98.7

# Per-entity metrics (with labels)
listener_entity_messages_processed_total{entity="contact"} 150
listener_entity_messages_failed_total{entity="contact"} 2
listener_entity_success_rate_percent{entity="contact"} 98.7

listener_entity_messages_processed_total{entity="crimson_vacancy"} 75
listener_entity_messages_failed_total{entity="crimson_vacancy"} 1
listener_entity_success_rate_percent{entity="crimson_vacancy"} 98.7
```

#### **Available Metrics**

**Overall Metrics:**
- `listener_uptime_seconds` - Total uptime in seconds
- `listener_messages_processed_total` - Total messages processed
- `listener_messages_failed_total` - Total messages failed
- `listener_success_rate_percent` - Overall success rate percentage
- `listener_service_bus_connected` - Service Bus connection status
- `listener_dataverse_connected` - Dataverse connection status
- `listener_ready` - Readiness status
- `listener_healthy` - Health status

**Per-Entity Metrics:**
- `listener_entity_messages_processed_total{entity="entity_name"}` - Messages processed per entity
- `listener_entity_messages_failed_total{entity="entity_name"}` - Messages failed per entity
- `listener_entity_success_rate_percent{entity="entity_name"}` - Success rate per entity

## 🛠️ **Advanced Configuration**

### **Thread Pool Optimization**

```python
# High-throughput configuration
config = {
    "processors": [
        {
            "type": "contact",
            "max_workers": 16,
            "max_retries": 5,
            "retry_delay": 2.0
        }
    ]
}

# Low-latency configuration
config = {
    "processors": [
        {
            "type": "contact",
            "max_workers": 2,
            "max_retries": 2,
            "retry_delay": 1.0
        }
    ]
}
```

### **Priority-Based Processing**

```json
{
  "processors": [
    {
      "type": "contact",
      "priority": 10,
      "max_workers": 8
    },
    {
      "type": "crimson_vacancy",
      "priority": 5,
      "max_workers": 4
    },
    {
      "type": "custom_entity",
      "priority": 1,
      "max_workers": 2
    }
  ]
}
```

### **Environment-Specific Settings**

```python
# Development
if environment == "SANDBOX":
    config = {
        "max_workers": 2,
        "max_retries": 2,
        "retry_delay": 3.0
    }
# Production
elif environment == "PROD":
    config = {
        "max_workers": 8,
        "max_retries": 5,
        "retry_delay": 10.0
    }
```

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Import Errors**
   ```bash
   # Ensure all dependencies are installed
   pip install -r requirements.txt
   ```

2. **Service Bus Connection Issues**
   ```bash
   # Check connection string format
   # Should be: "Endpoint=sb://namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=key"
   ```

3. **Dataverse Authentication Issues**
   ```bash
   # Check environment variables
   export ENVIRONMENT="SANDBOX"  # or "PROD"
   ```

4. **Processor Not Registered**
   ```python
   # Ensure processor is added to factory function
   processors = {
       'contact': ContactProcessor,
       'crimson_vacancy': VacancyProcessor,
       'your_processor': YourProcessor,  # Add this
   }
   ```

5. **Channel Configuration Issues**
   ```bash
   # Missing subscription name for topics
   # Ensure subscription_name is provided when channel_type is "topic"
   
   # Invalid channel type
   # channel_type must be "topic" or "queue"
   
   # Service Bus permissions
   # Ensure proper access to topics/queues in Service Bus
   ```

6. **Message Storage Issues**
   ```bash
   # Check storage directory permissions
   # Ensure /mnt/incoming/mercury_events is writable
   
   # Disable message storage if not needed
   export MERCURY_EVENTS_STORAGE_ENABLED=false
   ```

### **Debug Mode**

```python
# Enable debug logging
logger_config = {
    "level": "DEBUG",
    "log_to_stdout": True
}

# Run with debug
python main.py --debug
```

### **Performance Monitoring**

```python
# Monitor processing statistics
stats = listener.get_status()
print(f"Processed: {stats['total_messages_processed']}")
print(f"Failed: {stats['total_messages_failed']}")
print(f"Active: {stats['active_tasks']}")
```

## 📚 **API Reference**

### **Core Classes**

#### **`BaseEntityProcessor`**
Abstract base class for all entity processors.

**Methods:**
- `process_entity_update(entity_info)`: Process entity update
- `get_entity_fields()`: Get Dataverse fields to fetch
- `validate_entity_data(entity_data)`: Validate entity data
- `pre_process(entity_info)`: Pre-processing hook
- `post_process(entity_info, success)`: Post-processing hook
- `handle_error(entity_info, error)`: Error handling hook

#### **`ProcessorConfig`**
Configuration dataclass for processors.

**Fields:**
- `entity_name`: Entity type name
- `channel_name`: Service Bus topic or queue name
- `channel_type`: Channel type (`ChannelType.TOPIC` or `ChannelType.QUEUE`)
- `subscription_name`: Service Bus subscription name (topics only)
- `max_workers`: Number of concurrent workers
- `max_retries`: Maximum retry attempts
- `retry_delay`: Base retry delay
- `enabled`: Whether processor is enabled
- `priority`: Processing priority
- `environment`: Dataverse environment

**Backward Compatibility Properties:**
- `topic_name`: Returns channel name for topics (raises error for queues)
- `queue_name`: Returns channel name for queues (raises error for topics)

#### **`GenericDataverseListener`**
Main listener class that orchestrates processing.

**Methods:**
- `register_processor(processor)`: Register a processor
- `initialize()`: Initialize the listener
- `start()`: Start listening for messages
- `stop()`: Stop the listener
- `get_status()`: Get system status

### **Factory Functions**

#### **`create_processor(processor_type, config, logger)`**
Create a processor instance by type.

#### **`get_default_contact_config()`**
Get default configuration for contact processor.

#### **`get_default_vacancy_config()`**
Get default configuration for vacancy processor.

## 🚀 **Deployment**

### **Docker Deployment**

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["python", "main.py"]
```

### **Kubernetes Deployment**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dataverse-updates
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dataverse-updates
  template:
    metadata:
      labels:
        app: dataverse-updates
    spec:
      containers:
      - name: dataverse-updates
        image: dataverse-updates:latest
        env:
        - name: SERVICE_BUS_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: service-bus-secret
              key: connection-string
        - name: ENVIRONMENT
          value: "PROD"
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
```

### **Environment Variables**

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `SERVICE_BUS_CONNECTION_STRING` | ✅ | Azure Service Bus connection string | `Endpoint=sb://...` |
| `ENVIRONMENT` | ❌ | Dataverse environment | `SANDBOX` or `PROD` |
| `LOG_LEVEL` | ❌ | Logging level | `INFO`, `DEBUG`, `WARNING` |
| `HEALTH_CHECK_PORT` | ❌ | Health check server port | `8080` |


## 🛠️ **Utilities & Tools**

### **Dead Letter Queue Purger**

A command-line utility to purge dead letter messages from Azure Service Bus queues when experiencing space limitations or DLQ issues.

#### **Quick Start**

```bash
# Check DLQ message count
python utils/dlq_purger.py --queue-name my-queue --connection-string-env AZURE_SB_CONN_STR --check-only

# Purge all dead letter messages
python utils/dlq_purger.py --queue-name my-queue --connection-string-env AZURE_SB_CONN_STR

# Dry run to see what would be purged
python utils/dlq_purger.py --queue-name my-queue --connection-string-env AZURE_SB_CONN_STR --dry-run
```

#### **Features**

- ✅ **Safe Batch Processing**: Processes messages in configurable batches
- ✅ **Environment Variable Support**: Uses common env var names automatically
- ✅ **Reliable Purging**: Uses proven approach that continues until no messages remain
- ✅ **Dry Run Mode**: Preview what would be purged
- ✅ **Check Only Mode**: Count messages without purging
- ✅ **Force Mode**: Bypass count-based approach for compatibility
- ✅ **Progress Logging**: Real-time progress updates with AppLogger
- ✅ **Error Handling**: Continues processing even if individual messages fail
- ✅ **Consistent Logging**: Uses the same AppLogger system as the rest of the project
- ✅ **Custom Logger Names**: Use `--logger-name` to create loggers with custom names

#### **Common Use Cases**

1. **Emergency Space Cleanup**: When DLQ is full and blocking new messages
2. **Regular Maintenance**: Scheduled cleanup of old dead letter messages
3. **Troubleshooting**: Check DLQ status before investigating issues

For detailed usage instructions, see [DLQ Purger Documentation](./docs/dlq_purger_usage.md).

## 📚 **References**

- [Dataverse - Azure Integration](https://learn.microsoft.com/en-us/power-apps/developer/data-platform/azure-integration)
  - [Dataverse - Azure SAS Integration](https://learn.microsoft.com/en-us/power-apps/developer/data-platform/walkthrough-configure-azure-sas-integration)
  - [Dataverse - Plugin Registration tool](https://learn.microsoft.com/en-us/power-apps/developer/data-platform/walkthrough-register-azure-aware-plug-in-using-plug-in-registration-tool)
- [Azure Service Bus](https://learn.microsoft.com/en-us/power-apps/developer/data-platform/azure-integration)
  - [Queues, Topics and Subscriptions](https://learn.microsoft.com/en-us/azure/service-bus-messaging/service-bus-queues-topics-subscriptions)
  - [Azure Service Bus Python Samples](https://learn.microsoft.com/en-us/samples/azure/azure-sdk-for-python/servicebus-samples/)  

## 📞 **Support**

For issues and questions:

1. **Check Logs**: Start with application logs
2. **Review Configuration**: Verify all settings are correct
3. **Test Connectivity**: Ensure Service Bus and Dataverse connections work
4. **Check Health Endpoints**: Use `/health` and `/status` endpoints
5. **Review Documentation**: Check this README for solutions
