#!/usr/bin/env python3
"""
Main entry point for the Dataverse Updates application.

This is the primary entry point for running the generic Dataverse listener
with configuration-driven processor setup and FastAPI health check endpoints.
"""

import sys
import os

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

from dataverse_sb_listener.core.app import Dataverse<PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Dataverse Listener Application")
    parser.add_argument(
        "--config", 
        help="Path to configuration file (JSON format)"
    )
    parser.add_argument(
        "--env",
        choices=["SANDBOX", "UAT", "PROD"],
        help="Environment to run in (SANDBOX, UAT, PROD). Overrides ENVIRONMENT environment variable."
    )
    parser.add_argument(
        "--mercury-storage-enabled",
        choices=["true", "false"],
        help="Enable/disable Mercury events storage. Overrides MERCURY_EVENTS_STORAGE_ENABLED environment variable."
    )
    parser.add_argument(
        "--mercury-events-dir",
        help="Directory path for storing Mercury events. Overrides MERCURY_EVENTS_DIR environment variable."
    )
    
    args = parser.parse_args()
    
    # Set environment variables from command line if provided
    if args.env:
        os.environ['ENVIRONMENT'] = args.env
        print(f"Environment set from command line: {args.env}")
    
    if args.mercury_storage_enabled:
        os.environ['MERCURY_EVENTS_STORAGE_ENABLED'] = args.mercury_storage_enabled
        print(f"Mercury storage enabled set from command line: {args.mercury_storage_enabled}")
    
    if args.mercury_events_dir:
        os.environ['MERCURY_EVENTS_DIR'] = args.mercury_events_dir
        print(f"Mercury events directory set from command line: {args.mercury_events_dir}")
    
    # Create and run the application
    app = DataverseListenerApp()
    
    # Add debug logging to verify configuration
    print(f"Environment: {app.settings.ENVIRONMENT}")
    print(f"Log Level: {app.settings.LOG_LEVEL}")
    print(f"Log to Stdout: {app.settings.LOG_TO_STDOUT}")
    print(f"Log File: {app.settings.LOG_FILE}")
    
    return app.run(args.config)

if __name__ == "__main__":
    exit(main()) 