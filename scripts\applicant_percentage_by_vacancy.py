#!/usr/bin/env python3
"""
Applicant Percentage By Vacancy

For every entry (row) in the vacancy_runs table:
- Take its vacancy_id
- Get all candidate_ids from candidate_application_shortlists for that vacancy_id
- For each candidate_id, read candidates.resume_data JSON and extract the 'modified' date
- If 'modified' is within the last N days (default 30), count as an applicant
- Output the percentage of applicants per vacancy_run row (showing run id and vacancy_id)
- If vacancy_runs.applicants_percentage is NULL, compute and save it
- Then, for each vacancy_id consider its latest run (max regenerate_number), join to vacancies to fetch subcategory and category names, and print aggregated stats per subcategory and per category
- Do not include N/A buckets; instead, print the list of vacancy_ids where subcategory is NULL

Usage:
    python applicant_percentage_by_vacancy.py [--days 30]
"""

import sys
import os
import argparse
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from common.secrets_env import load_secrets_env_variables
from common.db.global_dbconnector import GlobalDBConnector
from common.db.config_postgres import PostgresEnvironment


def create_default_logger():
    logger_config = {
        "level": "INFO",
        "log_to_stdout": False,
        "use_json": False,
        "log_file": "/mnt/incoming/logs/applicant_percentage_by_vacancy.log",
    }
    return AppLogger(logger_config)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Compute applicant percentage per vacancy_run row based on resume modified date",
    )
    parser.add_argument(
        "--days",
        type=int,
        default=30,
        help="Window in days. Candidates with resume modified within this window are counted as applicants (default: 30)",
    )
    return parser.parse_args()


def fetch_all_vacancy_runs(db_connector, logger) -> List[Tuple[int, str, Optional[float], Optional[int]]]:
    """Return a list of (run_id, vacancy_id, applicants_percentage, regenerate_number) for every row in vacancy_runs where vacancy_id is not null."""
    conn = db_connector.connect()
    if not conn:
        logger.error("Failed to connect to PostgreSQL database when fetching vacancy_runs")
        return []
    try:
        schema = db_connector.schema
        cur = conn.cursor()
        query = f"""
            SELECT id, vacancy_id, applicants_percentage, COALESCE(regenerate_number, 0) AS regenerate_number
            FROM {schema}.vacancy_runs
            WHERE vacancy_id IS NOT NULL
            ORDER BY id
        """
        cur.execute(query)
        rows = cur.fetchall()
        cur.close()
        conn.close()
        parsed_rows: List[Tuple[int, str, Optional[float], Optional[int]]] = []
        for r in rows:
            run_id = int(r[0])
            vacancy_id = str(r[1])
            ap = r[2]
            ap_float: Optional[float] = float(ap) if ap is not None else None
            regen_num = int(r[3]) if r[3] is not None else 0
            parsed_rows.append((run_id, vacancy_id, ap_float, regen_num))
        return parsed_rows
    except Exception as e:
        logger.error(f"Error fetching rows from vacancy_runs: {e}")
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        return []


def fetch_candidate_ids_for_vacancy(db_connector, vacancy_id: str, logger) -> List[str]:
    conn = db_connector.connect()
    if not conn:
        logger.error("Failed to connect to PostgreSQL database when fetching candidate_ids")
        return []
    try:
        schema = db_connector.schema
        cur = conn.cursor()
        query = f"""
            SELECT contact_id
            FROM {schema}.candidate_application_shortlists
            WHERE vacancy_id = %s::uuid
        """
        cur.execute(query, (vacancy_id,))
        rows = cur.fetchall()
        cur.close()
        conn.close()
        return [str(r[0]) for r in rows if r and r[0] is not None]
    except Exception as e:
        logger.error(f"Error fetching candidate_ids for vacancy {vacancy_id}: {e}")
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        return []


def fetch_resume_data_for_contacts(db_connector, contact_ids: List[str], logger) -> Dict[str, dict]:
    if not contact_ids:
        return {}
    conn = db_connector.connect()
    if not conn:
        logger.error("Failed to connect to PostgreSQL database when fetching resume_data")
        return {}
    try:
        schema = db_connector.schema
        cur = conn.cursor()
        result: Dict[str, dict] = {}
        batch_size = 1000
        for i in range(0, len(contact_ids), batch_size):
            batch = contact_ids[i:i + batch_size]
            placeholders = ",".join(["%s::uuid"] * len(batch))
            query = f"""
                SELECT contact_id, resume_data
                FROM {schema}.candidates
                WHERE contact_id IN ({placeholders})
            """
            cur.execute(query, batch)
            for contact_id, resume_data in cur.fetchall():
                result[str(contact_id)] = resume_data
        cur.close()
        conn.close()
        return result
    except Exception as e:
        logger.error(f"Error fetching resume_data for {len(contact_ids)} contacts: {e}")
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        return {}


def parse_modified_datetime(resume_data) -> datetime:
    if not resume_data:
        return None
    try:
        # resume_data is expected to be a dict(JSON); if string, skip gracefully
        modified_str = None
        if isinstance(resume_data, dict):
            modified_str = resume_data.get('modified')
        elif isinstance(resume_data, str):
            # optionally parse JSON string
            try:
                import json
                data = json.loads(resume_data)
                modified_str = data.get('modified') if isinstance(data, dict) else None
            except Exception:
                modified_str = None
        if not modified_str:
            return None
        # parse common formats
        if 'T' in modified_str:
            # ISO format, handle Z
            dt = datetime.fromisoformat(modified_str.replace('Z', '+00:00'))
        else:
            # date-only
            dt = datetime.strptime(modified_str, "%Y-%m-%d")
        # Ensure timezone-aware (UTC)
        if dt.tzinfo is None or dt.tzinfo.utcoffset(dt) is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt
    except Exception:
        return None


def compute_applicant_percentage(db_connector, vacancy_id: str, days_window: int, logger) -> Tuple[int, int, float]:
    candidate_ids = fetch_candidate_ids_for_vacancy(db_connector, vacancy_id, logger)
    total_candidates = len(candidate_ids)
    if total_candidates == 0:
        return 0, 0, 0.0
    resume_map = fetch_resume_data_for_contacts(db_connector, candidate_ids, logger)
    threshold_dt = datetime.now(timezone.utc) - timedelta(days=days_window)
    applicants = 0
    for contact_id in candidate_ids:
        modified_dt = parse_modified_datetime(resume_map.get(contact_id))
        if modified_dt is not None and modified_dt >= threshold_dt:
            applicants += 1
    percentage = (applicants / total_candidates) * 100.0 if total_candidates else 0.0
    return applicants, total_candidates, percentage


def update_applicants_percentage(db_connector, run_id: int, percentage: float, logger) -> bool:
    """Update vacancy_runs.applicants_percentage for the given run_id."""
    conn = db_connector.connect()
    if not conn:
        logger.error("Failed to connect to PostgreSQL database when updating applicants_percentage")
        return False
    try:
        schema = db_connector.schema
        cur = conn.cursor()
        query = f"""
            UPDATE {schema}.vacancy_runs
            SET applicants_percentage = %s
            WHERE id = %s
        """
        cur.execute(query, (percentage, run_id))
        conn.commit()
        cur.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error updating applicants_percentage for run_id {run_id}: {e}")
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        return False


def fetch_subcategory_category_for_vacancies(db_connector, vacancy_ids: List[str], logger) -> Dict[str, Tuple[Optional[str], Optional[str], bool]]:
    """Return mapping vacancy_id -> (subcategory_name, category_name, subcategory_is_null)."""
    if not vacancy_ids:
        return {}
    conn = db_connector.connect()
    if not conn:
        logger.error("Failed to connect to PostgreSQL database when fetching subcategory/category")
        return {}
    try:
        schema = db_connector.schema
        cur = conn.cursor()
        # Use VALUES table for input IDs
        values_clause = ", ".join(["(%s::uuid)"] * len(vacancy_ids))
        query = f"""
            WITH input_vacancies(vacancy_id) AS (
                VALUES {values_clause}
            )
            SELECT v.vacancy_id,
                   s.name AS subcategory_name,
                   c.name AS category_name,
                   (v.subcategory_id IS NULL) AS subcategory_is_null
            FROM {schema}.vacancies v
            LEFT JOIN {schema}.subcategories s ON v.subcategory_id = s.id
            LEFT JOIN {schema}.categories c ON s.category_id = c.id
            INNER JOIN input_vacancies iv ON v.vacancy_id = iv.vacancy_id
        """
        cur.execute(query, [str(v) for v in vacancy_ids])
        mapping: Dict[str, Tuple[Optional[str], Optional[str], bool]] = {}
        for row in cur.fetchall():
            mapping[str(row[0])] = (row[1], row[2], bool(row[3]))
        cur.close()
        conn.close()
        return mapping
    except Exception as e:
        logger.error(f"Error fetching subcategory/category mapping: {e}")
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        return {}


def main():
    args = parse_args()
    logger = create_default_logger()

    # Load env (DB creds, etc.)
    load_secrets_env_variables()

    # Connect to PROD Postgres
    db_connector = GlobalDBConnector.get_connector_v2(PostgresEnvironment.PROD, logger)

    # Get all vacancy_runs (run_id, vacancy_id, applicants_percentage, regenerate_number)
    vacancy_runs = fetch_all_vacancy_runs(db_connector, logger)
    logger.info(f"Found {len(vacancy_runs)} rows in vacancy_runs with a vacancy_id")

    if not vacancy_runs:
        logger.info("No vacancy_runs to process. Exiting.")
        return

    # Compute and print per run
    logger.info("\n" + "="*160)
    logger.info(f"APPLICANT PERCENTAGE BY VACANCY RUN (window: {args.days} day(s))")
    logger.info("="*160)
    logger.info(f"{'Run ID':>8} {'Vacancy ID':<40} {'Applicants':>12} {'Total':>8} {'% Applicants':>14} {'Updated?':>10}")
    logger.info("-"*160)

    # Cache results per vacancy_id to avoid recomputing repeatedly
    cache: Dict[str, Tuple[int, int, float]] = {}

    updated_count = 0
    skipped_count = 0

    for run_id, vacancy_id, existing_pct, regen_num in vacancy_runs:
        if vacancy_id not in cache:
            cache[vacancy_id] = compute_applicant_percentage(db_connector, vacancy_id, args.days, logger)
        applicants, total, pct = cache[vacancy_id]

        updated_flag = "skipped"
        if existing_pct is None:
            # Only update when empty/NULL
            if update_applicants_percentage(db_connector, run_id, pct, logger):
                updated_flag = "updated"
                updated_count += 1
            else:
                updated_flag = "failed"
        else:
            skipped_count += 1

        logger.info(f"{run_id:>8} {vacancy_id:<40} {applicants:>12} {total:>8} {pct:>14.2f} {updated_flag:>10}")

    logger.info("-"*160)
    logger.info(f"Updated {updated_count} run(s); skipped {skipped_count} run(s) with existing applicants_percentage")

    # Build latest run per vacancy (max regenerate_number)
    latest_regen_per_vacancy: Dict[str, int] = {}
    for _, vacancy_id, _, regen_num in vacancy_runs:
        current = latest_regen_per_vacancy.get(vacancy_id)
        if current is None or (regen_num or 0) > current:
            latest_regen_per_vacancy[vacancy_id] = regen_num or 0

    # Vacancies to include (unique, latest run considered)
    unique_vacancy_ids = list(latest_regen_per_vacancy.keys())

    # Fetch subcategory/category mapping (names)
    subcat_cat_map = fetch_subcategory_category_for_vacancies(db_connector, unique_vacancy_ids, logger)

    # Aggregate stats per subcategory and per category (weighted and unweighted) using names
    subcat_stats: Dict[str, Dict[str, float]] = {}
    cat_stats: Dict[str, Dict[str, float]] = {}
    null_subcat_vacancy_ids: List[str] = []

    def add_stats(container: Dict[str, Dict[str, float]], key: Optional[str], applicants: int, total: int, pct: float):
        if key is None:
            return
        s = container.setdefault(key, {"vacancies": 0, "sum_pct": 0.0, "sum_app": 0.0, "sum_total": 0.0})
        s["vacancies"] += 1
        s["sum_pct"] += pct
        s["sum_app"] += applicants
        s["sum_total"] += total

    for vacancy_id in unique_vacancy_ids:
        applicants, total, pct = cache.get(vacancy_id, (0, 0, 0.0))
        subcat_name, cat_name, subcat_is_null = subcat_cat_map.get(vacancy_id, (None, None, True))
        if subcat_is_null:
            null_subcat_vacancy_ids.append(vacancy_id)
        # Skip adding to aggregates if names are missing (no N/A buckets)
        add_stats(subcat_stats, subcat_name, applicants, total, pct)
        add_stats(cat_stats, cat_name, applicants, total, pct)

    # Print summaries with names
    logger.info("\n" + "="*120)
    logger.info("APPLICANT STATS BY SUBCATEGORY (latest run per vacancy)")
    logger.info("="*120)
    logger.info(f"{'Subcategory':<40} {'Vacancies':>10} {'Avg % (unw)':>14} {'% (weighted)':>14}")
    logger.info("-"*120)
    for subcat_name, s in sorted(subcat_stats.items()):
        vacancies = int(s["vacancies"]) or 1
        avg_unweighted = s["sum_pct"] / vacancies
        weighted = (s["sum_app"] / s["sum_total"] * 100.0) if s["sum_total"] > 0 else 0.0
        logger.info(f"{subcat_name:<40} {vacancies:>10} {avg_unweighted:>14.2f} {weighted:>14.2f}")

    logger.info("\n" + "="*120)
    logger.info("APPLICANT STATS BY CATEGORY (latest run per vacancy)")
    logger.info("="*120)
    logger.info(f"{'Category':<40} {'Vacancies':>10} {'Avg % (unw)':>14} {'% (weighted)':>14}")
    logger.info("-"*120)
    for cat_name, s in sorted(cat_stats.items()):
        vacancies = int(s["vacancies"]) or 1
        avg_unweighted = s["sum_pct"] / vacancies
        weighted = (s["sum_app"] / s["sum_total"] * 100.0) if s["sum_total"] > 0 else 0.0
        logger.info(f"{cat_name:<40} {vacancies:>10} {avg_unweighted:>14.2f} {weighted:>14.2f}")

    # Print vacancy_ids with NULL subcategory
    if null_subcat_vacancy_ids:
        logger.info("\n" + "="*120)
        logger.info(f"VACANCY IDS WITH NULL SUBCATEGORY ({len(null_subcat_vacancy_ids)})")
        logger.info("="*120)
        for vid in sorted(null_subcat_vacancy_ids):
            logger.info(vid)

    logger.info("-"*120)
    logger.info("Done.")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        # In case logger isn't initialized
        print(f"Script failed with error: {e}")
        sys.exit(1) 