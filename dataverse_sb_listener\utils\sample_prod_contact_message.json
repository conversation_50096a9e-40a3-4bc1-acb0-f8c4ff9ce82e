{"metadata": {"message_id": "062d45bbac1a44c08551ee62e07e4e9e", "entity_name": "contact", "entity_id": "e5684464-40a8-ef11-8a69-7c1e525c7700", "stored_at": "2025-07-29T18:25:18.116989+00:00", "original_timestamp": "20250729_182518_116", "file_path": "/mnt/incoming/mercury_events/contact/2025-07-29/062d45bbac1a44c08551ee62e07e4e9e_e5684464-40a8-ef11-8a69-7c1e525c7700_20250729_182518_116.json"}, "entity_info": {"entity_name": "contact", "entity_id": "e5684464-40a8-ef11-8a69-7c1e525c7700", "message_type": "Update", "correlation_id": "ffca45c0-6dd2-4e8c-a5e0-c38eb257bacb", "request_id": "520f1ae4-9051-445d-9a3e-4c055ef6d6a3", "stage": 40, "attributes": {"modifiedby": {"raw_value": {"__type": "EntityReference:http://schemas.microsoft.com/xrm/2011/Contracts", "Id": "99ee048b-0bda-ee11-904d-0022482144d4", "KeyAttributes": [], "LogicalName": "systemuser", "Name": null, "RowVersion": null}, "type": "other"}, "recruit_availability": {"raw_value": "/Date(1753747200000+0000)/", "parsed_date": "2025-07-29 00:00:00+00:00", "type": "date"}, "recruit_lastmodifiedui": {"raw_value": "/Date(1753761600000)/", "parsed_date": "2025-07-29 04:00:00+00:00", "type": "date"}, "recruit_candidatetagcontrol3": {"raw_value": "Pediatrics", "type": "other"}, "recruit_candidatetagcontrol2": {"raw_value": "Nursing & Therapy (CC)", "type": "other"}, "recruit_situation": {"raw_value": {"__type": "EntityReference:http://schemas.microsoft.com/xrm/2011/Contracts", "Id": "081336c2-8eb4-ee11-a569-00224823f66a", "KeyAttributes": [], "LogicalName": "mercury_situation", "Name": null, "RowVersion": null}, "type": "other"}, "timezoneruleversionnumber": {"raw_value": 0, "type": "other"}, "recruit_mobilehome": {"raw_value": "+14083064682", "type": "other"}, "modifiedon": {"raw_value": "/Date(1753799118000)/", "parsed_date": "2025-07-29 14:25:18+00:00", "type": "date"}, "contactid": {"raw_value": "e5684464-40a8-ef11-8a69-7c1e525c7700", "type": "other"}, "modifiedonbehalfby": {"raw_value": null, "type": "other"}, "recruit_needsindexing": {"raw_value": true, "type": "other"}, "recruit_lastindexablechange": {"raw_value": "/Date(1753799118000)/", "parsed_date": "2025-07-29 14:25:18+00:00", "type": "date"}, "recruit_normalisedphone": {"raw_value": ",***********,***********,", "type": "other"}}, "post_entity_images": {"AsynchronousStepPrimaryName": {"logical_name": "contact", "id": "e5684464-40a8-ef11-8a69-7c1e525c7700", "attributes": {"fullname": {"raw_value": "<PERSON><PERSON><PERSON>", "type": "other"}, "contactid": {"raw_value": "e5684464-40a8-ef11-8a69-7c1e525c7700", "type": "other"}}}}, "pre_entity_images": {}}, "raw_message": {"message_id": "062d45bbac1a44c08551ee62e07e4e9e", "correlation_id": "{ffca45c0-6dd2-4e8c-a5e0-c38eb257bacb}", "session_id": null, "reply_to": null, "reply_to_session_id": null, "to": null, "content_type": "application/json", "subject": null, "application_properties": {"http://schemas.microsoft.com/xrm/2011/Claims/Organization": "tandymgroup.crm.dynamics.com", "http://schemas.microsoft.com/xrm/2011/Claims/User": null, "http://schemas.microsoft.com/xrm/2011/Claims/InitiatingUser": null, "http://schemas.microsoft.com/xrm/2011/Claims/EntityLogicalName": "contact", "http://schemas.microsoft.com/xrm/2011/Claims/RequestName": "Update", "http://schemas.microsoft.com/xrm/2011/Claims/InitiatingUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"}, "body": "{\"BusinessUnitId\":\"22b900b1-55ac-ee11-a569-000d3a9a80bf\",\"CorrelationId\":\"ffca45c0-6dd2-4e8c-a5e0-c38eb257bacb\",\"Depth\":1,\"InitiatingUserAgent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/138.0.0.0 Safari\\/537.36\",\"InitiatingUserAzureActiveDirectoryObjectId\":\"14209eaa-bced-4518-b9fe-ef3a861626e2\",\"InitiatingUserId\":\"99ee048b-0bda-ee11-904d-0022482144d4\",\"InputParameters\":[{\"key\":\"Target\",\"value\":{\"__type\":\"Entity:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Attributes\":[{\"key\":\"modifiedby\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"99ee048b-0bda-ee11-904d-0022482144d4\",\"KeyAttributes\":[],\"LogicalName\":\"systemuser\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"recruit_availability\",\"value\":\"\\/Date(1753747200000+0000)\\/\"},{\"key\":\"recruit_lastmodifiedui\",\"value\":\"\\/Date(1753761600000)\\/\"},{\"key\":\"recruit_candidatetagcontrol3\",\"value\":\"Pediatrics\"},{\"key\":\"recruit_candidatetagcontrol2\",\"value\":\"Nursing & Therapy (CC)\"},{\"key\":\"recruit_situation\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"081336c2-8eb4-ee11-a569-00224823f66a\",\"KeyAttributes\":[],\"LogicalName\":\"mercury_situation\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"timezoneruleversionnumber\",\"value\":0},{\"key\":\"recruit_mobilehome\",\"value\":\"+***********\"},{\"key\":\"modifiedon\",\"value\":\"\\/Date(1753799118000)\\/\"},{\"key\":\"contactid\",\"value\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\"},{\"key\":\"modifiedonbehalfby\",\"value\":null},{\"key\":\"recruit_needsindexing\",\"value\":true},{\"key\":\"recruit_lastindexablechange\",\"value\":\"\\/Date(1753799118000)\\/\"},{\"key\":\"recruit_normalisedphone\",\"value\":\",***********,***********,\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":\"5500310661\"}}],\"IsExecutingOffline\":false,\"IsInTransaction\":false,\"IsOfflinePlayback\":false,\"IsolationMode\":1,\"MessageName\":\"Update\",\"Mode\":1,\"OperationCreatedOn\":\"\\/Date(1753799118000+0000)\\/\",\"OperationId\":\"7bb85ed4-876c-f011-bec2-7c1e5269fb3a\",\"OrganizationId\":\"7d72795e-24b1-ee11-a564-0022482cc90d\",\"OrganizationName\":\"unq7d72795e24b1ee11a5640022482cc\",\"OutputParameters\":[],\"OwningExtension\":{\"Id\":\"d537122f-7366-f011-bec2-7ced8d1b14fc\",\"KeyAttributes\":[],\"LogicalName\":\"sdkmessageprocessingstep\",\"Name\":null,\"RowVersion\":null},\"ParentContext\":{\"BusinessUnitId\":\"22b900b1-55ac-ee11-a569-000d3a9a80bf\",\"CorrelationId\":\"ffca45c0-6dd2-4e8c-a5e0-c38eb257bacb\",\"Depth\":1,\"InitiatingUserAgent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/138.0.0.0 Safari\\/537.36\",\"InitiatingUserAzureActiveDirectoryObjectId\":\"14209eaa-bced-4518-b9fe-ef3a861626e2\",\"InitiatingUserId\":\"99ee048b-0bda-ee11-904d-0022482144d4\",\"InputParameters\":[{\"key\":\"Target\",\"value\":{\"__type\":\"Entity:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Attributes\":[{\"key\":\"recruit_lastmodifiedui\",\"value\":\"\\/Date(1753761600000)\\/\"},{\"key\":\"recruit_availability\",\"value\":\"\\/Date(1753747200000+0000)\\/\"},{\"key\":\"recruit_mobilehome\",\"value\":\"+***********\"},{\"key\":\"recruit_candidatetagcontrol2\",\"value\":\"Nursing & Therapy (CC)\"},{\"key\":\"recruit_candidatetagcontrol3\",\"value\":\"Pediatrics\"},{\"key\":\"recruit_situation\",\"value\":{\"__type\":\"EntityReference:http:\\/\\/schemas.microsoft.com\\/xrm\\/2011\\/Contracts\",\"Id\":\"081336c2-8eb4-ee11-a569-00224823f66a\",\"KeyAttributes\":[],\"LogicalName\":\"mercury_situation\",\"Name\":null,\"RowVersion\":null}},{\"key\":\"contactid\",\"value\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":null}},{\"key\":\"x-ms-app-name\",\"value\":\"mercury_Mercury\"},{\"key\":\"SuppressDuplicateDetection\",\"value\":false}],\"IsExecutingOffline\":false,\"IsInTransaction\":false,\"IsOfflinePlayback\":false,\"IsolationMode\":1,\"MessageName\":\"Update\",\"Mode\":1,\"OperationCreatedOn\":\"\\/Date(1753799118000+0000)\\/\",\"OperationId\":\"7bb85ed4-876c-f011-bec2-7c1e5269fb3a\",\"OrganizationId\":\"7d72795e-24b1-ee11-a564-0022482cc90d\",\"OrganizationName\":\"unq7d72795e24b1ee11a5640022482cc\",\"OutputParameters\":[],\"OwningExtension\":{\"Id\":\"d537122f-7366-f011-bec2-7ced8d1b14fc\",\"KeyAttributes\":[],\"LogicalName\":\"sdkmessageprocessingstep\",\"Name\":null,\"RowVersion\":null},\"ParentContext\":null,\"PostEntityImages\":[],\"PreEntityImages\":[],\"PrimaryEntityId\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\",\"PrimaryEntityName\":\"contact\",\"RequestId\":\"520f1ae4-9051-445d-9a3e-4c055ef6d6a3\",\"SecondaryEntityName\":\"none\",\"SharedVariables\":[{\"key\":\"IsAutoTransact\",\"value\":true},{\"key\":\"AcceptLang\",\"value\":\"en-US,en;q=0.9\"},{\"key\":\"x-ms-app-name\",\"value\":\"mercury_Mercury\"},{\"key\":\"ChangedEntityTypes\",\"value\":[{\"__type\":\"KeyValuePairOfstringstring:#System.Collections.Generic\",\"key\":\"contact\",\"value\":\"Update\"}]}],\"Stage\":30,\"UserAzureActiveDirectoryObjectId\":\"14209eaa-bced-4518-b9fe-ef3a861626e2\",\"UserId\":\"99ee048b-0bda-ee11-904d-0022482144d4\"},\"PostEntityImages\":[{\"key\":\"AsynchronousStepPrimaryName\",\"value\":{\"Attributes\":[{\"key\":\"fullname\",\"value\":\"BRAD MULLIGAN\"},{\"key\":\"contactid\",\"value\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\"}],\"EntityState\":null,\"FormattedValues\":[],\"Id\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\",\"KeyAttributes\":[],\"LogicalName\":\"contact\",\"RelatedEntities\":[],\"RowVersion\":null}}],\"PreEntityImages\":[],\"PrimaryEntityId\":\"e5684464-40a8-ef11-8a69-7c1e525c7700\",\"PrimaryEntityName\":\"contact\",\"RequestId\":\"520f1ae4-9051-445d-9a3e-4c055ef6d6a3\",\"SecondaryEntityName\":\"none\",\"SharedVariables\":[{\"key\":\"IsAutoTransact\",\"value\":true},{\"key\":\"AcceptLang\",\"value\":\"en-US,en;q=0.9\"},{\"key\":\"x-ms-app-name\",\"value\":\"mercury_Mercury\"}],\"Stage\":40,\"UserAzureActiveDirectoryObjectId\":\"14209eaa-bced-4518-b9fe-ef3a861626e2\",\"UserId\":\"99ee048b-0bda-ee11-904d-0022482144d4\"}"}}