#!/usr/bin/env python3
"""
Robust message processor for Dataverse entity updates.

This module provides a thread-pool based message processor with retry logic,
health monitoring, and at-least-once processing guarantees.
"""

import sys
import os
import json
import time
import threading
import queue
from concurrent.futures import Thread<PERSON>oolExecutor, Future, as_completed
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger
from dataverse_sb_listener.messaging.dataverse_message_parser import DataverseMessageParser
from dataverse_sb_listener.messaging.message_storage import MessageStorage
from azure.servicebus import ServiceBusReceiver
from dataverse_sb_listener.health.health_check import (
    update_message_processed, update_message_failed, 
    set_healthy, set_last_error
)

class ProcessingStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class MessageTask:
    """Represents a message processing task"""
    message_id: str
    entity_info: Dict[str, Any]
    receiver: ServiceBusReceiver
    message: Any
    status: ProcessingStatus
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    processor_func: Optional[Callable] = None
    entity_name: Optional[str] = None  # Track which entity type this task is for
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.entity_name is None:
            self.entity_name = self.entity_info.get('entity_name')

class DataverseMessageProcessor:
    """
    Robust message processor with separate thread pools per processor type.
    """
    
    def __init__(
        self, 
        logger: Optional[AppLogger] = None,
        processor_configs: Optional[Dict[str, Dict[str, Any]]] = None,
        max_retries: int = 3,
        retry_delay: float = 5.0,
        enable_message_storage: Optional[bool] = None
    ):
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        self.parser = DataverseMessageParser(self.logger)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Processor configurations (entity_name -> config)
        self.processor_configs = processor_configs or {}
        
        # Separate thread pools per processor type
        self.processor_executors: Dict[str, ThreadPoolExecutor] = {}
        
        # Determine if message storage should be enabled
        if enable_message_storage is None:
            # Check environment variable, default to True
            env_enabled = os.getenv('MERCURY_EVENTS_STORAGE_ENABLED', 'true').lower()
            enable_message_storage = env_enabled not in ('false', '0', 'no', 'disabled')
        
        # Initialize message storage
        self.message_storage = MessageStorage(
            logger=self.logger,
            enabled=enable_message_storage
        )
        
        # Track active tasks per processor
        self.active_tasks: Dict[str, Dict[str, MessageTask]] = {}
        self.task_lock = threading.Lock()
        
        # Processing callbacks
        self.entity_processors: Dict[str, Callable] = {}
        
        # Statistics per processor
        self.stats = {
            'processed': 0,
            'failed': 0,
            'retried': 0,
            'total_processing_time': 0.0,
            'active_tasks': 0,
            'processor_stats': {}  # Per-processor statistics
        }
        self.stats_lock = threading.Lock()
        
        # Shutdown flag
        self.shutdown_requested = False
        
        # Health monitoring
        self._setup_health_monitoring()
        
    def _setup_health_monitoring(self):
        """Setup health monitoring."""
        # Start with healthy status
        set_healthy(True)
        
        # Monitor for health issues
        self.health_monitor_thread = threading.Thread(target=self._health_monitor, daemon=True)
        self.health_monitor_thread.start()
        
    def _health_monitor(self):
        """Monitor health and update status."""
        while not self.shutdown_requested:
            try:
                time.sleep(30)  # Check every 30 seconds
                
                with self.stats_lock:
                    # Check if we're processing messages
                    if self.stats['processed'] > 0:
                        success_rate = (self.stats['processed'] / (self.stats['processed'] + self.stats['failed'])) * 100
                        
                        # Mark as unhealthy if success rate is too low
                        if success_rate < 80:  # Less than 80% success rate
                            set_healthy(False)
                            set_last_error(f"Low success rate: {success_rate:.1f}%")
                        else:
                            set_healthy(True)
                    
                    # Check for too many active tasks (potential backlog)
                    total_active = self.stats['active_tasks']
                    total_max_workers = sum(
                        stats.get('max_workers', 0) 
                        for stats in self.stats['processor_stats'].values()
                    )
                    if total_active > total_max_workers * 2:
                        set_healthy(False)
                        set_last_error(f"Too many active tasks: {total_active}")
                        
            except Exception as e:
                self.logger.error(f"Error in health monitor: {e}")
                set_healthy(False)
                set_last_error(f"Health monitor error: {e}")
        
    def register_entity_processor(self, entity_name: str, processor_func: Callable, max_workers: int = 4):
        """
        Register a processor function for a specific entity type with its own thread pool.
        
        Args:
            entity_name: Name of the entity (e.g., 'contact', 'crimson_vacancy')
            processor_func: Function to process the entity update
            max_workers: Number of threads for this processor's thread pool
        """
        self.entity_processors[entity_name] = processor_func
        
        # Create separate thread pool for this processor
        executor = ThreadPoolExecutor(
            max_workers=max_workers, 
            thread_name_prefix=f"Processor-{entity_name}"
        )
        self.processor_executors[entity_name] = executor
        
        # Initialize per-processor tracking
        self.active_tasks[entity_name] = {}
        self.stats['processor_stats'][entity_name] = {
            'processed': 0,
            'failed': 0,
            'retried': 0,
            'total_processing_time': 0.0,
            'active_tasks': 0,
            'max_workers': max_workers
        }
        
        self.logger.info(f"Registered processor for entity: {entity_name} with {max_workers} threads")
    
    def process_message(self, msg, receiver: ServiceBusReceiver) -> bool:
        """
        Process a message by queuing it for processing in the appropriate processor's thread pool.
        
        Args:
            msg: Service Bus message
            receiver: Service Bus receiver
            
        Returns:
            True if message was queued successfully, False otherwise
        """
        # Check if shutdown was requested
        if self.shutdown_requested:
            self.logger.warning(f"Shutdown requested, rejecting message {getattr(msg, 'message_id', 'unknown')}")
            return False
        
        try:
            # Parse the message
            entity_info = self.parser.process_message(msg)
            if not entity_info:
                self.logger.error(f"Failed to parse message {getattr(msg, 'message_id', 'unknown')}")
                # Complete the message since we can't process it
                receiver.complete_message(msg)
                return False
            
            # Get the appropriate processor function
            entity_name = entity_info.get('entity_name')
            processor_func = self.entity_processors.get(entity_name)
            if not processor_func:
                self.logger.warning(f"No processor registered for entity: {entity_name}")
                # Complete the message since we don't know how to process it
                receiver.complete_message(msg)
                return False
            
            # Store message for replay purposes
            self.message_storage.store_message(msg, entity_info)
            
            # Create message task
            task = MessageTask(
                message_id=getattr(msg, 'message_id', 'unknown'),
                entity_info=entity_info,
                receiver=receiver,
                message=msg,
                status=ProcessingStatus.PENDING,
                max_retries=self.max_retries,
                entity_name=entity_name
            )
            
            # Set the processor function
            task.processor_func = processor_func
            
            # Add to active tasks for this processor
            with self.task_lock:
                self.active_tasks[entity_name][task.message_id] = task
                self.stats['active_tasks'] += 1
                self.stats['processor_stats'][entity_name]['active_tasks'] += 1
            
            # Submit to the appropriate processor's thread pool
            try:
                executor = self.processor_executors[entity_name]
                future = executor.submit(self._process_task, task)
                self.logger.info(f"Queued {entity_name} message {task.message_id} for processing")
                return True
            except RuntimeError as e:
                if "cannot schedule new futures after shutdown" in str(e):
                    self.logger.warning(f"Executor shutdown during message queuing for {task.message_id}")
                    # Remove from active tasks since we couldn't queue it
                    with self.task_lock:
                        if task.message_id in self.active_tasks[entity_name]:
                            del self.active_tasks[entity_name][task.message_id]
                            self.stats['active_tasks'] -= 1
                            self.stats['processor_stats'][entity_name]['active_tasks'] -= 1
                    return False
                else:
                    # Re-raise other RuntimeErrors
                    raise
                
        except Exception as e:
            self.logger.error(f"Error processing message {getattr(msg, 'message_id', 'unknown')}: {e}")
            return False
    
    def _process_task(self, task: MessageTask):
        """Process a message task."""
        start_time = time.time()
        task.start_time = start_time
        task.status = ProcessingStatus.PROCESSING
        
        entity_name = task.entity_name
        entity_id = task.entity_info.get('entity_id')
        
        self.logger.info(f"Processing {entity_name} update for ID: {entity_id}")
        
        try:
            # Process the message
            success = task.processor_func(task.entity_info)
            
            processing_time = time.time() - start_time
            
            if success:
                # Update health metrics with entity name
                update_message_processed(entity_name)
                
                with self.stats_lock:
                    self.stats['processed'] += 1
                    self.stats['total_processing_time'] += processing_time
                    self.stats['processor_stats'][entity_name]['processed'] += 1
                    self.stats['processor_stats'][entity_name]['total_processing_time'] += processing_time
                
                self.logger.info(f"Successfully processed {entity_name} {entity_id} in {processing_time:.2f}s")
                self._complete_message(task, success=True)
            else:
                # Update health metrics with entity name
                update_message_failed(entity_name)
                
                with self.stats_lock:
                    self.stats['failed'] += 1
                    self.stats['processor_stats'][entity_name]['failed'] += 1
                
                self.logger.error(f"Error processing message {task.message_id}: Processor returned False for {entity_name} {entity_id}")
                self._handle_processing_failure(task)
                
        except Exception as e:
            # Update health metrics with entity name
            update_message_failed(entity_name)
            
            with self.stats_lock:
                self.stats['failed'] += 1
                self.stats['processor_stats'][entity_name]['failed'] += 1
            
            self.logger.error(f"Error processing message {task.message_id}: {e}")
            task.error = str(e)
            self._handle_processing_failure(task)
    
    def _handle_processing_failure(self, task: MessageTask):
        """Handle processing failure with retry logic."""
        entity_name = task.entity_name
        
        if task.retry_count < self.max_retries and not self.shutdown_requested:
            task.retry_count += 1
            task.status = ProcessingStatus.RETRYING
            
            with self.stats_lock:
                self.stats['retried'] += 1
                self.stats['processor_stats'][entity_name]['retried'] += 1
            
            # Calculate retry delay with exponential backoff
            delay = self.retry_delay * task.retry_count
            
            self.logger.info(f"Retrying {entity_name} message {task.message_id} (attempt {task.retry_count}/{self.max_retries})")
            
            # Schedule retry in the same processor's thread pool
            try:
                executor = self.processor_executors[entity_name]
                executor.submit(self._retry_task, task, delay)
            except RuntimeError as e:
                if "cannot schedule new futures after shutdown" in str(e):
                    self.logger.warning(f"Executor shutdown during retry scheduling for message {task.message_id}, completing as failed")
                    self._complete_message(task, success=False)
                else:
                    # Re-raise other RuntimeErrors
                    raise
        else:
            # Max retries exceeded or shutdown requested, complete as failed
            if self.shutdown_requested:
                self.logger.warning(f"Shutdown requested, completing message {task.message_id} as failed without retry")
            else:
                self.logger.error(f"Max retries exceeded for message {task.message_id}. Completing message.")
            self._complete_message(task, success=False)
    
    def _retry_task(self, task: MessageTask, delay: float):
        """Retry a failed task after delay."""
        time.sleep(delay)
        
        # Check if shutdown was requested during the delay
        if self.shutdown_requested:
            self.logger.warning(f"Shutdown requested, skipping retry for message {task.message_id}")
            # Complete the message as failed since we can't retry during shutdown
            self._complete_message(task, success=False)
            return
        
        try:
            # Re-submit for processing in the same processor's thread pool
            entity_name = task.entity_name
            executor = self.processor_executors[entity_name]
            executor.submit(self._process_task, task)
        except RuntimeError as e:
            if "cannot schedule new futures after shutdown" in str(e):
                self.logger.warning(f"Executor shutdown during retry for message {task.message_id}, completing as failed")
                # Complete the message as failed since we can't retry
                self._complete_message(task, success=False)
            else:
                # Re-raise other RuntimeErrors
                raise
    
    def _complete_message(self, task: MessageTask, success: bool):
        """Complete a message task."""
        try:
            # Complete the Service Bus message
            task.receiver.complete_message(task.message)
            task.status = ProcessingStatus.COMPLETED
            task.completed_at = datetime.now()
            
            # Remove from active tasks
            entity_name = task.entity_name
            with self.task_lock:
                if task.message_id in self.active_tasks[entity_name]:
                    del self.active_tasks[entity_name][task.message_id]
                    self.stats['active_tasks'] -= 1
                    self.stats['processor_stats'][entity_name]['active_tasks'] -= 1
            
            self.logger.info(f"Completed {entity_name} message {task.message_id} (success: {success})")
            
        except Exception as e:
            self.logger.error(f"Error completing message {task.message_id}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        with self.stats_lock:
            stats = self.stats.copy()
            
            # Add per-processor thread pool info
            for entity_name, executor in self.processor_executors.items():
                if entity_name in stats['processor_stats']:
                    stats['processor_stats'][entity_name]['thread_pool_size'] = executor._max_workers
                    stats['processor_stats'][entity_name]['thread_pool_active'] = len(executor._threads)
            
            return stats
    
    def shutdown(self, timeout: float = 30.0) -> None:
        """Shutdown all thread pools gracefully."""
        self.logger.info("Shutting down message processor...")
        self.shutdown_requested = True
        
        # Shutdown all processor thread pools
        shutdown_futures = []
        for entity_name, executor in self.processor_executors.items():
            self.logger.info(f"Shutting down {entity_name} processor thread pool...")
            executor.shutdown(wait=False)
            shutdown_futures.append((entity_name, executor))
        
        # Wait for shutdown with timeout
        start_time = time.time()
        for entity_name, executor in shutdown_futures:
            try:
                executor.shutdown(wait=True, timeout=max(0, timeout - (time.time() - start_time)))
                self.logger.info(f"Shutdown {entity_name} processor thread pool completed")
            except Exception as e:
                self.logger.warning(f"Error shutting down {entity_name} processor thread pool: {e}")
        
        # Shutdown message storage
        if self.message_storage:
            self.message_storage.shutdown()
        
        self.logger.info("Message processor shutdown completed")
    
    def is_shutting_down(self) -> bool:
        """Check if shutdown has been requested."""
        return self.shutdown_requested
    
    def get_active_tasks(self) -> Dict[str, Dict[str, MessageTask]]:
        """Get all active tasks grouped by processor."""
        with self.task_lock:
            return {k: v.copy() for k, v in self.active_tasks.items()} 