import pytest
from unittest.mock import Mock, patch
from dpserver.services.candidate_stats_service import CandidateStatsService

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    connector = Mock()
    connector.connect = Mock()
    return connector

@pytest.fixture
def service(mock_logger, mock_db_connector):
    # Patch DB connection methods to avoid real DB calls
    with patch("dpserver.services.candidate_stats_service.getProdDBConnection") as mock_conn:
        mock_conn.return_value.cursor.return_value = Mock()
        yield CandidateStatsService(mock_logger, mock_db_connector)

def test_get_all_stats_success(service):
    # Patch all methods used in get_all_stats
    with patch.object(service, "get_temp_candidates_no_resume", return_value=2), \
         patch.object(service, "get_total_temp_candidates", return_value=5), \
         patch("dpserver.services.candidate_stats_service.SubcategoryPoolService") as MockPoolService, \
         patch.object(service, "get_total_candidates", return_value=10), \
         patch.object(service, "get_total_active_candidates", return_value=8), \
         patch.object(service, "get_temp_candidates_no_resume_with_email", return_value=1), \
         patch.object(service, "get_temp_candidates_no_resume_with_phone", return_value=1), \
         patch.object(service, "get_temp_candidates_no_resume_with_phone_email", return_value=0):

        pool_instance = MockPoolService.return_value
        pool_instance.get_total_tagged_candidates.return_value = 3

        stats = service.get_all_stats()

        assert stats["total_candidates"] == 10
        assert stats["total_active_candidates"] == 8
        assert stats["total_temp_candidates"] == 5
        assert stats["temp_candidates_with_resume"] == 3  # 5 - 2
        assert stats["temp_candidates_no_resume_with_email"] == 1
        assert stats["temp_candidates_no_resume_with_phone"] == 1
        assert stats["temp_candidates_no_resume_with_phone_email"] == 0
        assert stats["total_tagged_candidates"] == 3

def test_get_all_stats_exception(service, mock_logger):
    # Simulate an exception in a dependency
    with patch.object(service, "get_temp_candidates_no_resume", side_effect=Exception("DB error")):
        stats = service.get_all_stats()
        assert stats == {}
        mock_logger.error.assert_called_once()
        assert "Error getting candidate statistics" in mock_logger.error.call_args[0][0]

def test_get_all_stats_zero_candidates(service,mock_logger):
    # All counts are zero
    with patch.object(service, "get_temp_candidates_no_resume", return_value=0), \
         patch.object(service, "get_total_temp_candidates", return_value=0), \
         patch("dpserver.services.candidate_stats_service.SubcategoryPoolService") as MockPoolService, \
         patch.object(service, "get_total_candidates", return_value=0), \
         patch.object(service, "get_total_active_candidates", return_value=0), \
         patch.object(service, "get_temp_candidates_no_resume_with_email", return_value=0), \
         patch.object(service, "get_temp_candidates_no_resume_with_phone", return_value=0), \
         patch.object(service, "get_temp_candidates_no_resume_with_phone_email", return_value=0):

        pool_instance = MockPoolService.return_value
        pool_instance.get_total_tagged_candidates.return_value = 0

        stats = service.get_all_stats()

        assert all(value == 0 for value in stats.values())
        mock_logger.error.assert_not_called()