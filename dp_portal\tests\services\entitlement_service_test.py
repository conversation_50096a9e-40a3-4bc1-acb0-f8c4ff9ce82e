import pytest
import os
import json
from unittest.mock import Mock,MagicMock,patch
from dp_portal.services.entitlement_service import EntitlementService

@pytest.fixture
def mock_logger():
    return Mock()

@pytest.fixture
def mock_db_connector():
    mock_db = Mock()
    mock_db.schema = "test_schema"
    mock_db.connection = Mock()
    return mock_db

@pytest.fixture
def service(mock_logger, mock_db_connector):
    return EntitlementService(logger=mock_logger, db_connector=mock_db_connector)

def set_env(entitlements):
    os.environ["EntitlementsJson"] = json.dumps(entitlements)
    
# def setup_entitlement_db_mock(mock_db_connector, user_entitled=True):
#     list_cursor = MagicMock()
#     list_cursor.fetchone.return_value = [1] if user_entitled else [0]

#     insert_cursor = MagicMock()

#     mock_conn = MagicMock()
#     mock_conn.closed = 0
#     mock_conn.cursor.side_effect = [
#         MagicMock(__enter__=lambda s: list_cursor, __exit__=lambda *a: None),
#         MagicMock(__enter__=lambda s: insert_cursor, __exit__=lambda *a: None),
#     ]

#     mock_db_connector.connection = mock_conn
#     return mock_conn  # Optional if you want to use it further

def setup_entitlement_db_mock(mock_db_connector, user_entitled=True):
    # Return a real integer value in a tuple
    list_cursor = MagicMock()
    list_cursor.fetchone.return_value = (1 if user_entitled else 0,)  # Use a tuple, not list

    list_context = MagicMock()
    list_context.__enter__.return_value = list_cursor
    list_context.__exit__.return_value = False

    # Mock insert cursor
    insert_cursor = MagicMock()
    insert_context = MagicMock()
    insert_context.__enter__.return_value = insert_cursor
    insert_context.__exit__.return_value = False

    mock_conn = MagicMock()
    mock_conn.closed = 0
    mock_conn.cursor.side_effect = [list_context] * 11 + [insert_context]  # One per feature, then audit log
    mock_conn.commit.return_value = None

    mock_db_connector.connection = mock_conn

def test_all_entitled(service, mock_db_connector):
    set_env({
        "work_force_index": "Entitled",
        "sub_catregory": "Entitled",
        "vacancy": "Entitled",
        "search_match": "Entitled",
        "sc_score_config": "Entitled",
        "candidate_tunning_page": "Entitled",
        "shorting_listing": "Entitled",
        "historical_logs": "Entitled",
        "regenerate": "Entitled",
        "update_availability": "Entitled",
        "filter_feature": "Entitled"
    })

    # ✅ Setup mock DB context manager
    setup_entitlement_db_mock(mock_db_connector)

    # Act
    result = service.get_entitlement("<EMAIL>", "portal")

    # Assert
    assert result["error"] is False
    assert all(result["entitlement"].values())
    assert result["code"] == "TR_01"
    
def test_all_none(service, mock_db_connector):
    set_env({
        "work_force_index": "None",
        "sub_catregory": "None",
        "vacancy": "None",
        "search_match": "None",
        "sc_score_config": "None"
    })

    setup_entitlement_db_mock(mock_db_connector, user_entitled=False)

    result = service.get_entitlement("<EMAIL>", "portal")
    print("ENTITLEMENT RESULT:", result)  # Helpful debug
    assert result["error"] is False
    assert not any(result["entitlement"].values())
    assert result["code"] == "TR_01"
        
# def test_list_user_exists(service, mock_db_connector):
#     set_env({
#         "work_force_index": "List",
#         "sub_catregory": "List",
#         "vacancy": "List",
#         "search_match": "List",
#         "sc_score_config": "List"
#     })

#     setup_entitlement_db_mock(mock_db_connector, user_entitled=True)
#     service.db = mock_db_connector
#     service.logger = Mock()

#     result = service.get_entitlement("<EMAIL>", "portal")

#     assert result["error"] is False
#     assert all(result["entitlement"].values())
#     assert result["code"] == "TR_01"
def test_list_user_exists(service, mock_db_connector):
    set_env({
        "work_force_index": "List",
        "sub_catregory": "List",
        "vacancy": "List",
        "search_match": "List",
        "sc_score_config": "List",
        "candidate_tunning_page": "List",
        "shorting_listing": "List",
        "historical_logs": "List",
        "regenerate": "List",
        "update_availability": "List",
        "filter_feature": "List"        
    })

    setup_entitlement_db_mock(mock_db_connector, user_entitled=True)
    service.db = mock_db_connector
    service.logger = Mock()

    result = service.get_entitlement("<EMAIL>", "portal")
    print("ENTITLEMENT RESULT:", result)  # Helpful debug


    assert result["error"] is False
    assert all(result["entitlement"].values())
    assert result["code"] == "TR_01"


def test_list_user_not_exists(service, mock_db_connector):
    set_env({
        "work_force_index": "List",
        "sub_catregory": "List",
        "vacancy": "List",
        "search_match": "List",
        "sc_score_config": "List",
        "candidate_tunning_page": "List",
        "shorting_listing": "List",
        "historical_logs": "List",
        "regenerate": "List",
        "update_availability": "List",
        "filter_feature": "List"                
    })
    # Mock DB to return user does not exist
    # mock_cursor = Mock()
    # mock_cursor.fetchone.return_value = [0]
    # mock_db_connector.connection.cursor.return_value = mock_cursor

    setup_entitlement_db_mock(mock_db_connector, user_entitled=True)
    service.db = mock_db_connector
    service.logger = Mock()

    result = service.get_entitlement("<EMAIL>", "portal")
    assert result["error"] is False
    assert any(result["entitlement"].values())
    assert result["code"] == "TR_01"


def test_unexpected_value(service, mock_db_connector):
    set_env({
        "work_force_index": "Unknown",
        "sub_catregory": "Entitled",
        "vacancy": "None",
        "search_match": "List",
        "sc_score_config": "None"
    })
    # Mock DB to return user exists for "List"
    # mock_cursor = Mock()
    # mock_cursor.fetchone.return_value = [1]
    # mock_db_connector.connection.cursor.return_value = mock_cursor
    setup_entitlement_db_mock(mock_db_connector, user_entitled=True)
    service.db = mock_db_connector
    service.logger = Mock()

    result = service.get_entitlement("<EMAIL>", "portal")
    assert result["error"] is False
    assert result["entitlement"]["Work_force_Index"] is False
    assert result["entitlement"]["Sub_Catregory"] is True
    assert result["entitlement"]["Vacancy"] is False
    assert result["entitlement"]["Search_Match"] is True
    assert result["entitlement"]["Sc_Score_Config"] is False
    
def test_db_exception(service, mock_db_connector):
    set_env({
        "work_force_index": "List",
        "sub_catregory": "Entitled",
        "vacancy": "None",
        "search_match": "List",
        "sc_score_config": "None"
    })

    # Simulate DB error during list mode check
    list_cursor = MagicMock()
    list_cursor.execute.side_effect = Exception("DB error")  # 💥 Simulate DB error
    list_cursor.fetchone.return_value = None

    list_context = MagicMock()
    list_context.__enter__.return_value = list_cursor
    list_context.__exit__.return_value = False

    insert_cursor = MagicMock()
    insert_context = MagicMock()
    insert_context.__enter__.return_value = insert_cursor
    insert_context.__exit__.return_value = False

    # Simulate DB connection with 5 list fields + 1 insert = 6 cursor calls
    mock_conn = MagicMock()
    mock_conn.closed = 0
    mock_conn.cursor.side_effect = [list_context] + [insert_context]
    mock_conn.rollback.return_value = None

    mock_db_connector.connection = mock_conn
    service.db = mock_db_connector
    service.logger = Mock()

    # Act
    result = service.get_entitlement("<EMAIL>", "portal")

    # Assert
    assert result["error"] is True
    assert result["code"] == "TR_ERR"
    assert "DB error" in result["message"]
