import os
import json
#
# Create subcategory based files with a list of candidates with high match score.
# Hopefully we can get this list once it is in the database quickly without storing it in a file.
# Run it weekly or daily? so we get all the latest tagged candidates,
#
def ensure_directory_exists(directory):
    """Ensure the output directory exists."""
    os.makedirs(directory, exist_ok=True)

def load_json(file_path):
    """Load JSON data from a file."""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return json.load(file)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def process_json_file(file_path, output_directory, processed_files):
    """Process a single JSON file and write to respective subcategory files if conditions meet."""
    data = load_json(file_path)
    if not data or "sub_cats file" not in data or "score" not in data:
        return

    sub_cats = data["sub_cats file"]
    scores = data["score"]
    filename = os.path.basename(file_path)

    for sub_cat, score in zip(sub_cats, scores):
        if score in ["1", "2", "3", "4"]:  # Check if the score is low
            txt_filename = f"{sub_cat}_high.txt"
            txt_path = os.path.join(output_directory, txt_filename)
            #print(txt_path)

            # Read existing contents only once per subcategory file
            if txt_path not in processed_files:
                if os.path.exists(txt_path):
                    with open(txt_path, "r", encoding="utf-8") as txt_file:
                        processed_files[txt_path] = set(txt_file.read().splitlines())
                else:
                    processed_files[txt_path] = set()

            # Append only if not already present
            if filename not in processed_files[txt_path]:
                with open(txt_path, "a", encoding="utf-8") as txt_file:
                    txt_file.write(filename + "\n")
                processed_files[txt_path].add(filename)

def process_directory(input_directory, output_directory):
    """Process all JSON files in a directory."""
    ensure_directory_exists(output_directory)
    processed_files = {}

    for filename in os.listdir(input_directory):
        if filename.endswith(".json"):  # Process only JSON files
            file_path = os.path.join(input_directory, filename)
            process_json_file(file_path, output_directory, processed_files)

def main():
    """Main function to execute the script."""
    input_directory = "/mnt/incoming/classify-prod/"  # Update this
    output_directory = '/mnt/incoming/match-results/subcat_classify/'  # Update this

    process_directory(input_directory, output_directory)
    print("Processing complete. All output files are stored in:", output_directory)

if __name__ == "__main__":
    main()
