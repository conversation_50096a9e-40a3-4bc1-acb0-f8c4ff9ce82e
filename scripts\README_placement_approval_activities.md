# Placement Approval Activities Reader

This script reads the `mercury_placementapprovalactivities` table from Dataverse, filtering for records with `statecode = 1` and specific approval step values.

## Files

- `get_placement_approval_activities.py` - Main Python script
- `run_placement_approval_activities.sh` - Shell wrapper script
- Logs are written to: `/mnt/incoming/logs/get_placement_approval_activities.log`

## Environment-specific Filters

The script automatically applies environment-specific filters:

- **PROD**: `_mercury_approvalstep_value = "a1f2ca86-0e07-ef11-9f89-000d3a4fadb9"`
- **UAT**: `_mercury_approvalstep_value = "a1f2ca86-0e07-ef11-9f89-000d3a4fadb9"`

## Usage

### Direct Python Script

```bash
# Single run on UAT
python3 scripts/get_placement_approval_activities.py uat

# Single run on PROD
python3 scripts/get_placement_approval_activities.py prod

# With date filter (records created after specified date)
python3 scripts/get_placement_approval_activities.py uat --after-date 2024-01-01

# Run in hourly loop mode
python3 scripts/get_placement_approval_activities.py prod --hourly

# With debug logging
python3 scripts/get_placement_approval_activities.py uat --log-level DEBUG
```

### Using Shell Script

```bash
# Single run on UAT
./scripts/run_placement_approval_activities.sh uat

# Single run on PROD with date filter
./scripts/run_placement_approval_activities.sh prod --after-date 2024-01-01

# Continuous hourly execution
./scripts/run_placement_approval_activities.sh uat --hourly

# With debug logging
./scripts/run_placement_approval_activities.sh prod --log-level DEBUG
```

## Cron Job Setup

To run the script every hour using cron:

```bash
# Edit crontab
crontab -e

# Add one of these lines:
# Run every hour on UAT
0 * * * * /home/<USER>/code/tandym-dataprocessing/scripts/run_placement_approval_activities.sh uat

# Run every hour on PROD
0 * * * * /home/<USER>/code/tandym-dataprocessing/scripts/run_placement_approval_activities.sh prod

# Run every hour on UAT with date filter
0 * * * * /home/<USER>/code/tandym-dataprocessing/scripts/run_placement_approval_activities.sh uat --after-date 2024-01-01
```

## Fields Retrieved

The script retrieves the following fields from the table:

- `activityid` - Unique identifier for the activity
- `subject` - Subject of the activity
- `statecode` - State code (filtered to 1)
- `statuscode` - Status code
- `createdon` - Creation date
- `modifiedon` - Last modified date
- `_mercury_approvalstep_value` - The approval step value (filtered by environment)

## Output

The script provides:

1. **Console Output**: Summary statistics including total records and approval step distribution
2. **Log File**: Detailed logging at `/mnt/incoming/logs/get_placement_approval_activities.log`
3. **Return Data**: List of matching records with processing statistics

## Environment Variables Required

The script requires these environment variables to be set for authentication:

### For UAT
- `AZURE_DATAVERSE_UAT_TENANT_ID`
- `AZURE_DATAVERSE_UAT_CLIENT_ID`
- `AZURE_DATAVERSE_UAT_CLIENT_SECRET`

### For PROD
- `AZURE_DATAVERSE_PROD_TENANT_ID`
- `AZURE_DATAVERSE_PROD_CLIENT_ID`
- `AZURE_DATAVERSE_PROD_CLIENT_SECRET`

## Error Handling

- Validates date format for `--after-date` parameter
- Retries on errors when running in hourly mode (waits 5 minutes before retry)
- Comprehensive logging for troubleshooting
- Graceful handling of KeyboardInterrupt in hourly mode 