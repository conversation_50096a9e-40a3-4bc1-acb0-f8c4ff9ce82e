"""
Tests for JSON utilities that handle control characters.
"""

import pytest
from common.utils.json_utils import safe_json_loads


def test_safe_json_loads_valid_json():
    """Test that valid JSON is parsed correctly."""
    valid_json = '{"key": "value", "number": 42}'
    result = safe_json_loads(valid_json)
    assert result == {"key": "value", "number": 42}


def test_safe_json_loads_with_control_characters():
    """Test that JSON with control characters is cleaned and parsed."""
    # JSON with unescaped newlines and control characters
    problematic_json = '''{
    "candidate_summary": "Licensed Occupational Therapist (OTR/L) with 3+ years of continuous, school-based experience at P.S. 161 in New York City, supporting 30+ pre-K through elementary students with ASD, ADHD and other IDD diagnoses. Holds an M.S. in Occupational Therapy and has a strong record of improving fine motor, handwriting and self-regulation skills while collaborating closely with teachers and parents. Proficient in SESIS, Microsoft 365 and virtual platforms, ensuring compliant documentation and effective tele/onsite service delivery.",
    "matching_key_requirements": "• School-based pediatric/IDD experience: Currently delivers 10+ OT sessions daily at P.S. 161, creating classroom interventions for students with autism, ADHD and other developmental disabilities.
• Conduct evaluations & write IEP-aligned goals: Performs comprehensive screenings and writes progress reports in SESIS, tailoring goals that boosted fine-motor proficiency for 15+ students.
• Implement fine/gross motor & sensory processing interventions: Designed developmentally graded activities—movement breaks, adaptive seating and sensory integration plans—raising classroom productivity for over 30 children.
• Collaboration & staff/parent training: Coaches five pre-K/kindergarten teachers on movement strategies and regularly communicates progress and HEPs to families.
• Required credentials (OTR/L, MS OT): Holds Master's in OT, national NBCOT certification and active NY OT license, meeting all regulatory standards.",
    "final_fit_justification": "• Direct, recent school-based OT experience mirrors the role's setting, schedule and pediatric caseload.
• Proven ability to evaluate,"
}'''
    
    result = safe_json_loads(problematic_json)
    assert "candidate_summary" in result
    assert "matching_key_requirements" in result
    assert "final_fit_justification" in result





def test_safe_json_loads_empty_string():
    """Test that empty string raises ValueError."""
    with pytest.raises(ValueError, match="Empty JSON string provided"):
        safe_json_loads("")


def test_safe_json_loads_extract_json_from_text():
    """Test that JSON is extracted from surrounding text."""
    text_with_json = '''
    Here is some text before the JSON.
    {
        "key": "value",
        "nested": {
            "inner": "data"
        }
    }
    And some text after the JSON.
    '''
    
    result = safe_json_loads(text_with_json)
    assert result == {"key": "value", "nested": {"inner": "data"}}


if __name__ == "__main__":
    pytest.main([__file__])
