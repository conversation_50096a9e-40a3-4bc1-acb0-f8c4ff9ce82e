import sys
import os

# Add project root to Python path BEFORE importing local modules
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

import re
import json
import argparse
from datetime import datetime, timed<PERSON><PERSON>
from common.appLogger import AppLogger
from azure.servicebus.management import ServiceBusAdministrationClient
from azure.core.exceptions import ResourceNotFoundError, ServiceRequestError, ClientAuthenticationError

# Initialize logger with default configuration
logger_config = {"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"}
logger = AppLogger(logger_config)

def check_service_bus_connection(connection_string):
    """Test if the service bus connection is valid"""
    try:
        client = ServiceBusAdministrationClient.from_connection_string(connection_string)
        # Try to list topics to test the connection
        list(client.list_topics())
        return client
    except (ServiceRequestError, ClientAuthenticationError) as e:
        logger.error(f"Failed to connect to Service Bus: {e}")
        return None

def topic_exists(client, topic_name):
    """Check if a topic exists"""
    try:
        client.get_topic(topic_name=topic_name)
        return True
    except ResourceNotFoundError:
        return False
    except Exception as e:
        logger.error(f"Error checking if topic '{topic_name}' exists: {e}")
        return False

def subscription_exists(client, topic_name, subscription_name):
    """Check if a subscription exists"""
    try:
        client.get_subscription(topic_name=topic_name, subscription_name=subscription_name)
        return True
    except ResourceNotFoundError:
        return False
    except Exception as e:
        logger.error(f"Error checking if subscription '{subscription_name}' exists on topic '{topic_name}': {e}")
        return False

def create_topic_and_subscription(connection_string: str, topic_name: str, subscription_name: str = "sandbox-test") -> bool:
    """
    Create a topic and subscription if they don't exist.
    
    Args:
        connection_string: Service Bus connection string
        topic_name: Name of the topic to create
        subscription_name: Name of the subscription to create
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Test connection and get client
        admin_client = check_service_bus_connection(connection_string)
        if not admin_client:
            logger.error("Failed to establish connection to Service Bus. Please check your connection string.")
            return False
        
        # Check if topic exists, create if it doesn't
        if not topic_exists(admin_client, topic_name):
            try:
                admin_client.create_topic(topic_name=topic_name)
                logger.info(f"Created topic '{topic_name}'")
            except Exception as e:
                logger.error(f"Failed to create topic '{topic_name}': {e}")
                return False
        else:
            logger.info(f"Topic '{topic_name}' already exists.")
        
        # Check if subscription exists, create if it doesn't
        if not subscription_exists(admin_client, topic_name, subscription_name):
            try:
                admin_client.create_subscription(topic_name=topic_name, subscription_name=subscription_name)
                logger.info(f"Created subscription '{subscription_name}' on topic '{topic_name}'")
            except Exception as e:
                logger.error(f"Failed to create subscription '{subscription_name}' on topic '{topic_name}': {e}")
                return False
        else:
            logger.info(f"Subscription '{subscription_name}' on topic '{topic_name}' already exists.")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating topic and subscription: {e}")
        return False

def create_all_topics_and_subscriptions(connection_string: str, subscription_name: str = "sandbox-test") -> bool:
    """
    Create all required topics and subscriptions.
    
    Args:
        connection_string: Service Bus connection string
        subscription_name: Name of the subscription to create
        
    Returns:
        True if successful, False otherwise
    """
    topics = ["contacts", "vacancies"]
    success = True
    
    for topic in topics:
        if not create_topic_and_subscription(connection_string, topic, subscription_name):
            success = False
    
    return success

# Environment variables or configuration
SERVICE_BUS_CONNECTION_STRING = os.getenv('SERVICE_BUS_CONNECTION_STRING')

# Note: This module is deprecated. Use channel_setup.py instead.
# The automatic topic creation has been removed to prevent unwanted execution during imports.