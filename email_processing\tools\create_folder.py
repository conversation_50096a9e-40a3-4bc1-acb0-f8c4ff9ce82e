#!/usr/bin/env python3
"""
Create Mail Folder

One-time utility to create new mail folders.
Run this separately when you need to create folders for organization.
"""

import os
import sys

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from config import setup_logger_and_secrets
from azure_client import AzureEmailFetcher


def create_folder(folder_name, parent_folder_id=None):
    """Create a new mail folder"""
    print("📁 CREATING MAIL FOLDER")
    print("=" * 50)
    
    # Setup logger and load secrets
    logger = setup_logger_and_secrets()
    
    try:
        # Create email fetcher instance
        email_fetcher = AzureEmailFetcher(logger=logger)
        
        # Create folder
        if parent_folder_id:
            print(f"Creating folder '{folder_name}' under parent folder...")
            print(f"Parent ID: {parent_folder_id}")
        else:
            print(f"Creating folder '{folder_name}' at root level...")
        
        created_folder = email_fetcher.create_folder(
            folder_name=folder_name,
            mailbox="<EMAIL>",
            parent_folder_id=parent_folder_id
        )
        
        # Display result
        folder_id = created_folder.get('id')
        display_name = created_folder.get('displayName')
        
        print(f"\n✅ Success!")
        print(f"📂 Folder Name: {display_name}")
        print(f"🆔 Folder ID: {folder_id}")
        
        if parent_folder_id:
            print(f"📁 Parent ID: {parent_folder_id}")
        else:
            print(f"📁 Location: Root level")
        
        print(f"\n💡 Usage:")
        print(f"Use this folder ID to fetch emails from this folder:")
        print(f"  python3 ../main.py --folder-id {folder_id}")
        
        return created_folder
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all Azure credentials are set in the GPG secrets")
        print("2. Verify Azure App Registration has Mail.ReadWrite permissions")
        print("3. Check that the mailbox exists and is accessible")
        print("4. Verify parent folder ID is valid (if specified)")
        print("\nRun tools/test_credentials.py to verify setup")
        print("Run tools/list_folders.py to see available parent folders")
        sys.exit(1)


def print_help():
    """Print help information"""
    print("Create Mail Folder Tool")
    print("=" * 30)
    print("Usage:")
    print("  python3 create_folder.py '<folder_name>'")
    print("  python3 create_folder.py '<folder_name>' '<parent_folder_id>'")
    print()
    print("Examples:")
    print("  python3 create_folder.py 'Processed Resumes'")
    print("  python3 create_folder.py 'Archive' 'AQMkAGE3NwA5YTM4...'")
    print()
    print("Notes:")
    print("- Folder names with spaces should be quoted")
    print("- Use tools/list_folders.py to get parent folder IDs")
    print("- Duplicate folder names are automatically detected")


def main():
    """Main function"""
    print("Azure Email Fetcher - Folder Creation Tool")
    print("This is a one-time utility for creating folders.\n")
    
    if len(sys.argv) < 2:
        print("❌ Error: Folder name is required")
        print()
        print_help()
        sys.exit(1)
    
    folder_name = sys.argv[1]
    parent_folder_id = None
    
    if len(sys.argv) >= 3:
        parent_folder_id = sys.argv[2]
    
    if sys.argv[1] in ["--help", "-h", "help"]:
        print_help()
        return
    
    # Validate inputs
    if not folder_name.strip():
        print("❌ Error: Folder name cannot be empty")
        sys.exit(1)
    
    if parent_folder_id and not parent_folder_id.strip():
        print("❌ Error: Parent folder ID cannot be empty")
        sys.exit(1)
    
    # Create the folder
    create_folder(folder_name, parent_folder_id)


if __name__ == "__main__":
    main() 