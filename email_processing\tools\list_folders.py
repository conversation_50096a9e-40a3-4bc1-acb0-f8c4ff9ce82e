#!/usr/bin/env python3
"""
List Mail Folders

One-time utility to list all mail folders and their IDs.
Run this separately when you need to see available folders.
"""

import os
import sys

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from config import setup_logger_and_secrets
from azure_client import AzureEmailFetcher


def print_folders_summary(folders):
    """Print a formatted summary of the mail folders"""
    if not folders:
        print("📁 No folders found")
        return
    
    print(f"\n📁 ALL <NAME_EMAIL>")
    print("=" * 80)
    
    for i, folder in enumerate(folders, 1):
        folder_id = folder.get('id', 'Unknown')
        display_name = folder.get('displayName', 'Unknown')
        parent_folder_id = folder.get('parentFolderId', None)
        child_folder_count = folder.get('childFolderCount', 0)
        unread_count = folder.get('unreadItemCount', 0)
        total_count = folder.get('totalItemCount', 0)
        
        # Format output
        print(f"\n{i:2d}. 📂 {display_name}")
        print(f"    🆔 ID: {folder_id}")
        
        if parent_folder_id:
            print(f"    📁 Parent: {parent_folder_id}")
        else:
            print(f"    📁 Parent: Root folder")
        
        # Item counts
        print(f"    📊 Items: {total_count} total, {unread_count} unread")
        
        if child_folder_count > 0:
            print(f"    📂 Subfolders: {child_folder_count}")
    
    print("\n" + "=" * 80)
    print(f"✅ Total folders displayed: {len(folders)}")


def list_folders():
    """List all mail folders"""
    print("📁 LISTING MAIL FOLDERS")
    print("=" * 50)
    
    # Setup logger and load secrets
    logger = setup_logger_and_secrets()
    
    try:
        # Create email fetcher instance
        email_fetcher = AzureEmailFetcher(logger=logger)
        
        # Fetch folders
        print("Fetching <NAME_EMAIL>...")
        folders = email_fetcher.fetch_folders(
            mailbox="<EMAIL>"
        )
        
        # Print summary
        print_folders_summary(folders)
        
        print("\n💡 Usage Tips:")
        print("- Copy folder IDs to use with main.py --folder-id <id>")
        print("- Inbox ID is typically the longest ID")
        print("- Parent folder IDs can be used for creating subfolders")
        
        return folders
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all Azure credentials are set in the GPG secrets")
        print("2. Verify Azure App Registration has Mail.Read permissions")
        print("3. Check that the mailbox exists and is accessible")
        print("\nRun tools/test_credentials.py to verify setup")
        sys.exit(1)


def main():
    """Main function"""
    print("Azure Email Fetcher - Folder List Tool")
    print("This is a one-time utility for listing available folders.\n")
    
    folders = list_folders()
    
    print(f"\nFound {len(folders)} folders total.")
    print("\nNext steps:")
    print("- Use folder IDs with the main email fetcher")
    print("- Create new folders with tools/create_folder.py")


if __name__ == "__main__":
    main() 