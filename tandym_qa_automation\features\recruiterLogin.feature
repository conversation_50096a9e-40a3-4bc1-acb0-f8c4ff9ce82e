Feature: Validate recruiter login functionality of homePage

@tc:4400 @automation @recruiterPortal
Scenario: Access the portal with a valid regression key
When user generates a unique UUID as the regression key
And encrypts the key using the cryptographic library
And inserts the encrypted key into the database with a 30-minute expiry and is_key_allowed set to true
# And opens the recruiter portal URL with the regression_key as a query parameter
# Then the user should be navigated to the portal without AD authentication

