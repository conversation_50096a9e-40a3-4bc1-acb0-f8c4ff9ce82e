from behave import given, when, then
from features.pages.home_page import user_is_on_homepage, user_sees_welcome_message, user_sees_message_below_welcome, about_recruitment_portal, user_clicks_named_button,user_redirected_to_page
from features.pages.home_page import section_in_homepage, section_message_in_homepage, user_clicks_section_named_button

@given('user is on the HomePage of RecruiterPortal')
def step_user_on_homepage(context):
    user_is_on_homepage(context)

@then('user should see the welcome message')
def step_user_sees_welcome_message(context):
    user_sees_welcome_message(context)

@then(u'verify user should see the message below the welcome message')
def step_verify_user_sees_message_below_welcome(context):
    user_sees_message_below_welcome(context)

@then(u'verify user should see the About Recruitment Portal section')
def step_verify_about_recruitment_portal(context):
    about_recruitment_portal(context)

@when('user clicks on "{button_name}"')
def step_user_clicks_named_button(context, button_name):
    user_clicks_named_button(context, button_name)

@then('user should be redirected to the "{page_name}" page')
def step_user_redirected_to_page(context, page_name):
    user_redirected_to_page(context, page_name)

@then(u'verify user should see the "{section}" section in HomePage')
def step_verify_section_in_homepage(context, section):
    section_in_homepage(context, section)

@then(u'verify user should see the "{section}" section message')
def step_verify_section_message_in_homepage(context, section):
    section_message_in_homepage(context, section)

@when('user clicks on "{section}" option')
def step_user_clicks_section_option(context, section):
    user_clicks_section_named_button(context, section)
