from selenium.webdriver.common.by import By
from features.utils.helpers import CommonHelper


class HomePage:
    WELCOME_MESSAGE = (By.XPATH, "(//p[contains(text(), 'Welcome')])[2]")
    Below_WelcmMsg = (By.XPATH, "(//p[contains(text(), 'Welcome')]//following-sibling::p[1])[2]")
    aboutRecruitmentPortal = (By.XPATH, "//p[contains(text(), 'About Recruitment Portal')]")
    afterNavigationToSubCategoryLibrary = (By.XPATH, "//label[contains(text(), 'Select Subcategory:')]")
    afterNavigationToWorkforceReadinessIndex = (By.XPATH, "//h2[normalize-space()='Workforce Readiness Index']")
    afterNavigationToVacancy = (By.XPATH, "//h2[normalize-space()='Vacancies Portal']")
    subCategoryLibrarySection = (By.XPATH, "//p[contains(text(), 'Sub-Category Library') and contains(@class, 'text-fluid')]")
    workForceIndexSection = (By.XPATH, "//p[contains(text(), 'Workforce Readiness Index') and contains(@class, 'text-fluid')]")
    vacancySection = (By.XPATH, "//p[contains(text(), 'Vacancy') and contains(@class, 'leading-none')]")
    subCategoryLibrarySectionButton = (By.XPATH, "//button[contains(text(), 'Sub-Category Library')]")
    workForceIndexSectionButton = (By.XPATH, "//button[contains(text(), 'Workforce Readiness Index')]")
    vacancySectionButton = (By.XPATH, "//button[contains(text(), 'Vacancy')]")

    def __init__(self, driver):
        self.driver = driver
        self.helper = CommonHelper(driver)

    def verify_user_is_on_homepage(self, timeout=10):
        if not self.helper.wait_for_element_visible(self.WELCOME_MESSAGE, timeout):
            raise AssertionError("Homepage not loaded properly")
        print("************* Welcome message is displayed. *************")

    def user_sees_welcome_message(self):
        self.verify_user_is_on_homepage()

    def verify_user_sees_message_below_welcome(self):
        element = self.helper.wait_for_element_visible(self.Below_WelcmMsg)
        if not element:
            raise AssertionError("Message below welcome not visible")

        expected_text = "Find the Right Fit, Fast."
        actual_text = element.text.strip()
        assert actual_text == expected_text, f"Expected '{expected_text}', but got '{actual_text}'"
        print("************* Message below welcome is displayed. *************")

    def verify_about_recruitment_portal(self):
        element = self.helper.wait_for_element_visible(self.aboutRecruitmentPortal)
        if not element:
            raise AssertionError("About Recruitment Portal section not found")
        print("************* About Recruitment Portal section is displayed. *************")

    def verify_user_clicks_named_button(self, button_name, timeout=10):
        match button_name:
            case "Sub-Category Library":
                locator = (By.XPATH, "//button//p[contains(text(), 'Sub-Category Library')]")
            case "Workforce Readiness Index":
                locator = (By.XPATH, "//button[normalize-space()='Workforce Readiness Index']")
            case "Vacancy":
                locator = (By.XPATH, "//button[normalize-space()='Vacancy']")
            case "Home":
                locator = (By.XPATH, "//button[normalize-space()='Home']")
            case _:
                raise ValueError(f"No element mapped for: {button_name}")

        self.helper.click_element(locator)
        print(f"************* Clicked on '{button_name}' *************")

    def verify_user_redirected_to_page(self, page_name, timeout=10):
        match page_name:
            case "Sub-Category Library":
                locator = self.afterNavigationToSubCategoryLibrary
                expected_text = "Select Subcategory:"
            case "Workforce Readiness Index":
                locator = self.afterNavigationToWorkforceReadinessIndex
                expected_text = "Workforce Readiness Index"
            case "Vacancy":
                locator = self.afterNavigationToVacancy
                expected_text = "Vacancies Portal"
            case "Home":
                locator = self.WELCOME_MESSAGE
                expected_text = "Welcome"
            case _:
                raise ValueError(f"No locator mapped for: {page_name}")

        element = self.helper.wait_for_element_visible(locator, timeout)
        if not element:
            raise AssertionError(f"Page verification failed for: {page_name}")

        actual_text = element.text.strip()
        assert expected_text in actual_text, f"Expected '{expected_text}', but got '{actual_text}'"
        print(f"************* Redirected to '{page_name}' page and verified expected content. *************")

    def verify_section_in_homepage(self, section, timeout=10):
        match section:
            case "Sub-Category Library":
                locator = self.subCategoryLibrarySection
                expected_text = "Sub-Category Library"
            case "Workforce Readiness Index":
                locator = self.workForceIndexSection
                expected_text = "Workforce Readiness Index"
            case "Vacancy":
                locator = self.vacancySection
                expected_text = "Vacancy"
            case _:
                raise ValueError(f"No section mapped for: {section}")

        element = self.helper.wait_for_element_visible(locator, timeout)
        if not element:
            raise AssertionError(f"Section verification failed for: {section}")

        actual_text = element.text.strip()
        assert expected_text in actual_text, f"Expected '{expected_text}', but got '{actual_text}'"
        print(f"************* Verified '{section}' section in HomePage. *************")

    def verify_section_message_in_homepage(self, section, timeout=10):
        match section:
            case "Sub-Category Library":
                base_locator = self.subCategoryLibrarySection
            case "Workforce Readiness Index":
                base_locator = self.workForceIndexSection
            case "Vacancy":
                base_locator = self.vacancySection
            case _:
                raise ValueError(f"No section message mapped for: {section}")

        base_element = self.helper.wait_for_element_visible(base_locator, timeout)
        if not base_element:
            raise AssertionError(f"Base element not found for section: {section}")

        sibling_element = base_element.find_element(By.XPATH, "./following-sibling::p")
        if not sibling_element.is_displayed():
            raise AssertionError(f"Section message verification failed for: {section}")

        print(f"************* Verified '{section}' section message in HomePage. *************")

    def verify_user_clicks_section_named_button(self, section, timeout=10):
        match section:
            case "Sub-Category Library":
                locator = self.subCategoryLibrarySectionButton
            case "Workforce Readiness Index":
                locator = self.workForceIndexSectionButton
            case "Vacancy":
                locator = self.vacancySectionButton
            case _:
                raise ValueError(f"No section button mapped for: {section}")

        self.helper.click_element(locator)
        self.helper.wait_until_loader_completes()
        print(f"************* Clicked on '{section}' section button *************")


#  Declare test-facing functions here, not as wrappers but as the actual test-facing API

def user_is_on_homepage(context):
    HomePage(context.driver).verify_user_is_on_homepage()

def user_sees_welcome_message(context):
    HomePage(context.driver).user_sees_welcome_message()

def user_sees_message_below_welcome(context):
    HomePage(context.driver).verify_user_sees_message_below_welcome()

def about_recruitment_portal(context):
    HomePage(context.driver).verify_about_recruitment_portal()

def user_clicks_named_button(context, button_name):
    HomePage(context.driver).verify_user_clicks_named_button(button_name)

def user_redirected_to_page(context, page_name):
    HomePage(context.driver).verify_user_redirected_to_page(page_name)

def section_in_homepage(context, section):
    HomePage(context.driver).verify_section_in_homepage(section)

def section_message_in_homepage(context, section):
    HomePage(context.driver).verify_section_message_in_homepage(section)

def user_clicks_section_named_button(context, section):
    HomePage(context.driver).verify_user_clicks_section_named_button(section)
