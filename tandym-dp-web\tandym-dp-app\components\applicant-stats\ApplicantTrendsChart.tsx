"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TrendingUp, Users, Building2 } from "lucide-react";
import {
  ApplicantTrendsResponse,
  formatDate,
  formatNumber,
} from "@/app/stats/applicants/helper";

interface ApplicantTrendsChartProps {
  trendsData: ApplicantTrendsResponse;
}

const ApplicantTrendsChart: React.FC<ApplicantTrendsChartProps> = ({
  trendsData,
}) => {
  if (!trendsData.trends || trendsData.trends.length === 0) {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Applicant Trends
          </CardTitle>
          <div className="text-sm text-gray-500">
            No trend data available for the selected time range
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-12 text-gray-500">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No trend data available
              </h3>
              <p className="text-sm text-gray-600">
                No applicant trends were recorded for the selected time range.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const trends = trendsData.trends;

  // Helper function to get weekday name
  const getWeekdayName = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { weekday: "short" });
  };

  // Helper function to check if date is weekend
  const isWeekend = (dateString: string) => {
    const date = new Date(dateString);
    return date.getDay() === 0 || date.getDay() === 6;
  };

  // Calculate weekday statistics
  const weekdayStats = trends.reduce((acc, trend) => {
    const weekday = getWeekdayName(trend.date);
    if (!acc[weekday]) {
      acc[weekday] = { applicant_count: 0, vacancy_count: 0, days: 0 };
    }
    acc[weekday].applicant_count += trend.applicant_count;
    acc[weekday].vacancy_count += trend.vacancy_count;
    acc[weekday].days += 1;
    return acc;
  }, {} as Record<string, { applicant_count: number; vacancy_count: number; days: number }>);

  // Calculate averages per weekday
  const weekdayAverages = Object.entries(weekdayStats)
    .map(([weekday, stats]) => ({
      weekday,
      avg_applicants: stats.days > 0 ? stats.applicant_count / stats.days : 0,
      avg_vacancies: stats.days > 0 ? stats.vacancy_count / stats.days : 0,
      total_applicants: stats.applicant_count,
      total_vacancies: stats.vacancy_count,
      days: stats.days,
    }))
    .sort((a, b) => {
      // Sort by weekday order (Mon, Tue, Wed, Thu, Fri, Sat, Sun)
      const weekdayOrder = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
      return weekdayOrder.indexOf(a.weekday) - weekdayOrder.indexOf(b.weekday);
    });
  const chartWidth = 1000;
  const chartHeight = 450;
  const margin = { top: 20, right: 50, bottom: 80, left: 60 };
  const innerWidth = chartWidth - margin.left - margin.right;
  const innerHeight = chartHeight - margin.top - margin.bottom;

  // Find min and max values for scaling
  const maxApplicants = Math.max(...trends.map((t) => t.applicant_count));
  const maxVacancies = Math.max(...trends.map((t) => t.vacancy_count));
  const maxValue = Math.max(maxApplicants, maxVacancies);

  // Scale functions
  const xScale = (index: number) =>
    margin.left + (index / (trends.length - 1)) * innerWidth;
  const yScale = (value: number) =>
    margin.top + innerHeight - (value / maxValue) * innerHeight;

  // Generate path for applicants line
  const applicantPath = trends
    .map((trend, index) => {
      const x = xScale(index);
      const y = yScale(trend.applicant_count);
      return `${index === 0 ? "M" : "L"} ${x} ${y}`;
    })
    .join(" ");

  // Generate path for vacancies line
  const vacancyPath = trends
    .map((trend, index) => {
      const x = xScale(index);
      const y = yScale(trend.vacancy_count);
      return `${index === 0 ? "M" : "L"} ${x} ${y}`;
    })
    .join(" ");

  // Generate grid lines
  const gridLines = [];
  const numGridLines = 5;
  for (let i = 0; i <= numGridLines; i++) {
    const y = margin.top + (i / numGridLines) * innerHeight;
    const value = maxValue - (i / numGridLines) * maxValue;
    gridLines.push({ y, value });
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Applicant Trends
        </CardTitle>
        <div className="text-sm text-gray-500">
          Average {trendsData.average_applicants_per_day.toFixed(2)} applicants
          per day
        </div>
        <div className="text-sm text-gray-600 mt-2 font-medium">
          <span className="text-blue-600 font-bold">●</span> Daily applicants •{" "}
          <span className="text-green-600 font-bold">●</span> Daily vacancies •{" "}
          <span className="text-red-600 font-bold">●</span> Weekends
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <svg width={chartWidth} height={chartHeight} className="mx-auto">
            {/* Grid lines */}
            {gridLines.map((line, index) => (
              <g key={index}>
                <line
                  x1={margin.left}
                  y1={line.y}
                  x2={chartWidth - margin.right}
                  y2={line.y}
                  stroke="#e5e7eb"
                  strokeWidth="1"
                />
                <text
                  x={margin.left - 10}
                  y={line.y + 4}
                  textAnchor="end"
                  fontSize="12"
                  fill="#6b7280"
                >
                  {Math.round(line.value)}
                </text>
              </g>
            ))}

            {/* X-axis */}
            <line
              x1={margin.left}
              y1={chartHeight - margin.bottom}
              x2={chartWidth - margin.right}
              y2={chartHeight - margin.bottom}
              stroke="#374151"
              strokeWidth="2"
            />

            {/* Y-axis */}
            <line
              x1={margin.left}
              y1={margin.top}
              x2={margin.left}
              y2={chartHeight - margin.bottom}
              stroke="#374151"
              strokeWidth="2"
            />

            {/* X-axis labels */}
            {trends.map((trend, index) => {
              const x = xScale(index);
              const shouldShow =
                index % Math.max(1, Math.floor(trends.length / 8)) === 0 ||
                index === trends.length - 1;
              if (!shouldShow) return null;

              const weekday = getWeekdayName(trend.date);
              const weekend = isWeekend(trend.date);

              return (
                <g key={index}>
                  {/* Date label */}
                  <text
                    x={x}
                    y={chartHeight - margin.bottom + 20}
                    textAnchor="middle"
                    fontSize="11"
                    fill={weekend ? "#ef4444" : "#6b7280"}
                    transform={`rotate(-45 ${x} ${
                      chartHeight - margin.bottom + 20
                    })`}
                  >
                    {formatDate(trend.date)}
                  </text>
                  {/* Weekday label */}
                  <text
                    x={x}
                    y={chartHeight - margin.bottom + 35}
                    textAnchor="middle"
                    fontSize="10"
                    fill={weekend ? "#ef4444" : "#9ca3af"}
                    fontWeight={weekend ? "600" : "400"}
                  >
                    {weekday}
                  </text>
                </g>
              );
            })}

            {/* Applicant line */}
            <path
              d={applicantPath}
              fill="none"
              stroke="#3b82f6"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Vacancy line */}
            <path
              d={vacancyPath}
              fill="none"
              stroke="#10b981"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Data points for applicants */}
            {trends.map((trend, index) => (
              <circle
                key={`applicant-${index}`}
                cx={xScale(index)}
                cy={yScale(trend.applicant_count)}
                r="4"
                fill="#3b82f6"
                stroke="white"
                strokeWidth="2"
              />
            ))}

            {/* Data points for vacancies */}
            {trends.map((trend, index) => (
              <circle
                key={`vacancy-${index}`}
                cx={xScale(index)}
                cy={yScale(trend.vacancy_count)}
                r="4"
                fill="#10b981"
                stroke="white"
                strokeWidth="2"
              />
            ))}

            {/* Legend */}
            <g
              transform={`translate(${chartWidth - margin.right - 180}, ${
                margin.top
              })`}
            >
              <rect
                width="180"
                height="80"
                fill="white"
                stroke="#e5e7eb"
                rx="4"
              />
              <line
                x1="15"
                y1="25"
                x2="35"
                y2="25"
                stroke="#3b82f6"
                strokeWidth="3"
              />
              <circle
                cx="25"
                cy="25"
                r="4"
                fill="#3b82f6"
                stroke="white"
                strokeWidth="2"
              />
              <text x="45" y="30" fontSize="12" fill="#374151">
                <tspan x="45" dy="0">
                  Daily Applicants
                </tspan>
                <tspan x="45" dy="16" fontSize="10" fill="#6b7280">
                  {formatNumber(trendsData.total_applicants)} total
                </tspan>
              </text>
              <line
                x1="15"
                y1="55"
                x2="35"
                y2="55"
                stroke="#10b981"
                strokeWidth="3"
              />
              <circle
                cx="25"
                cy="55"
                r="4"
                fill="#10b981"
                stroke="white"
                strokeWidth="2"
              />
              <text x="45" y="60" fontSize="12" fill="#374151">
                <tspan x="45" dy="0">
                  Vacancies w/ Apps
                </tspan>
                <tspan x="45" dy="16" fontSize="10" fill="#6b7280">
                  {formatNumber(trendsData.total_vacancies)} unique
                </tspan>
              </text>
            </g>
          </svg>
        </div>

        {/* Summary stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <Users className="h-5 w-5 text-blue-600" />
            <div>
              <div className="font-semibold text-blue-900">
                {formatNumber(trendsData.total_applicants)}
              </div>
              <div className="text-sm text-blue-600">Total Applicants</div>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
            <Building2 className="h-5 w-5 text-green-600" />
            <div>
              <div className="font-semibold text-green-900">
                {formatNumber(trendsData.total_vacancies)}
              </div>
              <div className="text-sm text-green-600">Unique Vacancies</div>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <TrendingUp className="h-5 w-5 text-gray-600" />
            <div>
              <div className="font-semibold text-gray-900">
                {trendsData.average_applicants_per_day.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">Avg/Day</div>
            </div>
          </div>
        </div>

        {/* Weekday Analysis */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Weekday Patterns
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-7 gap-2">
            {weekdayAverages.map((day) => {
              const isWeekend = day.weekday === "Sat" || day.weekday === "Sun";
              return (
                <div
                  key={day.weekday}
                  className={`p-3 rounded-lg text-center ${
                    isWeekend
                      ? "bg-red-50 border border-red-200"
                      : "bg-gray-50 border border-gray-200"
                  }`}
                >
                  <div
                    className={`text-xs font-medium ${
                      isWeekend ? "text-red-700" : "text-gray-700"
                    }`}
                  >
                    {day.weekday}
                  </div>
                  <div className="text-lg font-bold text-gray-900 mt-1">
                    {day.avg_applicants.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-500">avg applicants</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {day.days} days
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApplicantTrendsChart;
