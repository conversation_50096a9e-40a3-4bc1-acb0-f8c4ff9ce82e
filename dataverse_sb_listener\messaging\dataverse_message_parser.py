#!/usr/bin/env python3
"""
Message parser for Dataverse entity updates.

This module handles parsing of Service Bus messages containing Dataverse entity updates,
including Microsoft .NET JSON date format parsing.
"""

import sys
import os
import re
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Union

# Add project root to Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger

class DataverseMessageParser:
    """
    Utility class for parsing Dataverse webhook messages from Service Bus.
    Handles date parsing, message structure, and provides common functionality.
    """
    
    def __init__(self, logger: Optional[AppLogger] = None):
        self.logger = logger or AppLogger({"log_level": "INFO", "log_to_stdout": True, "log_mode": "append"})
        
    def parse_service_bus_message(self, msg) -> Optional[Dict[str, Any]]:
        """
        Parse a Service Bus message containing Dataverse webhook data.
        
        Args:
            msg: ServiceBusReceivedMessage object
            
        Returns:
            Parsed message data or None if parsing fails
        """
        try:
            # Log message type and properties for debugging
            self.logger.info(f"Message type: {type(msg)}")
            self.logger.info(f"Message properties: {getattr(msg, 'application_properties', {})}")
            
            # Extract message body properly
            if hasattr(msg, "body"):
                # ServiceBusReceivedMessage body is already bytes, no need to join
                if isinstance(msg.body, bytes):
                    body_bytes = msg.body
                elif hasattr(msg.body, '__iter__'):
                    # If body is an iterable of bytes, join them
                    body_bytes = b"".join(msg.body)
                else:
                    # Fallback: convert to string and encode
                    body_bytes = str(msg.body).encode('utf-8')
                
                # Try to decode as UTF-8
                try:
                    body_str = body_bytes.decode('utf-8')
                    self.logger.info(f"Raw message body: {body_str[:500]}...")  # Log first 500 chars
                    
                    # Try to parse as JSON
                    data = json.loads(body_str)
                    
                except UnicodeDecodeError as e:
                    self.logger.error(f"Failed to decode message body as UTF-8: {e}")
                    # Try other encodings
                    for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                        try:
                            body_str = body_bytes.decode(encoding)
                            data = json.loads(body_str)
                            self.logger.info(f"Successfully decoded using {encoding}")
                            break
                        except (UnicodeDecodeError, json.JSONDecodeError):
                            continue
                    else:
                        raise Exception(f"Failed to decode message body with any encoding")
                        
            else:
                # Fallback for non-ServiceBusReceivedMessage objects
                self.logger.warning("Message doesn't have 'body' attribute, trying string conversion")
                data = json.loads(str(msg))
            
            return data
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON: {e}")
            self.logger.error(f"Raw body (first 1000 chars): {body_str[:1000] if 'body_str' in locals() else 'Unknown'}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to parse message: {e}")
            self.logger.error(f"Message object: {msg}")
            self.logger.error(f"Message attributes: {getattr(msg, '__dict__', {})}")
            return None
    
    def parse_dotnet_date(self, date_str: str) -> Optional[datetime]:
        """
        Parse Microsoft .NET JSON date format: /Date(timestamp+offset)/
        
        Args:
            date_str: Date string in format /Date(timestamp+offset)/
            
        Returns:
            datetime object or None if parsing fails
        """
        if not date_str or date_str == "None":
            return None
            
        try:
            # Handle .NET date format: /Date(1756512000000+0000)/
            match = re.match(r'/Date\((\d+)([+-]\d{4})?\)/', date_str)
            if match:
                timestamp_ms = int(match.group(1))
                offset_str = match.group(2) if match.group(2) else "+0000"
                
                # Convert milliseconds to seconds
                timestamp_s = timestamp_ms / 1000
                
                # Create datetime object (timestamp is in UTC)
                dt = datetime.fromtimestamp(timestamp_s, tz=timezone.utc)
                
                # Apply offset if provided
                if offset_str:
                    offset_hours = int(offset_str[1:3])
                    offset_minutes = int(offset_str[3:5])
                    offset_sign = -1 if offset_str[0] == '-' else 1
                    offset_seconds = offset_sign * (offset_hours * 3600 + offset_minutes * 60)
                    
                    # Adjust for timezone offset
                    dt = dt.replace(tzinfo=timezone.utc).astimezone(
                        timezone(timedelta(seconds=offset_seconds))
                    )
                
                return dt
            
            # Try ISO 8601 format as fallback
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            
        except (ValueError, TypeError) as e:
            self.logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None
    
    def extract_entity_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract key entity information from parsed message data.
        
        Args:
            data: Parsed message data
            
        Returns:
            Dictionary containing entity information
        """
        entity_info = {
            'entity_name': data.get('PrimaryEntityName'),
            'entity_id': data.get('PrimaryEntityId'),
            'message_type': data.get('MessageName'),
            'correlation_id': data.get('CorrelationId'),
            'request_id': data.get('RequestId'),
            'stage': data.get('Stage'),
            'attributes': {},
            'post_entity_images': {},
            'pre_entity_images': {}
        }
        
        # Extract attributes from InputParameters
        input_params = data.get('InputParameters', [])
        for param in input_params:
            if isinstance(param, dict) and param.get('key') == 'Target':
                target = param.get('value', {})
                if isinstance(target, dict):
                    attributes = target.get('Attributes', [])
                    for attr in attributes:
                        if isinstance(attr, dict) and 'key' in attr and 'value' in attr:
                            attr_key = attr['key']
                            attr_value = attr['value']
                            
                            # Parse dates if the value looks like a date
                            if isinstance(attr_value, str) and '/Date(' in attr_value:
                                parsed_date = self.parse_dotnet_date(attr_value)
                                entity_info['attributes'][attr_key] = {
                                    'raw_value': attr_value,
                                    'parsed_date': parsed_date,
                                    'type': 'date'
                                }
                            else:
                                entity_info['attributes'][attr_key] = {
                                    'raw_value': attr_value,
                                    'type': 'other'
                                }
        
        # Extract PostEntityImages
        post_images = data.get('PostEntityImages', [])
        for img in post_images:
            if isinstance(img, dict) and 'key' in img and 'value' in img:
                img_key = img['key']
                img_value = img['value']
                if isinstance(img_value, dict):
                    entity_info['post_entity_images'][img_key] = {
                        'logical_name': img_value.get('LogicalName'),
                        'id': img_value.get('Id'),
                        'attributes': {}
                    }
                    # Extract attributes from post entity image
                    img_attrs = img_value.get('Attributes', [])
                    for attr in img_attrs:
                        if isinstance(attr, dict) and 'key' in attr and 'value' in attr:
                            attr_key = attr['key']
                            attr_value = attr['value']
                            if isinstance(attr_value, str) and '/Date(' in attr_value:
                                parsed_date = self.parse_dotnet_date(attr_value)
                                entity_info['post_entity_images'][img_key]['attributes'][attr_key] = {
                                    'raw_value': attr_value,
                                    'parsed_date': parsed_date,
                                    'type': 'date'
                                }
                            else:
                                entity_info['post_entity_images'][img_key]['attributes'][attr_key] = {
                                    'raw_value': attr_value,
                                    'type': 'other'
                                }
        
        return entity_info
    
    def log_entity_info(self, entity_info: Dict[str, Any]) -> None:
        """
        Log entity information in a structured format.
        
        Args:
            entity_info: Entity information dictionary
        """
        self.logger.info("-" * 40)
        self.logger.info(f"Entity: {entity_info['entity_name']}")
        self.logger.info(f"Entity ID: {entity_info['entity_id']}")
        self.logger.info(f"Message Type: {entity_info['message_type']}")
        self.logger.info(f"Correlation ID: {entity_info['correlation_id']}")
        self.logger.info(f"Request ID: {entity_info['request_id']}")
        self.logger.info(f"Stage: {entity_info['stage']}")
        
        # Log attributes
        if entity_info['attributes']:
            self.logger.info(f"Attributes ({len(entity_info['attributes'])} found):")
            for attr_key, attr_info in entity_info['attributes'].items():
                if attr_info['type'] == 'date':
                    self.logger.info(f"  {attr_key}: {attr_info['raw_value']} -> {attr_info['parsed_date']}")
                else:
                    self.logger.info(f"  {attr_key}: {attr_info['raw_value']}")
        
        # Log post entity images
        if entity_info['post_entity_images']:
            self.logger.info(f"Post Entity Images ({len(entity_info['post_entity_images'])} found):")
            for img_key, img_info in entity_info['post_entity_images'].items():
                self.logger.info(f"  {img_key}: {img_info['logical_name']} ({img_info['id']})")
                if img_info['attributes']:
                    for attr_key, attr_info in img_info['attributes'].items():
                        if attr_info['type'] == 'date':
                            self.logger.info(f"    {attr_key}: {attr_info['raw_value']} -> {attr_info['parsed_date']}")
                        else:
                            self.logger.info(f"    {attr_key}: {attr_info['raw_value']}")
        
        self.logger.info("-" * 40)
    
    def process_message(self, msg) -> Optional[Dict[str, Any]]:
        """
        Complete message processing pipeline.
        
        Args:
            msg: ServiceBusReceivedMessage object
            
        Returns:
            Processed entity information or None if processing fails
        """
        # Parse the message
        data = self.parse_service_bus_message(msg)
        if not data:
            return None
        
        # Extract entity information
        entity_info = self.extract_entity_info(data)
        
        # Log the information
        self.log_entity_info(entity_info)
        
        return entity_info 