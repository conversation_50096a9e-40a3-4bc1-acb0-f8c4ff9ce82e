import json
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, Mock,PropertyMock
from dp_portal.services.historical_service import HistoricalService  # replace with actual import


@pytest.fixture
def service():
    mock_logger = Mock()
    mock_db_connector = Mock()
    return HistoricalService(logger=mock_logger, db_connector=mock_db_connector)

def mock_connection_with_cursor(exec_side_effect=None, fetch_return=None):
    mock_cursor = MagicMock()
    mock_cursor.__enter__.return_value = mock_cursor
    mock_cursor.__exit__.return_value = None
    if exec_side_effect:
        mock_cursor.execute.side_effect = exec_side_effect

    mock_conn = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_conn.closed = 0

    return mock_conn, mock_cursor


def test_insert_historical_log_success(service):
    mock_conn, mock_cursor = mock_connection_with_cursor()
    service.db = Mock(connection=mock_conn)
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result == {
        "error": False,
        "code": "TR_01",
        "message": "Historical log inserted successfully"
    }
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_called_once()


# def test_insert_historical_log_connection_none(service):
#     service.db = Mock(connection=None)
#     service.logger = Mock()

#     result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

#     assert result["error"] is True
#     assert result["code"] == "TR_ERR"
#     assert "Database connection is not available" in result["message"]

def test_insert_historical_log_connection_none(service):
    mock_db = Mock()
    type(mock_db).connection = PropertyMock(return_value=None)
    service.db = mock_db
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result["error"] is True
    assert result["code"] == "TR_ERR"


def test_insert_historical_log_connection_closed(service):
    mock_conn = Mock(closed=1)
    service.db = Mock(connection=mock_conn)
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result["error"] is True
    assert result["code"] == "TR_ERR"
    assert "Database connection is not available" in result["message"]


def test_insert_historical_log_db_insert_exception(service):
    insert_error = Exception("DB insert failed")
    mock_conn, mock_cursor = mock_connection_with_cursor(exec_side_effect=insert_error)
    service.db = Mock(connection=mock_conn)
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result["error"] is True
    assert result["code"] == "TR_DB_ERR"
    assert "DB insert failed" in result["message"]
    mock_conn.rollback.assert_called_once()
    service.logger.error.assert_called()


def test_insert_historical_log_outer_exception_with_rollback(service):
    # Simulate unexpected error like conn.cursor raising error
    service.db = Mock()
    service.db.connection = MagicMock()
    service.db.connection.closed = 0
    service.db.connection.cursor.side_effect = Exception("Outer error")
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result["error"] is True
    assert result["code"] == "TR_DB_ERR"
    assert "Outer error" in result["message"]
    service.logger.error.assert_called()
    service.db.connection.rollback.assert_called_once()


def test_insert_historical_log_outer_exception_and_rollback_fail(service):
    # Rollback itself fails, should still return TR_ERR gracefully
    broken_conn = MagicMock()
    broken_conn.closed = 0
    broken_conn.cursor.side_effect = Exception("DB cursor fails")
    broken_conn.rollback.side_effect = Exception("Rollback fails")

    service.db = Mock(connection=broken_conn)
    service.logger = Mock()

    result = service.insert_historical_log("<EMAIL>", "portal", "test_feature", {"key": "value"})

    assert result["error"] is True
    assert result["code"] == "TR_ERR"
    service.logger.error.assert_called()
