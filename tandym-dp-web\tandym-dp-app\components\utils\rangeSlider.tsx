import React, { useRef } from "react";
import { Input } from "@/components/ui/input";

interface RangeSliderProps {
  label: string;
  min: number;
  max: number;
  step?: number;
  value: [number, number];
  onChange: (value: [number, number]) => void;
  unit?: string;
  decimal?: boolean;
  className?: string;
}

export const RangeSlider: React.FC<RangeSliderProps> = ({
  label,
  min,
  max,
  step = 1,
  value,
  onChange,
  unit = "",
  decimal = false,
  className = "",
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  // always up to date value for drag
  const valueRef = useRef<[number, number]>(value);
  valueRef.current = value;

  const percent = (val: number) => ((val - min) / (max - min)) * 100;

  const pxToValue = (px: number) => {
    const width = containerRef.current?.getBoundingClientRect().width || 1;
    let percentX = Math.max(0, Math.min(1, px / width));
    let newValue = min + (max - min) * percentX;
    // For decimal, round to nearest step (0.01 etc), else integer step
    if (decimal) {
      newValue = Math.round(newValue / step) * step;
      return Number(newValue.toFixed(2));
    } else {
      newValue = Math.round(newValue / step) * step;
      return Math.round(newValue);
    }
  };

  // Drag Handler
  const handleDrag =
    (handleIdx: 0 | 1) => (e: React.MouseEvent<HTMLDivElement>) => {
      e.preventDefault();
      const container = containerRef.current;
      if (!container) return;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        const rect = container.getBoundingClientRect();
        const x = moveEvent.clientX - rect.left;
        let newValue = pxToValue(x);

        let [currentMin, currentMax] = valueRef.current;

        if (handleIdx === 0) {
          // dragging min
          newValue = Math.min(newValue, currentMax - step); // Make sure min < max
          newValue = Math.max(newValue, min);
          onChange([newValue, currentMax]);
        } else {
          // dragging max
          newValue = Math.max(newValue, currentMin + step); // Make sure max > min
          newValue = Math.min(newValue, max);
          onChange([currentMin, newValue]);
        }
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

  return (
    <div className={`p-2 w-[95%] ${className}`}>
      <p>{label}</p>
      <div className="px-4 py-2">
        <div className="flex gap-2 items-center mb-2">
          <span className="text-sm">Min:</span>
          <Input
            type="number"
            min={min}
            max={max}
            step={step}
            value={decimal ? value?.[0].toFixed(2) : value?.[0]}
            onChange={(e) => {
              let val = decimal
                ? parseFloat(e.target.value)
                : parseInt(e.target.value, 10);
              if (isNaN(val)) val = min;
              // Clamp so min <= max-step
              val = Math.min(val, value?.[1] - step);
              val = Math.max(min, val);
              if (val !== value?.[0]) onChange([val, value?.[1]]);
            }}
            className="w-20 border border-gray-300 rounded px-2 py-1 text-sm"
          />
          <span className="text-sm">Max:</span>
          <Input
            type="number"
            min={min}
            max={max}
            step={step}
            value={decimal ? value?.[1].toFixed(2) : value?.[1]}
            onChange={(e) => {
              let val = decimal
                ? parseFloat(e.target.value)
                : parseInt(e.target.value, 10);
              if (isNaN(val)) val = max;
              // Clamp so max >= min+step
              val = Math.max(val, value?.[0] + step);
              val = Math.min(max, val);
              if (val !== value?.[1]) onChange([value?.[0], val]);
            }}
            className="w-20 border border-gray-300 rounded px-2 py-1 text-sm"
          />
        </div>
        {/* Slider UI */}
        <div className="relative h-6 bg-gray-200 rounded-lg" ref={containerRef}>
          <div
            className="absolute h-2 bg-blue-500 rounded-lg pointer-events-none"
            style={{
              left: `${percent(value?.[0])}%`,
              width: `${Math.max(
                percent(value?.[1]) - percent(value?.[0]),
                0
              )}%`,
              top: "50%",
              transform: "translateY(-50%)",
            }}
          ></div>

          {/* Min handle */}
          <div
            className="absolute w-4 h-4 bg-white border-2 border-blue-500 rounded-full cursor-pointer z-10"
            style={{
              left: `calc(${percent(value?.[0])}% - 8px)`,
              top: "50%",
              transform: "translateY(-50%)",
              // Always allow pointer events for min handle
              pointerEvents: "auto",
            }}
            onMouseDown={handleDrag(0)}
            tabIndex={0}
            aria-label="Minimum value"
          ></div>
          {/* Max handle */}
          <div
            className="absolute w-4 h-4 bg-white border-2 border-blue-500 rounded-full cursor-pointer z-10"
            style={{
              left: `calc(${percent(value?.[1])}% - 8px)`,
              top: "50%",
              transform: "translateY(-50%)",
              pointerEvents: value?.[1] <= value?.[0] + step ? "none" : "auto",
            }}
            onMouseDown={
              value?.[1] > value?.[0] + step ? handleDrag(1) : undefined
            }
            tabIndex={0}
            aria-label="Maximum value"
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-2">
          <span>
            {decimal ? value?.[0].toFixed(2) : value?.[0]} {unit}
          </span>
          <span>
            {decimal ? value?.[1].toFixed(2) : value?.[1]} {unit}
          </span>
        </div>
      </div>
    </div>
  );
};
