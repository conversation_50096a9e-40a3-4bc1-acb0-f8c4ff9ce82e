// api/serverActions.ts
"use server";
import "server-only";
import { cookies } from "next/headers";
import { API_ENDPOINTS } from "./config";
import { postData } from "./post";
import { updateData } from "./put";
import { EntitlementResponse } from "./types";

// Golden rule: Never return Request, Response, NextRequest, NextResponse, IncomingMessage, ClientRequest, Socket, or raw Axios error objects
type Ok<T>  = { data: { success: true; data?: T; message?: string } };
type Err    = { data: { success: false; message: string; code?: number; details?: unknown } };
type Result<T> = Ok<T> | Err;

function errMsg(e: unknown) {
  // strings only; never return the raw error object
  try {
    if (e && typeof e === "object" && "message" in (e as any)) return String((e as any).message);
    return String(e);
  } catch { return "Unknown error"; }
}

export async function fetchAllSubCategories() {
  try {
    const response = await fetch(API_ENDPOINTS.subCategories);
    const data = await response.json();
    return data?.subcategories || [];
  } catch (error) {
    console.error("Error fetching subcategories:", error);
    return [];
  }
}

export async function deleteAttributeTitleById(attributeId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.deleteAttribute.replace(
        ":attribute_id",
        attributeId.toString()
      ),
      { method: "DELETE" }
    );
    return response.ok;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchAttributeBySubcategoryId(subCategoryId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.fetchAttributesBySubCategory.replace(
        ":sub_category_id",
        subCategoryId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function updateAttributeWeight(
  subCategoryId: number,
  updatedData: any
): Promise<Result<any>> {
  const url = API_ENDPOINTS.updateAttributeWeight.replace(
    ":sub_category_id",
    subCategoryId.toString()
  );

  try {
    const response = await postData(url, updatedData);
    const safe = JSON.parse(JSON.stringify(response));
    return { data: { success: true, data: safe, message: "Attribute weight updated successfully" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}

export async function updateSubcategoryOfAttribute(
  attributeId: number,
  data: { new_subcategory_id: number }
) {
  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(
    ":attribute_id",
    attributeId.toString()
  );

  try {
    const response = await updateData(url, data);
    return response;
  } catch (error) {
    throw new Error("Failed to update attribute subcategory");
  }
}

export async function updateAttributeApprovalStatus(
  attributeId: number,
  data: { is_approved: boolean }
): Promise<Result<any>> {
  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(
    ":attribute_id",
    attributeId.toString()
  );

  try {
    const response = await updateData(url, data);
    const safe = JSON.parse(JSON.stringify(response));
    return { data: { success: true, data: safe, message: "Attribute approval status updated successfully" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}

export async function fetchWeightsBySubcategoryId(subCategoryId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.fetchWeightsBySubCategory.replace(
        ":sub_category_id",
        subCategoryId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchVacancies() {
  try {
    const response = await fetch(API_ENDPOINTS.getVacancies);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchVacanciesByVacancyId(vacancyId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getVacancyByVacancyId.replace(
        ":vacancy_id",
        vacancyId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error while fetching vacancy with ${vacancyId}`, error);
    throw error;
  }
}

export async function fetchCandidatesByVacancyId(vacancyId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getCandidatesByVacancyId.replace(
        ":vacancy_id",
        vacancyId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchResumeByCandidateId(contactId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getResumeByContactId.replace(
        ":contact_id",
        contactId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error while fetching resume:", error);
    return false;
  }
}

interface RecruiterPostReview {
  hiring_decision: string;
  review_message: string;
  candidate_contact_id: string;
  recruiter_email: string;
}

export async function updateRecruiterReview(data: any) {
  const url = API_ENDPOINTS.updateCandidatesReviewData;
  try {
    const response = await postData(url, data);
    return response; // Return the API response
  } catch (error) {
    console.error("Error updating attribute weight:", error);
    throw new Error("Failed to update attribute weight");
  }
}

export async function updateWhyFit(data: any) {
  try {
    const url = API_ENDPOINTS.updateWhyFitData;
    const response = await postData(url, data);
    return response;
  } catch (error) {
    console.log("Error while updating whyfit: ", error);
    return false;
  }
}
export async function fetchEntitlements(
  email_id: string,
  saveHistoryLogs?: boolean
): Promise<EntitlementResponse | false> {
  try {
    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === "true";
    if (!isEntitlementEnabled) {
      const response = {
        error: false,
        code: "TR_01",
        message: "Successful",
        entitlement: {
          Work_force_Index: true,
          Sub_Catregory: true,
          Vacancy: true,
          Search_Match: true,
          Sc_Score_Config: true,
          candidate_tunning_page: true,
          Shorting_Listing: true,
          Historical_Logs: true,
          Regenerate: true,
          Update_Availability: true,
        },
      };
      if (saveHistoryLogs) {
        // Save entitlement in cookies
        const cookieStore = await cookies();
        cookieStore.set("entitlement", JSON.stringify(response.entitlement), {
          secure: true,
        });
      }
      return response;
    }
    const portal_name = "recruiter";
    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(
      email_id
    )}&portal_name=${encodeURIComponent(portal_name)}`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);
    }

    const data: EntitlementResponse = await response.json();
    if (saveHistoryLogs) {
      // Save entitlement in cookies
      (await cookies()).set("entitlement", JSON.stringify(data.entitlement), {
        secure: true,
      });
    }

    return data;
  } catch (error) {
    console.error("Error fetching entitlement data:", error);
    return false;
  }
}
export async function getAllSubcategoryWeightConfigs() {
  try {
    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);
    const data = await response.json();
    return data?.subcategory_weight_configs || [];
  } catch (error) {
    console.error("Error fetching subcategories:", error);
    return [];
  }
}

export async function updateSubcategoryWeightConfig(
  subcategoryId: number,
  data: any
): Promise<Result<any>> {
  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(
    ":subcategory_id",
    subcategoryId.toString()
  );

  try {
    const response = await updateData(url, data);
    const safe = JSON.parse(JSON.stringify(response));
    return { data: { success: true, data: safe, message: "Subcategory weight config updated successfully" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}

export async function postVacanciesShortlisted(data: any) {
  const url = API_ENDPOINTS.vacanciesShortlisted;
  try {
    const response = await postData(url, data);
    return response;
  } catch (error) {
    console.error("error::", error);
    throw error;
  }
}

export async function postHistoricalLogs(
  email: string,
  portalName: string,
  featureName: string,
  metaData: any = null
) {
  const url = API_ENDPOINTS.saveHistoryLogs
    .replace("{email_id}", email)
    .replace("{portal_name}", portalName)
    .replace("{feature}", featureName);

  try {
    const response = await postData(url, metaData);

    if (!response || response.status >= 400) {
      console.error(
        `[postHistoricalLogs] Failed request: ${response.status} ${response.statusText}`
      );
      return null;
    }

    if (!response.data) {
      console.warn(`[postHistoricalLogs] Response missing data:`, response);
      throw new Error(`No data in response for historical logs.`);
    }

    return response.data;
  } catch (error: any) {
    console.error(`[postHistoricalLogs] Exception for ${email}:`, error);
    return null;
  }
}

export async function fetchCatalystMatchStatus(vacancyId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getCatalystMatchStatus.replace(
        ":vacancy_id",
        vacancyId.toString()
      )
    );
    if (!response.ok) {
      const errorData = await response.json(); // Still extract JSON body
      const error = {
        error: true,
        status: response.status,
        message: errorData.detail.error.message || "Unknown error",
      };
      throw new Error(JSON.stringify(error));
    }
    const data = await response.json();
    return data;
  } catch (error) {
    if (
      typeof error === "object" &&
      error !== null &&
      "message" in error &&
      typeof (error as any).message === "string"
    ) {
      throw new Error(
        JSON.stringify({
          error: true,
          status: JSON.parse((error as any).message).status || 500,
          message:
            JSON.parse((error as any).message).message ||
            "Failed to fetch Catalyst Match Status",
        })
      );
    }
  }
}

export async function regenerateCatalystMatch(vacancy_id: string, data: any) {
  const url = API_ENDPOINTS.regenerateCatalystMatch.replace(
    ":vacancy_id",
    vacancy_id
  );
  try {
    const response = await postData(url, data);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function submitCatalystMatchForm(payload: any): Promise<Result<null>> {
  try {
    const resp = await fetch(API_ENDPOINTS.submitCatalystMatch, {
      method: "POST",
      headers: { "Content-Type": "application/json", "Accept": "application/json" },
      body: JSON.stringify(payload),
    });

    const body = await resp.json().catch(() => ({}));

    if (!resp.ok) {
      return { data: { success: false, message: body?.message || "Failed to submit", code: resp.status } };
    }
    return { data: { success: true, message: body?.message || "Saved" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}

export async function getVacancyTemplate(
  { vacancy_id }: { vacancy_id: string }
): Promise<Result<any>> {
  try {
    const resp = await fetch(API_ENDPOINTS.getVacancyTemplate, {
      method: "POST",
      headers: { "Content-Type": "application/json", "Accept": "application/json" },
      body: JSON.stringify({ vacancy_id }),
    });

    const body = await resp.json().catch(() => ({}));

    if (!resp.ok) {
      return { data: { success: false, message: body?.message || "Failed to load template", code: resp.status } };
    }

    const safe = JSON.parse(JSON.stringify(body?.data ?? body));
    return { data: { success: true, data: safe, message: body?.message || "Template retrieved successfully" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}



export async function saveVacancyTemplate(
  templateData: any, 
  regenerate: boolean = false
): Promise<Result<any>> {
  try {
    // Convert camelCase to snake_case and structure the payload correctly
    const templatePayload = {
      vacancy_id: templateData.vacancy_id,
      required_skills: templateData.requiredSkills || [],
      preferred_skills: templateData.preferredSkills || [],
      recency_must_have_skills: templateData.recencyMustHaveSkills || "Current +1",
      additional_job_titles: templateData.additionalJobTitles || [],
      city: templateData.city || "",
      state: templateData.state || "",
      miles: templateData.miles || "50",
      years_experience: templateData.yearsExperience || "",
      degrees: templateData.degrees || [],
      certifications_licenses: templateData.certificationsLicenses || [],
      industry: templateData.industry || "No",
      confidential: templateData.confidential || "No",
      enable_catalyst_match: templateData.enableCatalystMatch || false,
    };

    const payload = {
      template_data: templatePayload,
      regenerate: regenerate
    };

    const resp = await fetch(API_ENDPOINTS.saveVacancyTemplate, {
      method: "POST",
      headers: { "Content-Type": "application/json", "Accept": "application/json" },
      body: JSON.stringify(payload),
    });

    const body = await resp.json().catch(() => ({}));

    if (!resp.ok) {
      return { data: { success: false, message: body?.message || "Failed to save template", code: resp.status } };
    }

    const safe = JSON.parse(JSON.stringify(body?.data ?? body));
    return { data: { success: true, data: safe, message: body?.message || "Template saved successfully" } };
  } catch (e) {
    return { data: { success: false, message: errMsg(e) } };
  }
}
