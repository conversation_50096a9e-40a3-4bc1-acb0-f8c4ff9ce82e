#!/usr/bin/env python3
"""
Configuration module for email processing tools

Contains shared configuration functions for setting up logger, secrets, and Azure credentials.
"""

import os
import sys

# Add parent directory to path to import common modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from common.secrets_env import load_secrets_env_variables
from common.appLogger import App<PERSON>ogger


def setup_logger_and_secrets():
    """Setup logger and load secrets following AI_Agents pattern"""
    # Logger configuration following AI_Agents pattern
    config = {
        "name": "logger_fetch_email",
        "use_syslog": False,
        "log_to_stdout": False,
        "log_file": f"/mnt/incoming/logs/fetch_email.log",
        "log_level": "INFO",
    }
    logger = AppLogger(config)
    
    # Load secrets using the same pattern as AI_Agents
    print("🔑 Loading secrets from GPG files...")
    load_secrets_env_variables()
    
    return logger


def get_azure_credentials():
    """
    Get Azure credentials from environment variables
    
    Returns:
        dict: Dictionary containing Azure credentials
    """
    credentials = {
        'client_id': os.getenv('MAILBOX_RESUMES_CLIENT_ID'),
        'client_secret': os.getenv('MAILBOX_RESUMES_CLIENT_SECRET'),
        'tenant_id': os.getenv('MAILBOX_RESUMES_TENANT_ID')
    }
    
    # Validate that all required credentials are present
    missing_credentials = [key for key, value in credentials.items() if not value]
    if missing_credentials:
        raise ValueError(f"Missing Azure credentials: {missing_credentials}")
    
    return credentials 