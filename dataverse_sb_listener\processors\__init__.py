"""
Entity processors for Dataverse Updates.

This package contains the base processor interface and implementations
for processing different entity types (contacts, vacancies, etc.).
"""

from .entity_processors import (
    BaseEntityProcessor, 
    ProcessorConfig, 
    ProcessorType,
    ContactProcessor,
    VacancyProcessor,
    create_processor, 
    get_default_contact_config, 
    get_default_vacancy_config
)

__all__ = [
    'BaseEntityProcessor',
    'ProcessorConfig', 
    'ProcessorType',
    'ContactProcessor',
    'VacancyProcessor',
    'create_processor',
    'get_default_contact_config',
    'get_default_vacancy_config'
] 