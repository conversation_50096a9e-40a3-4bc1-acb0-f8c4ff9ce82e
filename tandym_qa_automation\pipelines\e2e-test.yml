parameters:
  - name: environment
    type: string
    default: dv

  - name: testSuite
    type: string
    default: tandym_dataprocessing

  - name: captureScreenshots
    type: boolean
    default: false

variables:
  - group: BUILD_VARIABLES_VG
  - name: CI
    value: 'true'
  - name: pythonVersion
    value: '3.11'
  - name: reportDir
    value: 'test-reports'
  - name: AZURE_CLIENT_ID
    value: '571d0a95-3861-4862-a8f1-1dd9d85a474a'
  - name: AZURE_CLIENT_SECRET
    value: 'eTfNa123N6wiwJWq1WsLd0bI9ahncN18zaMSbUiMbTk='
  - name: AZURE_TENANT_ID
    value: 'a197dec9-0864-4030-8028-86c9809afa83'
  - name: AppConfigurationEndpoint
    value: 'https://tg-dvue-dp-appc001.azconfig.io'

jobs:
  - job: RunAutomation
    displayName: Run Python Automation Tests
    pool:
      name: tg-${{ parameters.environment }}-pool

    steps:
      - checkout: self

      - task: UseDotNet@2
        displayName: 'Install .NET 8.0 SDK'
        inputs:
          version: '8.0.x'
          packageType: sdk

      - script: |
          dotnet new tool-manifest
          dotnet tool install SpecSync.AzureDevOps
          dotnet specsync version
        displayName: 'Install SpecSync CLI'

      - script: |
          sudo apt update
          sudo apt install -y python3-pip python3-venv unzip apt-transport-https wget gnupg
          python3 -m venv venv
          source venv/bin/activate
          pip install --upgrade pip
          pip install -r tandym_qa_automation/features/requirements.txt
        displayName: 'Set up Python Environment'
        workingDirectory: $(System.DefaultWorkingDirectory)

      - script: |
          echo "Installing stable Google Chrome via apt..."
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
          sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list'
          sudo apt update
          sudo apt install -y google-chrome-stable
          google-chrome --version
        displayName: 'Install Chrome Stable via APT'

      - script: |
          echo "🧪 Chrome Version:"
          google-chrome --version
        displayName: 'Verify Chrome Installation'

      - script: |
          source venv/bin/activate
          export PYTHONPATH=$(System.DefaultWorkingDirectory):$(System.DefaultWorkingDirectory)/tandym_qa_automation:$(System.DefaultWorkingDirectory)/tandym_qa_automation/features/steps
          export CHROME_BIN=/usr/bin/google-chrome

          mkdir -p tandym_qa_automation/$(reportDir)
          report_file_path="tandym_qa_automation/$(reportDir)/behave-report.json"
          > "$report_file_path"

          echo "Running @automation tagged scenarios only..."
          behave tandym_qa_automation/features \
            --tags=@automation \
            --format json.pretty \
            --outfile "$report_file_path" \
            --no-capture --no-capture-stderr
        displayName: 'Run Behave Tests (Tagged: @automation)'
        condition: succeededOrFailed()
        workingDirectory: $(System.DefaultWorkingDirectory)
        env:
          CAPTURE_ALL_SCREENSHOTS: ${{ parameters.captureScreenshots }}
          AZURE_CLIENT_ID: $(AZURE_CLIENT_ID)
          AZURE_CLIENT_SECRET: $(AZURE_CLIENT_SECRET)
          AZURE_TENANT_ID: $(AZURE_TENANT_ID)
          AppConfigurationEndpoint: $(AppConfigurationEndpoint)
          CHROME_BIN: /usr/bin/google-chrome

      - task: PublishTestResults@2
        displayName: 'Publish Test Results'
        inputs:
          testResultsFiles: '**/behave-report.json'
          testRunTitle: 'Recruiter Portal Test Results'
          failTaskOnFailedTests: false

      - task: PublishPipelineArtifact@1
        displayName: 'Publish Test Report Artifact'
        inputs:
          targetPath: '$(System.DefaultWorkingDirectory)/tandym_qa_automation/$(reportDir)'
          artifactName: 'Test Reports'

      - script: |
          echo "Checking for Screenshots folder..."
          if [ -d $(System.DefaultWorkingDirectory)/Screenshots ]; then
            echo "Screenshots folder found."
            echo "##vso[task.setvariable variable=screenshotsFolderExists;]true"
          else
            echo "No screenshots folder found."
          fi
        displayName: 'Check for Screenshots Folder'
        condition: succeededOrFailed()

      - task: PublishPipelineArtifact@1
        condition: eq(variables.screenshotsFolderExists, 'true')
        displayName: 'Upload Screenshot Artifacts'
        inputs:
          targetPath: '$(System.DefaultWorkingDirectory)/Screenshots'
          artifactName: 'Screenshots-$(Build.BuildId)'

      - script: |
          echo "Cleaning up Screenshots folder..."
          rm -rf $(System.DefaultWorkingDirectory)/Screenshots
        displayName: 'Cleanup Screenshots Folder'
        condition: eq(variables.screenshotsFolderExists, 'true')

      - script: |
          cat tandym_qa_automation/features/specsync.json
          ls -l tandym_qa_automation/$(reportDir)
          cat tandym_qa_automation/$(reportDir)/behave-report.json
        displayName: 'Verify Artifacts'
        condition: succeededOrFailed()

      - script: |
          dotnet specsync publish-test-results tandym_qa_automation/features/specsync.json \
            --testSuite "${{ parameters.environment }}_${{ parameters.testSuite }}" \
            --testResultFileFormat CucumberJson \
            --testResultFile tandym_qa_automation/$(reportDir)/behave-report.json \
            --user "$(DevopsPAT)" \
            --buildId "$(Build.BuildId)" \
            --verbose \
            --runName "Published from Specsync" \
            --testConfiguration "${{ parameters.environment }}_${{ parameters.testSuite }}"
        displayName: 'Publish to SpecSync'
        condition: succeededOrFailed()
