"use client";

import React, { useMemo } from "react";
import { usePathname } from "next/navigation";
import { TAB_ROUTE_MAP } from "@/utils/tabRoutes";
import NotificationBar from "@/components/NotificationBar";
import dynamic from "next/dynamic";

const EntitlementReadyWrapper = dynamic(
  () => import("@/components/HeaderTabs/EntitlementReadyWrapper"),
  {
    ssr: false,
  }
);

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const routes = useMemo(() => Object.values(TAB_ROUTE_MAP) as string[], []);
  
  return (
    <>
      {routes.includes(pathname) && (
        <>
          <NotificationBar />
          <EntitlementReadyWrapper />
        </>
      )}
      <div className="h-full">
        <div className="mx-3">{children}</div>
      </div>
    </>
  );
} 