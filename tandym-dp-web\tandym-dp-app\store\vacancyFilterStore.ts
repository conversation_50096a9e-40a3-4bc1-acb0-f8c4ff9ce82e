"use client";
import { VACANCY_FILTER_LABELS } from "@/library/utils";
import { create } from "zustand";
import { persist } from "zustand/middleware";

export type VacancyFilter = {
  [VACANCY_FILTER_LABELS.STATE]: string[];
  [VACANCY_FILTER_LABELS.CITY]: string[];
  [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: string[];
  [VACANCY_FILTER_LABELS.SHORT_LISTED]: string[];
  [VACANCY_FILTER_LABELS.REVIEW_DECISION]: string[];
  [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: string[];
  [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: string[];
  [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [number, number];
  [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: { from: string; to: string };
  [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: string[];
  [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [number, number];
};

export type ArrayFilterKeys =
  | VACANCY_FILTER_LABELS.STATE
  | VACANCY_FILTER_LABELS.CITY
  | VACANCY_FILTER_LABELS.FRESHNESS_INDEX
  | VACANCY_FILTER_LABELS.SHORT_LISTED
  | VACANCY_FILTER_LABELS.REVIEW_DECISION
  | VACANCY_FILTER_LABELS.AI_AGENT_STATUS
  | VACANCY_FILTER_LABELS.SEARCH_BY_NAME;

type VacancyFilterMap = {
  [vacancyId: string]: VacancyFilter;
};

type FilterStore = {
  ShowFilter: boolean;
  filters: VacancyFilter;
  setFilterField: <K extends keyof VacancyFilter>(
    field: K,
    value: VacancyFilter[K]
  ) => void;
  resetFilter: (maxDistance: number, maxTotalScore: number) => void;
  toggleSearchField: (field: string) => void;
  handleCheckBoxSelection: (label: ArrayFilterKeys, value: string) => void;
  perVacancyFilters: VacancyFilterMap;
  saveFiltersForVacancy: (vacancyId: string, filters: VacancyFilter) => void;
  loadFiltersForVacancy: (vacancyId: string) => VacancyFilter | null;
  saveCurrentFiltersForVacancy: (vacancyId: string) => void;
  clearFiltersForVacancy: (vacancyId: string) => void;
  setDistanceRange: (range: [number, number]) => void;
};

export const defaultFilter: VacancyFilter = {
  [VACANCY_FILTER_LABELS.STATE]: [],
  [VACANCY_FILTER_LABELS.CITY]: [],
  [VACANCY_FILTER_LABELS.FRESHNESS_INDEX]: [],
  [VACANCY_FILTER_LABELS.SHORT_LISTED]: [],
  [VACANCY_FILTER_LABELS.REVIEW_DECISION]: [],
  [VACANCY_FILTER_LABELS.AI_AGENT_STATUS]: [],
  [VACANCY_FILTER_LABELS.SEARCH_BY_NAME]: [],
  [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [0, 0],
  [VACANCY_FILTER_LABELS.AVAILABILITY_DATE_RANGE]: { from: "", to: "" },
  [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: [],
  [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [0, 0], // Default distance range
};

const createFilterStore = (storageKey: string) =>
  create<FilterStore>()(
    persist(
      (set, get) => ({
        ShowFilter: true,
        mercuryPortal: storageKey.includes("mercury"),
        filters: { ...defaultFilter },
        perVacancyFilters: {},

        setFilterField: (field, value) =>
          set((state) => ({
            filters: {
              ...state.filters,
              [field]: value,
            },
          })),

        resetFilter: (maxDistance: number, maxTotalScore: number) =>
          set({
            filters: {
              ...defaultFilter,
              [VACANCY_FILTER_LABELS.TOTAL_SCORE_RANGE]: [0, maxTotalScore],
              [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: [0, maxDistance],
            },
          }),

        toggleSearchField: (field) =>
          set((state) => {
            const searchFields = state?.filters?.[
              VACANCY_FILTER_LABELS.SEARCH_FIELDS
            ].includes(field)
              ? state?.filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS].filter(
                  (f) => f !== field
                )
              : [
                  ...state?.filters?.[VACANCY_FILTER_LABELS.SEARCH_FIELDS],
                  field,
                ];
            return {
              filters: {
                ...state?.filters,
                [VACANCY_FILTER_LABELS.SEARCH_FIELDS]: searchFields,
              },
            };
          }),

        saveFiltersForVacancy: (vacancyId, filters) =>
          set((state) => ({
            perVacancyFilters: {
              ...state.perVacancyFilters,
              [vacancyId.toLowerCase()]: filters,
            },
          })),

        loadFiltersForVacancy: (vacancyId) => {
          const stored = get()?.perVacancyFilters[vacancyId.toLowerCase()];
          return stored ?? null;
        },

        saveCurrentFiltersForVacancy: (vacancyId: string) => {
          const filters = get().filters;
          get().saveFiltersForVacancy(vacancyId, filters);
        },

        setDistanceRange: (range: [number, number]) => {
          set((state) => ({
            filters: {
              ...state?.filters,
              [VACANCY_FILTER_LABELS.DISTANCE_RANGE]: range,
            },
          }));
        },

        clearFiltersForVacancy: (vacancyId: string) => {
          set((state) => {
            const key = vacancyId.toLowerCase();
            const newPerVacancyFilters = { ...state.perVacancyFilters };
            delete newPerVacancyFilters[key];
            return { perVacancyFilters: newPerVacancyFilters };
          });
        },

        handleCheckBoxSelection: (label, selectedValue) =>
          set((state) => {
            const validLabels: ArrayFilterKeys[] = [
              VACANCY_FILTER_LABELS.STATE,
              VACANCY_FILTER_LABELS.CITY,
              VACANCY_FILTER_LABELS.FRESHNESS_INDEX,
              VACANCY_FILTER_LABELS.SHORT_LISTED,
              VACANCY_FILTER_LABELS.REVIEW_DECISION,
              VACANCY_FILTER_LABELS.AI_AGENT_STATUS,
              VACANCY_FILTER_LABELS.SEARCH_BY_NAME,
            ];

            if (!validLabels.includes(label)) {
              console.warn(`Invalid filter label: ${label}`);
              return {};
            }

            const currentValues = state?.filters[label] as string[];
            const newValues = currentValues.includes(selectedValue)
              ? currentValues?.filter((v) => v !== selectedValue)
              : [...currentValues, selectedValue];

            return {
              filters: {
                ...state.filters,
                [label]: newValues,
              },
            };
          }),
      }),
      {
        name: storageKey,
        partialize: (state) => ({
          filters: state?.filters,
          perVacancyFilters: state?.perVacancyFilters,
        }),
      }
    )
  );

// Export both stores — one for mercuryPortal, one for default
export const useMercuryFilterStore = createFilterStore("mercury-filter-store");
export const useDefaultFilterStore = createFilterStore(
  "recruiter-filter-store"
);
